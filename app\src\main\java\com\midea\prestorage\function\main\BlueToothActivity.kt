package com.midea.prestorage.function.main

import android.graphics.*
import android.os.Bundle
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.view.View
import android.widget.TextView
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.printer.BluetoothConnectBack
import com.midea.prestorage.printer.HPRTConnectUtils
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.AppUtils
import com.midea.prestoragesaas.R
import com.snbc.sdk.barcode.BarInstructionImpl.BarPrinter
import cpcl.PrinterHelper
import io.reactivex.disposables.Disposable


class BlueToothActivity : BaseActivity() {
    var printer: BarPrinter? = null

    var subscribe: Disposable? = null
    private val blueBack = object : BluetoothConnectBack {
        override fun success() {
            waitingDialogHelp.hidenDialog()
        }

        override fun fail() {
            AppUtils.showToast(application, "打印机连接失败，请重启打印机!")
        }

        override fun connect() {
            bluetoothOpen()
        }
    }

    private fun bluetoothOpen() {
        Printer.openBluetooth(this, blueBack)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_blue_tooth)
        findViewById<View>(R.id.tv_print).setOnClickListener {
            printPackedInfoOperation()
        }

        Printer.openBluetooth(this, blueBack)
//        HPRTConnectUtils.openBluetooth(this, blueBack)
    }

    override fun onDestroy() {
        super.onDestroy()
        Printer.closeBluetooth()
    }

    override fun getTvInfo(): TextView? {
        return null
    }

    private fun print1() {
        PrinterHelper.printAreaSize("0", "0", "0", "400", "1")
        PrinterHelper.PageWidth("550")
        PrinterHelper.Align(PrinterHelper.CENTER)

        PrinterHelper.Box("0", "10", "550", "390", "2")

        PrinterHelper.Align(PrinterHelper.CENTER)
        PrinterHelper.Barcode(
            PrinterHelper.BARCODE,
            PrinterHelper.code128,
            "2",
            "0",
            "80",
            "0",
            "25",
            false,
            "7",
            "5",
            "5",
            "11111111122222" //商品编码
        )

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            24,
            "0",
            "115",
            "11111111122222", //商品编码
            0,
            false,
            100
        )

        PrinterHelper.Align(PrinterHelper.LEFT)

        printText(20, 155, "111111111111111111111111111111111111111111111111111111111111111111111111", 40)

        var point = 155 + (getLinesPrint("111111111111111111111111111111111111111111111111111111111111111111111111", 40) - 1) * 20 + 20

        printText(20, point, "2222222222222222222", 35)

        point += (getLinesPrint("2222222222222222222", 35) - 1) * 20 + 20

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            24,
            "20",
            point.toString(),
            "SKU数：33", //商品编码
            0,
            false,
            100
        )

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            24,
            "270",
            point.toString(),
            "总数量：300", //商品编码
            0,
            false,
            100
        )

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            24,
            "20",
            (point + 40).toString(),
            "张三/2024-07-22 16:36:00", //商品编码
            0,
            false,
            100
        )

        PrinterHelper.Form()
        PrinterHelper.Print()
    }

    private fun print() {
        PrinterHelper.printAreaSize("0", "0", "0", "400", "1")
        PrinterHelper.PageWidth("550")
        PrinterHelper.Align(PrinterHelper.CENTER)

        PrinterHelper.Box("0", "20", "550", "400", "2")

        PrinterHelper.Align(PrinterHelper.LEFT)

        PrinterHelper.Barcode(
            PrinterHelper.BARCODE,
            PrinterHelper.code128,
            "1",
            "0",
            "60",
            "50",
            "30",
            false,
            "7",
            "5",
            "5",
            "54543543543435435"
        )

        PrinterHelper.SetBold("5")//对下⾯的字体进⾏加粗（如不需要加粗不⽤添加）
        PrinterHelper.SetMag("3", "3")//对下⾯的字体进⾏放⼤（如不需要不⽤添加

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            16,
            "360",
            "35",
            "E2-1321",
            0,
            false,
            100
        )

        PrinterHelper.SetBold("0")
        PrinterHelper.SetMag("1", "1")

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            24,
            "50",
            "100",
            "SKU数：1",
            1,
            false,
            100
        )

        PrinterHelper.Align(PrinterHelper.LEFT)

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            24,
            "190",
            "100",
            "整件：100",
            1,
            false,
            100
        )

        PrinterHelper.Align(PrinterHelper.LEFT)

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            24,
            "330",
            "100",
            "拆零：0.0001",
            1,
            false,
            100
        )

        printText(50, 140, "门店：好丽友好丽友好丽友好丽友好丽友好丽友", 38, bold = true)

        var point = 140 + (getLinesPrint("门店：好丽友好丽友好丽友好丽友好丽友好丽友", 38) - 1) * 20 + 40

        printText(
            50,
            point,
            "备注：好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友好丽友111",
            38,
            2
        )

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            16,
            "50",
            "300",
            "拣货：",
            0,
            false,
            100
        )

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            16,
            "380",
            "300",
            "复核：",
            0,
            false,
            100
        )


        PrinterHelper.Form()
        PrinterHelper.Print()
    }

    private fun printPackedInfoOperation() {
        PrinterHelper.printAreaSize("0", "0", "0", "400", "1")
        PrinterHelper.PageWidth("550")
        PrinterHelper.Align(PrinterHelper.CENTER)

        PrinterHelper.Box("0", "20", "550", "400", "2")

        PrinterHelper.Align(PrinterHelper.LEFT)

        PrinterHelper.Barcode(
            PrinterHelper.BARCODE,
            PrinterHelper.code128,
            "1",
            "0",
            "60",
            "30",
            "30",
            false,
            "7",
            "5",
            "5",
            "54543543543435435"
        )

        PrinterHelper.SetBold("5")//对下⾯的字体进⾏加粗（如不需要加粗不⽤添加）
        PrinterHelper.SetMag("2", "2")//对下⾯的字体进⾏放⼤（如不需要不⽤添加

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            16,
            "340",
            "45",
            "散件第1箱",
            0,
            false,
            100
        )

        PrinterHelper.SetBold("0")
        PrinterHelper.SetMag("1", "1")

        printText(30, 100, "好丽友好丽友好丽友好丽友好丽友好丽友", 22, bold = true)

        var point = 100 + (getLinesPrint("好丽友好丽友好丽友好丽友好丽友好丽友", 22) - 1) * 20 + 30

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            24,
            "30",
            point.toString(),
            "SKU数：1",
            1,
            false,
            100
        )

        PrinterHelper.Align(PrinterHelper.LEFT)

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            24,
            "170",
            point.toString(),
            "总数量：100",
            1,
            false,
            100
        )

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            24,
            "30",
            (point + 40).toString(),
            "cust143242342343243243",
            0,
            false,
            100
        )

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            24,
            "30",
            (point + 80).toString(),
            "张三/2024-9-21 09:35:12",
            0,
            false,
            100
        )

        PrinterHelper .PrintQR(PrinterHelper.BARCODE, "340", "100", "1", "6", "(1)库存共享商品130：2.0000EA\\r\\n")

        PrinterHelper.PrintTextCPCL(
            PrinterHelper.TEXT,
            16,
            "360",
            "280",
            "浏览器扫扫查详情",
            0,
            false,
            100
        )

        PrinterHelper.Form()
        PrinterHelper.Print()
    }

    private fun printText(
        x: Int,
        y: Int,
        data: String,
        maxLength: Int,
        maxLine: Int = 0,
        bold: Boolean = false
    ) {
        var length = 0f
        var times = 0
        var sb = StringBuffer()
        data.forEachIndexed { index, c ->
            length += if (isChinese(c)) {
                2f
            } else {
                1f
            }
            sb.append(c)
            if (length >= maxLength || index == data.length - 1) {
                PrinterHelper.PrintTextCPCL(
                    PrinterHelper.TEXT,
                    24,
                    x.toString(),
                    (y + (times * 25)).toString(),
                    sb.toString(),
                    if (bold) 1 else 0,
                    false,
                    100
                )
                length = 0f
                sb = StringBuffer()
                times++
                if (maxLine != 0 && times >= maxLine) {
                    return
                }
            }
        }
    }

    private fun isChinese(c: Char): Boolean {
        return c.toInt() in 0x4E00..0x9FA5 // 根据字节码判断
    }

    private fun getLinesPrint(str: String, maxLength: Int): Int {
        var length = 0f
        var times = 1
        var sb = StringBuffer()
        str.forEachIndexed { index, c ->
            length += if (isChinese(c)) {
                2f
            } else {
                1f
            }
            sb.append(c)
            if (length >= maxLength || index == str.length - 1) {
                length = 0f
                sb = StringBuffer()
                times++
            }
        }

        return times
    }

    private fun getNoBlankStr(str: String?): String {
        return if (str.isNullOrBlank()) {
            ""
        } else {
            str!!.trim()
        }
    }

    private fun getNoBlankAndEnterStr(str: String?): String {
        var temp = str
        while (temp != null && temp.contains("\t")) {
            temp = temp.replace("\t", "")
        }
        return getNoBlankStr(temp)
    }


    fun toBitmap(bitmap: Bitmap, width: Int, height: Int): Bitmap? {
        val target = Bitmap.createBitmap(width, height, bitmap.config)
        val canvas = Canvas(target)
        canvas.drawBitmap(bitmap, null, Rect(0, 0, target.width, target.height), null)
        return target
    }

    private fun textAsBitmap(text: String?, textSize: Float): Bitmap {
        val textPaint = TextPaint()
        textPaint.color = Color.BLACK
        textPaint.textSize = textSize
        val font: Typeface = Typeface.create(text, Typeface.BOLD)
        textPaint.typeface = font
        val layout = StaticLayout(
            text, textPaint,
            textPaint.measureText(text).toInt(),
            Layout.Alignment.ALIGN_NORMAL, 1.3f, 0.0f, true
        )
        val bitmap = Bitmap.createBitmap(
            layout.width + 20,
            layout.height + 20, Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(bitmap)
        canvas.drawColor(Color.WHITE) //这个随便设置的，反正最后也会变透明
        layout.draw(canvas)
        return bitmap
    }
}