package com.midea.prestorage.function.containerpick

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayoutMediator
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.App
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.ErrorToaster
import com.midea.prestorage.beans.net.PickingContainer
import com.midea.prestorage.function.containerpick.fragment.BulkAlreadyPackedFragment
import com.midea.prestorage.function.containerpick.fragment.BulkToBePackedFragment
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.printer.BluetoothConnectBack
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.fixTouchSlop
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityBulkPackingBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class BulkPackingActivity : BaseViewModelActivity<BulkPackingVM>(),
    ErrorToaster {

    lateinit var binding: ActivityBulkPackingUnionBinding
    private val titles = arrayOf("待打包", "已打包")

    val fragments = mutableListOf<Fragment>(
        BulkToBePackedFragment.newInstance(),
        BulkAlreadyPackedFragment.newInstance()
    )

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityBulkPackingUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_bulk_packing_care
                )
            )
        } else {
            ActivityBulkPackingUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_bulk_packing
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        vm = ViewModelProvider(this, ViewModelProvider.AndroidViewModelFactory(App.mInstance))
            .get(BulkPackingVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        initData()
        initViewPage()

        binding.viewPager.fixTouchSlop()

        lifecycleScope.launch {
            while (Constants.isShowContainerPick) {
                delay(60 * 1000)
                bluetoothOpen(true)
            }
        }

        vm.mBluetoothOpen.observe(this, Observer<Boolean> {
            if (it) {
                bluetoothOpen(false)
            }
        })
    }

    fun initData() {
        vm.taskCode.set(intent.getStringExtra("taskCode"))
        vm.containerCode.set(intent.getStringExtra("containerCode"))
        binding.vm?.pickingContainerList =
            intent.getParcelableArrayListExtra<PickingContainer>("pickingContainerList")
    }

    private fun initViewPage() {
        binding.viewPager.adapter = PackFragmentAdapter(this)
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                vm.updateCurrent(position)
            }
        })

        val tabLayoutMediator =
            TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
                tab.text = titles[position]
            }
        tabLayoutMediator.attach()

        binding.viewPager.offscreenPageLimit = 1
    }

    private val blueBack = object : BluetoothConnectBack {
        override fun success() {
            waitingDialogHelp.hidenDialog()
            vm.isPrintOk.set(Printer.connectUtils?.isPrintOk())
        }

        override fun fail() {
            vm.isPrintOk.set(Printer.connectUtils?.isPrintOk())
            AppUtils.showToast(this@BulkPackingActivity, "打印机连接失败，请重启打印机!")
        }

        override fun connect() {
            bluetoothOpen()
        }
    }

    fun bluetoothOpen(isAuto: Boolean = true) {
        if (!Printer.isPrintOk()) {
            Printer.openBluetooth(this, blueBack, isAuto)
        } else {
            vm.isPrintOk.set(true)
        }
    }

    override fun onResume() {
        super.onResume()
        if (Constants.isShowContainerPick) {
            bluetoothOpen(true)
        }
    }

    inner class PackFragmentAdapter(activity: FragmentActivity) : FragmentStateAdapter(activity) {

        override fun getItemCount(): Int {
            return fragments.size
        }

        override fun createFragment(position: Int): Fragment {
            return fragments[position]
        }
    }

    override fun showError(msg: String) {
        vm.showNotification(msg, false)
    }

}