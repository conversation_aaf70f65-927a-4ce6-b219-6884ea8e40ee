package com.midea.prestorage.function.inpool

import android.content.Intent
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.net.InPoolStorageList
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.function.instorage.InStorageActivity
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody


class InPoolStorageVM(val activity: InPoolStorageActivity) {

    val isRefreshing = ObservableBoolean(false)
    val isNoData = ObservableBoolean(false)

    // 搜索条件 入库类型
    var searchOrderType = ObservableField<String>("") //传给后端的英文
    var displaySearchOrderType = ObservableField<String>("") //前端显示的中文

    // 搜索条件 单号
    var searchOrderNo = ObservableField<String>("")

    // 车牌号
    var searchCarNo = ObservableField<String>("")

    // 搜索条件  日期范围 默认3天
    var searchDayRange = ObservableField<String>("")

    var status0 = ObservableField<Boolean>(false)
    var status100 = ObservableField<Boolean>(false)
    var status300 = ObservableField<Boolean>(false)
    var status900 = ObservableField<Boolean>(false)
    var status999 = ObservableField<Boolean>(false)

    // 当前页码
    var curPageNo = 1

    init {

    }

    fun resetDefault() {
        //初始化默认查询条件
        // 条件0. 仓库编码 读取当前账号
        // 条件1. 单号
        searchOrderNo.set("")
        // 条件2. 车牌号 置空
        searchCarNo.set("")
        // 条件3. 多选状态列表  默认选中新建
        status100.set(true)
        // 条件4. 入库类型
        searchOrderType.set("")
        displaySearchOrderType.set("全部订单类型")

        // 条件5. 日期范围
        searchDayRange.set("3天")

        status0.set(false)
        status100.set(true)
        status300.set(false)
        status900.set(false)
        status999.set(false)

        activity.adapter.data.clear()
        activity.adapter.notifyDataSetChanged()

    }

    fun onResetSearch() {
        activity.popBinding.tvOrderType.setText("")
        resetDefault()
    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        // 下拉刷新
        search(false)
    }

    fun onEnterOrderNo() {
        if (!searchOrderNo.get().isNullOrBlank()) {
            //activity.popupWindow.dismiss()
            search(false)
        }
    }


    fun search(isLoadNextPage: Boolean) {

        if (isLoadNextPage == false) {
            // 从头加载第一页数据
            curPageNo = 1
        }

        isRefreshing.set(true)

        val param = mutableMapOf<String, Any>(
            "whCode" to activity.getWhCode(),
            "content" to searchOrderNo.get().toString(),
            "carNo" to searchCarNo.get().toString()
        )

        // 条件3. 多选状态列表
        val statusList = mutableListOf<String>()
        if (status0.get()!!) {  //全部
            DCUtils.inOrderStatusC2N.forEach {
                // 从字典加载所有code
                statusList.add(it.key)
            }
        } else {
            // 100 新建 ， 200到货确认 ， 300 收货中 ， 350 收货完成 ，  900 关闭 ， 950拒收， 999 取消
            if (status100.get()!!) {
                statusList.add("100")
            }
            if (status300.get()!!) {
                statusList.add("300")
            }
            if (status900.get()!!) {
                statusList.add("900")
            }
            if (status999.get()!!) {
                statusList.add("999")
            }
        }
        param["statusList"] = statusList
        // 条件4. 入库订单类型
        if (!searchOrderType.get().isNullOrEmpty()) {
            param["receiptType"] = searchOrderType.get().toString()
        }
        // 条件5. 日期范围
        param["currentDay"] = searchDayRange.get().toString().split("天")[0]

        // 第几页
        param.put("pageNo", curPageNo)
        // 每页多少条数据
        param.put("pageSize", 20)

        if (!activity.isLoadParamFromMainActivity) {
            loadByParam(param);
        }
    }

    fun loadByParam(param: MutableMap<String, Any>) {

        activity.isLoadParamFromMainActivity = false

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        //activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getAppAPI()
            .inOrderList(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PageResult<InPoolStorageList>>(activity) {
                override fun success(page: PageResult<InPoolStorageList>?) {
                    isRefreshing.set(false)
                    isNoData.set(page == null || page.list == null || page.list.isEmpty())
                    activity.adapter.loadMoreModule.loadMoreComplete()

                    if (page != null && page.list != null) {
                        activity.setCbSelectChecked(false)
                        activity.binding.textResultCount.setText("筛选(" + page.totalCount + ")")
                        if (curPageNo == 1) {
                            activity.showData(page.list)
                        } else {
                            activity.adapter.addData(page.list)
                            activity.adapter.notifyDataSetChanged()
                        }

                        if (curPageNo >= page.totalPage) {
                            //加载到了最后一页
                            activity.adapter.loadMoreModule.loadMoreEnd()
                        } else {
                            curPageNo = page.pageNo + 1
                        }
                    }

                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isRefreshing.set(false)

                    //activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }


    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        result?.let {
            searchOrderNo.set(result)
            activity.binding.llContainer.performClick()
        }
    }

    fun initCarNo() {

        val param = mutableMapOf(
            "currentDay" to searchDayRange.get().toString().split("天")[0],
            "whCode" to activity.getWhCode()
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getAppAPI()
            .inOrderCarNo(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<String>>(activity) {
                override fun success(data: MutableList<String>?) {
                    if (data != null) {
                        val beans = mutableListOf<BaseItemShowInfo>()
                        var hashSetCarNo = HashSet<String>() //车牌号排重
                        data.forEach {
                            if (!it.isNullOrBlank() && !hashSetCarNo.contains(it)) {
                                val bean = BaseItemShowInfo()
                                bean.showInfo = it
                                beans.add(bean)
                                hashSetCarNo.add(it)
                            }
                        }
                        activity.initCarNoDialogData(beans)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }


    fun back() {
        activity.finish()
    }


    // 组单收货  按钮事件
    // 需求 勾选符合条件的入库订单生成波次号后跳转到前置仓收货界面用波次号收货
    fun waveReceive() {

        val list = activity.adapter.returnBeans as List<InPoolStorageList>
        if (list.size == 0) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "未勾选订单!")
            return
        }

        val inOrders = list.map {
            it.receiptCode
        }

        val param = mutableMapOf("receiptCodeList" to inOrders)
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        // 先禁用按钮 请求返回时才开启按钮  避免网络卡顿时用户重复点击
        activity.binding.btnWaveReceipt.setBackgroundResource(R.drawable.bg_bt_gray)
        activity.binding.btnWaveReceipt.isClickable = false
        activity.waitingDialogHelp.showDialog()

        RetrofitHelper.getAppAPI()
            .validateMultiSelectedInOrders(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {

                    activity.waitingDialogHelp.hidenDialog()
                    activity.binding.btnWaveReceipt.setBackgroundResource(R.drawable.bg_bt_green)
                    activity.binding.btnWaveReceipt.isClickable = true

                    data?.let { //校验成功
                        AlertDialogUtil.showOkAndCancelDialog(activity, "已生成波次号" + data.toString() + "，是否开始收货? ",
                            { dialogInterface, i ->  //点了确定
                                val it = Intent(activity, InStorageActivity::class.java)
                                // 这里塞一个 后端返回的波次号，启动扫码收货界面
                                it.putExtra("orderNo", data.toString())
                                it.putExtra("orderReceiveType", "wave")
                                activity.isNeedRefresh = true
                                activity.startActivity(it)
                            },
                            { dialogInterface, i ->  //点了取消
                                //组了单，生成了波次号，刷新下数据，才能马上看到生成好的波次号
                                search(false)
                            })
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {

                    activity.waitingDialogHelp.hidenDialog()
                    activity.binding.btnWaveReceipt.setBackgroundResource(R.drawable.bg_bt_green)
                    activity.binding.btnWaveReceipt.isClickable = true

                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }


    // 选择订单类型
    /*fun onSelectOrderType() {

        AlertDialogUtil.showSelectListDialog(activity, "选择订单类型", DCUtils.inOrderTypeN2C, object :
            AlertDialogUtil.OnSelectKey {
            override fun onSelect(key: String?, value: Any?) {
                activity.popBinding.tvOrderType.setText(key)
                searchOrderType.set(value.toString())   //搜索条件之一: 订单类型
                displaySearchOrderType.set(key)
                search()
            }
        })

    }*/

    fun clearOrderNo() {
        searchOrderNo.set("")
    }


    fun clickStatus(status: String) {
        if (status.equals("0")) {
            if (!status0.get()!!) {
                status0.set(true) //选了全部 ，则其他全部置灰
                status100.set(false)
                status300.set(false)
                status900.set(false)
                status999.set(false)
            } else {
                status0.set(false)
            }
        } else {
            status0.set(false)

            if (status.equals("100")) {
                status100.set(status100.get()?.not())
            } else if (status.equals("300")) {
                status300.set(status300.get()?.not())
            } else if (status.equals("900")) {
                status900.set(status900.get()?.not())
            } else if (status.equals("999")) {
                status999.set(status999.get()?.not())
            }
        }
        // 不需要立即刷新 收起筛选框的时候再刷新
        // search()
    }

    // 按单收货 按钮事件
    // 需求 勾选符合条件的入库订单后跳转到前置仓收货界面按单收货
    fun orderReceive() {

        // 先校验所选入库单，校验逻辑: 必须单选一个(不能多选，不能不选) 并且选的入库单 状态 要为‘新建’或者‘收货中’，status<350
        // 这个校验逻辑在后端  前端这边只需要把用户勾选的单号提交给后端 就行了
        var list = activity.adapter.returnBeans as List<InPoolStorageList>
        if (list == null || list.size == 0) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "未勾选订单!")
            return
        }

        val inOrders = list.map {
            it.receiptCode
        }

        val param = mutableMapOf("receiptCodeList" to inOrders)
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        // 先禁用按钮 请求返回时才开启按钮  避免网络卡顿时用户重复点击
        activity.binding.btnSingleReceipt.setBackgroundResource(R.drawable.bg_bt_gray)
        activity.binding.btnSingleReceipt.isClickable = false
        activity.waitingDialogHelp.showDialog()

        RetrofitHelper.getAppAPI()
            .validateSelectedInOrder(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {

                    activity.waitingDialogHelp.hidenDialog()
                    activity.binding.btnSingleReceipt.setBackgroundResource(R.drawable.bg_bt_blue)
                    activity.binding.btnSingleReceipt.isClickable = true

                    data?.let { //校验成功
                        if (inOrders.size > 0) {
                            val it = Intent(activity, InStorageActivity::class.java)
                            //这里要塞一个 后端返回的入库单号，启动扫码收货界面
                            it.putExtra("orderNo", data.toString())
                            it.putExtra("orderReceiveType", "receipt")  //收货方式:  receipt 入库单
                            activity.isNeedRefresh = true
                            activity.startActivity(it)
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    activity.binding.btnSingleReceipt.setBackgroundResource(R.drawable.bg_bt_blue)
                    activity.binding.btnSingleReceipt.isClickable = true
                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun onItemClick(bean: InPoolStorageList?) {
        val it = Intent(activity, InPoolStorageDetailActivity::class.java)
        it.putExtra("bean", bean)
        activity.isNeedRefresh = true
        activity.startActivity(it)
    }
}