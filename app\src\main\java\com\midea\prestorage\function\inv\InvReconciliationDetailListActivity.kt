package com.midea.prestorage.function.inv

import android.app.AlertDialog
import android.os.Bundle
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.RespAdjustDetailPage
import com.midea.prestorage.function.inv.response.RespMaterialList
import com.midea.prestorage.utils.AppUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityInvReconciliationDetailListBinding
import com.midea.prestoragesaas.databinding.PopViewForSelectCustemItemCodeBinding

class InvReconciliationDetailListActivity : BaseViewModelActivity<InvReconciliationDetailListVM>() {
    private lateinit var binding: ActivityInvReconciliationDetailListBinding

    //69码或sn码 查询出多个custItemCodes的时候 弹框让用户选择custItemCode
    lateinit var dlgSelectCustItemCode: AlertDialog
    private lateinit var popBindingSelectCustItemCode: PopViewForSelectCustemItemCodeBinding
    lateinit var popAdapterSelectCustItemCode: PopListAdapter

    private lateinit var adapter: InStorageOrderAdapter

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_inv_reconciliation_detail_list)
        vm = ViewModelProvider.AndroidViewModelFactory(application)
            .create(InvReconciliationDetailListVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        //选择商品
        vm.showGoods.observe(this, { data ->
            //扫69码 后端没有返回 custItemCode  如果有custItemCode数组，就弹框选择
            dlgSelectCustItemCode.show()
            popAdapterSelectCustItemCode.data.clear()
            data.forEach {
                popAdapterSelectCustItemCode.addData(it)
            }
            popAdapterSelectCustItemCode.notifyDataSetChanged()
        })

        vm.loadMoreComplete.observe(this, {
            if (it == 1) {
                adapter.loadMoreModule.loadMoreComplete()
            } else if (it == 2) {
                adapter.loadMoreModule.loadMoreEnd()
            }
        })

        vm.showDatas.observe(this, {
            showData(it)
        })

        vm.loadMoreDatas.observe(this, {
            loadMoreData(it)
        })

        initData()
        initPopWinSelectCustItemCode()
        initRecycleView()
        initLoadMore()
    }

    fun initData() {
        vm.respAdjustList =
            intent.getParcelableExtra("RespAdjustList")

        vm.respAdjustList?.let {
            vm.title.value = it.adNo
        }

        AppUtils.requestFocus(binding.etLocCode)
    }

    // 初始化选择客户商品编码的弹窗
    private fun initPopWinSelectCustItemCode() {

        val popViewSelectCustItemCode =
            LayoutInflater.from(this).inflate(R.layout.pop_view_for_select_custem_item_code, null)
        popBindingSelectCustItemCode = DataBindingUtil.bind(popViewSelectCustItemCode)!!

        val alertDialogBuilder = AlertDialog.Builder(this)
        alertDialogBuilder.setView(popViewSelectCustItemCode)
        dlgSelectCustItemCode = alertDialogBuilder.create()

        popAdapterSelectCustItemCode = PopListAdapter()
        popBindingSelectCustItemCode.recyclerViewInPop.layoutManager = LinearLayoutManager(this)
        popBindingSelectCustItemCode.recyclerViewInPop.adapter = popAdapterSelectCustItemCode

        popAdapterSelectCustItemCode.setOnItemClickListener { adapter, _, position ->
            val data = adapter.getItem(position) as RespMaterialList
            vm.serialNo.set(data.itemCode ?: "")
            vm.onRefreshCommand.onRefresh()
            dlgSelectCustItemCode.dismiss()
        }

        popBindingSelectCustItemCode.closePop.setOnClickListener {
            dlgSelectCustItemCode.dismiss()
        }

    }

    fun initRecycleView() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        adapter = InStorageOrderAdapter()
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.itemAnimator = null
        binding.recycle.adapter = adapter
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.initOrderList()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    fun showData(data: MutableList<RespAdjustDetailPage>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()

        vm.isNoData.set(adapter.data.isNullOrEmpty())
        vm.isRefreshing.set(false)
    }

    fun loadMoreData(data: MutableList<RespAdjustDetailPage>?) {
        data?.let { adapter.addData(it) }
        adapter.notifyDataSetChanged()
    }

    override fun onResume() {
        super.onResume()
        vm.onRefreshCommand.onRefresh()
    }

    class PopListAdapter :
        CommonAdapter<RespMaterialList>(R.layout.item_pop_view_for_select_cust_item_code_pop) {
        override fun convert(holder: BaseViewHolder?, item: RespMaterialList) {
            super.convert(holder, item)

            holder?.setGone(R.id.tv_item_name, item.itemName.isNullOrEmpty())
        }
    }

    class InStorageOrderAdapter :
        CommonAdapter<RespAdjustDetailPage>(
            R.layout.item_inv_reconciliation_detail
        ), LoadMoreModule {

        override fun convert(holder: BaseViewHolder?, item: RespAdjustDetailPage?) {
            super.convert(holder, item)

            holder?.setText(
                R.id.tv_actQty,
                AppUtils.getBigDecimalValueStr(item?.actQty) + " " + (item?.packageDetails?.find { "EA" == it.cdprUnit }?.cdprDesc
                    ?: "")
            )

        }
    }
}