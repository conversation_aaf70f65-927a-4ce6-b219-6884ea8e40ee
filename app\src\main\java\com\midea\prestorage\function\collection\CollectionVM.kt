package com.midea.prestorage.function.collection

import CheckUtil
import android.content.Intent
import android.text.TextUtils
import android.util.Log
import android.view.View
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.CollectionInfo
import com.midea.prestorage.beans.net.HandingInfo
import com.midea.prestorage.beans.setting.HandingInfoDb
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.dialog.TipDialog
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.DbUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import okhttp3.MediaType
import okhttp3.RequestBody
import org.xutils.db.sqlite.WhereBuilder

class CollectionVM(val activity: CollectionActivity) {

    val title = ObservableField("入库扫码")
    val num = ObservableField("")
    val totalQty = ObservableField("")
    val qty = ObservableField("")
    val notQty = ObservableField("")
    val waveNo = ObservableField("")
    val custOrderNo = ObservableField("")
    val statueStr = ObservableField("")
    val storeName = ObservableField("")
    val shipToAttentionTo = ObservableField("")
    var currentHandingList: HandingInfo.HandingList? = null
    val handlingGroupName = ObservableField<String>("")

    // 单号 (入库单号或波次单号)
    var curOrderNo = ObservableField("")

    val status = ObservableField(View.GONE)

    var bean: CollectionInfo? = null
    private val tipDialog: TipDialog = TipDialog(activity)
    var shipmentStatue: MutableList<DCBean>? = null
    val db = DbUtils.db

    private var handingDialog: FilterDialog

    init {
        DCUtils.shipmentStatus(activity as RxAppCompatActivity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                shipmentStatue = statusDC
            }
        })

        handingDialog = FilterDialog(activity)
        handingDialog.setTitle("请选择装卸组")
        handingDialog.dismissEdit()
        handingDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            currentHandingList = it as HandingInfo.HandingList
            handlingGroupName.set(it.showInfo)

            saveUserInfo()
            handingDialog.dismiss()
        })
    }

    private fun initHandling() {
        Observable.create<HandingInfoDb> {
            var handingInfo =
                db.selector(HandingInfoDb::class.java).where("userId", "==", Constants.userInfo?.id)
                    .and(WhereBuilder.b("mode", "==", 2))
                    .findFirst()

            if (handingInfo == null) {
                it.onNext(HandingInfoDb())
            } else {
                it.onNext(handingInfo)
            }
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<HandingInfoDb> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: HandingInfoDb) {
                    if (t.userId == null) {
                        handlingGroup(true)
                    } else {
                        handlingGroupName.set(t.handlingName)
                        currentHandingList = HandingInfo.HandingList()
                        currentHandingList!!.handlingName = t.handlingName
                        currentHandingList!!.handlingCode = t.handlingCode
                        handlingGroup(false)
                    }
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    private fun handlingGroup(b: Boolean) {

        RetrofitHelper.getAddGoodsService()
            .handlingGroup(Constants.tenantCode, activity.getWhCode(), "02")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<HandingInfo>(activity) {
                override fun success(data: HandingInfo?) {
                    data?.list?.forEach {
                        it.showInfo = it.handlingName
                    }
                    data?.list?.removeAll { it.showInfo.isNullOrBlank() }
                    if (data?.list != null && !data.list.isNullOrEmpty()) {
                        handingDialog.addAllData(data.list as MutableList<BaseItemShowInfo>)
                        if (b) {
                            handingDialog.setUnCancel()
                            handingDialog.show()
                        } else if (data?.list.none { item ->
                                item.handlingCode == currentHandingList!!.handlingCode
                            }) {
                            AppUtils.showToast(activity, "该装卸组已失效,请重新选择装卸组!")
                            handingDialog.setUnCancel()
                            handingDialog.show()
                        }
                    } else {
                        AppUtils.showToast(activity, "未配置装卸组,不能使用该功能!")
                        activity.finish()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }


    fun saveUserInfo() {
        Observable.create<String> {
            try {
                var findFirst = db.selector(HandingInfoDb::class.java)
                    .where("userId", "==", Constants.userInfo?.id)
                    .and(WhereBuilder.b("mode", "==", 2)).findFirst()

                if (findFirst == null) {
                    findFirst = HandingInfoDb()
                }
                findFirst.userId = Constants.userInfo?.id
                findFirst.handlingCode = currentHandingList?.handlingCode
                findFirst.handlingName = currentHandingList?.handlingName
                findFirst.mode = 2

                db.saveOrUpdate(findFirst)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {

                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        result?.let {
            curOrderNo.set(result)
            onEnterOrderNo()
        }
    }

    fun onEnterOrderNo() {
        if (CheckUtil.isFastDoubleClick()) {
            checkType()
        }
    }

    fun detail() {
        if (bean != null) {
            val it = Intent(activity, CollectionDetailActivity::class.java)
            it.putExtra("shipmentCode", bean?.shipmentCode)
            activity.startActivity(it)
        }
    }

    private fun checkType() {
        activity.waitingDialogHelp.showDialog()
        val map = mutableMapOf(
            "waybillNo" to curOrderNo.get().toString(),
            "userCode" to Constants.userInfo?.name,
            "whCode" to activity.getWhCode()
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        RetrofitHelper.getAddGoodsService()
            .waybillNo(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<CollectionInfo>(activity) {
                override fun success(data: CollectionInfo?) {
                    if (data != null) {
                        num.set(data.num.toString())
                        totalQty.set(data.totalQty)
                        qty.set(data.qty)
                        notQty.set(data.notQty)
                        waveNo.set(data.waveNo)
                        custOrderNo.set(data.custOrderNo)
                        statueStr.set(shipmentStatue?.find { it.value.toString() == data.status }?.key)
                        storeName.set(data.storeName)
                        shipToAttentionTo.set(data.shipToAttentionTo)

                        status.set(View.VISIBLE)
                        bean = data

                        if (data.status == "800") {
                            if (data.totalQty != data.qty) {
                                showTipDialog("订单数${data.totalQty}，已集货${data.qty},是否部分发运", 1)
                            } else {
                                showTipDialog("已集货完成，是否发运", 1)
                            }
                        }
                        if (!TextUtils.isEmpty(data.tips)) {
                            if (data.tips.contains("成功")) {
                                ToastUtils.getInstance()
                                    .showSuccessToastWithSound(activity, data.tips)
                            } else {
                                ToastUtils.getInstance()
                                    .showErrorToastWithSound(activity, data.tips)
                            }
                        }
                    }
                    curOrderNo.set("")
                    activity.waitingDialogHelp.hidenDialog()

                    initHandling()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    curOrderNo.set("")
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun send() {
        if (bean == null) {
            return
        }
        activity.waitingDialogHelp.showDialog()
        val map = mutableMapOf(
            "shipmentCode" to bean?.shipmentCode,
            "whCode" to activity.getWhCode(),
            "handingGroupName" to currentHandingList!!.handlingName,
            "handingGroupCode" to currentHandingList!!.handlingCode
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        RetrofitHelper.getAddGoodsService()
            .shipping(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    ToastUtils.getInstance()
                        .showSuccessToastWithSound(activity, "发运成功!")
                    statueStr.set("发货完成")
                    activity.waitingDialogHelp.hidenDialog()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun sendTip() {
        if (bean == null) {
            return
        }
        activity.waitingDialogHelp.showDialog()
        val map = mutableMapOf(
            "shipmentCode" to bean?.shipmentCode,
            "whCode" to activity.getWhCode(),
            "handingGroupName" to currentHandingList!!.handlingName,
            "handingGroupCode" to currentHandingList!!.handlingCode
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        RetrofitHelper.getAddGoodsService()
            .shortHair(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<String>(activity) {
                override fun success(data: String?) {
                    when (data) {
                        "B" -> send()
                        "Y" -> showTipDialog("该订单未按整单完成集货，配置允许短发，请确认是否发运?", 0)
                    }
                    activity.waitingDialogHelp.hidenDialog()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun showTipDialog(tipStr: String, i: Int) {
        tipDialog.setTitle("提示")
        tipDialog.setMsg(tipStr)
        tipDialog.setOnTipBackListener(object : TipDialog.OnTipBack {
            override fun onConfirmClick() {
                if (i == 0) {
                    send()
                } else if (i == 1) {
                    sendTip()
                }
            }

            override fun onDismissClick() {
            }
        })
        tipDialog.show()
    }

    fun selectHandlingGroup() {
        if (currentHandingList != null) {
            handingDialog.setCancel()
            handingDialog.show()
        }
    }

    fun back() {
        activity.finish()
    }
}

