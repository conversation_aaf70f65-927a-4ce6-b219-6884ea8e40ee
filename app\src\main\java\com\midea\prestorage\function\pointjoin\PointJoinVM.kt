package com.midea.prestorage.function.pointjoin

import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.net.EngineerCount
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody

class PointJoinVM(val activity: PointJoinActivity) {

    val isWaiting = ObservableField(true)
    var batchMode = "切换批量模式"
    var singleMode = "切换单个模式"
    val modeStr = ObservableField(batchMode)

    init {

        val param = mutableMapOf<String, Any>()
        param.put("whCode",activity.getWhCode())
        param.put("dayNum","3")

        loadByParam(param)

    }

    fun loadByParam(param: MutableMap<String, Any>) {

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .engineerCount(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<EngineerCount>(activity as RxAppCompatActivity) {
                override fun success(data: EngineerCount?) {
                    if (data != null) {
                        activity.setTabNum1(data.waitCount)
                        activity.setTabNum2(data.confirmCount)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun back() {
        activity.finish()
    }

    fun modeChange() {
        if (modeStr.get() == batchMode) {
            modeStr.set(singleMode)
            activity.changeMode(true)
        } else {
            modeStr.set(batchMode)
            activity.changeMode(false)
        }
    }
}