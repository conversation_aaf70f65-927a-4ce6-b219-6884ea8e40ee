package com.midea.prestorage.beans.net

import android.os.Parcelable
import com.midea.prestorage.base.annotation.ShowAnnotation
import kotlinx.parcelize.Parcelize

@Parcelize
data class BarcodeCollectionInfoResp(
    @ShowAnnotation
    val cjNo: String?,
    @ShowAnnotation
    val custItemCode: String?,
    @ShowAnnotation
    val itemName: String?,
    @ShowAnnotation
    val scanQty: Int?,
) : Parcelable
