package com.midea.prestorage.function.outstorage.dialog

import android.app.AlertDialog
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.CdWhLotDetailDto
import com.midea.prestorage.beans.net.InReceiptSerialScanDto
import com.midea.prestorage.beans.net.OutStorageDeleteQueryBean
import com.midea.prestorage.beans.net.SerialScanDto
import com.midea.prestoragesaas.databinding.DialogInStorageDeleteSerialBinding
import com.midea.prestoragesaas.databinding.DialogInStorageEditLotinfoBinding
import com.midea.prestoragesaas.databinding.DialogOutStorageDeleteTipBinding
import com.midea.prestorage.function.instorage.InStorageDeleteActivity
import com.midea.prestorage.function.instorage.InStorageScanActivity

// 编辑批属性采集 对话框
class EditLotInfoDialog(var activity: InStorageScanActivity) : AlertDialog(activity) {

    var binding: DialogInStorageEditLotinfoBinding


    init {
        val contentView = LayoutInflater.from(activity).inflate(R.layout.dialog_in_storage_edit_lotinfo, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding!!.vm = EditLotInfoDialogVM(this)

        setCanceledOnTouchOutside(true)
    }


    fun showDlg(strTile: String, inReceiptSerialScanDto: InReceiptSerialScanDto, listLot: MutableList<CdWhLotDetailDto>) {
        super.show()
        binding!!.vm!!.show(strTile, inReceiptSerialScanDto, listLot)
    }
}
