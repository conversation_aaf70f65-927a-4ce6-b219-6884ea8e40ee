package com.midea.prestorage.beans.help;

import com.midea.prestorage.beans.base.BaseItemShowInfo;

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/10/22$
 */
public class DCBean extends BaseItemShowInfo {
    public static int SHOW_KEY = 0;
    public static int SHOW_VALUE = 1;
    private String key;
    private Object value;

    public DCBean(String key, Object value, int showFlag) {
        this.key = key;
        this.value = value;

        if (showFlag == SHOW_KEY) {
            setShowInfo(key);
        } else {
            setShowInfo(value.toString());
        }
    }

    public DCBean(String key, Object value) {
        this(key, value, SHOW_VALUE);
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }
}
