package com.midea.prestorage.function.inpool

import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestoragesaas.databinding.ActivityInStoragePoolDetailBinding


class InPoolStorageDetailActivity : BaseActivity() {

    lateinit var binding: ActivityInStoragePoolDetailBinding
    lateinit var adapter: InPoolDetailAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_in_storage_pool_detail)
        binding.vm = InPoolStorageDetailVM(this)
        adapter = InPoolDetailAdapter(ArrayList(),binding.vm as InPoolStorageDetailVM)
        initRecycle()
    }

    private fun initRecycle() {
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter
    }

    fun resetAdapterData(beans:MutableList<InPoolStorageDetailHelp>){
        adapter.setNewInstance(beans)
        adapter.notifyDataSetChanged()
    }

    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }
}