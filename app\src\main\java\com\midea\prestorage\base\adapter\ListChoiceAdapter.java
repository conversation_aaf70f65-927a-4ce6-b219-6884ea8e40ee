package com.midea.prestorage.base.adapter;

import android.view.View;
import android.widget.ImageView;

import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.midea.prestoragesaas.R;
import com.midea.prestorage.beans.base.BaseItemForPopup;

public class ListChoiceAdapter<T extends BaseItemForPopup> extends CommonAdapter<T> {
	//指定点击的控件ID,不指定默认整个
	private int clickId = -1;

	public ListChoiceAdapter(int layoutResId) {
		super(layoutResId);
	}

	@Override
	protected void convert(BaseViewHolder helper, BaseItemForPopup item) {
		super.convert(helper, (T) item);
		View view;
		if (clickId != -1){
			view = helper.itemView.findViewById(clickId);
		} else {
			view = helper.itemView;
		}
		view.setTag(item);

		view.setOnClickListener(v -> {
			BaseItemForPopup bean = (BaseItemForPopup) v.getTag();
			bean.setSelected(!bean.isSelected());

			for (int i = 0; i < getData().size(); i++) {
				if (getData().get(i) == bean) {
					continue;
				}
				getData().get(i).setSelected(false);
			}

			notifyDataSetChanged();
		});
	}

	public void setClickId(int clickId) {
		this.clickId = clickId;
	}

	public T getReturnBeans() {
		for (int i = 0; i < getData().size(); i++) {
			if (getData().get(i).isSelected()) {
				return getData().get(i);
			}
		}
		return null;
	}
}