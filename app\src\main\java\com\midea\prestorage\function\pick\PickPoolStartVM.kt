package com.midea.prestorage.function.pick

import android.content.Intent
import android.text.TextUtils
import androidx.core.content.ContextCompat
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.OutPickPoolStorageDetail
import com.midea.prestorage.beans.net.OutPickPoolStorageList
import com.midea.prestorage.beans.setting.OutStorageInfo
import com.midea.prestorage.function.outstorage.OutStorageActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.http.constants.Constants.Companion.userInfo
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.DbUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import okhttp3.MediaType
import okhttp3.RequestBody

class PickPoolStartVM(val activity: PickPoolStartActivity) {

    private var orderInfo: OutPickPoolStorageList? = null
    private var outStorageInfo: OutStorageInfo? = null

    val isShowBottom = ObservableField(false)
    val isChecked = ObservableField(true)
    val isStartTask = ObservableField(true)
    val taskNo = ObservableField("") //任务号
    val billOrWave = ObservableField("") //订单或者波次
    val confirmedBy = ObservableField("") //执行人
    val assignedBy = ObservableField("") //分配给
    val checked = ObservableField("0") //已拣
    val orderStatusStr = ObservableField("0") //拣货状态
    val all = ObservableField("0") //总数
    var orderStatus: MutableList<DCBean>? = null

    var beans: OutPickPoolStorageDetail? = null

    val db = DbUtils.db

    fun init() {
        //这个是方法
        orderInfo = activity.intent.getSerializableExtra("bean") as OutPickPoolStorageList?

        readProperty()

        DCUtils.fuOutTaskStatus(activity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                orderStatus = statusDC
                makeStatue()
            }
        })
    }

    fun initData() {
        activity.waitingDialogHelp.showDialog()

        val api = if (orderInfo != null) {
            RetrofitHelper.getOutStorageAPI()
                .outTaskHeaderDetail(orderInfo!!.id)
        } else {
            RetrofitHelper.getOutStorageAPI()
                .detailWaveNo(activity.intent.getStringExtra("id"))
        }

        api.compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<OutPickPoolStorageDetail>(activity) {
                override fun success(data: OutPickPoolStorageDetail?) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (data != null) {
                        beans = data
                        checked.set(data.toQtyInt.toString())
                        all.set(data.requstQty.toString())
                        //加波次号
                        data.detailResponses.forEach {
                            it.waveNo = data.outTaskHeaderDto.waveNo
                            it.confirmBy = data.outTaskHeaderDto.confirmedBy
                        }
                        if (data.outTaskHeaderDto.taskModel == "bill") {
                            isChecked.set(false)
                            showData(false, beans!!.detailResponses)
                            billOrWave.set("按订单")
                        } else {
                            isChecked.set(true)
                            showData(true, beans!!.detailResponses)
                            billOrWave.set("按波次")
                        }
                        if (data.outTaskHeaderDto.status != 100) {
                            if (userInfo?.isAutoStart!!) {
                                activity.setStartToEnd()
                                activity.adapter.setStarted()
                                if (!TextUtils.isEmpty(data.outTaskHeaderDto.confirmedBy) &&
                                    userInfo!!.name != data.outTaskHeaderDto.confirmedBy
                                ) {
                                    activity.setStartUnable()
                                }
                                if (data.outTaskHeaderDto.status >= 900) {
                                    activity.setStartUnable()
                                }
//                                val results = data.detailResponses.filter { it.status == 100 }
//                                if (!results.isNullOrEmpty()) {
//                                    startTaskOperation()
//                                }
                            } else {
                                activity.setStartUnable()
                                activity.setListEnable()
                            }
                            isShowBottom.set(true)
                            isStartTask.set(true)
                        } else {
                            activity.initSw()
                            isStartTask.set(false)
                        }

                        taskNo.set(data.outTaskHeaderDto.taskCode)
                        assignedBy.set(data.outTaskHeaderDto.assignedBy)
                        confirmedBy.set(data.outTaskHeaderDto.confirmedBy)
                        makeStatue()

                        if (data.outTaskHeaderDto.confirmedBy != Constants.userInfo?.name) {
                            activity.cancelDisable()
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun makeStatue() {
        val results =
            orderStatus?.filter { it.value.toString() == beans?.outTaskHeaderDto?.status.toString() }
        if (results != null && results.isNotEmpty()) {
            orderStatusStr.set(results[0].key)
        }
    }

    private fun showData(
        data: Boolean,
        detailResponses: MutableList<OutPickPoolStorageDetail.DetailResponses>
    ) {
        if (!data) {
            val finalBeans = mutableListOf<ArrayList<OutPickPoolStorageDetail.DetailResponses>>()
            val keys = mutableSetOf<String>()
            detailResponses.forEach {
                keys.add(it.referenceCode)
            }
            keys.forEach {
                val results = detailResponses.filter { item -> item.referenceCode == it }
                finalBeans.add(ArrayList(results))
            }
            activity.showWaveMode(finalBeans, data)
        } else {
            val combineList = combineList(detailResponses)
            activity.showWaveMode(mutableListOf(ArrayList(combineList)), data)
        }
    }

    private fun combineList(detailResponses: MutableList<OutPickPoolStorageDetail.DetailResponses>): MutableList<OutPickPoolStorageDetail.DetailResponses> {
        val keys = mutableSetOf<String>()
        detailResponses.forEach {
            keys.add(it.tag)
        }
        val finalBeans = mutableListOf<OutPickPoolStorageDetail.DetailResponses>()
        keys.forEach { item ->
            val results = detailResponses.filter { innerItem ->
                innerItem.tag == item
            }
            if (results.isNotEmpty()) {
                val clone = results[0].clone()
                clone.setRequstQty(null)
                results.forEach { resultsItem ->
                    clone.setRequstQty(clone.requestQtyBig.add(resultsItem.requestQtyBig))
                    clone.addId(resultsItem.id)
                }
                finalBeans.add(clone)
            }
        }
        return finalBeans
    }

    fun back() {
        activity.finish()
    }

    fun cancelPick() {
        toCancel()
    }

    private fun toCancel() {
        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getOutStorageAPI()
            .cancelCheck(beans?.outTaskHeaderDto?.id)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    val it = Intent(activity, CancelPickActivity::class.java)
                    it.putExtra("taskId", beans?.outTaskHeaderDto?.id)
                    activity.startActivity(it)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun startTask() {
        if (activity.getBtnTag() == "end") {
            endTask()
        } else {
            startTaskOperation()
        }
    }

    private fun endTask() {
        activity.waitingDialogHelp.showDialogUnCancel()
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "createUserCode" to Constants.userInfo?.name,
            "waveNo" to beans?.outTaskHeaderDto?.waveNo?.trim()
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .batchComplete(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    activity.setStartUnable()
                    initData()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun startTaskOperation() {
        activity.waitingDialogHelp.showDialogUnCancel()
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "createUserCode" to userInfo?.name,
            "orderNo" to beans?.outTaskHeaderDto?.waveNo?.trim(),
            "id" to beans?.outTaskHeaderDto?.id
        )
        if (activity.getShowMode()) {
            param["taskModel"] = "wave"
            billOrWave.set("按波次")
        } else {
            param["taskModel"] = "bill"
            billOrWave.set("按订单")
        }

        if (userInfo?.isAutoStart!!) {
            param["pickTaskExecuteMode"] = "QUICK"
        } else {
            param["pickTaskExecuteMode"] = "NORMAL"
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .begin(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    confirmedBy.set(userInfo?.name)
                    isShowBottom.set(true)
                    isStartTask.set(true)
                    orderStatusStr.set("拣货中")

                    activity.cancelEnable()

                    if (userInfo?.isAutoStart!!) {
                        activity.setStartToEnd()
                        activity.adapter.setStarted()
                    } else {
                        activity.setStartUnable()
                        activity.setListEnable()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun readProperty() {
        Observable.create<OutStorageInfo> {
            outStorageInfo = db.findFirst(OutStorageInfo::class.java)
            if (outStorageInfo != null) {
                it.onNext(outStorageInfo!!)
            }
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<OutStorageInfo> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: OutStorageInfo) {
                    if (!t.isDoNotNotice) {
                        activity.showPop()
                    }
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    private fun saveProperty() {
        Observable.create<String> {
            if (outStorageInfo == null) {
                outStorageInfo = OutStorageInfo()
            }
            outStorageInfo!!.isDoNotNotice = true
            db.saveOrUpdate(outStorageInfo)
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    fun scanOut() {
        val results =
            activity.adapter.fragments.filter { !it.isAllPick() }
        if (results.isNotEmpty()) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "拣货完成才能点扫码出库!")
            return
        }
        val it = Intent(activity, OutStorageActivity::class.java)
        it.putExtra("orderNo", beans?.outTaskHeaderDto?.waveNo)
        activity.startActivity(it)
    }

    fun changeMode(mode: Boolean) {
        beans?.detailResponses?.let { showData(mode, it) }
    }

    fun noticeOk() {
        saveProperty()
    }

    fun changeStatue() {
        beans?.outTaskHeaderDto?.status = 900
        makeStatue()
    }
}