package com.midea.prestorage.beans.net

import android.os.Parcelable
import com.midea.prestorage.base.annotation.ShowAnnotation
import com.midea.prestorage.function.inv.response.PackageRelationList
import kotlinx.parcelize.Parcelize
import java.io.Serializable
import java.math.BigDecimal

data class OutStorageBackTaskResp(
    val id: String?,
    @ShowAnnotation
    val itemName: String?,
    @ShowAnnotation
    val whBarcode69: String?,
    val whCsBarcode69: String?,
    val whMaxBarcode69: String?,
    val whIpBarcode69: String?,
    @ShowAnnotation
    val custItemCode: String?,
    val fromLoc: String?,
    @ShowAnnotation
    val toLoc: String?,
    @ShowAnnotation
    val itemCode: String?,
    @ShowAnnotation
    var lotAtt01: String?,
    val lotAtt04: String?,
    @ShowAnnotation
    val referenceCode: String?,
    @ShowAnnotation
    val lotAtt04Str: String?,
    val toQty: Int?,
    val fromQty: BigDecimal?,
    var packageRelationList: List<PackageRelation>?,
    val ids: List<Long>?,
    val status: String?,
    val ownerCode: String?,
    val lotNum: String?,
    val isDecimal: String?,
    @ShowAnnotation
    val cdpaFormat: String?,
) : Serializable