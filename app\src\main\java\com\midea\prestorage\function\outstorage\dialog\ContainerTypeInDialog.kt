package com.midea.prestorage.function.outstorage.dialog

import android.app.AlertDialog
import android.text.TextUtils
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogContainerTypeInBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

class ContainerTypeInDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {
    private var binding: DialogContainerTypeInBinding
    private var confirmBack: ConfirmBack? = null

    init {
        val contentView = LayoutInflater.from(mContext).inflate(R.layout.dialog_container_type_in, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = ContainerTypeInDialogVM(this)

        setCanceledOnTouchOutside(true)
    }

    fun setTitle(title: String) {
        if (!TextUtils.isEmpty(title)) {
            binding!!.vm!!.title.set(title)
        }
    }

    fun setContent(containerNo: String, containerLockNo: String) {
        if (!TextUtils.isEmpty(containerNo)) {
            binding!!.vm!!.containerNo.set("柜号：$containerNo")
        }
        if (!TextUtils.isEmpty(containerLockNo)) {
            binding!!.vm!!.containerLockNo.set("锁号：$containerLockNo")
        }
    }

    fun setConfirmBack(backImpl: ConfirmBack) {
        confirmBack = backImpl
    }

    fun backConfirm() {
        if (confirmBack != null) {
            confirmBack!!.confirmBack()
        }
    }

    interface ConfirmBack {
        fun confirmBack()
    }

}