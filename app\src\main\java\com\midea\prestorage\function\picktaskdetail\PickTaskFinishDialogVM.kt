package com.midea.prestorage.function.picktaskdetail

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.function.inv.response.InvStockTakeTaskDetail
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody

class PickTaskFinishDialogVM(application: Application) : BaseViewModel(application) {

    var isFinishiDialog = MutableLiveData<Boolean>(false)
    var isResultOk = MutableLiveData<Boolean>(false)
    var totalPickQty = MutableLiveData<String>("0")
    var dataList = MutableLiveData<MutableList<InvStockTakeTaskDetail>>()
    var isEnableSure = MutableLiveData<Boolean>(true)

    override fun init() {

    }

    fun onClose() {
        isFinishiDialog.value = true
    }

    fun onConfirm() {
        launch(showDialog = true,
            error = {
                //错误用这个方法
            }, finish = {
                //完成用这个方法
            }) {

            isEnableSure.value = false
            val mutableList = mutableListOf<String>()
            dataList.value?.forEach {
                val idsSplit = it.ids.split(",")

                idsSplit.forEach {
                    mutableList.add(it.toString().trim())
                }
            }

            val param = mutableMapOf(
                "toQty" to totalPickQty.value?.toDouble()?.toInt(),
                "ids" to mutableList,
                "whCode" to Constants.whInfo?.whCode
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )


            val result = withContext(Dispatchers.IO) {
                val returnWeb =
                    async { RetrofitHelper.getInventoryAPI().confirm(requestBody) }
                returnWeb.await()
            }

            isEnableSure.value = true

            if (result.code == 0L) {
                isResultOk.value = true
                isFinishiDialog.value = true

            } else {
                showNotification(result.msg as String, false)
            }
        }
    }
}