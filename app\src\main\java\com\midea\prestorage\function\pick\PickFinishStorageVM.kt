package com.midea.prestorage.function.pick

import android.content.Intent
import android.text.TextUtils
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.Creators
import com.midea.prestorage.beans.net.InPoolStorageList
import com.midea.prestorage.beans.net.OutPickPoolStorageList
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody


class PickFinishStorageVM(val activity: PickFinishStorageActivity) {

    val isRefreshing = ObservableBoolean(false)
    val isNoData = ObservableBoolean(false)
    var orderStatus: MutableList<DCBean>? = null
    var creatorStatus = mutableListOf<DCBean>()

    var dayInfo = DCUtils.days[1]
    var creatorStatue: DCBean? = null

    var orderNo = ObservableField<String>("")
    var personInfo = ObservableField<String>("")
    var dayInfoTv = ObservableField<String>(dayInfo.key)

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        isRefreshing.set(true)
        initOrderList()
    }

    fun init() {
        val id = activity.intent.getStringExtra("id")
        orderNo.set(id)

        onRefreshCommand.onRefresh()
        initCreator()
        initFilterInfo()

        DCUtils.fuOutTaskStatus(activity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                orderStatus = statusDC
                combineDCInfo()
            }
        })
    }

    fun initFilterInfo() {
        val filters = mutableListOf<String>()
        if (!TextUtils.isEmpty(orderNo.get())) {
            filters.add(orderNo.get()!!)
        }
        filters.add(dayInfo.key)
        if (creatorStatue != null) {
            creatorStatue?.key?.let { filters.add(it) }
        }
        activity.resetFilterInfo(filters)
    }

    fun initOrderList() {
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "orderNo" to orderNo.get()?.trim(),
            "dayNum" to dayInfo.value,
            "statusStr" to 900,
            "createUserCode" to creatorStatue?.value
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .outTaskHeaderList(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<OutPickPoolStorageList>>(activity) {
                override fun success(data: MutableList<OutPickPoolStorageList>?) {
                    if (data != null) {
                        activity.showData(data)
                        combineDCInfo()
                    }
                    isNoData.set(data == null || data.isEmpty())
                    isRefreshing.set(false)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    isRefreshing.set(false)
                }
            })
    }

    fun dayChange() {
        initCreator()
    }

    private fun initCreator() {
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "dayNum" to dayInfo.value
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .createUser(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<Creators>>(activity) {
                override fun success(data: MutableList<Creators>?) {
                    if (!data.isNullOrEmpty()) {
                        creatorStatus.clear()
                        data.forEach {
                            creatorStatus.add(
                                DCBean(
                                    it.createUserName,
                                    it.createUserCode,
                                    DCBean.SHOW_KEY
                                )
                            )
                        }
                        activity.personDataChange(creatorStatus.toMutableList() as MutableList<BaseItemShowInfo>)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun personClick() {
        if (creatorStatus.isEmpty()) {
            return
        }
        activity.showPersonDialog()
    }

    /**
     * 融合数据字典
     */
    fun combineDCInfo() {
        activity.adapter.data.forEach {
            if (orderStatus != null) {
                val result = orderStatus!!.find { item -> item.value.toString() == it.status }
                if (result != null) {
                    it.statusStr = result.key
                }
            }
        }
        activity.adapter.notifyDataSetChanged()
    }

    fun back() {
        activity.finish()
    }

    fun dayClick() {
        val days = DCUtils.days
        activity.showDaysDialog(days as MutableList<BaseItemShowInfo>)
    }

    // 重置按钮，点击
    fun resetClick() {
        orderNo.set("")

        dayInfo = DCUtils.days[1]
        dayInfoTv.set(dayInfo.key)

        personInfo.set("")
        creatorStatue = null
    }

    fun onItemClick(bean: OutPickPoolStorageList) {
        val it = Intent(activity, PickPoolStartActivity::class.java)
        it.putExtra("bean", bean)
        activity.startActivity(it)
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        orderNo.set(result)
    }
}