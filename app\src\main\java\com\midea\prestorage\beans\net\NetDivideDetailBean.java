package com.midea.prestorage.beans.net;

import java.io.Serializable;
import java.util.List;

/**
 * Created by LIXK5 on 2019/4/28.
 */

public class NetDivideDetailBean implements Serializable {
    private String id;
    private String pageSize;
    private String erpOrderCode;
    private String requestedBy;
    private String shipBy;
    private String waveId;
    private String states;
    private String requestedTime;
    private String shipTime;
    private String requestQty;
    private String orderStatus;
    private String trailingSts;
    private String requestBy;

    private List<NetDivideDetailListBean> shipmentEngineerSignLinesList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getErpOrderCode() {
        return erpOrderCode;
    }

    public void setErpOrderCode(String erpOrderCode) {
        this.erpOrderCode = erpOrderCode;
    }

    public String getRequestedBy() {
        return requestedBy;
    }

    public void setRequestedBy(String requestedBy) {
        this.requestedBy = requestedBy;
    }

    public String getShipBy() {
        return shipBy;
    }

    public void setShipBy(String shipBy) {
        this.shipBy = shipBy;
    }

    public String getWaveId() {
        return waveId;
    }

    public void setWaveId(String waveId) {
        this.waveId = waveId;
    }

    public String getStates() {
        return states;
    }

    public void setStates(String states) {
        this.states = states;
    }

    public String getRequestedTime() {
        return requestedTime;
    }

    public void setRequestedTime(String requestedTime) {
        this.requestedTime = requestedTime;
    }

    public String getShipTime() {
        return shipTime;
    }

    public void setShipTime(String shipTime) {
        this.shipTime = shipTime;
    }

    public String getRequestQty() {
        return requestQty;
    }

    public void setRequestQty(String requestQty) {
        this.requestQty = requestQty;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getTrailingSts() {
        return trailingSts;
    }

    public void setTrailingSts(String trailingSts) {
        this.trailingSts = trailingSts;
    }

    public String getRequestBy() {
        return requestBy;
    }

    public void setRequestBy(String requestBy) {
        this.requestBy = requestBy;
    }

    public List<NetDivideDetailListBean> getShipmentEngineerSignLinesList() {
        return shipmentEngineerSignLinesList;
    }

    public void setShipmentEngineerSignLinesList(List<NetDivideDetailListBean> shipmentEngineerSignLinesList) {
        this.shipmentEngineerSignLinesList = shipmentEngineerSignLinesList;
    }

    public class NetDivideDetailListBean {
        private String id;
        private String createTime;
        private String updateTime;
        private String createUserCode;
        private String createUserName;
        private String updateUserCode;
        private String updateUserName;
        private String version;
        private String deleteFlag;
        private String pageSize;
        private String headerId;
        private String warehouseCode;
        private String erpOrderCode;
        private String itemCode;
        private String itemName;
        private String requestQty;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getCreateUserCode() {
            return createUserCode;
        }

        public void setCreateUserCode(String createUserCode) {
            this.createUserCode = createUserCode;
        }

        public String getCreateUserName() {
            return createUserName;
        }

        public void setCreateUserName(String createUserName) {
            this.createUserName = createUserName;
        }

        public String getUpdateUserCode() {
            return updateUserCode;
        }

        public void setUpdateUserCode(String updateUserCode) {
            this.updateUserCode = updateUserCode;
        }

        public String getUpdateUserName() {
            return updateUserName;
        }

        public void setUpdateUserName(String updateUserName) {
            this.updateUserName = updateUserName;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getDeleteFlag() {
            return deleteFlag;
        }

        public void setDeleteFlag(String deleteFlag) {
            this.deleteFlag = deleteFlag;
        }

        public String getPageSize() {
            return pageSize;
        }

        public void setPageSize(String pageSize) {
            this.pageSize = pageSize;
        }

        public String getHeaderId() {
            return headerId;
        }

        public void setHeaderId(String headerId) {
            this.headerId = headerId;
        }

        public String getWarehouseCode() {
            return warehouseCode;
        }

        public void setWarehouseCode(String warehouseCode) {
            this.warehouseCode = warehouseCode;
        }

        public String getErpOrderCode() {
            return erpOrderCode;
        }

        public void setErpOrderCode(String erpOrderCode) {
            this.erpOrderCode = erpOrderCode;
        }

        public String getItemCode() {
            return itemCode;
        }

        public void setItemCode(String itemCode) {
            this.itemCode = itemCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getRequestQty() {
            return requestQty;
        }

        public void setRequestQty(String requestQty) {
            this.requestQty = requestQty;
        }
    }
}
