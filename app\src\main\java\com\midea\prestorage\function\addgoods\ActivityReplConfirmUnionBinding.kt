package com.midea.prestorage.function.addgoods

import android.widget.EditText
import android.widget.RelativeLayout
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestoragesaas.databinding.ActivityReplConfirmBinding
import com.midea.prestoragesaas.databinding.ActivityReplConfirmCareBinding
import com.midea.prestoragesaas.databinding.ActivityReplenishmentBinding
import com.midea.prestoragesaas.databinding.ActivityReplenishmentCareBinding

sealed class ActivityReplConfirmUnionBinding{
    abstract var vm: ReplConfirmActivityVM?
    abstract val llTitleBar: RelativeLayout
    abstract val edToLoc: EditText
    abstract val etOt: EditText
    abstract val etPl: EditText
    abstract val etCs: EditText
    abstract val etIp: EditText
    abstract val etEa: EditText
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: ActivityReplConfirmCareBinding) : ActivityReplConfirmUnionBinding() {
        override var vm: ReplConfirmActivityVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val edToLoc = binding.edToLoc
        override val etOt = binding.etOt
        override val etPl = binding.etPl
        override val etCs = binding.etCs
        override val etIp = binding.etIp
        override val etEa = binding.etEa
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: ActivityReplConfirmBinding) : ActivityReplConfirmUnionBinding() {
        override var vm: ReplConfirmActivityVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val edToLoc = binding.edToLoc
        override val etOt = binding.etOt
        override val etPl = binding.etPl
        override val etCs = binding.etCs
        override val etIp = binding.etIp
        override val etEa = binding.etEa
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
