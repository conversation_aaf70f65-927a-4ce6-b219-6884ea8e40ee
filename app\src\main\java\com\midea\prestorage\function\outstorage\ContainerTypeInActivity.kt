package com.midea.prestorage.function.outstorage

import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.ListChoiceClickPositionAdapter
import com.midea.prestoragesaas.databinding.ActivityContainerTypeInBinding
import com.midea.prestorage.function.outstorage.dialog.ContainerTypeInDialog
import com.midea.prestorage.function.outstorage.response.RespContainerQuery
import com.midea.prestorage.function.outstorage.response.RespShipmentDetail

class ContainerTypeInActivity : BaseViewModelActivity<ContainerTypeInVM>() {
    private lateinit var binding: ActivityContainerTypeInBinding
    private lateinit var containerTypeInDialog: ContainerTypeInDialog
    var adapter = OutStorageScanGoodsAdapter()

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_container_type_in)
        vm = ViewModelProvider.AndroidViewModelFactory(application).create(ContainerTypeInVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        //返回
        vm.finishActivity.observe(this, Observer<Boolean> {
            if (it) {
                finish()
            }
        })

        //波次号/订单号查询成功后光标调到柜号栏位
        vm.goodsRequest.observe(this, Observer<Boolean> {
            if (it) {
                goodsRequest()
            }
        })

        //柜号回车后光标调到锁号
        vm.lockNoRequest.observe(this, Observer<Boolean> {
            if (it) {
                binding.edContainerLockNo.requestFocus()
            }
        })

        //提示重置
        vm.containerChangeTip.observe(this, Observer<Boolean> {
            if (it) {
                containerChangeTip()
            }
        })

        //提交
        vm.isShowTipDialog.observe(this, Observer<Boolean> {
            if (it) {
                containerTypeInDialog.setContent(vm.containerNo.value.toString(), vm.containerLockNo.value.toString())
                containerTypeInDialog.show()
            }
        })

        vm.showDatas.observe(this, Observer<MutableList<RespContainerQuery>> {
            showData(it)
        })

        initDialog()
        initRecycleView()
    }

    private fun initDialog() {

        //提交的提示
        containerTypeInDialog = ContainerTypeInDialog(this)
        containerTypeInDialog.setTitle("提示")
        containerTypeInDialog.setConfirmBack(object: ContainerTypeInDialog.ConfirmBack {
            override fun confirmBack() {
                containerTypeInDialog.dismiss()
                val data = mutableListOf<String>()
                adapter.data.forEach {
                    data.add(it.shipmentCode)
                }
                vm.sendConfirm(data) //提交
            }

        })

    }

    private fun containerChangeTip() {
        containerRequest()
    }

    /**
     * 重置操作
     */
    fun containerRequest() {
        binding.etOrderNo.setText("")
        binding.edContainerNo.setText("")
        binding.edContainerLockNo.setText("")
        binding.etOrderNo.isEnabled = true
        binding.etOrderNo.requestFocus()
        vm.isPalletEnter.value = false

        adapter.data.clear()
        adapter.notifyDataSetChanged()

        vm.processInfo.value = "0"
    }

    private fun goodsRequest() {
        binding.etOrderNo.isEnabled = false
        binding.edContainerNo.requestFocus()
    }

    override fun onResume() {
        super.onResume()
        binding.etOrderNo.onFocusChangeListener = onFocusChangeListener
        binding.etOrderNo.requestFocus()
    }

    private val onFocusChangeListener =
        View.OnFocusChangeListener { view: View, hasFocus: Boolean ->
            if (!hasFocus) {
                if (!vm.isPalletEnter.value!!) {
                    vm.showErrorNotification("请先回车查询单号", false)
                    view.post {
                        binding.etOrderNo.requestFocus()
                    }
                }
            }
        }

    fun initRecycleView() {
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter
    }

    fun showData(data: MutableList<RespContainerQuery>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()

        vm.processInfo.value = adapter.data.size.toString()
    }

    class OutStorageScanGoodsAdapter : ListChoiceClickPositionAdapter<RespContainerQuery>(R.layout.item_container_type_in) {

        override fun convert(helper: BaseViewHolder, item: RespContainerQuery) {
            super.convert(helper, item)

            if(item.upperReceiverName.isNullOrEmpty()) {
                helper.setText(R.id.tv_receiver_name, "")
            }else {
                helper.setText(R.id.tv_receiver_name, item.upperReceiverName)
            }

            if(item.custOrderNo.isNullOrEmpty()) {
                helper.setText(R.id.tv_cust_order_no, "")
            }else {
                helper.setText(R.id.tv_cust_order_no, item.custOrderNo)
            }

            if(item.exportContainerNo.isNullOrEmpty()) {
                helper.setText(R.id.tv_container_no, "")
            }else {
                helper.setText(R.id.tv_container_no, item.exportContainerNo)
            }

            if(item.waveNo.isNullOrEmpty()) {
                helper.setText(R.id.tv_wave_no, "")
            }else {
                helper.setText(R.id.tv_wave_no, item.waveNo)
            }

            if(item.exportContainerLockNo.isNullOrEmpty()) {
                helper.setText(R.id.tv_container_lock_no, "")
            }else {
                helper.setText(R.id.tv_container_lock_no, item.exportContainerLockNo)
            }

        }
    }

}