package com.midea.prestorage.dialog

import android.app.AlertDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.adapter.ListCheckBoxAdapterV2
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.databinding.DialogMultiChooseBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/6/26$
 */
class MultiChooseDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {

    private var binding: DialogMultiChooseUnionBinding
    val adapter =
        ListCheckBoxAdapterV2<BaseItemShowInfo>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.dialog_list_multi_choose_item_care else R.layout.dialog_list_multi_choose_item)
    private var back: MultiChooseBack? = null

    init {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window?.setGravity(Gravity.CENTER)
            window?.attributes?.run {
                gravity = Gravity.CENTER
            }
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_multi_choose_care, null)
            setView(contentView)
            DialogMultiChooseUnionBinding.V2(DataBindingUtil.bind(contentView)!!)
        } else {
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_multi_choose, null)
            setView(contentView)
            DialogMultiChooseUnionBinding.V1(DataBindingUtil.bind(contentView)!!)
        }
        binding.vm = MultiChooseDialogVM(this)

        setCanceledOnTouchOutside(false)
        setCancelable(false)

        initRecycleView()

        binding.llAllChoose.setOnClickListener {
            binding.cbSelectAll.performClick()
        }

        binding.llAllChoose.setOnClickListener {
            binding.cbSelectAll.isChecked = !binding.cbSelectAll.isChecked
            adapter.allSelect(binding.cbSelectAll.isChecked)
        }
    }

    private fun initRecycleView() {
        binding.recycle.layoutManager = LinearLayoutManager(mContext)
        binding.recycle.adapter = adapter

        adapter.setChangeSelectStatus {
            val any = adapter.data.any { !it.isTempSelected }
            binding.cbSelectAll.isChecked = !any
        }
    }

    fun addAllData(beans: MutableList<BaseItemShowInfo>) {
        binding.vm!!.allData = beans
        changeDataNotify(beans)
    }

    fun changeDataNotify(beans: List<BaseItemShowInfo>) {
        binding.cbSelectAll.isChecked = !beans.any { !it.isSelected }

        adapter.setNewInstance(beans.toMutableList())
        adapter.notifyDataSetChanged()
    }

    fun setTitle(title: String) {
        if (!TextUtils.isEmpty(title)) {
            binding!!.vm!!.title.set(title)
        }
    }

    fun getCheckData(): MutableList<BaseItemShowInfo> {
        return adapter.returnBeans
    }

    fun setBack(backImpl: MultiChooseBack) {
        back = backImpl
    }

    fun backConfirm(baseInfo: MutableList<BaseItemShowInfo>) {
        if (back != null) {
            back!!.multiChooseBack(baseInfo)
        }
    }

    interface MultiChooseBack {
        fun multiChooseBack(baseInfo: MutableList<BaseItemShowInfo>)
    }
}
