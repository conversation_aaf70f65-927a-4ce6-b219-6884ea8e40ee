package com.midea.prestorage.function.pickhastaskdetail

import android.app.Application
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.function.inv.response.InvStockTakeTaskDetailHasPick
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import okhttp3.Dispatcher
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal

class PickHasTaskDetailVM(application: Application) : BaseViewModel(application) {

    var waveNo = MutableLiveData<String>("")
    var custItemCode = MutableLiveData<String>("")
    var isUpdataList = MutableLiveData<Boolean>(false)
    var returnDataList = MutableLiveData<MutableList<InvStockTakeTaskDetailHasPick>>(null)
    var totalToQty = MutableLiveData<String>("")
    var isRecyleView = MutableLiveData<Boolean>(false)

    override fun init() {
    }

    fun webRequest() {
        launch(showDialog = true, error = {
        }, finish = {}) {
            isRecyleView.value = false

            val param = mutableMapOf(
                "waveNo" to waveNo.value as String,
                "whCode" to Constants.whInfo?.whCode
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val status =
                    withContext(Dispatchers.Default) {
                        RetrofitHelper.getDirectionAPI().searchDictNew("CL_INVENTORY_STS")
                    }
                val details =
                    withContext(Dispatchers.Default) {
                        RetrofitHelper.getInventoryAPI().getPickTaskFinishDetail(requestBody)
                    }

                if (status.code == 0L) {
                    status.data?.removeAll { it.enableFlag == 0 }
                    details.data?.forEach {
                        it.lotAtt04Str = status.data?.find { item ->
                            item.code == it.lotAtt04
                        }?.name
                    }
                }

                details
            }

            //返回更新结果
            if (result.code == 0L) {
                returnDataList.value = result.data
                isUpdataList.value = true

                //有数据才显示
                isRecyleView.value = result.data?.size as Int > 0
                val result = result.data?.map { item ->
                    AppUtils.getBigDecimalValue(item.toQty)
                }?.fold(BigDecimal.ZERO, BigDecimal::add)

                totalToQty.value = AppUtils.getBigDecimalValueStr(result)
            } else {
                showNotification(result.msg as String, false)
                isRecyleView.value = false
            }
        }
    }
}