package com.midea.prestorage.base

import android.content.Intent
import android.os.Bundle
import android.os.PersistableBundle
import android.widget.TextView
import androidx.lifecycle.Observer

/**
 * <AUTHOR>
 * @date 2020/10/21
 */
abstract class BaseViewModelActivity<VM : BaseViewModel> : BaseActivity() {

    lateinit var vm: VM

    override fun getTvInfo(): TextView? {
        return null
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        beforeCreate(savedInstanceState)

        //展示转圈圈
        vm.isDialogShow.observe(this, Observer<Int> {
            when (it) {
                1 -> waitingDialogHelp.showDialog()
                2 -> waitingDialogHelp.showDialogUnCancel()
                else -> waitingDialogHelp.hidenDialog()
            }
        })

        vm.backNotification.observe(this, Observer<Boolean> {
            if (it) {
                finish()
            }
        })

        vm.toActivity.observe(this, Observer<Map<Intent, Class<*>>> {
            val intent = it.keys.iterator().next()
            intent.setClass(this, it.values.iterator().next())
            startActivity(it.keys.iterator().next())
        })

        vm.init()
    }

    abstract fun beforeCreate(savedInstanceState: Bundle?)
}