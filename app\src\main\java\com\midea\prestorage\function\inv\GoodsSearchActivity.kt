package com.midea.prestorage.function.inv

import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.daimajia.swipe.SimpleSwipeListener
import com.daimajia.swipe.SwipeLayout
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.App
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickPositionAdapter
import com.midea.prestorage.beans.net.PlanStockDetailList
import com.midea.prestorage.beans.net.PrintBean
import com.midea.prestorage.beans.net.ReceiveInfo
import com.midea.prestorage.function.inv.dialog.PrintNumDialog
import com.midea.prestorage.function.inv.response.ItemRfVO
import com.midea.prestorage.function.inv.response.RespFuInvLocationInventory
import com.midea.prestorage.function.inv.response.RespMaterial
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.printer.BluetoothConnectBack
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.LotAttUnit
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityGoodsSearchBinding
import com.midea.prestoragesaas.databinding.PopViewForSelectCustemItemCodeBinding
import com.xuexiang.xqrcode.XQRCode
import java.math.BigDecimal
import java.math.RoundingMode

class GoodsSearchActivity : BaseViewModelActivity<GoodsSearchVM>() {
    private lateinit var binding: ActivityGoodsSearchUnionBinding
    private lateinit var adapter: InStorageOrderAdapter

    //69码或sn码 查询出多个custItemCodes的时候 弹框让用户选择custItemCode
    lateinit var dlgSelectCustItemCode: AlertDialog
    private lateinit var popBindingSelectCustItemCode: PopViewForSelectCustemItemCodeBinding
    lateinit var popAdapterSelectCustItemCode: PopListAdapter
    lateinit var printNumDialog: PrintNumDialog

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityGoodsSearchUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_goods_search_care
                )
            )
        } else {
            ActivityGoodsSearchUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_goods_search
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        vm = ViewModelProvider.AndroidViewModelFactory(application)
            .create(GoodsSearchVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        //扫码
        vm.mScanCode.observe(this, Observer<Boolean> {
            if (it) {
                XQRCode.startScan(this, BaseActivity.QR_CODE_BACK)
            }
        })

        //重置
        vm.mResetSearch.observe(this, Observer<Boolean> {
            if (it) {
                adapter.data.clear()
                adapter.notifyDataSetChanged()
            }
        })

        vm.mBluetoothOpen.observe(this, Observer<Boolean> {
            if (it) {
                bluetoothOpen(false)
            }
        })

        vm.showDatas.observe(this, Observer<MutableList<RespMaterial>> {
            showData(it)
        })

        vm.showPopDatas.observe(this, Observer<MutableList<ItemRfVO>> { data ->
            dlgSelectCustItemCode.show()
            popAdapterSelectCustItemCode.data.clear()
            data.forEach {
                popAdapterSelectCustItemCode.addData(it)
            }
            popAdapterSelectCustItemCode.notifyDataSetChanged()
        })

        initSpinner()
        initRecycleView()
        initPopWinSelectCustItemCode()
        initDialog()
        if (Constants.isShowGoodsSearch) {
            bluetoothOpen(true)
        }

        AppUtils.requestFocus(binding.etCode)
    }

    private val blueBack = object : BluetoothConnectBack {
        override fun success() {
            waitingDialogHelp.hidenDialog()
            vm.isPrintOk.set(true)
        }

        override fun fail() {
            vm.isPrintOk.set(false)
            AppUtils.showToast(this@GoodsSearchActivity, "打印机连接失败，请重启打印机!")
        }

        override fun connect() {
            bluetoothOpen()
        }
    }

    fun bluetoothOpen(isAuto: Boolean = true) {
        if (!Printer.isPrintOk()) {
            Printer.openBluetooth(this, blueBack, isAuto)
        }
    }

    private fun initDialog() {

        //打印条码dialog
        printNumDialog = PrintNumDialog(this)
        printNumDialog.setTitle("请输入打印数量")
        printNumDialog.setPrintBack(object : PrintNumDialog.PrintBarcodeBack {
            override fun printBarcodeBack(printBean: PrintBean) {
                printNumDialog.dismiss()
                Printer.printBarcode(printBean)
            }
        })

        printNumDialog.setOnDismissListener {
            adapter.notifyItemChanged(binding.vm?.currentItemFlag?.get()!!)
        }

    }

    fun initSpinner() {
        val beans = mutableListOf<String>(
            "货品编码",
            "库位编码"
        )
        binding.spinnerStatus.setItems(beans)
        binding.spinnerStatus.selectedIndex = 0

        binding.spinnerStatus.setOnItemSelectedListener { _, position, _, _ ->
            if (position == 0) {
                vm.searchMode = 0
                binding.etCode.hint = "输入客户货品编码或69码"
            } else {
                vm.searchMode = 1
                binding.etCode.hint = "输入库位编码"
            }
            vm.searchType.set(beans[position])
            vm.curCode.set("")
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果
            binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    fun initRecycleView() {
        adapter = InStorageOrderAdapter(vm)
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter

        adapter.setOnItemChildClickListener { adapter, view, position ->
            when (view.id) {
                R.id.fl_bg -> {
                    val list = adapter.data
                    val bean = list[position] as RespMaterial
                    if (bean.isEnable == 1) {
                        vm.showInfoNotification("停用商品不支持修改", false)
                    }else {
                        val intent = Intent(this, InfoCollectionActivity::class.java)
                        intent.putExtra("RespFuInvLocationInventory", bean)
                        startActivity(intent)
                    }
                }

                //打印条码
                R.id.bottom_wrapper -> {
                    val list = adapter.data
                    val bean = list[position] as RespMaterial
                    var printBean = PrintBean()
                    printBean.itemCode = bean.cdcmMaterialNo ?: ""
                    printBean.itemName = bean.cdcmNameCn ?: ""
                    printBean.cdpaFormat = bean.cdpaFormat ?: ""
                    printBean.printCount = 1
                    vm?.currentItemFlag?.set(position) //标记是哪一行的库位异常
                    printNumDialog.setData(printBean)
                    printNumDialog.show()
                }

                R.id.tv_print -> {
                    val list = adapter.data
                    val bean = list[position] as RespMaterial
                    var printBean = PrintBean()
                    printBean.itemCode = bean.cdcmMaterialNo ?: ""
                    printBean.itemName = bean.cdcmNameCn ?: ""
                    printBean.cdpaFormat = bean.cdpaFormat ?: ""
                    printBean.printCount = 1
                    vm?.currentItemFlag?.set(position) //标记是哪一行的库位异常
                    printNumDialog.setData(printBean)
                    printNumDialog.show()
                }
            }
        }
    }

    // 初始化选择客户商品编码的弹窗
    private fun initPopWinSelectCustItemCode() {

        val popViewSelectCustItemCode =
            LayoutInflater.from(this).inflate(R.layout.pop_view_for_select_custem_item_code, null)
        popBindingSelectCustItemCode = DataBindingUtil.bind(popViewSelectCustItemCode)!!

        val alertDialogBuilder = AlertDialog.Builder(this)
        alertDialogBuilder.setView(popViewSelectCustItemCode)
        dlgSelectCustItemCode = alertDialogBuilder.create()

        popAdapterSelectCustItemCode = PopListAdapter()
        popBindingSelectCustItemCode.recyclerViewInPop.layoutManager = LinearLayoutManager(this)
        popBindingSelectCustItemCode.recyclerViewInPop.adapter = popAdapterSelectCustItemCode

        popAdapterSelectCustItemCode.setOnItemClickListener { adapter, view, position ->
            val item = adapter.getItem(position) as ItemRfVO
            binding.etCode.setText(item.custItemCode)
            vm.curSearchCode = item.custItemCode.trim()
            dlgSelectCustItemCode.dismiss()
            binding.vm?.startSearch()
        }

        popBindingSelectCustItemCode.closePop.setOnClickListener {
            dlgSelectCustItemCode.dismiss()
        }

    }

    fun showData(data: MutableList<RespMaterial>?) {
        data?.forEachIndexed { index, it ->
            it.index = 1 + index
        }
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()

        vm.isNoData.value = adapter.data.isNullOrEmpty()
    }


    override fun onResume() {
        super.onResume()
        if (!vm.isFirst && adapter.data.size > 0) {
            vm.startSearch()
        }
        vm.isFirst = false
    }

    override fun onDestroy() {
        super.onDestroy()
        Printer.closeBluetooth()
    }

    class InStorageOrderAdapter(private val vm: GoodsSearchVM?) :
        ListChoiceClickPositionAdapter<RespMaterial>(
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_goods_material_care else R.layout.item_goods_material
        ) {

        fun getUnit(unit: String?): String {
            return when (unit) {
                "YEAR" -> {
                    "年"
                }
                "MONTH" -> {
                    "月"
                }
                "WEEK" -> {
                    "周"
                }
                "DAY" -> {
                    "天"
                }
                else -> {
                    ""
                }
            }
        }

        fun getCdcmIsDecimal(cdcmIsDecimal: Int?): String {
            return if (cdcmIsDecimal != 0) {
                "是"
            } else {
                "否"
            }
        }

        fun getEnable(isEnable: Int?): String {
            return if (isEnable == 1) {
                "停用"
            } else {
                "启用"
            }
        }

        private var currentSwipeLayout: SwipeLayout? = null

        init {
            addChildClickViewIds(R.id.fl_bg, R.id.bottom_wrapper, R.id.tv_print)
        }

        override fun convert(helper: BaseViewHolder, item: RespMaterial) {
            super.convert(helper, item)

            val swipeLayout = helper?.itemView?.findViewById<SwipeLayout>(R.id.sample)
            if (swipeLayout?.openStatus == SwipeLayout.Status.Open) {
                swipeLayout?.close(true)
            }

            swipeLayout?.isSwipeEnabled = !(SPUtils[ProfileVM.CARE_MODE, false] as Boolean)

            helper.setText(R.id.tvCustItemCode, item.cdcmCustMaterialNo)

            if (item.csBarcode69.isNullOrEmpty() && item.cdcmBarcode69.isNullOrEmpty()) {
                helper.setGone(R.id.tv_carcode69, true)
            } else {
                helper.setGone(R.id.tv_carcode69, false)
                helper.setText(
                    R.id.tv_carcode69,
                    LotAttUnit.formatWhBarcode69(item.csBarcode69, item.cdcmBarcode69)
                )
            }

            helper.setText(
                R.id.tv_dimension,
                AppUtils.getBigDecimalValueStr(item.cdcmLength) + "*" + AppUtils.getBigDecimalValueStr(
                    item.cdcmWidth
                ) + "*" + AppUtils.getBigDecimalValueStr(item.cdcmHeight)
            )

            helper.setText(
                R.id.tv_cdcmWeight,
                AppUtils.getBigDecimalValueStr(item.cdcmWeight) + "kg"
            )
            helper.setText(R.id.tv_cdcmCube, AppUtils.getBigDecimalValueStr(item.cdcmCube) + "m³")
            helper.setText(
                R.id.tv_cdcmPeriodOfValidity,
                AppUtils.getBigDecimalValueStr(item.cdcmPeriodOfValidity) + getUnit(item.cdcmValidityUnit)
            )
            helper.setText(
                R.id.tv_stackLevel,
                AppUtils.getBigDecimalValueStr(item.layerQty) + "箱*" + AppUtils.getBigDecimalValueStr(
                    item.stackLevel
                ) + "层"
            )
            helper.setText(R.id.tv_cdcmIsDecimal, getCdcmIsDecimal(item.cdcmIsDecimal))
            helper.setText(R.id.tv_status, getEnable(item.isEnable))

        }

        override fun createBaseViewHolder(view: View): BaseViewHolder {
            val swipeLayout = view.findViewById<SwipeLayout>(R.id.sample)

            swipeLayout?.addSwipeListener(object : SimpleSwipeListener() {
                override fun onStartOpen(layout: SwipeLayout?) {
                    // 如果当前已经有 item 处于打开状态，则关闭该 item
                    if (currentSwipeLayout != null && currentSwipeLayout != layout) {
                        currentSwipeLayout?.close()
                    }
                    // 记录当前打开的 item
                    currentSwipeLayout = layout
                }

                override fun onOpen(layout: SwipeLayout?) {
                    // 如果当前已经有 item 处于打开状态，则关闭该 item
                    if (currentSwipeLayout != null && currentSwipeLayout != layout) {
                        currentSwipeLayout?.close()
                    }
                    // 记录当前打开的 item
                    currentSwipeLayout = layout
                }

                override fun onClose(layout: SwipeLayout?) {
                    // 如果当前关闭的 item 与当前打开的 item 相同，则清空记录
                    if (currentSwipeLayout == layout) {
                        currentSwipeLayout = null
                    }
                }
            })
            return BaseViewHolder(view)
        }
    }

    class PopListAdapter :
        CommonAdapter<ItemRfVO>(R.layout.item_pop_view_for_select_cust_item_code_pop) {
        override fun convert(holder: BaseViewHolder?, item: ItemRfVO) {
            super.convert(holder, item)

            holder?.setGone(R.id.tv_item_name, item.itemName.isNullOrEmpty())
        }
    }
}