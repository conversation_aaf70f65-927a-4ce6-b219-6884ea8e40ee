package com.midea.prestorage.function.inv

import android.view.View
import android.widget.CompoundButton
import android.widget.Toast
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.SerialScanDto
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.function.inv.CountInTimeSettingVM.Companion.COVER
import com.midea.prestorage.function.inv.CountInTimeSettingVM.Companion.ONE
import com.midea.prestorage.function.inv.CountInTimeSettingVM.Companion.countingMethod
import com.midea.prestorage.function.inv.dialog.InputNumActualDialog
import com.midea.prestorage.function.inv.response.InvStocktakeDetail
import com.midea.prestorage.function.inv.response.RespCountInTimeTask
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.HttpSuccessCallback
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.FilterDigitTextWatcher
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal

class CountInTimeVM(val activity: CountInTimeActivity) {


    // 盘点库位 编码
    val locationCode = ""


    var countNum = ObservableField<String>("")  //盘点到的数量 (编辑框)

    // 服务器返回的盘点明细
    var curRespCountInTimeTask = RespCountInTimeTask()

    var displayCustItemCode = ObservableField<String>("")
    var displayItemName = ObservableField<String>("")
    //var displayLotAttr4 = ObservableField<String>("")

    var curDetail = InvStocktakeDetail()

    var curSelectLot4Name = ""

    // 是否可以 扫条码
    var isReadyForScanNext = ObservableField<Boolean>(true)
    var isEnableClick = ObservableField(true)

    init {

        // 默认隐藏库存状态选项 2021年11月11日 星期四 默认显示  (也许以后会改成默认隐藏)
        activity.binding.layoutLocInvStatus.visibility = View.VISIBLE

        resetDefault()

        activity.binding.etCustItemCode.setOnFocusChangeListener { v, hasFocus ->

            if (hasFocus) {
                if (activity.binding.etLocationCode.isEnabled) {
                    AppUtils.showToast(activity, "请先输入库位!")
                    v.post {
                        activity.binding.etLocationCode.requestFocus()
                    }
                }
            }
        }
    }

    fun submitTask(data: InvStocktakeDetail, num: BigDecimal) {
        val detail = InvStocktakeDetail()
        detail.itemCode = data.itemCode
        detail.itemName = data.itemName
        detail.whCode = data.whCode
        detail.ownerCode = data.ownerCode
        detail.id = data.id
        detail.detailCode = data.detailCode
        try {
            detail.firstQty = num
        } catch (e: NumberFormatException) {
            // 避免恶意用户或测试输入稀奇古怪的数值
            ToastUtils.getInstance()
                .showErrorToastWithSound(activity, "盘点数量填写错误(格式错误或数量过大)")
            return
        }
        detail.sysQty = data.sysQty
        detail.stocktakeSeqNum = data.stocktakeSeqNum
        detail.status = data.status
        detail.lotAtt04 = data.lotAtt04
        detail.custItemCode = data.custItemCode

        val param = mutableMapOf<String, Any>()
        param.put("stocktakeCode", data.stocktakeCode ?: "")
        // 传参数:  库位编码 ， 仓库编码 ， 仓库名称
        param["locCode"] = activity.binding.etLocationCode.text.toString().trim()
        param["whCode"] = activity.getWhCode()
        param["whName"] = activity.getWhName()
        param["lotAtt04"] = data.lotAtt04

        val listDetail = mutableListOf<InvStocktakeDetail>()
        listDetail.add(detail)
        param["invStocktakeDetailList"] = listDetail
        param["isAddFirstQty"] = countingMethod != COVER

        activity.waitingDialogHelp.showDialog()
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getWareManageAPI()
            .submitCountInTimeTask(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    isEnableClick.set(true)
                    activity.waitingDialogHelp.hidenDialog()
                    // 清空数据
                    curDetail = InvStocktakeDetail()
                    ToastUtils.getInstance()
                        .toastWithOkSound(activity, "已提交此次盘点明细", Toast.LENGTH_SHORT)
                    isReadyForScanNext.set(true)
                    activity.binding.etCustItemCode.setText("")  //需求： 逐件扫描模式下，扫一次就清空一次条码框
                    //2021年10月28日 需求: 逐件盘点，扫完一个条码，光标应定位在条码输入框，方便继续扫码]
                    activity.binding.etCustItemCode.requestFocus()

                    getCountInTimeTask(HttpSuccessCallback {})
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isEnableClick.set(true)
                    isReadyForScanNext.set(true)
                    activity.binding.etCustItemCode.setText("")  //需求： 逐件扫描模式下，扫一次就清空一次条码框
                    activity.binding.etCustItemCode.requestFocus()

                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun removeItem(item: InvStocktakeDetail) {
        // 本地不存在的话才调用条码接口 解析编码
        val param = mutableMapOf(
            "id" to item.id,
            "status" to curRespCountInTimeTask.status
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getAppAPI()
            .stockadeDel(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    onEnterLocationCode()
                    ToastUtils.getInstance().showSuccessToastWithSound(activity, "重置成功")
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.binding.etCustItemCode.setText("")
                }
            })
    }

    fun resetDefault() {

        val result = DCUtils.goodsStatue?.find { it.value == "Y" }
        if (result != null) {
            curSelectLot4Name = result.key
            activity.binding.spinnerLot4.selectedIndex = DCUtils.goodsStatue!!.indexOf(result)
        } else {
            //默认选第一个
            if (activity.lot4Options.size > 0) {
                activity.binding.spinnerLot4.selectedIndex = 0
                curSelectLot4Name = activity.lot4Options[0]
            }
        }

        activity.binding.etLocationCode.setText("")
        activity.binding.etCustItemCode.setText("")

        // 释放 库位编码 输入框，允许编辑点击
        releaseEditTextLocCode()

        isReadyForScanNext.set(true)
        // 2021年11月11日 星期四 默认显示  (也许以后会改成默认隐藏)
        //activity.binding.layoutLocInvStatus.visibility = View.GONE
        activity.binding.cbStatus.isChecked = false

        curRespCountInTimeTask = RespCountInTimeTask()

        displayCustItemCode.set("")
        displayItemName.set("")
        //displayLotAttr4.set("")

        activity.binding.etLocationCode.requestFocus()

        activity.adapter.data.clear()
        activity.adapter.notifyDataSetChanged()
    }

    //后退键
    val back = View.OnClickListener {
        activity.finish()
    }

    // 输入 库位编码并按下回车后
    fun onEnterLocationCode() {
        // 向后端 获取盘点明细列表
        if (CheckUtil.isFastDoubleClick()) {
            scanLocation(null)
        }
    }

    fun onEnterCountNum(isEnter: Boolean) {
        if (CheckUtil.isFastDoubleClick(isEnter)) {
            if (curDetail.custItemCode.isNullOrBlank()) {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "提交失败，未输入条码或未按回车识别条码")
                AppUtils.requestFocus(activity.binding.etCustItemCode)
                return
            }

            submitSingleDetail(HttpSuccessCallback {
                getCountInTimeTask(HttpSuccessCallback {})
            })
        }
    }

    // 扫描或输入 商品编码后   并按下回车
    // 2021年11月11日 星期四 需求 数量没确认就不能扫条码，扫完条码后把那个扫码栏位置灰，光标跳到数量栏位，用户回车确认数量后光标回到扫条码那里就好
    fun onEnterCustItemCode() {
        if (CheckUtil.isFastDoubleClick()) {
            if (activity.binding.etCustItemCode.text.isNullOrEmpty()) return
            recognizeNewCode()
        }
    }

    // 识别条码或编码 (先在本地列表里找 相同编码的该商品，找不到才调用条码识别接口)
    private fun recognizeNewCode() {

        //当前输入确认的货品编码 或sn码   去除前后空格
        val anyCode = activity.binding.etCustItemCode.text.toString().trim()

        // 本地不存在的话才调用条码接口 解析编码
        val param = mutableMapOf(
            "serialNo" to anyCode,
            "whCode" to activity.getWhCode()
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getAppAPI()
            .scanCodeOnTransferGood(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<SerialScanDto>(activity) {
                override fun success(data: SerialScanDto?) {
                    activity.waitingDialogHelp.hidenDialog()
                    data?.let {
                        if (data.itemRfVOS == null || data.itemRfVOS.size == 0) {
                            activity.binding.etCustItemCode.setText("")
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(activity, "输入或扫描的货品条码无效")
                        } else {
                            if (data.itemRfVOS.size == 1) {
                                var item = InvStocktakeDetail()
                                item.custItemCode = data.itemRfVOS[0].custItemCode
                                item.itemCode = data.itemRfVOS[0].itemCode
                                item.itemName = data.itemRfVOS[0].itemName
                                item.packageRelationList = data.itemRfVOS[0].packageRelationList
                                item.isDecimal = data.itemRfVOS[0].isDecimal
                                item.whCode = data.whCode
                                item.sysQty = BigDecimal.ZERO  //系统库存数量，由于是新扫描的商品(盘赢) ，所以库存中数量为0
                                if (!curRespCountInTimeTask.stocktakeCode.isNullOrBlank()) {
                                    item.stocktakeCode = curRespCountInTimeTask.stocktakeCode
                                }
                                if (DCUtils.goodsStatueN2C.get(curSelectLot4Name) != null) {
                                    item.lotAtt04 = DCUtils.goodsStatueN2C.get(curSelectLot4Name).toString()
                                }
                                item.cdpaFormat = data.itemRfVOS[0].cdpaFormat
                                item.whCsBarcode69 = data.itemRfVOS[0].whCsBarcode69
                                item.whBarcode69 = data.itemRfVOS[0].whBarcode69
                                curDetail = item
                                displayItemName.set(data.itemRfVOS[0].itemName)
                                if (countingMethod == ONE) {
                                    //逐件扫码模式 扫一个上传一个并刷新列表 (即自动触发  数量编辑框回车)
                                    onEnterCountNum(false)
                                }else {
                                    activity.showInputNumActualDialog(item)
                                }
                            } else {
                                //扫69码 后端没有返回 custItemCode  如果有custItemCode数组，就弹框选择
                                activity.dlgSelectCustItemCode.show()
                                activity.popAdapterSelectCustItemCode.data.clear()
                                data.itemRfVOS.forEach {
                                    activity.popAdapterSelectCustItemCode.addData(it)
                                }
                                activity.popAdapterSelectCustItemCode.notifyDataSetChanged()
                            }
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.binding.etCustItemCode.setText("")
                }
            })
    }

    // 扫码成功 后 的处理
    fun handleSerialScanDto(serialScanDto: SerialScanDto) {
        if (!serialScanDto.custItemCode.isNullOrBlank()) {
            ToastUtils.getInstance().toastWithOkSound(activity, "扫码成功", Toast.LENGTH_SHORT)
            isReadyForScanNext.set(false)

            // 2021-1228 :  skuBarcodeInfoDtos 的数量>1 才是大箱
            if (serialScanDto.skuBarcodeInfoDtos != null && serialScanDto.skuBarcodeInfoDtos.size > 1) {
                // 直接提交
                handleNewItem(serialScanDto)
            } else {
                // 扫到其他条码
                handleNewItem(serialScanDto)
            }

        }
    }

    fun isEmpty(text: String?): Boolean {
        return text.isNullOrBlank()
    }

    private fun handleNewItem(serialScanDto: SerialScanDto) {
        if (curRespCountInTimeTask.invStocktakeDetailList == null) {
            curRespCountInTimeTask.invStocktakeDetailList = mutableListOf<InvStocktakeDetail>()
        }

        // 没有匹配到  相同custItemCode和lot4 的话 则创建一个新的盘点detail
        val newDetail = InvStocktakeDetail()
        newDetail.sysQty = BigDecimal.ZERO  //系统库存数量，由于是新扫描的商品(盘赢) ，所以库存中数量为0


        // 默认  该商品 库存状态为正品
        var newLot4 = "Y"
        // 如果下拉选项 选了正品以外的状态  这里查字典map 匹配中文对应的英文
        DCUtils.goodsStatueN2C.get(curSelectLot4Name)?.let {
            newLot4 = DCUtils.goodsStatueN2C.get(curSelectLot4Name).toString()
        }
        newDetail.lotAtt04 = newLot4  //当前下拉选项 勾选的库存状态
        newDetail.custItemCode = serialScanDto.custItemCode
        newDetail.itemCode = serialScanDto.itemCode
        newDetail.itemName = serialScanDto.itemName
        newDetail.whCode = activity.getWhCode()


        displayCustItemCode.set(newDetail.custItemCode)
        displayItemName.set(newDetail.itemName)
        //displayLotAttr4.set(curSelectLot4Name)
        curDetail = newDetail

        //数量框不能编辑 表示当前是 逐件扫描模式
        if (countingMethod == ONE) {
            // 逐件扫描模式 ，+1
            countNum.set("1")
            newDetail.firstQty = AppUtils.getBigDecimalValue(countNum.get())

            if (serialScanDto.skuBarcodeInfoDtos != null && serialScanDto.skuBarcodeInfoDtos.size > 1) {
                // 如果扫到的是大箱 直接自动设置数量
                countNum.set(serialScanDto.skuBarcodeInfoDtos!!.size.toString())
            }
            //逐件扫码模式 扫一个上传一个并刷新列表 (即自动触发  数量编辑框回车)
            onEnterCountNum(false)
        } else {

            //普通模式 如果扫的是大箱条码 也直接提交 大箱里面的小箱总数
            if (serialScanDto.skuBarcodeInfoDtos != null && serialScanDto.skuBarcodeInfoDtos.size > 1) {
                onEnterCountNum(false)
            } else {
                // 20211125  其他情况 置空数量框 让用户对数量框 回车才提交
                countNum.set("")
            }
        }
    }

    //释放
    fun release() {
        releaseCountInTimeTask()
    }

    //盘点完成
    fun finishCount() {
        isEnableClick.set(false)
        if (curRespCountInTimeTask.invStocktakeDetailList == null || curRespCountInTimeTask.invStocktakeDetailList.size == 0) {
            // 20211124 判定到空库位时 提示空库位 并 重置界面
            ToastUtils.getInstance().showSuccessToastWithSound(activity, "空库位")
            resetDefault()
            isEnableClick.set(true)
            return
        }

        if (isReadyForScanNext.get() == false) {
            //如果当前有盘点明细未提交，则先提交完当前这个明细 后才进行 检查差异 然后 才完成盘点

            submitSingleDetail(HttpSuccessCallback {
                checkDifferentAndSubmit()
            })
        } else {
            checkDifferentAndSubmit()
        }
    }

    private fun checkDifferentAndSubmit() {
        //检查与原盘点明细是否存在差异，有的话先弹框确认，点确定  才进行提交盘点完成
        if (checkIsDifferent()) { //如果和系统库存不同，则先提示确认 才提交
            AlertDialogUtil.showOkAndCancelDialog(activity, "盘点存在差异，是否确认完成？",
                { _, i ->  //点了确定
                    confirmFinishCountInTimeTask()
                },
                { _, i ->  //点了取消
                    getCountInTimeTask(null)
                })
        } else {
            //如果和系统库存相同，直接提交
            confirmFinishCountInTimeTask()
        }
    }

    // 锁住 库位编码 输入框，不让编辑点击
    private fun lockEditTextLocCode() {
        activity.binding.etLocationCode.isEnabled = false
    }

    // 释放 库位编码 输入框，允许编辑点击
    private fun releaseEditTextLocCode() {
        activity.binding.etLocationCode.isEnabled = true
    }

    // 获取 盘点任务
    private fun scanLocation(successCallback: HttpSuccessCallback?) {
        // 锁住 库位编码 输入框，不让编辑点击
        lockEditTextLocCode()

        // 清除前后空格
        val locCode = activity.binding.etLocationCode.text.toString().trim()
        if (locCode.isBlank()) {
            // 释放 库位编码 输入框，允许编辑点击
            releaseEditTextLocCode()
            ToastUtils.getInstance().showErrorToastWithSound(activity, "库位编码不能为空")
            return
        }

        val param = mutableMapOf(
            "locCode" to locCode.toUpperCase(),
            "whCode" to activity.getWhCode(),
            "whName" to activity.getWhName()
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()

        RetrofitHelper.getWareManageAPI()
            .scanLocation(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    getCountInTimeTask(null)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    releaseEditTextLocCode()
                }
            })
    }

    // 获取 盘点任务
    private fun getCountInTimeTask(successCallback: HttpSuccessCallback?) {

        // 锁住 库位编码 输入框，不让编辑点击
        lockEditTextLocCode()

        // 清除前后空格
        val locCode = activity.binding.etLocationCode.text.toString().trim()
        if (locCode.isBlank()) {
            // 释放 库位编码 输入框，允许编辑点击
            releaseEditTextLocCode()

            ToastUtils.getInstance().showErrorToastWithSound(activity, "库位编码不能为空")

            isEnableClick.set(true)
            return
        }

        val param = mutableMapOf(
            "locCode" to locCode,
            "whCode" to activity.getWhCode(),
            "whName" to activity.getWhName()
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()

        RetrofitHelper.getWareManageAPI()
            .getCountInTimeTask(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<RespCountInTimeTask>(activity) {
                override fun success(data: RespCountInTimeTask?) {
                    isEnableClick.set(true)

                    activity.waitingDialogHelp.hidenDialog()
                    //编辑框:库位编码 设为只读
                    //activity.binding.etLocationCode.isEnabled = false

                    // 光标定位 货品条码
                    activity.binding.etCustItemCode.requestFocus()

                    activity.adapter.data.clear()
                    // 如果有返回盘点明细
                    if (data != null) {
                        curRespCountInTimeTask = data
                        //初始化 字典(库存状态(正品不良品))
                        DCUtils.goodsStatue(activity, object : DCUtils.DCBack {

                            override fun dcBack(statusDC: MutableList<DCBean>) {
                                data.invStocktakeDetailList.forEach {
                                    it.lotAtt04Str = DCUtils.goodsStatueC2N[it.lotAtt04].toString()
                                }
                                activity.adapter.setNewInstance(data.invStocktakeDetailList)
                            }
                        })
                    } else {
                        ToastUtils.getInstance()
                            .showSuccessToastWithSound(activity, "该库位目前无库存记录,可继续盘点")
                        curRespCountInTimeTask = RespCountInTimeTask()
                        activity.adapter.setNewInstance(curRespCountInTimeTask.invStocktakeDetailList)
                    }
                    //执行回调
                    successCallback?.onAfterSuccess()

                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isEnableClick.set(true)

                    // 释放 库位编码 输入框，允许编辑点击
                    releaseEditTextLocCode()

                    activity.waitingDialogHelp.hidenDialog()
                    if (statusCode == 716004L) {
                        // 20211124  716004 表示库位号不存在
                        activity.binding.etLocationCode.setText("")
                        // 库位不存在时清空
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, apiErrorModel.message)
                    } else {
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, apiErrorModel.message)
                    }
                }
            })
    }


    // 释放 盘点任务
    private fun releaseCountInTimeTask() {

        if (curRespCountInTimeTask.stocktakeCode.isNullOrBlank()) {
            // 选了一个空库位 后台没有生成盘点单
            resetDefault()
            return
        }

        val param = mutableMapOf<String, String>()

        if (!curRespCountInTimeTask.stocktakeCode.isNullOrBlank()) {
            param.put("stocktakeCode", curRespCountInTimeTask.stocktakeCode)
        }
        param.put("whCode", activity.getWhCode())
        param.put("locCode", activity.binding.etLocationCode.text.toString().trim())

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getWareManageAPI()
            .releaseCountInTimeTask(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    ToastUtils.getInstance().showSuccessToastWithSound(activity, "操作成功")
                    resetDefault()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }


    //提交 单个商品条码的盘点明细行
    fun submitSingleDetail(successCallback: HttpSuccessCallback?) {

        if (curDetail.custItemCode.isNullOrBlank()) {
            successCallback?.onAfterSuccess()
            return
        }

        // 2021年11月1日 星期一 submit接口要传的参数
        val detail = InvStocktakeDetail()
        detail.itemCode = curDetail.itemCode
        detail.itemName = curDetail.itemName
        detail.whCode = curDetail.whCode
//        detail.ownerCode = curDetail.ownerCode
//        detail.id = curDetail.id
        detail.detailCode = curDetail.detailCode
        if (countingMethod == ONE) {
            // 逐件扫描模式 ，+1
            countNum.set("1")
        }
        try {
            detail.firstQty = AppUtils.getBigDecimalValue(countNum.get())
        } catch (e: NumberFormatException) {
            // 避免恶意用户或测试输入稀奇古怪的数值
            ToastUtils.getInstance().showErrorToastWithSound(activity, "盘点数量填写错误(格式错误或数量过大)")
            return
        }
        detail.sysQty = curDetail.sysQty
        detail.stocktakeSeqNum = curDetail.stocktakeSeqNum
        detail.status = curDetail.status
        if (DCUtils.goodsStatueN2C.get(curSelectLot4Name) != null) {
            detail.lotAtt04 = DCUtils.goodsStatueN2C.get(curSelectLot4Name).toString()
        }
        detail.custItemCode = curDetail.custItemCode

        val param = mutableMapOf<String, Any>()

        if (!curRespCountInTimeTask.stocktakeCode.isNullOrBlank()) {
            param["stocktakeCode"] = curRespCountInTimeTask.stocktakeCode
        }

        // 传参数:  库位编码 ， 仓库编码 ， 仓库名称
        param["locCode"] = activity.binding.etLocationCode.text.toString().trim()
        param["whCode"] = activity.getWhCode()
        param["whName"] = activity.getWhName()

        if (DCUtils.goodsStatueN2C.get(curSelectLot4Name) != null) {
            param["lotAtt04"] = DCUtils.goodsStatueN2C.get(curSelectLot4Name).toString()
        }

        val listDetail = mutableListOf<InvStocktakeDetail>()
        listDetail.add(detail)
        param["invStocktakeDetailList"] = listDetail
        param["isAddFirstQty"] = countingMethod != COVER

        activity.waitingDialogHelp.showDialog()
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getWareManageAPI()
            .submitCountInTimeTask(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    isEnableClick.set(true)
                    activity.waitingDialogHelp.hidenDialog()
                    // 清空数据
                    curDetail = InvStocktakeDetail()
                    ToastUtils.getInstance()
                        .toastWithOkSound(activity, "已提交此次盘点明细", Toast.LENGTH_SHORT)
                    isReadyForScanNext.set(true)
                    activity.binding.etCustItemCode.setText("")  //需求： 逐件扫描模式下，扫一次就清空一次条码框
                    //2021年10月28日 需求: 逐件盘点，扫完一个条码，光标应定位在条码输入框，方便继续扫码]
                    activity.binding.etCustItemCode.requestFocus()

                    successCallback?.onAfterSuccess()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isEnableClick.set(true)
                    isReadyForScanNext.set(true)
                    activity.binding.etCustItemCode.setText("")  //需求： 逐件扫描模式下，扫一次就清空一次条码框
                    activity.binding.etCustItemCode.requestFocus()

                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    //判断两个列表 是否相同 (数量是否相同，每个元素的 itemCode,custItemCode， 当前盘点明细的实际盘点数量 和 系统盘点数量是否相同)
    private fun checkIsDifferent(): Boolean {
        for (i in 1..curRespCountInTimeTask.invStocktakeDetailList.size) {
            val cur = curRespCountInTimeTask.invStocktakeDetailList.get(i - 1)
            // 实盘数量不同
            if (cur.firstQty.compareTo(cur.sysQty) != 0) {
                return true
            }
        }

        return false
    }

    // 盘点完成 确认
    private fun confirmFinishCountInTimeTask() {
        val param = mutableMapOf<String, String>()
        if (!curRespCountInTimeTask.stocktakeCode.isNullOrBlank()) {
            param.put("stocktakeCode", curRespCountInTimeTask.stocktakeCode)
        }
        param.put("whCode", activity.getWhCode())
        param.put("locCode", activity.binding.etLocationCode.text.toString().trim())

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()

        RetrofitHelper.getWareManageAPI()
            .confirmFinishCountInTimeTask(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    isEnableClick.set(true)

                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showSuccessToastWithSound(activity, "操作成功")
                    resetDefault()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isEnableClick.set(true)

                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    val scan_type_location = 1
    val scan_type_anycode = 2
    var curSanType = 0

    fun startScan(scanType: Int) {

        if (scanType == scan_type_anycode && activity.binding.etLocationCode.isEnabled) {
            // 只有条码编辑框  能编辑时  才能扫条码
            return
        }

        if (scanType == scan_type_location && !activity.binding.etLocationCode.isEnabled) {
            // 只有库位编辑框  能编辑时  才能扫库位
            return
        }

        curSanType = scanType
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        if (!result.isNullOrBlank()) {
            if (curSanType == scan_type_anycode) {
                activity.binding.etCustItemCode.setText(result)
                AppUtils.requestFocus(activity.binding.etCustItemCode)
                onEnterCustItemCode()
            } else if (curSanType == scan_type_location) {
                activity.binding.etLocationCode.setText(result)
                AppUtils.requestFocus(activity.binding.etLocationCode)
                onEnterLocationCode()
            }
        }
    }

    fun onItemClick(invStocktakeDetail: InvStocktakeDetail) {
        activity.showInputNumActualDialog(invStocktakeDetail)
    }

    fun showTypeMenu() {
        activity.toSetttingPage()
    }
}