package com.midea.prestorage.function.inv.response;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.beans.base.BaseItemForPopup;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class FuInvLocationInventory extends BaseItemForPopup implements Serializable {

    Long id;

    /**
     * 仓库
     */
    String whCode;

    /**
     * 货主
     */
    String ownerCode;

    /**
     * 货主名称
     */
    String ownerName;

    /**
     * 库位
     */
    @ShowAnnotation
    String locCode;

    @ShowAnnotation
    String desc;

    /**
     * 客户货品编码
     */
    @ShowAnnotation
    String custItemCode;

    /**
     * 安得货品编号
     */
    @ShowAnnotation
    String itemCode;

    /**
     * 货品描述
     */
    @ShowAnnotation
    String itemName;

    /**
     * 入库日期
     */
    String inDate;

    /**
     * 批属性号
     */
    @ShowAnnotation
    String lotNum;

    /**
     * 在库数量
     */
    @ShowAnnotation
    BigDecimal onHandQty;

    /**
     * 账面库存数
     */
    @ShowAnnotation
    BigDecimal onHandQtyTotal;

    /**
     * 分配数量
     */
    @ShowAnnotation
    BigDecimal allocatedQty;

    @ShowAnnotation
    int index;//下标


    /**
     * 在途数量
     */
    BigDecimal inTransitQty;

    /**
     * 冻结数量
     */
    BigDecimal lockedQty;


    /**
     * 补货下架数
     */
    BigDecimal repOutQty;


    /**
     * 跟踪号
     */
    String traceId;

    /**
     * 状态(1:可用 0:不可用)
     */
    BigDecimal status;

    /**
     * 创建用户
     */
    String createUserCode;

    /**
     * 创建人名称
     */
    String createUserName;

    /**
     * 创建时间
     */
    String createTime;

    /**
     * 修改用户
     */
    String updateUserCode;

    /**
     * 修改人名称
     */
    String updateUserName;

    /**
     * updateTime
     */
    String updateTime;

    /**
     * 租户编码
     */
    String tenantCode;

    /**
     * 备注
     */
    String remark;


    @ShowAnnotation
    String lotAtt01;
    @ShowAnnotation
    String lotAtt02;
    @ShowAnnotation
    String lotAtt03;
    String lotAtt04;
    @ShowAnnotation
    String lotAtt05;
    String lotAtt06;
    String lotAtt07;
    String lotAtt08;
    String lotAtt09;
    String lotAtt10;
    String lotAtt11;
    String lotAtt12;

    Boolean isCheck = false;

    BigDecimal usableQty; //可用库存

    BigDecimal moveNum = BigDecimal.ZERO;//移动数量
    BigDecimal tempMoveNum = BigDecimal.ZERO;//临时移动数量
    BigDecimal editNum = BigDecimal.ZERO;//填写的EA数量
    BigDecimal editNum1 = BigDecimal.ZERO;//填写的OT数量
    BigDecimal editNum2 = BigDecimal.ZERO;//填写的PL数量
    BigDecimal editNum3 = BigDecimal.ZERO;//填写的CS数量
    BigDecimal editNum4 = BigDecimal.ZERO;//填写的IP数量

    private List<PackageRelation> packageRelationList;

    private BigDecimal cdprQuantity;
    private String cdprUnit;

    String whBarcode69;
    private String whCsBarcode69;

    int isVisible = 0; //“库存”是否显示,为0则显示数量（默认），为1则隐藏

    private boolean isJustClick; //是否刚刚点击过了

    @ShowAnnotation
    private String onHandQtyStr;

    private int isDecimal;

    private String isValidity;
    private BigDecimal periodOfValidity;
    private String validityUnit;

    private String cdpaFormat;

    private BigDecimal occupyQty;

    BigDecimal skuNum;

    public BigDecimal getSkuNum() {
        return skuNum;
    }

    public void setSkuNum(BigDecimal skuNum) {
        this.skuNum = skuNum;
    }

    private BigDecimal otQuantity;
    private BigDecimal plQuantity;
    private BigDecimal csQuantity;
    private BigDecimal ipQuantity;
    private BigDecimal eaQuantity;

    public BigDecimal getOtQuantity() {
        return otQuantity;
    }

    public void setOtQuantity(BigDecimal otQuantity) {
        this.otQuantity = otQuantity;
    }

    public BigDecimal getPlQuantity() {
        return plQuantity;
    }

    public void setPlQuantity(BigDecimal plQuantity) {
        this.plQuantity = plQuantity;
    }

    public BigDecimal getCsQuantity() {
        return csQuantity;
    }

    public void setCsQuantity(BigDecimal csQuantity) {
        this.csQuantity = csQuantity;
    }

    public BigDecimal getIpQuantity() {
        return ipQuantity;
    }

    public void setIpQuantity(BigDecimal ipQuantity) {
        this.ipQuantity = ipQuantity;
    }

    public BigDecimal getEaQuantity() {
        return eaQuantity;
    }

    public void setEaQuantity(BigDecimal eaQuantity) {
        this.eaQuantity = eaQuantity;
    }

    public BigDecimal getOccupyQty() {
        return occupyQty;
    }

    public void setOccupyQty(BigDecimal occupyQty) {
        this.occupyQty = occupyQty;
    }

    public String getWhCsBarcode69() {
        return whCsBarcode69;
    }

    public void setWhCsBarcode69(String whCsBarcode69) {
        this.whCsBarcode69 = whCsBarcode69;
    }

    public String getCdpaFormat() {
        return cdpaFormat;
    }

    public void setCdpaFormat(String cdpaFormat) {
        this.cdpaFormat = cdpaFormat;
    }

    public String getIsValidity() {
        return isValidity;
    }

    public void setIsValidity(String isValidity) {
        this.isValidity = isValidity;
    }

    public BigDecimal getPeriodOfValidity() {
        return periodOfValidity;
    }

    public void setPeriodOfValidity(BigDecimal periodOfValidity) {
        this.periodOfValidity = periodOfValidity;
    }

    public String getValidityUnit() {
        return validityUnit;
    }

    public void setValidityUnit(String validityUnit) {
        this.validityUnit = validityUnit;
    }

    public int getIsDecimal() {
        return isDecimal;
    }

    public void setIsDecimal(int isDecimal) {
        this.isDecimal = isDecimal;
    }

    public String getOnHandQtyStr() {
        return onHandQtyStr;
    }

    public void setOnHandQtyStr(String onHandQtyStr) {
        this.onHandQtyStr = onHandQtyStr;
    }

    public BigDecimal getTempMoveNum() {
        return tempMoveNum;
    }

    public void setTempMoveNum(BigDecimal tempMoveNum) {
        this.tempMoveNum = tempMoveNum;
    }

    public BigDecimal getEditNum() {
        return editNum;
    }

    public void setEditNum(BigDecimal editNum) {
        this.editNum = editNum;
    }

    public BigDecimal getEditNum1() {
        return editNum1;
    }

    public void setEditNum1(BigDecimal editNum1) {
        this.editNum1 = editNum1;
    }

    public BigDecimal getEditNum2() {
        return editNum2;
    }

    public void setEditNum2(BigDecimal editNum2) {
        this.editNum2 = editNum2;
    }

    public BigDecimal getEditNum3() {
        return editNum3;
    }

    public void setEditNum3(BigDecimal editNum3) {
        this.editNum3 = editNum3;
    }

    public BigDecimal getEditNum4() {
        return editNum4;
    }

    public void setEditNum4(BigDecimal editNum4) {
        this.editNum4 = editNum4;
    }

    public String getCdprUnit() {
        return cdprUnit;
    }

    public void setCdprUnit(String cdprUnit) {
        this.cdprUnit = cdprUnit;
    }

    public BigDecimal getCdprQuantity() {
        return cdprQuantity;
    }

    public void setCdprQuantity(BigDecimal cdprQuantity) {
        this.cdprQuantity = cdprQuantity;
    }

    public List<PackageRelation> getPackageRelationList() {
        return packageRelationList;
    }

    public void setPackageRelationList(List<PackageRelation> packageRelationList) {
        this.packageRelationList = packageRelationList;
    }

    public int getIsVisible() {
        return isVisible;
    }

    public void setIsVisible(int isVisible) {
        this.isVisible = isVisible;
    }

    public Boolean getCheck() {
        return isCheck;
    }

    public void setCheck(Boolean check) {
        isCheck = check;
    }


    public BigDecimal getMoveNum() {
        return moveNum;
    }

    public void setMoveNum(BigDecimal moveNum) {
        this.moveNum = moveNum;
    }


    public String getWhBarcode69() {
        return whBarcode69;
    }

    public void setWhBarcode69(String whBarcode69) {
        this.whBarcode69 = whBarcode69;
    }

    public String getLotAtt01() {
        return lotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        this.lotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return lotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        this.lotAtt02 = lotAtt02;
    }

    public String getLotAtt03() {
        return lotAtt03;
    }

    public void setLotAtt03(String lotAtt03) {
        this.lotAtt03 = lotAtt03;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getLotAtt05() {
        return lotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        this.lotAtt05 = lotAtt05;
    }

    public String getLotAtt06() {
        return lotAtt06;
    }

    public void setLotAtt06(String lotAtt06) {
        this.lotAtt06 = lotAtt06;
    }

    public String getLotAtt07() {
        return lotAtt07;
    }

    public void setLotAtt07(String lotAtt07) {
        this.lotAtt07 = lotAtt07;
    }

    public String getLotAtt08() {
        return lotAtt08;
    }

    public void setLotAtt08(String lotAtt08) {
        this.lotAtt08 = lotAtt08;
    }

    public String getLotAtt09() {
        return lotAtt09;
    }

    public void setLotAtt09(String lotAtt09) {
        this.lotAtt09 = lotAtt09;
    }

    public String getLotAtt10() {
        return lotAtt10;
    }

    public void setLotAtt10(String lotAtt10) {
        this.lotAtt10 = lotAtt10;
    }

    public String getLotAtt11() {
        return lotAtt11;
    }

    public void setLotAtt11(String lotAtt11) {
        this.lotAtt11 = lotAtt11;
    }

    public String getLotAtt12() {
        return lotAtt12;
    }

    public void setLotAtt12(String lotAtt12) {
        this.lotAtt12 = lotAtt12;
    }

    public BigDecimal getUsableQty() {
        return usableQty;
    }

    public void setUsableQty(BigDecimal usableQty) {
        this.usableQty = usableQty;
    }


    public BigDecimal getRepOutQty() {
        return repOutQty;
    }

    public void setRepOutQty(BigDecimal repOutQty) {
        this.repOutQty = repOutQty;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }


    public void setAllocatedQty(BigDecimal allocatedQty) {
        this.allocatedQty = allocatedQty;
    }

    public String getLocCode() {
        return locCode;
    }

    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getInDate() {
        return inDate;
    }

    public void setInDate(String inDate) {
        this.inDate = inDate;
    }

    public String getLotNum() {
        return lotNum;
    }

    public void setLotNum(String lotNum) {
        this.lotNum = lotNum;
    }


    public BigDecimal getAllocatedQty() {
        return allocatedQty;
    }

    public BigDecimal getInTransitQty() {
        return inTransitQty;
    }

    public void setInTransitQty(BigDecimal inTransitQty) {
        this.inTransitQty = inTransitQty;
    }

    public BigDecimal getLockedQty() {
        return lockedQty;
    }

    public void setLockedQty(BigDecimal lockedQty) {
        this.lockedQty = lockedQty;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public BigDecimal getStatus() {
        return status;
    }

    public void setStatus(BigDecimal status) {
        this.status = status;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }


    public BigDecimal getOnHandQty() {
        return onHandQty;
    }

    public void setOnHandQty(BigDecimal onHandQty) {
        this.onHandQty = onHandQty;
    }

    public BigDecimal getOnHandQtyTotal() {
        return onHandQtyTotal;
    }

    public void setOnHandQtyTotal(BigDecimal onHandQtyTotal) {
        this.onHandQtyTotal = onHandQtyTotal;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public boolean isJustClick() {
        return isJustClick;
    }

    public void setJustClick(boolean justClick) {
        isJustClick = justClick;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public int getIndex() {
        return index;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}