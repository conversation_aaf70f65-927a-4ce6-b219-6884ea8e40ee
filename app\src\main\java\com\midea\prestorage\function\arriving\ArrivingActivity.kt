package com.midea.prestorage.function.arriving

import android.content.Intent
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.ArrivingBean
import com.midea.prestoragesaas.databinding.ActivityArrivingBinding
import com.midea.prestoragesaas.databinding.PopViewArivingBinding
import com.midea.prestorage.dialog.FilterDialog
import com.xuexiang.xqrcode.XQRCode

// 入库订单池
@Suppress("UNCHECKED_CAST")
class ArrivingActivity : BaseActivity() {

    private lateinit var binding: ActivityArrivingBinding
    private lateinit var popBinding: PopViewArivingBinding
    private lateinit var popupWindow: PopupWindow
    private lateinit var daysDialog: FilterDialog
    private lateinit var statueDialog: FilterDialog
    lateinit var shStatues: FilterDialog //审核状态
    lateinit var taskStatus: FilterDialog
    lateinit var taskType: FilterDialog
    private var vm = ArrivingVM(this)
    val adapter = OutPoolStorageAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_arriving)
        binding.vm = vm

        initPop()
        initRecycle()
        initDialog()
        initLoadMore()
    }

    override fun onResume() {
        super.onResume()
        vm.init()
    }

    private fun initPop() {
        val popView = LayoutInflater.from(this).inflate(R.layout.pop_view_ariving, null)
        popBinding = DataBindingUtil.bind(popView)!!
        popBinding.vm = vm

        popupWindow = PopupWindow(
            popView,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        popupWindow.setBackgroundDrawable(
            ColorDrawable(
                ContextCompat.getColor(
                    this,
                    R.color.colorOutlineGrey
                )
            )
        )

        popupWindow.setOnDismissListener {
            vm.initFilterInfo()
            vm.onRefreshCommand.onRefresh()
        }

        popupWindow.isOutsideTouchable = true

        binding.llContainer.setOnClickListener {
            vm.popShow()
            popupWindow.showAsDropDown(it)

            popBinding.etOrderNo.requestFocus()
        }

        binding.llAllChoose.setOnClickListener {
            binding.cbSelectAll.performClick()
        }

        popBinding.llFilterClose.setOnClickListener {
            popupWindow.dismiss()
        }

        binding.cbSelectAll.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                adapter.allSelect(true)
            }
        }

        binding.cbSelectAll.setOnClickListener {
            if (!binding.cbSelectAll.isChecked) {
                adapter.allSelect(false)
            }
        }

        popBinding.tvTaskStatue.setOnClickListener {
            taskStatus.show()
        }

        popBinding.tvOrderType.setOnClickListener {
            taskType.show()
        }

        popBinding.tvShStatue.setOnClickListener {
            shStatues.show()
        }
    }

    private fun initRecycle() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter
        adapter.setClickId(R.id.ll_checked)

        adapter.setOnItemClickListener { adapter, _, position ->
            val bean = adapter.data[position] as ArrivingBean
            vm.onItemClick(bean)
        }

        adapter.setChangeSelectStatus {
            val results = adapter.data.filter { !it.isSelected }
            binding.cbSelectAll.isChecked = results.isEmpty()
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.loadMore()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    fun setCbSelectChecked(isChecked: Boolean) {
        binding.cbSelectAll.isChecked = isChecked
    }

    private fun initDialog() {
        daysDialog = FilterDialog(this)
        daysDialog.setTitle("请选择天数")
        daysDialog.dismissEdit()
        daysDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            popBinding.tvDays.text = it.showInfo
            vm.dayInfo = it as DCBean
            daysDialog.dismiss()
        })

        statueDialog = FilterDialog(this)
        statueDialog.setTitle("请选择商品状态")
        statueDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            popBinding.tvTaskStatue.text = it.showInfo
            vm.taskStatue = it as DCBean
            statueDialog.dismiss()
        })

        taskStatus = FilterDialog(this)
        taskStatus.setTitle("请选择任务状态")
        taskStatus.dismissEdit()
        taskStatus.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            popBinding.tvTaskStatue.text = it.showInfo
            vm.taskStatue = it as DCBean
            taskStatus.dismiss()
        })
        val arrives = mutableListOf(
            DCBean("未到车", 0, DCBean.SHOW_KEY),
            DCBean("全部", 1, DCBean.SHOW_KEY)
        )
        taskStatus.addAllData(arrives as MutableList<BaseItemShowInfo>)

        taskType = FilterDialog(this)
        taskType.setTitle("请选择任务类型")
        taskType.dismissEdit()
        taskType.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            popBinding.tvOrderType.text = it.showInfo
            vm.taskType = it as DCBean
            taskType.dismiss()
        })

        shStatues = FilterDialog(this)
        shStatues.setTitle("请选择审核状态")
        shStatues.dismissEdit()
        shStatues.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            popBinding.tvShStatue.text = it.showInfo
            vm.shStatue = it as DCBean
            shStatues.dismiss()
        })
    }

    fun resetFilterInfo(filters: MutableList<String>) {
        binding.warpLinear.removeAllViews()
        filters.forEach {
            binding.warpLinear.addView(getShowView(it))
        }
    }

    private fun getShowView(text: String): View {
        var view: View = LayoutInflater.from(this).inflate(R.layout.item_filter, null)
        view.findViewById<TextView>(R.id.tv_item).text = text
        return view
    }

    fun showDaysDialog(days: MutableList<BaseItemShowInfo>) {
        daysDialog.addAllData(days)
        daysDialog.show()
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun showData(data: MutableList<ArrivingBean>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()

        vm.isNoData.set(adapter.data.isNullOrEmpty())
    }

    fun loadMoreData(data: MutableList<ArrivingBean>?) {
        data?.let { adapter.addData(it) }
        adapter.notifyDataSetChanged()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            vm.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    fun popDismiss() {
        popupWindow.dismiss()
    }

    class OutPoolStorageAdapter :
        ListCheckBoxAdapter<ArrivingBean>(R.layout.item_arriving), LoadMoreModule {
        override fun convert(helper: BaseViewHolder, item: BaseItemForPopup) {
            super.convert(helper, item as ArrivingBean)

            val check = helper.getView<ImageView>(R.id.img_select)
            if (item.isSelected()) {
                check.setImageResource(R.drawable.ic_check_selected)
            } else {
                check.setImageResource(R.drawable.ic_check_unselect)
            }

            if (item.orderStatus.toInt() >= 310) {
                if (item.isSelected) {
                    item.isSelected = false
                    notifyDataSetChanged()
                }
                helper.setGone(R.id.img_select, true)
            } else {
                helper.setGone(R.id.img_select, false)
            }
        }
    }
}