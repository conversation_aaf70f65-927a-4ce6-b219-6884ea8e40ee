package com.midea.prestorage.function.planstock

import android.app.Application
import android.content.Intent
import android.text.InputFilter
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.FuShipmentStatue
import com.midea.prestorage.beans.net.ReplenishmentBean
import com.midea.prestorage.beans.net.RespPlanStockList
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.launch
import com.midea.prestorage.widgets.ViewBindingAdapter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal

class PlanStockListVM(application: Application) : BaseViewModel(application) {

    val isRefreshing = ObservableBoolean(false)
    val isNoData = ObservableBoolean(false)

    // 搜索条件 单号
    var searchOrderNo = ObservableField<String>("")

    var taskStatues: MutableList<FuShipmentStatue>? = null
    var taskRanges: MutableList<FuShipmentStatue>? = null
    var taskTypes: MutableList<FuShipmentStatue>? = null

    // 当前页码
    var pageNo = 1

    val loadMoreComplete = MutableLiveData(0)
    var showDatas = MutableLiveData<MutableList<RespPlanStockList>>()
    var loadMoreDatas = MutableLiveData<MutableList<RespPlanStockList>>()

    val filter = InputFilter { source, _, _, _, _, _ ->
        source.toString().replace("\n", "").replace("\r", "")
    }

    override fun init() {

    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        isRefreshing.set(true)
        initData(true)
    }

    fun initData(isRefresh: Boolean) {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            if (isRefresh) {
                pageNo = 1
            }

            val map = mutableMapOf(
                "stocktakeCode" to searchOrderNo.get()?.trim(),
                "whCode" to Constants.whInfo?.whCode,
//                "statusList" to mutableListOf("200", "300"),
                "pageSize" to "10",
                "pageNo" to pageNo.toString()
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(map)
            )

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getWareManageAPI().taskHeaderPage(requestBody)
            }

            if (result.code == 0L) {
                if (pageNo == 1) {
                    isRefreshing.set(false)
                }
                loadMoreComplete.value = 1

                result.data?.let {
                    if (pageNo == 1) {
                        showDatas.value = it.list
                    } else {
                        loadMoreDatas.value = it.list
                    }
                    if (pageNo < it.totalPage) {
                        pageNo++
                    } else {
                        loadMoreComplete.value = 2
                    }
                }
            } else {
                isRefreshing.set(false)
                result.msg?.let { showNotification(it, false) }
                if (pageNo == 1) {
                    val emptyList = mutableListOf<RespPlanStockList>()
                    showDatas.value = emptyList
                }
            }


        }
    }

    val serialKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                initData(true)
            }
        }
    }

    fun scanResult(result: String?) {
        result?.let {
            searchOrderNo.set(result)
        }
    }

    fun clearOrderNo() {
        searchOrderNo.set("")
        initData(true)
    }

    fun onItemClick(bean: RespPlanStockList) {
        val it = Intent()
        it.putExtra("stocktakeCode", bean.stocktakeCode)
        it.putExtra("locNum", AppUtils.getBigDecimalValueStrNull(bean.locNum))
        it.putExtra("usedLocNum", AppUtils.getBigDecimalValueStrNull(bean.usedLocNum))
        if (bean.locUsePercent != null) {
            it.putExtra(
                "stocktakeSchedule", "${
                    AppUtils.getBigDecimalValueStr(
                        bean.locUsePercent!!.multiply(
                            BigDecimal(100)
                        )
                    )
                }%"
            )
        } else {
            it.putExtra("stocktakeSchedule", "0%")
        }
        toActivity(it, PlanStockActivity::class.java)
    }

    fun startSearch() {
        if (pageNo > 1) {
            initData(false)
        }
    }
}