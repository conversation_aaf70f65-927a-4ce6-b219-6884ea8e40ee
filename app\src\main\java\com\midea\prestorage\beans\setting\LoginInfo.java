package com.midea.prestorage.beans.setting;

import org.xutils.db.annotation.Column;
import org.xutils.db.annotation.Table;

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/9/23$
 */
@Table(name = "LoginInfo")
public class LoginInfo {

    @Column(name = "id", isId = true, autoGen = true, property = "NOT NULL")
    private int id;

    @Column(name = "name")
    private String name = "";

    @Column(name = "userName")
    private String userName = "";

    @Column(name = "pwd")
    private String pwd = "";

    @Column(name = "isRemember")
    private boolean isRemember; //是否记录密码

    @Column(name = "isFirst")
    private boolean isFirst; // 是否当前登陆用户

    @Column(name = "isAutoStart")
    private boolean isAutoStart; // 是否自动开始

    @Column(name = "whName")
    private String whName; //仓库名

    @Column(name = "whCode")
    private String whCode; //仓库编码

    @Column(name = "whType")
    private int whType; //仓库类型 2是云贵，4是前置仓

    @Column(name = "speedRate")
    private float speedRate; // 语音播报语速

    @Column(name = "putMode") // 补货上架模块
    private Integer putMode; // 0为默认上海库位为推荐库位，1为记住上次上架库位

    @Column(name = "rememberLoc") // 补货上架模块
    private String rememberLoc; // 记住的货位

    @Column(name = "scanNum") // 补货下架模块
    private boolean scanNum; // 默认取货数量为应取数量

    @Column(name = "scanJump") // 补货下架模块
    private boolean scanJump; // 确认下架时自动跳转到下个任务

    @Column(name = "scanReceiveMode") // 容器收货模块
    private Integer scanReceiveMode; // 0为逐条收货，1为批量收货

    @Column(name = "scanPutMode") // 容器上架模块
    private Integer scanPutMode; // 0为逐条收货，1为批量收货

    @Column(name = "environment")//登陆环境
    private String environment;

    @Column(name = "account")
    private String account = "";

    public LoginInfo() {
    }

    public LoginInfo(String name, String pwd) {
        this.name = name;
        this.pwd = pwd;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public boolean isRemember() {
        return isRemember;
    }

    public void setRemember(boolean remember) {
        isRemember = remember;
    }

    public boolean isFirst() {
        return isFirst;
    }

    public void setFirst(boolean first) {
        isFirst = first;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getWhName() {
        return whName;
    }

    public void setWhName(String whName) {
        this.whName = whName;
    }

    public float getSpeedRate() {
        return speedRate;
    }

    public void setSpeedRate(float speedRate) {
        this.speedRate = speedRate;
    }

    public Integer getPutMode() {
        return putMode;
    }

    public void setPutMode(Integer putMode) {
        this.putMode = putMode;
    }

    public String getRememberLoc() {
        return rememberLoc;
    }

    public void setRememberLoc(String rememberLoc) {
        this.rememberLoc = rememberLoc;
    }

    public boolean getScanNum() {
        return scanNum;
    }

    public void setScanNum(boolean scanNum) {
        this.scanNum = scanNum;
    }

    public boolean getScanJump() {
        return scanJump;
    }

    public void setScanJump(boolean scanJump) {
        this.scanJump = scanJump;
    }

    public Integer getScanReceiveMode() {
        return scanReceiveMode;
    }

    public void setScanReceiveMode(Integer scanReceiveMode) {
        this.scanReceiveMode = scanReceiveMode;
    }

    public Integer getScanPutMode() {
        return scanPutMode;
    }

    public void setScanPutMode(Integer scanPutMode) {
        this.scanPutMode = scanPutMode;
    }

    public int getWhType() {
        return whType;
    }

    public void setWhType(int whType) {
        this.whType = whType;
    }

    public boolean isAutoStart() {
        return isAutoStart;
    }

    public void setAutoStart(boolean autoStart) {
        isAutoStart = autoStart;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
