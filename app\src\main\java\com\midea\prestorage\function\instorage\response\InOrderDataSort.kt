package com.midea.prestorage.function.instorage.response

import android.text.TextUtils
import androidx.databinding.ObservableField

class InOrderDataSort(var item: InOrderData): Comparable<InOrderDataSort> {

    val vs = ViewStyle()

    inner class ViewStyle {
        val sortFlag = ObservableField<Int>(0)
        val sortRecvFlag = ObservableField<Int>(0)
    }

    init {
        vs.sortFlag.set(if (item.scanNum == 0 && item.totalQty == 0.toDouble()) 1 else if (item.scanNum == 0) 1 else if (item.totalQty > item.scanNum) 0 else 2)
        vs.sortRecvFlag.set(if (item.receiptQty == 0.toDouble()) 0 else if (item.totalQty > item.receiptQty) 1 else 2)
    }

    override fun compareTo(other: InOrderDataSort): Int {
        var result1 = vs.sortFlag.get()!! - other.vs.sortFlag.get()!!
        var result2 = vs.sortRecvFlag.get()!! - other.vs.sortRecvFlag.get()!!
        return if(result1 == 0) {
            result2
        }else {
            result1
        }
    }
}