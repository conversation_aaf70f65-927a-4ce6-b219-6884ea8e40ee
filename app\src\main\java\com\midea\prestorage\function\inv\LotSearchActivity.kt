package com.midea.prestorage.function.inv

import android.app.AlertDialog
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestoragesaas.databinding.ActivityLotSearchBinding
import com.midea.prestoragesaas.databinding.PopViewForSelectCustemItemCodeBinding
import com.midea.prestorage.function.inv.response.ItemRfVO
import com.midea.prestorage.function.inv.response.FuInvLocationInventory
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.planstock.ActivityPlanStockDetailUnionBinding
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.LotAttUnit
import com.midea.prestorage.utils.SPUtils
import com.xuexiang.xqrcode.XQRCode

class LotSearchActivity : BaseActivity() {

    lateinit var binding: ActivityLotSearchUnionBinding
    var adapter = ListInvLotInfoAdapter()
    var isNeedRefresh = false; //是否需要刷新，如果需要，会在onResume的时候刷新

    //69码 查询出多个custItemCodes的时候 弹框让用户选择custItemCode
    lateinit var dlgSelectCustItemCode: AlertDialog
    private lateinit var popBindingSelectCustItemCode: PopViewForUnionBinding
    lateinit var popAdapterSelectCustItemCode: CommonAdapter<BaseItemForPopup>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityLotSearchUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_lot_search_care
                )
            )
        } else {
            ActivityLotSearchUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_lot_search
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        binding.vm = LotSearchVM(this)

        //初始化库存状态字典
        DCUtils.initLot4dicts(this, null)

        initRecycleView()

        initPopWinSelectCustItemCode()

        //光标默认定位在 etLocCode
        AppUtils.requestFocus(binding.etLocCode)

        //初始化货主列表
        binding.vm!!.initOwnerList()
    }

    // 初始化选择客户商品编码的弹窗
    private fun initPopWinSelectCustItemCode() {

        val alertDialogBuilder = AlertDialog.Builder(this)

        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            val popViewSelectCustItemCode =
                LayoutInflater.from(this)
                    .inflate(R.layout.pop_view_for_select_custem_item_code_care, null)
            popBindingSelectCustItemCode =
                PopViewForUnionBinding.V2(DataBindingUtil.bind(popViewSelectCustItemCode)!!)
            alertDialogBuilder.setView(popViewSelectCustItemCode)
        } else {
            val popViewSelectCustItemCode =
                LayoutInflater.from(this)
                    .inflate(R.layout.pop_view_for_select_custem_item_code, null)
            popBindingSelectCustItemCode =
                PopViewForUnionBinding.V1(DataBindingUtil.bind(popViewSelectCustItemCode)!!)
            alertDialogBuilder.setView(popViewSelectCustItemCode)
        }

        dlgSelectCustItemCode = alertDialogBuilder.create()

        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            dlgSelectCustItemCode.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            dlgSelectCustItemCode.window?.setGravity(Gravity.CENTER)
            dlgSelectCustItemCode.window?.attributes?.run {
                gravity = Gravity.CENTER
            }
        }

        popAdapterSelectCustItemCode = ListCustItemCodeAdapter()
        popBindingSelectCustItemCode.recyclerViewInPop.layoutManager = LinearLayoutManager(this)
        popBindingSelectCustItemCode.recyclerViewInPop.adapter = popAdapterSelectCustItemCode

        popAdapterSelectCustItemCode.setOnItemClickListener { adapter, view, position ->
            val item = adapter.getItem(position) as ItemRfVO
            binding.etCustItemCode.setText(item.custItemCode)
            dlgSelectCustItemCode.dismiss()
            binding.vm!!.onEnterSearch()
        }

        popBindingSelectCustItemCode.closePop.setOnClickListener {
            dlgSelectCustItemCode.dismiss()
        }
    }

    class ListCustItemCodeAdapter :
        CommonAdapter<BaseItemForPopup>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_pop_view_for_select_cust_item_code_69_care else R.layout.item_pop_view_for_select_cust_item_code_69) {
        override fun convert(helper: BaseViewHolder, item: BaseItemForPopup) {
            super.convert(helper, item as ItemRfVO)
            // 客户商品编码
            helper.setText(
                R.id.tvCustItemCode, item.custItemCode
            )

            if (item?.whCsBarcode69.isNullOrEmpty() && item?.whBarcode69.isNullOrEmpty()) {
                helper.setGone(R.id.tv_carcode69, true)
            } else {
                helper.setGone(R.id.tv_carcode69, false)
                helper.setText(
                    R.id.tv_carcode69,
                    LotAttUnit.formatWhBarcode69(item?.whCsBarcode69, item?.whBarcode69)
                )
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (isNeedRefresh) {
            isNeedRefresh = false;
            binding.vm!!.onEnterSearch()
        }
    }

    fun initRecycleView() {

        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter

        adapter.setOnCheckListener {
            isNeedRefresh = true;
            val intent = Intent(this, LotEditActivity::class.java)
            intent.putExtra("locationInv", it)
            startActivity(intent)
        }
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == BaseActivity.QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果
            binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    class ListInvLotInfoAdapter :
        ListChoiceClickAdapter<FuInvLocationInventory>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_for_lot_search_care else R.layout.item_for_lot_search) {

        override fun convert(helper: BaseViewHolder, item: FuInvLocationInventory) {
            super.convert(helper, item)


            //货品编码+69码
            helper.setText(R.id.custItemCode, item.custItemCode)

            helper?.setText(
                R.id.tv_carcode69,
                LotAttUnit.formatWhBarcode69(item?.whCsBarcode69, item?.whBarcode69)
            )

            //货品描述
            if (!item.itemName.isNullOrEmpty()) {
                helper.setText(R.id.tvItemName, item.itemName)
            } else {
                helper.setText(R.id.tvItemName, "")
            }

            //  可用库存
            if (item.usableQty != null) {
                helper.setText(R.id.tvCanUseNum, AppUtils.getBigDecimalValueStr(item.usableQty))
            } else {
                helper.setText(R.id.tvCanUseNum, "")
            }

            // 入库日期
            /*
            if (!item.lotAtt03.isNullOrEmpty()) {
                  if (item.lotAtt03.split(" ").size > 0) {
                      helper.setText(R.id.tvLot3, item.lotAtt03.split(" ")[0])
                  } else {
                      helper.setText(R.id.tvLot3, item.lotAtt03)
                  }
              }*/


            // 属性4  状态
            if (!item.lotAtt04.isNullOrEmpty()) {
                if (DCUtils.lot4TypeC2N != null && DCUtils.lot4TypeC2N.get(item.lotAtt04) != null) {
                    helper.setText(R.id.tvLot4, DCUtils.lot4TypeC2N.get(item.lotAtt04).toString())
                } else {
                    helper.setText(R.id.tvLot4, item.lotAtt04)
                }
            } else {
                helper.setText(R.id.tvLot4, "")
            }

            // 除了入库日期 lot3  和 状态 lot4 以外的其他属性，如果有就显示
            var lotInfos = "";
            var arr = arrayListOf<String>()
            var joinStr = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                "-"
            } else {
                ": "
            }

            if (!item.lotAtt05.isNullOrEmpty()) {
                arr.add("批次${joinStr}" + item.lotAtt05)
            }
            if (!item.lotAtt01.isNullOrEmpty()) {
                var info = item.lotAtt01
                if (info.split(" ").size > 0) {
                    info = item.lotAtt01.split(" ")[0]
                }
                arr.add("生产日期${joinStr}" + info.replace("-", ""))
            }
            if (!item.lotAtt02.isNullOrEmpty()) {
                var info = item.lotAtt02
                if (info.split(" ").size > 0) {
                    info = item.lotAtt02.split(" ")[0]
                }
                arr.add("失效日期${joinStr}" + info.replace("-", ""))
            }
            if (!item.lotAtt03.isNullOrEmpty()) {
                var info = item.lotAtt03
                if (info.split(" ").size > 0) {
                    info = item.lotAtt03.split(" ")[0]
                }
                arr.add("入库日期${joinStr}" + info.replace("-", ""))
            }

            if (!item.lotAtt06.isNullOrEmpty()) {
                arr.add("属性6${joinStr}" + item.lotAtt06)
            }
            if (!item.lotAtt07.isNullOrEmpty()) {
                arr.add("属性7${joinStr}" + item.lotAtt07)
            }
            if (!item.lotAtt08.isNullOrEmpty()) {
                arr.add("属性8${joinStr}" + item.lotAtt08)
            }
            if (!item.lotAtt09.isNullOrEmpty()) {
                arr.add("属性9${joinStr}" + item.lotAtt09)
            }
            if (!item.lotAtt10.isNullOrEmpty()) {
                arr.add("属性10${joinStr}" + item.lotAtt10)
            }
            if (!item.lotAtt11.isNullOrEmpty()) {
                arr.add("属性11${joinStr}" + item.lotAtt11)
            }
            if (!item.lotAtt12.isNullOrEmpty()) {
                arr.add("属性12${joinStr}" + item.lotAtt12)
            }
            lotInfos = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                arr.joinToString(separator = "\n")
            } else {
                arr.joinToString(separator = " / ")
            }

            helper.setText(R.id.tvLotInfos, lotInfos)
        }
    }
}