package com.midea.prestorage.beans.net;

import android.text.TextUtils;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.beans.base.BaseItemForPopup;

import java.io.Serializable;

public class ArrivingBean extends BaseItemForPopup {

    @ShowAnnotation
    private String taskNo; // 任务号
    @ShowAnnotation
    private String waybillNo; // 运单号
    @ShowAnnotation
    private String customerOrderNo; // 客户单号
    @ShowAnnotation
    private String companyName; // 公司
    @ShowAnnotation
    private String createTime; // 时间
    @ShowAnnotation
    private String orderStatusStr;

    private String orderStatus;

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public String getCustomerOrderNo() {
        return customerOrderNo;
    }

    public void setCustomerOrderNo(String customerOrderNo) {
        this.customerOrderNo = customerOrderNo;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getOrderStatusStr() {
        if (TextUtils.isEmpty(orderStatusStr)) {
            return "";
        }
        return orderStatusStr;
    }

    public void setOrderStatusStr(String orderStatusStr) {
        this.orderStatusStr = orderStatusStr;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getWaybillNo() {
        return waybillNo;
    }

    public void setWaybillNo(String waybillNo) {
        this.waybillNo = waybillNo;
    }
}
