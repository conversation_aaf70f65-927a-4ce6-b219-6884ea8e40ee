package com.midea.prestorage.function.containerpick

import CheckUtil
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.*
import com.midea.prestorage.dialog.ContainerCodeDialog
import com.midea.prestorage.dialog.TipDialog
import com.midea.prestorage.dialog.TipNoCancelDialog
import com.midea.prestorage.function.outstorage.response.PrintShipmentContainer
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.printer.BluetoothConnectBack
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.MySoundUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestoragesaas.R
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.util.*

class ContainerPickSecondVM(val activity: ContainerPickSecondActivity) {

    val isNoData = ObservableBoolean(false)
    val isStart = ObservableBoolean(false)
    val isEditAble = ObservableBoolean(true)
    val isPrintOk = ObservableField(false)
    val isShowBulkPack = ObservableBoolean(true) //是否显示 散货打包

    var totalQty = ObservableField("")
    var totalCsQty = ObservableField("")
    var totalEaQty = ObservableField("")
    var csUnit = ObservableField("")
    var eaUnit = ObservableField("")

    var shippingLoc = ObservableField("")

    var search69Code = ObservableField("")

    var title = ObservableField("")
    var localInfo = ObservableField("")
    var pickInfo = ObservableField("")
    var containerCode = ObservableField("")
    private var containerPickList: ContainerPickList? = null

    var configValue = ObservableField(0) //1定位到库位，0定位到条码

    var isShowContainerCode = ObservableField(0)

    var taskCode = ObservableField("")

    private var tipDialog: TipDialog? = null

    private var containerCodeDialog: ContainerCodeDialog? = null

    val containerPickTaskType: ContainerPickTaskType = ContainerPickTaskType()

    var canAutoClose = true

    var isPickPackTogether = "N" //是否开启边拣边包

    companion object {
        val TASK_TYPE_CSPICK = "CSPICK"
    }

    private val blueBack = object : BluetoothConnectBack {
        override fun success() {
            activity.waitingDialogHelp.hidenDialog()
            isPrintOk.set(Printer.connectUtils?.isPrintOk())
        }

        override fun fail() {
            isPrintOk.set(Printer.connectUtils?.isPrintOk())
            AppUtils.showToast(activity, "打印机连接失败，请重启打印机!")
        }

        override fun connect() {
            bluetoothOpen()
        }
    }

    fun bluetoothOpen(isAuto: Boolean = true) {
        if (!Printer.isPrintOk()) {
            Printer.openBluetooth(activity, blueBack, isAuto)
        } else {
            isPrintOk.set(true)
        }
    }

    private val startActivity =
        activity.registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            //此处是跳转的result回调方法
            if (it.data != null && it.resultCode == Activity.RESULT_OK) {
                val result = it.data?.getIntExtra("result", -1)
                if (result != -1) {
                    val code = it.data?.getStringExtra("containerCode")
                    containerCode.set(code)
                    isEditAble.set(false)

                    if (configValue.get() == 1) {
                        activity.locationRequestFocus()
                    } else {
                        activity.goodsRequestFocus()
                    }
                }
            }
        }

    fun init() {
        activity.binding.edContainer.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val str = s.toString()
                if (str.isNotEmpty()) {
                    if (containsLowerCase(str)) {
                        activity.binding.edContainer.setText(str.toUpperCase())
                        activity.binding.edContainer.setSelection(activity.binding.edContainer.text.length)
                    }
                }
            }

            override fun afterTextChanged(s: Editable?) {}
        })

        containerPickList =
            activity.intent.getSerializableExtra("ContainerPickList") as ContainerPickList

        containerPickList?.let {
            taskCode.set(it.taskCode)

            isShowBulkPack.set("PROCESSING" != it.commonFlag)
        }

        if (containerPickList?.status != 100 && containerPickList?.status != 300) { //已经开始拣货
            activity.containerRequestFocus()
            isStart.set(true)
            queryContainerCode() //查询正在进行容器号和任务号
        } else { //还未开始拣货
            if (!containerPickList?.id.isNullOrEmpty()) {
                initWaveNo(containerPickList?.id)
            }
            title.set(containerPickList?.waveNo)
        }

        tipDialog = TipDialog(activity)
        containerCodeDialog = ContainerCodeDialog(activity)

        queryConfigManage()
        initConfig()
    }

    fun resume() {
//        if (containerPickList?.status == 100 || activity.adapter.data.isNotEmpty()) {
        if (!containerPickList?.id.isNullOrEmpty()) {
            initWaveNo(containerPickList?.id)
        }
//        }
    }

    private fun queryConfigManage() {
        RetrofitHelper.getOutStorageAPI()
            .queryConfigManage()
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ContainerConfigBean>(activity) {
                override fun success(data: ContainerConfigBean?) {
                    data?.let {
                        isShowContainerCode.set(it.configValue as? Int ?: 0)
                        if (it.configValue == 1) {
                            isEditAble.set(false)
                            if (configValue.get() == 1) {
                                activity.locationRequestFocus()
                            } else {
                                activity.goodsRequestFocus()
                            }
                        } else {
                            isEditAble.set(true)
                            activity.containerRequestFocus()
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                }
            })
    }

    private fun initConfig() {
        RetrofitHelper.getOutStorageAPI()
            .queryConfig()
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ContainerConfigBean>(activity) {
                override fun success(data: ContainerConfigBean?) {
                    data?.let {
                        configValue.set(it.configValue as? Int ?: 0)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
//                    ToastUtils.getInstance()
//                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    /**
     * 查询正在进行容器号和任务号
     */
    private fun queryContainerCode() {
        activity.waitingDialogHelp.showDialogUnCancel()

        RetrofitHelper.getOutStorageAPI()
            .queryContainerCode()
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ResqContainerCode>(activity) {
                override fun success(data: ResqContainerCode?) {
                    activity.waitingDialogHelp.hidenDialog()
                    data?.let {
                        if (data.containerCode.isNullOrEmpty() || data.waveNo.isNullOrEmpty() || data.taskCode.isNullOrEmpty()) { //如果不存在，则按现有逻辑查从上一界面传递的任务信息
                            if (!containerPickList?.id.isNullOrEmpty()) {
                                initWaveNo(containerPickList?.id)
                            }
                            title.set(containerPickList?.waveNo)
                        } else { //如果存在，则弹窗提示，点击【确认】时，把弹窗的【容器号】写入容器号栏位，并且根据弹窗的【任务号】调用{拣货任务明细查询接口}，获取当前容器对应的拣货任务信息
                            if (data.taskHeaderId == containerPickList?.id) {//同一个任务的时候，不需要在提示上一次的拣货容器未关闭，直接拣货就可以了
                                title.set(data.waveNo)
                                containerCode.set(data.containerCode)
                                containerPickList?.id = data.taskHeaderId
                                initWaveNo(data.taskHeaderId)
                            } else {
                                containerCodeDialog?.setTitle("询问")
                                containerCodeDialog?.setMsg(activity.resources.getString(R.string.container_code_tip))
                                containerCodeDialog?.setContainerCode(data.containerCode)
                                containerCodeDialog?.setTaskCode(data.taskCode)
                                containerCodeDialog?.setOnTipBackListener(object :
                                    ContainerCodeDialog.OnTipBack {
                                    override fun onConfirmClick() {
                                        taskCode.set(data.taskCode)
                                        title.set(data.waveNo)
                                        containerCode.set(data.containerCode)
                                        containerPickList?.id = data.taskHeaderId
                                        initWaveNo(data.taskHeaderId)
                                    }
                                })
                                containerCodeDialog?.show()
                            }
                        }
                    } ?: run {
                        if (!containerPickList?.id.isNullOrEmpty()) {
                            initWaveNo(containerPickList?.id)
                        }
                        title.set(containerPickList?.waveNo)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    if (!containerPickList?.id.isNullOrEmpty()) {
                        initWaveNo(containerPickList?.id)
                    }
                    title.set(containerPickList?.waveNo)
                }
            })
    }

    private fun initWaveNo(id: String?) {
        RetrofitHelper.getOutStorageAPI()
            .queryDetail(id)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<ContainerPickSecondList>>(activity) {
                override fun success(data: MutableList<ContainerPickSecondList>?) {
                    if (data.isNullOrEmpty()) {
                        shippingLoc.set("")
                        isNoData.set(true)
                    } else {
                        isNoData.set(false)

                        shippingLoc.set(
                            data.getOrNull(0)?.shippingLocList?.distinct()
                                ?.joinToString(separator = ",")
                        )

                        //合并操作
                        val combines = mutableListOf<ContainerPickSecondList>()
                        data.forEach {
                            it.lotAtt01 = it.lotAtt01?.split(" ")?.get(0)

                            if (combines.contains(it)) {
                                val result = combines.find { item -> item == it }

                                result?.sumQty = result?.sumQty?.add(it.sumQty)
                                result?.sumCsQty = result?.sumCsQty?.add(it.sumCsQty)
                                result?.sumEaQty = result?.sumEaQty?.add(it.sumEaQty)
                                result?.fromQty = result?.fromQty?.add(it.fromQty)
                                result?.toQty = result?.toQty?.add(it.toQty)

                                result?.repFlag = result?.repFlag?.add(it.repFlag)

                                result?.ids?.add(it.id)
                                if (!AppUtils.isZero(it.repFlag)) {
                                    result?.taskDetailIdList?.add(it.id)
                                }
                                result?.taskCombines?.add(
                                    ContainerPickTaskTypeList(
                                        it.eaUnit,
                                        it.fromQty - it.toQty,
                                        it.gridNumber
                                    )
                                )

                                if (result?.isSortPick != true) {
                                    result?.isSortPick = it.shortPick == "1"
                                }
                            } else {
                                it.isSortPick = it.shortPick == "1"

                                it.ids.add(it.id)
                                if (!AppUtils.isZero(it.repFlag)) {
                                    it.taskDetailIdList.add(it.id)
                                }
                                it.taskCombines.add(
                                    ContainerPickTaskTypeList(
                                        it.eaUnit,
                                        it.fromQty - it.toQty,
                                        it.gridNumber
                                    )
                                )
                                combines.add(it)
                            }
                        }

                        //打上下标
                        combines.forEachIndexed { index, it ->
                            // 重新用箱规计算
                            val packagePara = AppUtils.getBigDecimalValue(it.packagePara)
                            val packageParaIp = AppUtils.getBigDecimalValue(it.packageParaIp)
                            val sumQtyBig = AppUtils.getBigDecimalValue(it.sumQty)
                            if (packagePara.compareTo(BigDecimal(0)) == 1) {
                                val divide =
                                    sumQtyBig.divide(packagePara, BigDecimal.ROUND_DOWN)
                                if (divide.compareTo(BigDecimal(1)) == -1) {
                                    it.sumCsQty = BigDecimal(0)
                                    if (packageParaIp.compareTo(BigDecimal(0)) == 1) {
                                        val divideIp =
                                            sumQtyBig.divide(packageParaIp, BigDecimal.ROUND_DOWN)
                                        if (divideIp.compareTo(BigDecimal(1)) == -1) {
                                            it.sumIpQty = BigDecimal(0)
                                            it.sumEaQty = sumQtyBig
                                        } else {
                                            it.sumIpQty =
                                                divideIp.setScale(0, BigDecimal.ROUND_DOWN)
                                            it.sumEaQty =
                                                it.sumQty.subtract(
                                                    it.sumIpQty.multiply(
                                                        packageParaIp
                                                    )
                                                )
                                        }
                                    } else {
                                        it.sumIpQty = BigDecimal(0)
                                        it.sumEaQty = sumQtyBig
                                    }
                                } else {
                                    it.sumCsQty = divide.setScale(0, BigDecimal.ROUND_DOWN)
                                    var sumQty =
                                        it.sumQty.subtract(it.sumCsQty.multiply(packagePara))
                                    if (packageParaIp.compareTo(BigDecimal(0)) == 1) {
                                        val divideIp =
                                            sumQty.divide(packageParaIp, BigDecimal.ROUND_DOWN)
                                        if (divideIp.compareTo(BigDecimal(1)) == -1) {
                                            it.sumIpQty = BigDecimal(0)
                                            it.sumEaQty = sumQty
                                        } else {
                                            it.sumIpQty =
                                                divideIp.setScale(0, BigDecimal.ROUND_DOWN)
                                            it.sumEaQty =
                                                sumQty.subtract(it.sumIpQty.multiply(packageParaIp))
                                        }
                                    } else {
                                        it.sumIpQty = BigDecimal(0)
                                        it.sumEaQty = sumQty
                                    }
                                }
                            } else {
                                it.sumCsQty = BigDecimal(0)
                                if (packageParaIp.compareTo(BigDecimal(0)) == 1) {
                                    val divideIp =
                                        sumQtyBig.divide(packageParaIp, BigDecimal.ROUND_DOWN)
                                    if (divideIp.compareTo(BigDecimal(1)) == -1) {
                                        it.sumIpQty = BigDecimal(0)
                                        it.sumEaQty = sumQtyBig
                                    } else {
                                        it.sumIpQty = divideIp.setScale(0, BigDecimal.ROUND_DOWN)
                                        it.sumEaQty =
                                            it.sumQty.subtract(it.sumIpQty.multiply(packageParaIp))
                                    }
                                } else {
                                    it.sumIpQty = BigDecimal(0)
                                    it.sumEaQty = sumQtyBig
                                }
                            }

                            it.index = index + 1
                            it.lotAtt04 = statusDC[it.lotAtt04]

                            val sumCsQtyStr = AppUtils.getBigDecimalValueStr(it.sumCsQty)
                            val sumEaQtyStr = AppUtils.getBigDecimalValueStr(it.sumEaQty)
                            val sumIpQtyStr = AppUtils.getBigDecimalValueStr(it.sumIpQty)

                            if (!AppUtils.isZero(it.sumCsQty)) {
                                it.qtyInfo = "${sumCsQtyStr}${it.csUnit}"
                            }


                            if (!AppUtils.isZero(it.sumIpQty)) {
                                if (it.qtyInfo.isNullOrEmpty()) {
                                    it.qtyInfo = "${sumIpQtyStr}${it.ipUnit}"
                                } else {
                                    it.qtyInfo = "${it.qtyInfo}${sumIpQtyStr}${it.ipUnit}"
                                }
                            }

                            if (!AppUtils.isZero(it.sumEaQty)) {
                                if (it.qtyInfo.isNullOrEmpty()) {
                                    it.qtyInfo = "${sumEaQtyStr}${it.eaUnit}"
                                } else {
                                    it.qtyInfo = "${it.qtyInfo}${sumEaQtyStr}${it.eaUnit}"
                                }
                            }

                            if (it.qtyInfo.isNullOrEmpty()) {
                                if (!it.lotAtt04.isNullOrEmpty()) {
                                    it.qtyInfo = it.lotAtt04
                                }
                            } else {
                                if (!it.lotAtt04.isNullOrEmpty()) {
                                    it.qtyInfo = "${it.qtyInfo}，${it.lotAtt04}"
                                }
                            }

                            val fromQtyStr = AppUtils.getBigDecimalValueStr(it.fromQty)
                            val packageParaStr = AppUtils.getBigDecimalValueStr(it.packagePara)
                            val packageParaIpStr = AppUtils.getBigDecimalValueStr(it.packageParaIp)

//                            if ((it.packagePara == null || AppUtils.isZero(it.packagePara)) && (it.packageParaIp == null || AppUtils.isZero(
//                                    it.packageParaIp
//                                ))
//                            ) {
//                                it.allQtyInfo = "${fromQtyStr}${it.eaUnit}"
//                            } else if (it.packageParaIp == null || AppUtils.isZero(it.packageParaIp)) {
//                                it.allQtyInfo =
//                                    "${fromQtyStr}${it.eaUnit}，${packageParaStr}${it.eaUnit}/${it.csUnit}"
//                            } else if (it.packagePara == null || AppUtils.isZero(it.packagePara)) {
//                                it.allQtyInfo =
//                                    "${fromQtyStr}${it.eaUnit}，${packageParaIpStr}${it.eaUnit}/${it.ipUnit}"
//                            } else {
//                                it.allQtyInfo =
//                                    "${fromQtyStr}${it.eaUnit}，${packageParaIpStr}${it.eaUnit}/${it.ipUnit}，${packageParaStr}${it.eaUnit}/${it.csUnit}"
//                            }
                            if (it.cdpaFormat.isNullOrEmpty()) {
                                it.allQtyInfo = "${fromQtyStr}${it.eaUnit}"
                            } else {
                                it.allQtyInfo = "${fromQtyStr}${it.eaUnit}，${it.cdpaFormat}"
                            }
                        }

                        val count = combines.count {
                            AppUtils.getBigDecimalValue(it.fromQty)
                                .compareTo(AppUtils.getBigDecimalValue(it.toQty)) == 1
                        }
                        pickInfo.set(count.toString())

                        val sumTotal =
                            combines.map { item -> AppUtils.getBigDecimalValue(item.sumQty) }
                                .fold(BigDecimal.ZERO, BigDecimal::add)
                        val sumCS =
                            combines.map { item -> AppUtils.getBigDecimalValue(item.sumCsQty) }
                                .fold(BigDecimal.ZERO, BigDecimal::add)
                        val sumEA =
                            combines.map { item -> AppUtils.getBigDecimalValue(item.sumEaQty) }
                                .fold(BigDecimal.ZERO, BigDecimal::add)

                        val sumTotalStr = AppUtils.getBigDecimalValueStr(sumTotal)
                        val sumCSStr = AppUtils.getBigDecimalValueStr(sumCS)
                        val sumEaStr = AppUtils.getBigDecimalValueStr(sumEA)

                        totalQty.set(sumTotalStr)
                        totalCsQty.set(sumCSStr)
                        csUnit.set(combines[0].csUnit)
                        totalEaQty.set(sumEaStr)
                        eaUnit.set(combines[0].eaUnit)

                        val it = combines.iterator()
                        while (it.hasNext()) {
                            val bean = it.next()
                            if (bean.fromQty.compareTo(bean.toQty) == 0) {
                                it.remove()
                            }
                        }

                        val results = combines.filter { it.detailStatus != "900" }.toMutableList()
                        results.sortBy { it.isSortPick }
                        activity.showData(results)

                        if (results.isEmpty() && canAutoClose) {
                            sendClosePost(true)
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun bulkPacking() {
        if (CheckUtil.isFastDoubleClick()) {
            if (containerCode.get().toString().trim().isNullOrEmpty()) {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "容器号不能为空!")
                return
            }
            val it = Intent(activity, BulkPackingActivity::class.java)
            it.putExtra("containerCode", containerCode.get().toString().trim())
            it.putExtra("taskCode", taskCode.get())
            startActivity.launch(it)
        }
    }

    fun bottomClick() {
        if (CheckUtil.isFastDoubleClick()) {
            if (containerPickList == null) {
                return
            }
            if (!isStart.get()) {
                startTask()
            } else {
                closeTask()
            }
        }
    }

    @SuppressLint("SimpleDateFormat")
    private fun closeTask() {
        if (containerCode.get().toString().trim().isNullOrEmpty()) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "容器号不能为空!")
            return
        }

        tipDialog?.setTitle("询问")
        tipDialog?.setMsg("需要关闭当前拣货容器吗？")
        tipDialog?.setOnTipBackListener(object : TipDialog.OnTipBack {
            override fun onConfirmClick() {
                sendClosePost(false)
            }

            override fun onDismissClick() {
            }
        })
        tipDialog?.show()
    }

    @SuppressLint("SimpleDateFormat")
    private fun sendClosePost(autoClose: Boolean) {
        activity.waitingDialogHelp.showDialogUnCancel()

        val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        val param = mutableMapOf(
            "containerCode" to containerCode.get().toString().trim(),
            "pickUserCode" to Constants.userInfo?.name,
            "pickStartTime" to format.format(Date()),
            "whCode" to Constants.whInfo?.whCode,
            "waveNo" to title.get()
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .pickClose(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    MySoundUtils.getInstance().dingSound()

                    AppUtils.showToast(activity, "关箱成功!")
                    if (autoClose) {
                        canAutoClose = false
                    }
                    queryPickContainerPrintInfo()

                    isEditAble.set(true)
                    activity.containerRequestFocus()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    private fun queryPickContainerPrintInfo() {
        activity.waitingDialogHelp.showDialogUnCancel()

        val param = mutableMapOf(
            "containerCode" to containerCode.get().toString().trim(),
            "pickUserCode" to Constants.userInfo?.name
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .queryPickContainerPrintInfo(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<MutableList<ContainerPickSecondPrintList>>(activity) {
                override fun success(data: MutableList<ContainerPickSecondPrintList>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    data?.let { mData ->
                        var containerCodeList = mutableListOf<String>()
                        mData?.forEach { it ->
                            it.pickContainerCode?.let { it2 ->
                                containerCodeList.add(it2)
                            }
                        }
                        if (Printer.isPrintOk()) {
                            printInfoSaveOrUpdate(
                                containerCodeList.distinct()?.toMutableList()
                            ) //打印记录接口
                        }
                        Printer.printContainerNew(mData)
                    }
                    if (TASK_TYPE_CSPICK == containerPickList?.taskType) {
                        activity.finish()
                    } else {
                        queryPickPackTogether {
                            if (isPickPackTogether == "N") {
                                activity.finish()
                            }
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                    if (TASK_TYPE_CSPICK == containerPickList?.taskType) {
                        activity.finish()
                    } else {
                        queryPickPackTogether {
                            if (isPickPackTogether == "N") {
                                activity.finish()
                            }
                        }
                    }
                }
            })
    }

    //是否开启边拣边包
    private fun queryPickPackTogether(onSuccess: () -> Unit) {
        activity.waitingDialogHelp.showDialogUnCancel()
        RetrofitHelper.getBarcodeAPI()
            .queryInfoByCode(activity.getWhCode(), "PICK_PACK_TOGETHER")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<InfoByCodeBean>(activity) {
                override fun success(data: InfoByCodeBean?) {
                    activity.waitingDialogHelp.hidenDialog()
                    data?.run {
                        Constants.isShowContainerReceive = configValue?.contains("1") == true
                        isPickPackTogether = configValue ?: "N"
                        onSuccess()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    isPickPackTogether = "N"
                    onSuccess()
                }
            })
    }

    fun printInfoSaveOrUpdate(
        containerCodeList: MutableList<String>
    ) {

        val param = mutableMapOf(
            "whCode" to Constants.whInfo?.whCode,
            "printCode" to "pick_container_tag",
            "printType" to "pick",
            "containerCodeList" to containerCodeList
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .printInfoSaveOrUpdate(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                }
            })
    }

    @SuppressLint("SimpleDateFormat")
    private fun startTask() {
        activity.waitingDialogHelp.showDialog()

        val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        val startDate = format.format(Date())

        val param = mutableMapOf(
            "taskHeaderIds" to containerPickList!!.id,
            "taskStartTime" to startDate,
            "whCode" to Constants.whInfo?.whCode,
            "confirmedBy" to Constants.userInfo?.name
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .pickStart(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()

                    activity.containerRequestFocus()
                    isStart.set(true)

                    queryContainerCode() //查询正在进行容器号和任务号
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun onEnterContainer() {
        if (CheckUtil.isFastDoubleClick()) {
            checkContainer()
        }
    }

    /**
     * 容器校验
     */
    private fun checkContainer() {
        activity.waitingDialogHelp.showDialogUnCancel()
        RetrofitHelper.getOutStorageAPI()
            .checkContainer(containerCode.get().toString().trim(), title.get().toString())
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (configValue.get() == 0) {
                        activity.goodsRequestFocus()
                    } else {
                        activity.locationRequestFocus()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.binding.edContainer.post {
                        activity.binding.edContainer.requestFocus()
                        activity.binding.edContainer.selectAll()
                    }
                }
            })
    }

    fun onEnterLocal() {
        if (CheckUtil.isFastDoubleClick()) {
            if (localInfo.get().isNullOrEmpty()) {
                return
            }

            val results = activity.adapter.data.filter { it.fromLoc == localInfo.get() }
            if (results.isNullOrEmpty()) {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "暂无该库位下的数据!")
                localInfo.set("")
            } else {
                activity.showData(results.toMutableList())
                activity.goodsRequestFocus()
            }
        }
    }

    fun onEnterOrderNo() {
        if (CheckUtil.isFastDoubleClick()) {
            if (search69Code.get().isNullOrEmpty()) {
                return
            }
            val result = activity.adapter.data.find {
                it.whBarcode69 == search69Code.get().toString().trim() ||
                        it.custItemCode == search69Code.get().toString().trim() ||
                        it.itemCode == search69Code.get().toString().trim() ||
                        it.whMaxBarcode69 == search69Code.get().toString().trim() ||
                        it.csBarcode69 == search69Code.get().toString().trim() ||
                        it.whIpBarcode69 == search69Code.get().toString().trim()
            }
            if (result != null) {
                if (!AppUtils.isZero(result.repFlag)) {
                    ToastUtils.getInstance().showErrorToastWithSound(
                        activity,
                        "请先补货！"
                    )
                    return
                }
                onItemClick(result)
            } else {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "不存在该货品的拣货任务!")
                search69Code.set("")
            }
        }
    }

    fun toDetail() {
        if (containerCode.get().toString().trim().isNullOrEmpty()) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "容器号不能为空!")
            return
        }
        val it = Intent(activity, ContainerPickFourActivity::class.java)
        it.putExtra("containerCode", containerCode.get().toString().trim())
        activity.startActivity(it)
    }

    fun clearOrderNo() {
        containerCode.set("")
    }

    fun clearLocal() {
        localInfo.set("")
        initWaveNo(containerPickList?.id)
    }

    fun clearSerial() {
        search69Code.set("")
    }

    fun back() {
        activity.finish()
    }

    fun onItemClick(bean: ContainerPickSecondList) {
        if (!isStart.get()) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "请先开始拣货!")
            return
        }

        if (containerCode.get().toString().trim()
                .isNullOrEmpty() && isShowContainerCode.get() != 1
        ) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "容器号不能为空!")
            return
        }

        if (bean.isSortPick) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "已标记短拣任务不能再拣货！")
            return
        }

        //如果配置了容器号自动生成，就不需要调用容器校验接口，如果配置的是用户自己手动输入，才需要调用容器校验接口
        if (isShowContainerCode.get() == 1) {
            toPickThirdActivity(bean)
        } else {
            activity.waitingDialogHelp.showDialogUnCancel()
            RetrofitHelper.getOutStorageAPI()
                .checkContainer(containerCode.get().toString().trim(), title.get().toString())
                .compose(NetworkScheduler.compose())
                .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
                .subscribe(object : RequestCallback<Any>(activity) {
                    override fun success(data: Any?) {
                        activity.waitingDialogHelp.hidenDialog()
                        toPickThirdActivity(bean)
                    }

                    override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                        activity.waitingDialogHelp.hidenDialog()
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, apiErrorModel.message)
                        activity.binding.edContainer.post {
                            activity.binding.edContainer.requestFocus()
                            activity.binding.edContainer.selectAll()
                        }
                    }
                })
        }
    }

    private fun toPickThirdActivity(bean: ContainerPickSecondList) {
        val it = Intent(activity, ContainerPickThirdActivity::class.java)
        it.putExtra("containerCode", containerCode.get().toString().trim())
        it.putExtra("waveNo", title.get())
        it.putExtra("ContainerPickSecondList", bean)
        it.putExtra("taskType", containerPickList?.taskType)
        it.putExtra("autoGetContainerCode", isShowContainerCode.get().toString())

        //if("SORTPICK" == containerPickList?.taskType) { //若任务类型taskType=SORTPICK,则需展示分拣信息，若任务类型taskType不是SORTPICK，则不显示分拣信息
        val taskCombines = mutableListOf<ContainerPickTaskTypeList>()
        bean.taskCombines.forEach {

            if (taskCombines.contains(it)) {
                val result = taskCombines.find { item -> item == it }

                result?.fromQty = result?.fromQty?.add(it.fromQty)

            } else {
                taskCombines.add(it)
            }
        }
        containerPickTaskType.list = taskCombines
        //}

        it.putExtra("containerPickTaskType", containerPickTaskType)

        startActivity.launch(it)

        search69Code.set("")
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun containsLowerCase(str: String): Boolean {
        val regex = Regex("[a-z]")
        return regex.containsMatchIn(str)
    }

    val statusDC = mutableMapOf(
        "N" to "不良品",
        "B" to "包装破损",
        "Z" to "优惠品",
        "X" to "寄存品",
        "W" to "物料",
        "Q" to "待检",
        "F" to "样机",
        "E" to "优惠品",
        "D1" to "残次",
        "C" to "待返厂",
        "A" to "实物赔偿",
        "201" to "售后待鉴定",
        "109" to "上门退货残次",
        "108" to "箱损物料责任",
        "107" to "机损物流责任",
        "106" to "机损商家责任",
        "105" to "箱损待鉴定",
        "104" to "机损待鉴定",
        "101" to "菜鸟残次",
        "Y" to "正品",
        "D10" to "成品A",
        "D11" to "降级销售"
    )
}