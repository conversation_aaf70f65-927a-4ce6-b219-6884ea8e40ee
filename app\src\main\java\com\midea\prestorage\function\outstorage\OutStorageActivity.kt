package com.midea.prestorage.function.outstorage

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.github.zawadz88.materialpopupmenu.popupMenu
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.beans.net.OutStorageQuery
import com.midea.prestorage.beans.net.OutStorageScan
import com.midea.prestoragesaas.databinding.ActivityOutStorageBinding
import com.midea.prestorage.function.outstorage.dialog.InputDialog
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.printer.SNBCConnectUtils
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.MySoundUtils
import com.midea.prestorage.utils.TTSUtils
import com.xuexiang.xqrcode.XQRCode

// 按单收货
class OutStorageActivity : BaseActivity() {

    lateinit var binding: ActivityOutStorageBinding

    //var adapter: CommonAdapter<OutStorageQuery.ShipmentDetailSumQtyDtoList> = CommonAdapter(R.layout.item_order_goods_outstorage)
    var adapter = OutStorageListAdapter()
    private lateinit var inputDialog: InputDialog

    var toolMenu = popupMenu {
        section {
            title = "更多操作:"
            item {
                label = "申请不扫码"
                //icon = R.drawable.abc_ic_menu_copy_mtrl_am_alpha //optional
                callback = { //optional
                    binding.vm!!.requestUnscan()
                }
            }
            item {
                label = "切换打印机"
                //icon = R.drawable.abc_ic_menu_copy_mtrl_am_alpha //optional
                callback = { //optional
                    binding.vm!!.changePrint()
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_out_storage)
        binding.vm = OutStorageVM(this)
        inputDialog = InputDialog(this)
        inputDialog.inputBack = object : InputDialog.InputBack {
            override fun inputOk(data: OutStorageScan?) {
                if (!TextUtils.isEmpty(binding.vm!!.currentGoods)) {
                    if (binding.vm!!.currentGoods != data?.custItemCode) {
                        AppUtils.showToast(application, "编码切换!")
                        MySoundUtils.getInstance().codeChangeSound()
                    }
                    binding.vm!!.currentGoods = data?.custItemCode
                } else {
                    binding.vm!!.currentGoods = data?.custItemCode
                }

                inputDialog.dismiss()
                data.let {
                    binding.vm!!.startPrinter(data!!)
                }
                binding.vm!!.queryShipmentDetail(false)
                binding.vm!!.goodsNo.set("")
            }

            override fun inputFail() {
                binding.vm!!.queryShipmentDetail(false)
            }
        }

        initRecycle()
        binding.vm!!.init()
        binding.edOrderNo.requestFocus()

        TTSUtils.initTts(applicationContext)
    }

    fun orderNoRequest() {
        binding.edGoodsNo.requestFocus()
    }

    override fun onDestroy() {
        super.onDestroy()
        Printer.closeBluetooth()

        TTSUtils.stop()
        TTSUtils.shutdown()
    }

    override fun onResume() {
        super.onResume()
        binding.vm!!.queryShipmentDetail(false)
    }

    private fun initRecycle() {
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter
    }

    fun showDeleteTipDialog(data: OutStorageScan, bean: OutStorageQuery?) {
        inputDialog.deleteInfo = data
        inputDialog.bean = bean
        inputDialog.show()
    }

    fun reviewEnable() {
        binding.tvReview.isEnabled = true
        binding.tvReview.background = ContextCompat.getDrawable(this, R.drawable.bg_bt_green)
    }

    fun reviewUnable() {
        binding.tvReview.isEnabled = false
        binding.tvReview.background = ContextCompat.getDrawable(this, R.drawable.bg_bt_gray)
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun showData(beans: MutableList<OutStorageQuery.ShipmentDetailSumQtyDtoList>) {
        adapter.setNewInstance(beans)
        adapter.notifyDataSetChanged()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    class OutStorageListAdapter :
        ListCheckBoxAdapter<OutStorageQuery.ShipmentDetailSumQtyDtoList>(R.layout.item_order_goods_outstorage) {
        override fun convert(helper: BaseViewHolder, item: BaseItemForPopup) {
            super.convert(helper, item as OutStorageQuery.ShipmentDetailSumQtyDtoList)

            // 已完成的 标记为绿色背景色  未完成的白色背景色
            if (item.planQty == item.serialQty) {
                helper.setBackgroundColor(R.id.llScanItem, Color.parseColor("#8de0cc"))
            } else {
                helper.setBackgroundColor(R.id.llScanItem, Color.parseColor("#ffffff"))
            }

            // 大物流不扫码标记unScanMark   (注意和 前置仓不扫码标记unscanMark 区分 一个大写s 一个小写s)
            if (item.cdcmUnScanMark == "Y") {
                // 大物流不扫码标识
                helper.setText(R.id.tvUnScanMark, "不扫码")
            } else {
                helper.setText(R.id.tvUnScanMark, "")
            }
        }
    }
}