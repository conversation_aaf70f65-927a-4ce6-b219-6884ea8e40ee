package com.midea.prestorage.beans.setting;

import com.midea.prestorage.beans.base.BaseItemShowInfo;

import org.xutils.db.annotation.Column;
import org.xutils.db.annotation.Table;



@Table(name = "WhInfoListDb")
public class WhInfoListDb extends BaseItemShowInfo {

    @Column(name = "id", isId = true, autoGen = true, property = "NOT NULL")
    private int id;

    @Column(name = "cdwhName")
    private String cdwhName;

    @Column(name = "whCode")
    private String whCode;

    public WhInfoListDb() {
    }

    public WhInfoListDb(String cdwhName, String whCode) {
        this.cdwhName = cdwhName;
        this.whCode = whCode;
    }

    public String getCdwhName() {
        return cdwhName;
    }

    public void setCdwhName(String cdwhName) {
        this.cdwhName = cdwhName;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }
}
