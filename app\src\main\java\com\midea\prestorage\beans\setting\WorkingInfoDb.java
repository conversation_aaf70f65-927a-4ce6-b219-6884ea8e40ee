package com.midea.prestorage.beans.setting;

import org.xutils.db.annotation.Column;
import org.xutils.db.annotation.Table;

/**
 * Description:    作业组
 * Author:         wangqin
 * CreateDate:     2021/9/23$
 */
@Table(name = "WorkingInfoDb")
public class WorkingInfoDb {

    @Column(name = "id", isId = true, autoGen = true, property = "NOT NULL")
    private int id;

    @Column(name = "userId")
    private Integer userId;

    @Column(name = "workGroupCode")
    private String workGroupCode;

    @Column(name = "workGroupName")
    private String workGroupName;

    @Column(name = "whCode")
    private String whCode;

    public WorkingInfoDb() {
    }

    public WorkingInfoDb(Integer userId, String workGroupName, String workGroupCode) {
        this.userId = userId;
        this.workGroupName = workGroupName;
        this.workGroupCode = workGroupCode;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getWorkGroupCode() {
        return workGroupCode;
    }

    public void setWorkGroupCode(String workGroupCode) {
        this.workGroupCode = workGroupCode;
    }

    public String getWorkGroupName() {
        return workGroupName;
    }

    public void setWorkGroupName(String workGroupName) {
        this.workGroupName = workGroupName;
    }
}
