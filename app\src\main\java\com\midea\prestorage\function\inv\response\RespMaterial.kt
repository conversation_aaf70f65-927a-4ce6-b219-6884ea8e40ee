package com.midea.prestorage.function.inv.response

import com.midea.prestorage.base.annotation.ShowAnnotation
import java.io.Serializable
import java.math.BigDecimal

data class RespMaterial(
    @ShowAnnotation
    var index: Int = 0,
    @ShowAnnotation
    var cdcmCustMaterialNo: String? = null,
    @ShowAnnotation
    var cdcmCustomerName: String? = null,
    @ShowAnnotation
    var cdcmNameCn: String? = null,
    @ShowAnnotation
    var cdpaFormat: String? = null,
    var cdcmLength: BigDecimal? = null,
    var cdcmWidth: BigDecimal? = null,
    var cdcmHeight: BigDecimal? = null,
    var cdcmWeight: BigDecimal? = null,
    var cdcmCube: BigDecimal? = null,
    var cdcmPeriodOfValidity: BigDecimal? = null,
    var cdcmValidityUnit: String? = null,
    var cdcmIsValidity: String? = null,
    var layerQty: BigDecimal? = null,
    var stackLevel: BigDecimal? = null,
    @ShowAnnotation
    var cdcmBarcode69: String? = null,
    @ShowAnnotation
    var ipBarcode69: String? = null,
    @ShowAnnotation
    var csBarcode69: String? = null,
    @ShowAnnotation
    var otBarcode69: String? = null,
    @ShowAnnotation
    var ebmgNameCn: String? = null,
    @ShowAnnotation
    var cdcmBrand: String? = null,
    var cdcmIsDecimal: Int? = null,
    var isEnable: Int? = null,
    var cdcmMaterialNo: String? = null,
    var cdpaType: String? = null,
    var csUnitQty: BigDecimal? = null,
    var csUnitDesc: String? = null,
    var ipUnitQty: BigDecimal? = null,
    var ipUnitDesc: String? = null,
    var eaUnitQty: BigDecimal? = null,
    var eaUnitDesc: String? = null
) : Serializable
