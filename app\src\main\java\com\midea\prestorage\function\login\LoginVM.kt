package com.midea.prestorage.function.login

import android.annotation.SuppressLint
import android.content.Intent
import android.text.TextUtils
import android.util.Log
import android.widget.Toast
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.App
import com.midea.prestorage.beans.net.LoginLog
import com.midea.prestorage.beans.net.LoginResp
import com.midea.prestorage.beans.net.TenantResp
import com.midea.prestorage.beans.setting.ImplWarehouse
import com.midea.prestorage.beans.setting.LoginInfo
import com.midea.prestorage.function.mainyg.MainYgActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.RetrofitHelperTenant
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.*
import com.midea.prestoragesaas.BuildConfig
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import okhttp3.MediaType
import okhttp3.RequestBody
import org.xutils.db.sqlite.WhereBuilder
import java.util.*


@SuppressLint("CheckResult")
class LoginVM(val activity: LoginActivity) {

    val checkAgreement = ObservableField(false)

    val db = DbUtils.db

    var whDatas = mutableListOf<ImplWarehouse>()
    var curUserCode: String? = null
    var curUserName: String? = null

    init {
        initData()
    }

    private fun initData() {
        Observable.create<LoginInfo> {
            Constants.userInfo =
                db.selector(LoginInfo::class.java).orderBy("isFirst", true).findFirst()
            if (Constants.userInfo == null) {
                Constants.userInfo = LoginInfo()
            }
            Constants.whInfo =
                db.selector(ImplWarehouse::class.java).findFirst()

            it.onNext(Constants.userInfo!!)
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<LoginInfo> {
                override fun onComplete() {
                    if(!TextUtils.isEmpty((SPUtils["accessToken", ""] as String))) {
                        Constants.accessToken = (SPUtils["accessToken", ""] as String)
                        Constants.tenantCode = (SPUtils["tenantCode", ""] as String)
                        Constants.tenantName = (SPUtils["tenantName", ""] as String)
                        App.tenantCode = (SPUtils["tenantCode", ""] as String)

                        LoginActivity.HOST = (SPUtils["host", ""] as String)
                        LoginActivity.SERVER_NAME = (SPUtils["serverName", ""] as String)

                        when (LoginActivity.SERVER_NAME) {
                            "SIT" -> {
                                activity.binding.spinnerServer.selectedIndex = 0
                            }
                            "UAT" -> {
                                activity.binding.spinnerServer.selectedIndex = 1
                            }
                            "VER" -> {
                                activity.binding.spinnerServer.selectedIndex = 2
                            }
                            "LC" -> {
                                activity.binding.spinnerServer.selectedIndex = 3
                            }
                            else -> {
                                activity.binding.spinnerServer.selectedIndex = 0
                            }
                        }

                        activity.startActivity(Intent(activity, MainYgActivity::class.java))
                    }
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: LoginInfo) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    fun onLoginClick() {
        var userName = ""
        var passWord = ""
        if (activity.binding.viewPager.currentItem == 0) {
            activity.getAdapter().passwordLoginFragment.apply {
                userName = getVm()?.username?.get().toString().trim().lowercase(Locale.ROOT)
                passWord = getVm()?.password?.get().toString().trim()
            }
        }else {
            activity.getAdapter().smsLoginFragment.apply {
                userName = getVm()?.username?.get().toString().trim().lowercase(Locale.ROOT)
                passWord = getVm()?.password?.get().toString().trim()
            }
        }
        if (userName.isNullOrEmpty()) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "手机号不能为空!")
            return
        }

        if (!userName.startsWith("1") || userName.length != 11) {
            ToastUtils.getInstance()
                .showErrorToastWithSound(activity, "手机号输入不合法")
            return
        }

        if (passWord.isNullOrEmpty()) {
            if (activity.binding.viewPager.currentItem == 0) {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "密码不能为空!")
            } else {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "验证码不能为空!")
            }
            return
        }

        if (checkAgreement.get() != true) {
            activity.showAgreementDialog()
            return
        }

        if (activity.binding.viewPager.currentItem == 0) {
            pwdLogin(userName, passWord)
        } else {
            smsLogin(userName, passWord)
        }

    }

    fun pwdLogin(userName: String, passWord: String) {
        val param = mutableMapOf("mobile" to userName, "password" to AESCrypt.aesEncrypt(passWord)
            .substring(0,24))
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        //弹出等待框
        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getAppAPI()
            .pwdLogin(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<LoginResp>>(activity) {
                override fun success(data: MutableList<LoginResp>?) {
                    //activity.waitingDialogHelp.hidenDialog()
                    curUserCode = data?.get(0)?.userCode
                    curUserName = data?.get(0)?.userName
                    //saveUserInfo(data?.get(0)?.userCode, data?.get(0)?.userName)
                    Constants.accessToken = data?.get(0)?.accessToken
                    getTenant()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun smsLogin(userName: String, passWord: String) {
        //弹出等待框
        activity.waitingDialogHelp.showDialog()
        RetrofitHelperTenant.getAppAPI()
            .ssoLogin(userName, passWord)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<LoginResp>>(activity) {
                override fun success(data: MutableList<LoginResp>?) {
                    //activity.waitingDialogHelp.hidenDialog()
                    curUserCode = data?.get(0)?.userCode
                    curUserName = data?.get(0)?.userName
                    //saveUserInfo(data?.get(0)?.userCode, data?.get(0)?.userName)
                    Constants.accessToken = data?.get(0)?.accessToken
                    getTenant()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun getTenant() {
        RetrofitHelper.getBasicDataAPI()
            .getTenant(BuildConfig.APPLICATION_CODE)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<TenantResp>(activity) {
                override fun success(data: TenantResp?) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (data?.tenants.isNullOrEmpty()) {
                        val emptyDatas = mutableListOf<TenantResp.TenantsDTO>()
                        activity.setTenantInfo(emptyDatas)
                        ToastUtilsCare.toastBig(activity, "暂无租户", Toast.LENGTH_SHORT)
                    }else {
                        if (data?.tenants?.size == 1) {//如果只有1个，则默认该租户
                            activity.resetWarehouse()
                            switchTenant(data.tenants[0].tenantCode)
                            Constants.tenantCode = data.tenants[0].tenantCode
                            Constants.tenantName = data.tenants[0].tenantName
                            App.tenantCode = data.tenants[0].tenantCode
                            activity.showWhDialog(data.tenants[0].tenantCode, data.tenants[0].tenantName)
                        }else {
                            if (data?.currentTenant.isNull()) {
                                activity.showWhDialog("", "")
                            } else {
                                activity.resetWarehouse()
                                switchTenant(data?.currentTenant?.tenantCode ?: "")
                                Constants.tenantCode = data?.currentTenant?.tenantCode
                                Constants.tenantName = data?.currentTenant?.tenantName
                                App.tenantCode = data?.currentTenant?.tenantCode
                                activity.showWhDialog(data?.currentTenant?.tenantCode ?: "", data?.currentTenant?.tenantName ?: "")
                            }
                        }
                        data?.tenants?.forEach {
                            it.showInfo = it.tenantName
                        }
                        activity.setTenantInfo(data?.tenants)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    val emptyDatas = mutableListOf<TenantResp.TenantsDTO>()
                    activity.setTenantInfo(emptyDatas)
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    //切换租户
    fun switchTenant(tenantCode: String) {
        val map = mutableMapOf<String, Any?>()
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )
        //activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getBasicDataAPI()
            .switchTenant(requestBody, tenantCode, BuildConfig.APPLICATION_CODE)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<LoginResp>>(activity) {
                override fun success(data: MutableList<LoginResp>?) {
                    Constants.accessToken = data?.get(0)?.accessToken
                    //选择完租户之后，才允许选择仓库，只能选择已选租户下该用户数据权限范围内的仓库
                    getCodeAndName()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    //获取仓库
    fun getCodeAndName() {
        val map = mutableMapOf<String, Any?>()
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )
        RetrofitHelper.getBasicDataAPI()
            .getCodeAndName(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<ImplWarehouse>>(activity) {
                override fun success(data: MutableList<ImplWarehouse>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    var result = data?.filter { it.cdwhIsStop == 0 }?.toMutableList()
                    if (result.isNullOrEmpty()) {
                        val emptyDatas = mutableListOf<ImplWarehouse>()
                        activity.setWhInfo(emptyDatas)
                        ToastUtilsCare.toastBig(activity, "暂无仓库", Toast.LENGTH_SHORT)
                    }else {
                        if (result.size == 1) {//如果只有1个，则默认该仓库
                            activity.setWareHouse(result[0].whCode, result[0].cdwhName)
                            Constants.whInfo = result[0]
                            Constants.userInfo?.whName = result[0].cdwhName
                            Constants.userInfo?.whCode = result[0].whCode
                            Constants.userInfo?.whType = 2
                        }
                        result.forEach {
                            it.showInfo = it.cdwhName
                            it.whSystem = "2"
                        }
                        activity.setWhInfo(result)
                        whDatas = result
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    val emptyDatas = mutableListOf<ImplWarehouse>()
                    activity.setWhInfo(emptyDatas)
                    ToastUtilsCare.toastBig(activity, apiErrorModel.message, Toast.LENGTH_SHORT)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    // 上报登录成功记录
    fun uploadLoginLog(title: String) {
        val loginFrom = LoginLog()

        loginFrom.title = title

        // 参数
        loginFrom.apkVersion = AppUtils.getVersionCode(activity).toString()
        loginFrom.env = LoginActivity.SERVER_NAME

        RetrofitHelper.getBasicDataAPI()
            .recordLoginLog(loginFrom.toString())
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    Log.i("wms", "记录登录记录----成功")
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    Log.e("wms", "上报登录记录失败")
                    Log.e("wms", statusCode.toString() + ":" + apiErrorModel.message)
                }
            })
    }

    fun saveUserInfo(userCode: String?, userName: String?, whName: String?, whCode: String?) {
        Observable.create<String> {
            try {
                val findFirst =
                    db.selector(LoginInfo::class.java).where("name", "==", userCode)
                        .and(WhereBuilder.b("environment", "==", LoginActivity.HOST))
                        .findFirst()
                //如果切换用户，删掉数据库缓存的仓库，每个用户仓库都不同
                if (findFirst == null || !findFirst.isFirst) {
                    db.delete(ImplWarehouse::class.java)
                }
                if (findFirst == null) {
                    Constants.userInfo = LoginInfo()
                    Constants.userInfo?.name = userCode
                } else {
                    Constants.userInfo = findFirst
                }

                val findAll = db.findAll(LoginInfo::class.java)
                if (!findAll.isNullOrEmpty()) {
                    findAll.forEach { item -> item.isFirst = false }
                    db.saveOrUpdate(findAll)
                }

                if (activity.binding.viewPager.currentItem == 0) {
                    activity.getAdapter().passwordLoginFragment.apply {
                        if (getVm()?.isRemember?.get()!!) {
                            Constants.userInfo?.pwd = getVm()?.password?.get()
                        } else {
                            Constants.userInfo?.pwd = ""
                        }
                        Constants.userInfo?.isRemember = getVm()?.isRemember?.get()!!
                        Constants.userInfo?.account = getVm()?.username?.get()
                    }
                }else {
                    activity.getAdapter().smsLoginFragment.apply {
                        Constants.userInfo?.account = getVm()?.username?.get()
                    }
                }
                Constants.userInfo?.isFirst = true
                Constants.userInfo?.environment = LoginActivity.HOST
                Constants.userInfo?.userName = userName
                Constants.userInfo?.whName = whName
                Constants.userInfo?.whCode = whCode
                Constants.userInfo?.whType = 2
                db.saveOrUpdate(Constants.userInfo)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                    var title = "ewms-rf"
                    try {
                        uploadLoginLog(title)
                    } catch (e: java.lang.Exception) {
                        Log.e("wms", "上报登录记录失败")
                        e.printStackTrace()
                    }
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    fun saveWhInfo(list: MutableList<ImplWarehouse>?) {
        Observable.create<String> {
            try {
                db.delete(ImplWarehouse::class.java)
                db.saveOrUpdate(list)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    fun saveWhInfo() {
        Observable.create<String> {
            try {
                if (Constants.whInfo != null) {
                    db.saveOrUpdate(Constants.whInfo)
                    db.saveOrUpdate(Constants.userInfo)
                }
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {

                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }
}