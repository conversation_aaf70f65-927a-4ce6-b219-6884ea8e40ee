package com.midea.prestorage.function.outstorage

import CheckUtil
import android.content.Intent
import android.text.TextUtils
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.OutStorageQuery
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody

@Suppress("CAST_NEVER_SUCCEEDS")
class OutStorageOrderVM(val activity: OutStorageOrderActivity) {

    val orderNo = ObservableField("")

    fun init() {
        val orderNoStr = activity.intent.getStringExtra("orderNo")
        if (!TextUtils.isEmpty(orderNoStr)) {
            orderNo.set(orderNoStr)
        }
    }

    val orderEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                queryShipmentDetail(true)
            }
        }
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        orderNo.set(result)
        orderEnterKeyPress.onEnterKey()
    }

    fun queryShipmentDetail(isEnter: Boolean) {
        if (TextUtils.isEmpty(orderNo.get())) {
            if (isEnter) {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "请扫描货品条码!")
            }
            return
        }
        activity.waitingDialogHelp.showDialog()
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "queryCode" to orderNo.get()?.trim()
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .queryShipmentDetail(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<OutStorageQuery>(activity) {
                override fun success(data: OutStorageQuery?) {
                    activity.waitingDialogHelp.hidenDialog()
                    data.let {
                        val it = Intent(activity, OutStorageActivity::class.java)
                        it.putExtra("OutStorageQuery", it)
                        it.putExtra("orderNo", orderNo.get())
                        activity.startActivity(it)
                        activity.finish()
                    }
                    orderNo.set("")
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    orderNo.set("")
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun back() {
        activity.finish()
    }
}