package com.midea.prestorage.function.addgoods

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestoragesaas.databinding.ActivityPutOutGoodsBinding
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.FilterDigitTextWatcher
import com.xuexiang.xqrcode.XQRCode

@Suppress("UNCHECKED_CAST")
class PutOutActivity : BaseActivity() {

    companion object {

        fun newIntent(
            context: Context,
            toTitle: String,
            queryCode: String,
            zoneCodes: String?
        ): Intent {
            val intent = Intent(context, PutOutActivity::class.java)
            intent.putExtra("toTitle", toTitle)
            intent.putExtra("queryCode", queryCode)
            intent.putExtra("zoneCodes", zoneCodes)
            return intent
        }
    }

    private lateinit var binding: ActivityPutOutGoodsUnionBinding
    lateinit var inStorageVM: PutOutVM

    private lateinit var dayDialog: FilterDialog
//    private var dayDialog: SelectEntityDialog? = null

    var textWatcher: FilterDigitTextWatcher? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityPutOutGoodsUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_put_out_goods_care
                )
            )
        } else {
            ActivityPutOutGoodsUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_put_out_goods
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        inStorageVM = PutOutVM(this)
        binding.vm = inStorageVM

        initWatcher()
        initListen()

        AppUtils.requestFocus(binding.edGoods)
    }

    private fun initListen() {
        inStorageVM.isPoint.observe(this, {
            if (it != 9999) {
                setWatcher(it)
            }
        })
    }

    private fun initWatcher() {
        if (textWatcher == null) {
            textWatcher =
                FilterDigitTextWatcher(binding.edQty, 0, true) {
                    if ("该商品只能输入正整数" == it) {
                        binding.edQty.setText(
                            binding.edQty.text.toString().replace(Regex("[^0-9]"), "")
                        )
                        if (!binding.edQty.text.isNullOrEmpty()) {
                            binding.edQty.setSelection(binding.edQty.text!!.length)
                        }
                    }
                    AppUtils.showToast(this, it)
                }
        }
    }

    private fun setWatcher(isPoint: Int) {
        binding.edQty.removeTextChangedListener(textWatcher)
        if (isPoint != 0) {
            textWatcher?.limitDecimalPlaces = 4
        }else {
            textWatcher?.limitDecimalPlaces = 0
        }
        binding.edQty.addTextChangedListener(textWatcher)
    }

    override fun onResume() {
        super.onResume()
        readStorageInfo()
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    private fun readStorageInfo() {
        inStorageVM.isDefaultNum = Constants.userInfo?.scanNum ?: false
        inStorageVM.isDefaultNext = Constants.userInfo?.scanJump ?: false

        inStorageVM.setMode()
    }

    fun showNoDataInfo() {
        binding.llDataInfo.visibility = View.GONE
        binding.ivNoOrder.visibility = View.VISIBLE
    }

    fun initLocation(data: MutableList<DCBean>) {
        if (data.isNotEmpty()) {
            val firstBean = data[0]
            inStorageVM.location.set(firstBean.value.toString())
        }
        dayDialog = FilterDialog(this)
        dayDialog.setTitle("请选择库位")
        dayDialog.dismissEdit()
        dayDialog.addAllData(data as MutableList<BaseItemShowInfo>)
        dayDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            val dcBean = it as DCBean

            //刷新数据
            inStorageVM.setIndexToLocal(dcBean.value.toString())
            dayDialog.dismiss()
        })
    }

    fun resetLocation(data: MutableList<DCBean>) {
        dayDialog.addAllData(data as MutableList<BaseItemShowInfo>)
    }

    fun showDialog() {
        if (this::dayDialog.isInitialized) {
            dayDialog.show()
        }
    }

    fun goodsRequest() {
        binding.edGoods.requestFocus()
    }

    fun qtyRequest() {
        binding.edQty.requestFocus()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            inStorageVM.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }
}