package com.midea.prestorage.function.addgoods

import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import com.midea.prestorage.function.sendcheck.ActivitySerialCheckReprintUnionBinding
import com.midea.prestorage.function.sendcheck.SerialCheckReprintVM
import com.midea.prestoragesaas.databinding.ActivityAddGoodsBinding
import com.midea.prestoragesaas.databinding.ActivityAddGoodsCareBinding
import com.midea.prestoragesaas.databinding.ActivitySerialCheckReprintBinding
import com.midea.prestoragesaas.databinding.ActivitySerialCheckReprintCareBinding

sealed class ActivityAddGoodsUnionBinding{
    abstract var vm: AddGoodsVM?
    abstract val llTitleBar: RelativeLayout
    abstract val llWaitSend: LinearLayout
    abstract val llWaitSign: LinearLayout
    abstract val llSign: LinearLayout
    abstract val vp: ViewPager
    abstract val tvNotification: TextView

    class V2(val binding: ActivityAddGoodsCareBinding) : ActivityAddGoodsUnionBinding() {
        override var vm: AddGoodsVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val llWaitSend = binding.llWaitSend
        override val llWaitSign = binding.llWaitSign
        override val llSign = binding.llSign
        override val vp = binding.vp
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityAddGoodsBinding) : ActivityAddGoodsUnionBinding() {
        override var vm: AddGoodsVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val llWaitSend = binding.llWaitSend
        override val llWaitSign = binding.llWaitSign
        override val llSign = binding.llSign
        override val vp = binding.vp
        override val tvNotification = binding.tvNotification
    }
}
