package com.midea.prestorage.function.instorage.response;

import java.util.List;

public class RespInOrders {

    String planQty;
    String scanQty;
    String containerCode;

    List<InOrderDetail> inReceiptDetailInfoVOS;


    public String getPlanQty() {
        return planQty;
    }

    public void setPlanQty(String planQty) {
        this.planQty = planQty;
    }

    public String getScanQty() {
        return scanQty;
    }

    public void setScanQty(String scanQty) {
        this.scanQty = scanQty;
    }

    public List<InOrderDetail> getInReceiptDetailInfoVOS() {
        return inReceiptDetailInfoVOS;
    }

    public void setInReceiptDetailInfoVOS(List<InOrderDetail> inReceiptDetailInfoVOS) {
        this.inReceiptDetailInfoVOS = inReceiptDetailInfoVOS;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public String getContainerCode() {
        return containerCode;
    }
}
