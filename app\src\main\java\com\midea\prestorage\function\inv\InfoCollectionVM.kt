package com.midea.prestorage.function.inv

import android.app.Application
import android.widget.Toast
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.function.inv.response.RespFuInvLocationInventory
import com.midea.prestorage.function.inv.response.RespMaterial
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtilsCare
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody
import org.json.JSONObject
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.*

class InfoCollectionVM(application: Application) : BaseViewModel(application) {

    var cdcmMaterialNo = ObservableField("")
    var custItemCode = ObservableField("")
    var cdcmCustomerName = ObservableField("")
    var itemName = ObservableField("")
    var itemLength = ObservableField("")
    var itemWidth = ObservableField("")
    var itemHeight = ObservableField("")
    var itemVolume = ObservableField("")
    var itemRoughWeight = ObservableField("")
    var item69Code = ObservableField("")
    var itemIp69Code = ObservableField("")
    var itemCs69Code = ObservableField("")
    var itemMax69Code = ObservableField("")
    var layerQty = ObservableField("")
    var stackLevel = ObservableField("")
    var itemGoodsExpiryDate = ObservableField("")
    var ipQty = ObservableField("")
    var eaQty = ObservableField("")
    var csUnitDesc = ObservableField("")
    var ipUnitDesc = ObservableField("")
    var eaUnitDesc = ObservableField("")
    var cdpaFormat = ObservableField("")

    var validityControl = ObservableField("是")
    var mSubmitNotification = MutableLiveData(false)
    val isEditAble = ObservableBoolean(true)
    var respMaterial: RespMaterial? = null
    var goodsExpiryDate = 1
    var validityUnit = ObservableField("天")

    var inOrderType = MutableLiveData<MutableList<DCBean>>()
    var status: MutableMap<String, String>? = null

    var isOpenIp: Boolean = false

    val isPoint = MutableLiveData(9999)

    override fun init() {

    }

    companion object {
        const val YES_CN = "是"
        const val NO_CN = "否"
        const val DATE_YEAR = "年"
        const val DATE_MONTH = "月"
        const val DATE_WEEK = "周"
        const val DATE_DAY = "天"
    }

    fun calculateVolume() {
        val length =
            AppUtils.getBigDecimalValue(itemLength.get().toString()).divide(BigDecimal("1000"))
        val width =
            AppUtils.getBigDecimalValue(itemWidth.get().toString()).divide(BigDecimal("1000"))
        val height =
            AppUtils.getBigDecimalValue(itemHeight.get().toString()).divide(BigDecimal("1000"))

        if (!AppUtils.isZero(length) && !AppUtils.isZero(width) && !AppUtils.isZero(height)) {
            val volume = length.multiply(width).multiply(height)
            itemVolume.set(
                AppUtils.getBigDecimalValueStr(
                    volume.setScale(
                        8,
                        BigDecimal.ROUND_HALF_UP
                    )
                )
            )
            isEditAble.set(false) //填写了长宽高自动算出体积，不允许修改
        } else {
            isEditAble.set(true)
        }
    }

    fun toSave() {
        if (CheckUtil.isFastDoubleClick()) {
            mSubmitNotification.value = true
        }
    }

    fun initReceiptOrderType() {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {
            val it = withContext(Dispatchers.IO) {
                val direction =
                    async {
                        RetrofitHelper.getDcDirectionAPI()
                            .searchByDictCodeListToMap("SAASDC_BASE_UNIT")
                    }
                direction.await()
            }

            val sb = StringBuffer()
            val scanner = Scanner(it.byteStream())
            while (scanner.hasNext()) {
                sb.append(scanner.next())
            }
            val result = sb.toString()
            val jsonObject = JSONObject(result)
            if (jsonObject.getString("code") == "0") {
                initDictData(jsonObject.getJSONObject("data"))
            } else {
                showNotification("数据错误", false)
            }
        }
    }

    private fun initDictData(jsonObject: JSONObject) {
        if (jsonObject.has("SAASDC_BASE_UNIT")) {
            status = getMapFromJson(jsonObject.getJSONObject("SAASDC_BASE_UNIT"))
            if (status == null || status!!.isEmpty()) {
                showNotification("包装规格单位暂无数据", false)
            } else {
                val datas = mutableListOf<DCBean>()
                for ((key, value) in status!!) {
                    datas.add(DCBean(key, value, DCBean.SHOW_VALUE))
                }
                inOrderType.value = datas
            }
        }
    }

    fun getMapFromJson(json: JSONObject): MutableMap<String, String> {
        val map = mutableMapOf<String, String>()
        val it = json.keys().iterator()
        while (it.hasNext()) {
            val key = it.next()
            map[key] = json.getString(key)
        }
        return map
    }

    fun materialUpdate() {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            if (itemName.get().toString().trim().isBlank()) {
                showNotification("货品名称不能为空", false)
                return@launch
            }
            if (itemVolume.get().toString().trim().isBlank()) {
                showNotification("体积不能为空", false)
                return@launch
            }
            if (AppUtils.isZero(AppUtils.getBigDecimalValue(itemVolume.get().toString()))) {
                showNotification("体积不能为0", false)
                return@launch
            }
            if (!itemLength.get().toString().isNullOrEmpty() && AppUtils.isZero(
                    AppUtils.getBigDecimalValue(
                        itemLength.get().toString()
                    )
                )
            ) {
                showNotification("长度不能为0", false)
                return@launch
            }
            if (!itemWidth.get().toString().isNullOrEmpty() && AppUtils.isZero(
                    AppUtils.getBigDecimalValue(
                        itemWidth.get().toString()
                    )
                )
            ) {
                showNotification("宽度不能为0", false)
                return@launch
            }
            if (!itemHeight.get().toString().isNullOrEmpty() && AppUtils.isZero(
                    AppUtils.getBigDecimalValue(
                        itemHeight.get().toString()
                    )
                )
            ) {
                showNotification("高度不能为0", false)
                return@launch
            }
            if (itemRoughWeight.get().toString().trim().isBlank()) {
                showNotification("重量不能为空", false)
                return@launch
            }
            if (AppUtils.isZero(AppUtils.getBigDecimalValue(itemRoughWeight.get().toString()))) {
                showNotification("重量不能为0", false)
                return@launch
            }
            if (checkValidity() && itemGoodsExpiryDate.get().toString().trim()
                    .isBlank()
            ) {
                showNotification("效期不能为空", false)
                return@launch
            }

            val param = mutableMapOf(
                "whCode" to Constants.whInfo?.whCode.toString(),
                "cdcmMaterialNo" to cdcmMaterialNo.get().toString(),
                "cdcmNameCn" to itemName.get().toString().trim(),
                "cdcmLength" to AppUtils.getBigDecimalValue(itemLength.get().toString()),
                "cdcmWidth" to AppUtils.getBigDecimalValue(itemWidth.get().toString()),
                "cdcmHeight" to AppUtils.getBigDecimalValue(itemHeight.get().toString()),
                "cdcmCube" to itemVolume.get().toString().trim(),
                "cdcmWeight" to itemRoughWeight.get().toString().trim(),
                "cdcmBarcode69" to item69Code.get().toString().trim(),
                "ipBarcode69" to itemIp69Code.get().toString().trim(),
                "csBarcode69" to itemCs69Code.get().toString().trim(),
                "otBarcode69" to itemMax69Code.get().toString().trim(),
                "stackLevel" to stackLevel.get().toString().trim(),
                "layerQty" to layerQty.get().toString().trim(),
                "cdcmIsValidity" to if (checkValidity()) "Y" else "N",
                "cdpaType" to (respMaterial?.cdpaType ?: ""),
                "csUnitQty" to 1,
                "csUnitDesc" to csUnitDesc.get().toString()
            )
            if (!ipQty.get().toString().isNullOrEmpty() && !ipUnitDesc.get()
                    .toString().isNullOrEmpty() && !eaQty.get().toString()
                    .isNullOrEmpty() && !eaUnitDesc.get()
                    .toString().isNullOrEmpty()
            ) {
                if (isOpenIp) {
                    param["ipUnitQty"] = AppUtils.getBigDecimalValue(ipQty.get())
                    param["ipUnitDesc"] = ipUnitDesc.get().toString()
                }
                param["eaUnitQty"] = AppUtils.getBigDecimalValue(eaQty.get())
                param["eaUnitDesc"] = eaUnitDesc.get().toString()
            } else if (!ipQty.get().toString().isNullOrEmpty() && !ipUnitDesc.get()
                    .toString().isNullOrEmpty()
            ) {
                if (isOpenIp) {
                    param["eaUnitQty"] = AppUtils.getBigDecimalValue(ipQty.get())
                    param["eaUnitDesc"] = ipUnitDesc.get().toString()
                }
            } else if (!eaQty.get().toString()
                    .isNullOrEmpty() && !eaUnitDesc.get()
                    .toString().isNullOrEmpty()
            ) {
                param["eaUnitQty"] = AppUtils.getBigDecimalValue(eaQty.get())
                param["eaUnitDesc"] = eaUnitDesc.get().toString()
            }


            if (checkValidity()) {
                param["cdcmPeriodOfValidity"] = itemGoodsExpiryDate.get().toString().trim()
                param["cdcmValidityUnit"] = getValidityUnitDateStr()
            }

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getInventoryAPI().materialUpdate(requestBody)
            }

            if (result.code == 0L) {
                ToastUtilsCare.toastBig(getApplication(), "提交成功", Toast.LENGTH_SHORT)
                back()
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    private fun checkValidity(): Boolean {
        return YES_CN == validityControl.get()
    }

    private fun getValidityUnitDateStr(): String {
        return if (DATE_YEAR == validityUnit.get()) "YEAR" else if (DATE_MONTH == validityUnit.get()) "MONTH" else if (DATE_WEEK == validityUnit.get()) "WEEK" else "DAY"
    }

    fun setCdpaFormat(): String {
        var str = ""

        if (!csUnitDesc.get().toString().isNullOrEmpty()) {
            str = "1${csUnitDesc.get().toString()}"
        }

        if (!ipQty.get().toString().isNullOrEmpty() && !ipUnitDesc.get()
                .toString().isNullOrEmpty() && isOpenIp
        ) {
            str = if (str.isNullOrEmpty()) {
                ipQty.get().toString() + ipUnitDesc.get()
                    .toString()
            } else {
                str + "*" + ipQty.get().toString() + ipUnitDesc.get()
                    .toString()
            }
        }

        if (!eaQty.get().toString().isNullOrEmpty() && !eaUnitDesc.get()
                .toString().isNullOrEmpty()
        ) {
            str = if (str.isNullOrEmpty()) {
                eaQty.get().toString() + eaUnitDesc.get()
                    .toString()
            } else {
                str + "*" + eaQty.get().toString() + eaUnitDesc.get()
                    .toString()
            }
        }

        return str
    }

}