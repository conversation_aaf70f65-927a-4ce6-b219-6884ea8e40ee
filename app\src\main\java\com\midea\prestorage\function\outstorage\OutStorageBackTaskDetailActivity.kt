package com.midea.prestorage.function.outstorage

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.beans.net.OutStorageBackTaskResp
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.FilterDigitTextWatcher
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityOutStorageBackTaskDetailBinding
import com.xuexiang.xqrcode.XQRCode
import kotlinx.coroutines.launch

class OutStorageBackTaskDetailActivity :
    BaseViewModelActivity<OutStorageBackTaskDetailViewModel>() {

    companion object {

        private const val KEY_TASK = "TASK"

        fun newIntent(context: Context, task: OutStorageBackTaskResp) =
            Intent(context, OutStorageBackTaskDetailActivity::class.java).also {
                it.putExtra(KEY_TASK, task)
            }
    }

    private lateinit var binding: ActivityOutStorageBackTaskDetailUnionBinding

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityOutStorageBackTaskDetailUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_out_storage_back_task_detail_care
                )
            )
        } else {
            ActivityOutStorageBackTaskDetailUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_out_storage_back_task_detail
                )
            )
        }
        immersionBar {
            titleBarMarginTop(binding.clTitleLayout)
        }
        vm = ViewModelProvider(this).get(OutStorageBackTaskDetailViewModel::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this
        vm.finishEvent.observe(this) {
            finish()
        }
        vm.locLiveData.observe(this) {
            lifecycleScope.launch {
                val text = binding.etLoc.text
                if (text.isNotEmpty()) {
                    binding.etLoc.setSelection(text.length)
                }
            }
        }
        vm.scanEvent.observe(this) {
            XQRCode.startScan(this, QR_CODE_BACK)
        }
        val backTask = intent.getSerializableExtra(KEY_TASK) as? OutStorageBackTaskResp
        backTask?.let {
            vm.update(it)
        }
        binding.etQty4.addTextChangedListener(FilterDigitTextWatcher(binding.etQty4, if ("0" == backTask?.isDecimal) 0 else 4, false) {
        })

        AppUtils.requestFocus(binding.etLoc)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            vm.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

}