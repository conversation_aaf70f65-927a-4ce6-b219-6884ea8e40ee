package com.midea.prestorage.function.planstock

import android.os.Bundle
import android.os.Handler
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.RespPlanStockList
import com.midea.prestorage.function.inv.ActivityGoodsSearchUnionBinding
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.CareLoadMoreView
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityPlanStockListBinding
import java.math.BigDecimal

class PlanStockListActivity : BaseViewModelActivity<PlanStockListVM>() {

    lateinit var binding: ActivityPlanStockListUnionBinding
    val adapter = OutPoolStorageAdapter()

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityPlanStockListUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_plan_stock_list_care
                )
            )
        } else {
            ActivityPlanStockListUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_plan_stock_list
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        vm = ViewModelProvider.AndroidViewModelFactory(application)
            .create(PlanStockListVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        vm.loadMoreComplete.observe(this, Observer<Int> {
            if (it == 1) {
                adapter.loadMoreModule.loadMoreComplete()
            } else if (it == 2) {
                Handler(mainLooper).post {
                    adapter.loadMoreModule.loadMoreEnd()
                }
            }
        })

        vm.showDatas.observe(this, Observer<MutableList<RespPlanStockList>> {
            showData(it)
        })

        vm.loadMoreDatas.observe(this, Observer<MutableList<RespPlanStockList>> {
            loadMoreData(it)
        })

        initRecycle()
        initLoadMore()

        AppUtils.requestFocus(binding.etSearchOrderNo)
        binding?.etSearchOrderNo?.filters = arrayOf(vm.filter)
    }

    override fun onResume() {
        super.onResume()

        binding.vm!!.onRefreshCommand.onRefresh()
    }

    private fun initRecycle() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter

        adapter.setOnItemClickListener { adapter, _, position ->
            val bean = adapter.data[position] as RespPlanStockList
            vm.onItemClick(bean)
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.startSearch()
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            adapter.loadMoreModule.loadMoreView = CareLoadMoreView()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    fun showData(data: MutableList<RespPlanStockList>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()

        vm.isNoData.set(adapter.data.isNullOrEmpty())
        vm.isRefreshing.set(false)

    }

    fun loadMoreData(data: MutableList<RespPlanStockList>?) {
        data?.let { adapter.addData(it) }
        adapter.notifyDataSetChanged()
    }

    class OutPoolStorageAdapter :
        CommonAdapter<RespPlanStockList>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_plan_stock_list_care else R.layout.item_plan_stock_list),
        LoadMoreModule {

        override fun convert(holder: BaseViewHolder?, item: RespPlanStockList?) {
            super.convert(holder, item)

            val view01 = holder?.getView<View>(R.id.view01)
            val view02 = holder?.getView<View>(R.id.view02)
            val view03 = holder?.getView<View>(R.id.view03)
            val view04 = holder?.getView<View>(R.id.view04)

            if (item?.locUsePercent != null) {
                holder?.setText(
                    R.id.tv_stocktakeSchedule, "${
                        AppUtils.getBigDecimalValueStr(
                            item.locUsePercent!!.multiply(
                                BigDecimal(100)
                            )
                        )
                    }%"
                )

                holder?.setText(
                    R.id.tv_stocktakeSchedule02, "${
                        AppUtils.getBigDecimalValueStr(
                            item.locUsePercent!!.multiply(
                                BigDecimal(100)
                            )
                        )
                    }%"
                )

                val layoutParams1 = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    BigDecimal.ONE.subtract(item?.locUsePercent).toFloat()
                )
                val layoutParams3 = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    BigDecimal.ONE.subtract(item?.locUsePercent).toFloat()
                )
                view01?.layoutParams = layoutParams1
                view03?.layoutParams = layoutParams3

                val layoutParams2 = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    item?.locUsePercent!!.toFloat()
                )
                val layoutParams4 = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    item?.locUsePercent!!.toFloat()
                )
                view02?.layoutParams = layoutParams2
                view04?.layoutParams = layoutParams4

            } else {
                holder?.setText(R.id.tv_stocktakeSchedule, "0%")
                holder?.setText(R.id.tv_stocktakeSchedule02, "0%")
            }

        }
    }

}