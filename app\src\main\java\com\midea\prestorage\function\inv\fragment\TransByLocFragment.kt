package com.midea.prestorage.function.inv.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.ReceiveInfo
import com.midea.prestoragesaas.databinding.FragmentTransByLocBinding
import com.midea.prestorage.function.inv.response.FuInvLocationInventory
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.LotAttUnit
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.ScrollingLinearLayoutManager
import com.midea.prestoragesaas.databinding.FragmentTransByGoodBinding
import com.midea.prestoragesaas.databinding.FragmentTransByGoodCareBinding
import com.midea.prestoragesaas.databinding.FragmentTransByLocCareBinding
import com.trello.rxlifecycle2.components.support.RxFragment


// 按库位移库 的 fragment
class TransByLocFragment : RxFragment() {

    companion object {
        const val TAG = "TransByLocFragment"

        fun newInstance(position: Int): TransByLocFragment {
            val bundle = Bundle()
            bundle.putInt("position", position)
            val fragment = TransByLocFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    var adapter: CommonAdapter<FuInvLocationInventory> = TransByLocAdapter()
    lateinit var binding: FragmentTransByLocUnionBinding
    private var vm: TransByLocVM? = null

    fun getVm(): TransByLocVM? {
        return vm
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            FragmentTransByLocUnionBinding.V2(inflater.let {
                FragmentTransByLocCareBinding.inflate(
                    it,
                    container,
                    false
                )
            })
        } else {
            FragmentTransByLocUnionBinding.V1(inflater.let {
                FragmentTransByLocBinding.inflate(
                    it,
                    container,
                    false
                )
            })
        }

        initView()

        initRecycleView()

        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
    }

    private fun initView() {
        if (vm == null) {
            vm = TransByLocVM(this)
            binding.vm = vm
        }
    }


    fun initRecycleView() {
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            binding.recyclerView.layoutManager = ScrollingLinearLayoutManager(activity)
        } else {
            binding.recyclerView.layoutManager = LinearLayoutManager(activity)
        }
        binding.recyclerView.adapter = adapter
    }

    override fun onResume() {
        super.onResume()

        if (binding.etFromLocCode.isEnabled) {
            binding.etFromLocCode.post {
                AppUtils.requestFocus(binding.etFromLocCode)
            }
        }
    }

    class TransByLocAdapter :
        CommonAdapter<FuInvLocationInventory>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_for_transfer_by_loc_care else R.layout.item_for_transfer_by_loc) {
        override fun convert(holder: BaseViewHolder?, item: FuInvLocationInventory?) {
            super.convert(holder, item)
            holder?.setText(
                R.id.tv_cust_item_code, item?.custItemCode
            )

            holder?.setText(
                R.id.tv_carcode69,
                LotAttUnit.formatWhBarcode69(item?.whCsBarcode69, item?.whBarcode69)
            )
        }
    }

}