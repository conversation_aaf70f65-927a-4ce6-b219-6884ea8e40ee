package com.midea.prestorage.function.inv

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.KeyEvent
import android.view.View
import android.widget.EditText
import android.widget.GridLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.beans.net.SortInfoItem
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.sendcheck.SerialCheckFirstActivity
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.ScrollingLinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivitySortGoodsBinding
import com.xuexiang.xqrcode.XQRCode
import java.math.BigDecimal

class SortGoodsActivity : BaseViewModelActivity<SortGoodsVM>() {
    private lateinit var binding: ActivitySortGoodsUnionBinding
    private lateinit var adapter: ContainerPickFourAdapter

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivitySortGoodsUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_sort_goods_care
                )
            )
        } else {
            ActivitySortGoodsUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_sort_goods
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        vm = ViewModelProvider(this, ViewModelProvider.AndroidViewModelFactory(application))
            .get(SortGoodsVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        vm.isStartScan.observe(this, Observer<Boolean> {
            if (it) {
                startScan()
            }
        })

        vm.showDatas.observe(this, Observer<MutableList<SortInfoItem>> {
            showData(it)
        })

        vm.shippingLocList.observe(this, Observer<MutableList<String>> {
            initShippingLoc(it)
        })

        vm.toDetailEvent.observe(this) {
            val it = Intent(this, SortGoodsDetailActivity::class.java)
            it.putExtra("containerCode", vm.orderNo.get().toString().trim())
            startActivity(it)
        }

        //下一个集货位
        vm.nextEvent.observe(this) {
            nextTextView()
        }

        vm.comfirmSortEvent.observe(this) {
            val results = adapter.returnBeans
            if (results.isEmpty()) {
                vm.showNotification("请先选择分货明细", false)
            } else {
                vm.sortConfirm(results)
            }
        }

        initRecycle()
        AppUtils.requestFocus(binding.etSearchOrderNo)
    }

    private fun initRecycle() {
        adapter = ContainerPickFourAdapter(vm)
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            binding.recycle.layoutManager = ScrollingLinearLayoutManager(this)
        } else {
            binding.recycle.layoutManager = LinearLayoutManager(this)
        }
        binding.recycle.adapter = adapter
    }

    fun showData(data: MutableList<SortInfoItem>) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
    }

    fun startScan() {
        XQRCode.startScan(this, BaseActivity.QR_CODE_BACK)
    }

    private fun initShippingLoc(list: MutableList<String>) {
        binding.gridLayout.removeAllViews()

        //这里补齐条数至3个，这样设置权重weight是都为1，分为3等份，不然不够3个会导致显不对
        var fillNum = 0
        if (list.size == 1) {
            list.add(list.getOrNull(0) ?: "") //将字符串的最后一个字符更改为“空”,这样字符串的长度一致
            list.add(list.getOrNull(0) ?: "")
            fillNum = 2
        } else if (list.size == 2) {
            list.add(list.getOrNull(0) ?: "")
            fillNum = 1
        }

        val padding = resources.getDimensionPixelSize(R.dimen.ll_padding_xs1)
        val textColor = ContextCompat.getColor(
            this,
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.color.black else R.color.btn_blue
        )
        val normalTextColor = ContextCompat.getColor(this, R.color.colorTextL_care)
        val blueBackground = ContextCompat.getDrawable(
            this,
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.drawable.background_green_white_corner_care else R.drawable.background_green_corner
        )
        val whiteBackground = ContextCompat.getDrawable(
            this,
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.drawable.background_green_white_corner_care else R.drawable.background_green_white_corner
        )

        vm.curShippingLoc = 0 //每次刷新就重置
        list.forEachIndexed { index, s ->
            val textView = TextView(this).apply {
                text = s
                gravity = Gravity.CENTER
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    if (index == 0) setTextColor(textColor) else setTextColor(normalTextColor)
                } else {
                    setTextColor(textColor)
                }
                setPadding(padding, padding, padding, padding)
                background = if (index == 0) blueBackground else whiteBackground
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    textSize = 20F
                }
                visibility =
                    if (fillNum == 2 && index > 0 || fillNum == 1 && index > 1) View.INVISIBLE else View.VISIBLE
                setOnClickListener {
                    for (i in 0 until binding.gridLayout.childCount) {
                        val childView: TextView = binding.gridLayout.getChildAt(i) as TextView
                        childView.background = whiteBackground
                        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                            childView.setTextColor(normalTextColor)
                        }
                    }
                    background = blueBackground
                    if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                        setTextColor(textColor)
                    }
                    vm.curShippingLoc = index
                    vm.initList(vm.orderNo.get().toString().trim(), s)
                }
            }

            binding.gridLayout.addView(textView, GridLayout.LayoutParams().apply {
                columnSpec = GridLayout.spec(index % 3, 1f)
                rowSpec = GridLayout.spec(index / 3, 1f)
                setMargins(5, 5, 5, 5)
            })
        }
    }

    fun nextTextView() {
        if (binding.gridLayout.childCount > 0) {
            val blueBackground = ContextCompat.getDrawable(
                this,
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.drawable.background_green_white_corner_care else R.drawable.background_green_corner
            )
            val whiteBackground = ContextCompat.getDrawable(
                this,
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.drawable.background_green_white_corner_care else R.drawable.background_green_white_corner
            )
            val textColor = ContextCompat.getColor(
                this,
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.color.black else R.color.btn_blue
            )
            val normalTextColor = ContextCompat.getColor(this, R.color.colorTextL_care)
            for (i in 0 until binding.gridLayout.childCount) {
                val childView: TextView = binding.gridLayout.getChildAt(i) as TextView
                childView.background = whiteBackground
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    childView.setTextColor(normalTextColor)
                }
            }
            // 获取下一个子 view
            var nextIndex = (vm.curShippingLoc + 1) % binding.gridLayout.childCount
            var nextView = binding.gridLayout.getChildAt(nextIndex) as TextView
            while (nextView.visibility != View.VISIBLE) {
                nextIndex = (nextIndex + 1) % binding.gridLayout.childCount
                nextView = binding.gridLayout.getChildAt(nextIndex) as TextView
            }
            nextView.background = blueBackground
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                nextView.setTextColor(textColor)
            }
            vm.curShippingLoc = nextIndex
            vm.initList(vm.orderNo.get().toString().trim(), nextView.text.toString())
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            var result = data?.extras?.getString(XQRCode.RESULT_DATA)
            result?.let {
                vm.orderNo.set(it)
                binding.etSearchOrderNo.post {
                    if (binding.etSearchOrderNo.text!!.isNotEmpty()) {
                        binding.etSearchOrderNo.setSelection(binding.etSearchOrderNo.text!!.length)
                    }
                }
                vm.scanResult()
            }

        }
    }

    class ContainerPickFourAdapter(private val vm: SortGoodsVM?) :
        ListCheckBoxAdapter<SortInfoItem>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_sort_goods_care else R.layout.item_sort_goods) {

        override fun convert(holder: BaseViewHolder?, item: BaseItemForPopup?) {
            super.convert(holder, item as SortInfoItem)

            val csEditTextNum = holder?.getView<EditText>(R.id.csNum)
            val eaEditTextNum = holder?.getView<EditText>(R.id.eaNum)
            val tvMoveTag = holder?.getView<TextView>(R.id.tv_move_tag)
            csEditTextNum?.setOnEditorActionListener { _, _, event ->
                event != null && event.keyCode == KeyEvent.KEYCODE_ENTER  //禁止回车失去焦点后再次回车会直接走移库请求
            }
            eaEditTextNum?.setOnEditorActionListener { _, _, event ->
                event != null && event.keyCode == KeyEvent.KEYCODE_ENTER  //禁止回车失去焦点后再次回车会直接走移库请求
            }

            if (csEditTextNum?.tag is TextWatcher) {
                csEditTextNum.removeTextChangedListener(csEditTextNum.tag as TextWatcher)
            }

            if (eaEditTextNum?.tag is TextWatcher) {
                eaEditTextNum.removeTextChangedListener(eaEditTextNum.tag as TextWatcher)
            }

            val watcherCs = object : TextWatcher {

                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(
                    s: CharSequence?,
                    start: Int,
                    before: Int,
                    count: Int
                ) {
                }

                override fun afterTextChanged(s: Editable?) {
                    //如果存在就给与数值，否则这个项目就是清空的
                    val tvItem = tvMoveTag?.tag as SortInfoItem
                    if (s.toString().isNotEmpty()) {
                        var filterText = s.toString()
                        tvItem.totalNum = AppUtils.getBigDecimalValue(filterText)
                            .multiply(tvItem.packagePara) + (tvItem.eaNum ?: BigDecimal.ZERO)
                        tvItem.csNum = AppUtils.getBigDecimalValue(filterText)
                    } else {
                        tvItem.totalNum = tvItem.eaNum
                        tvItem.csNum = BigDecimal.ZERO
                    }

                    if (tvItem.waitSortQty == null) {
                        tvItem.waitSortQty = BigDecimal.ZERO
                    }

                    if (tvItem.totalNum?.compareTo(tvItem.waitSortQty) == 1) {
                        val tag = csEditTextNum?.tag as TextWatcher
                        csEditTextNum.removeTextChangedListener(tag)
                        csEditTextNum.setText("")
                        csEditTextNum.addTextChangedListener(tag)

                        tvItem.totalNum = tvItem.eaNum
                        tvItem.csNum = BigDecimal.ZERO
                        vm?.showNotification("输入数量不能超过待分货数量", false)
                    }
                }
            }

            val watcherEa = object : TextWatcher {

                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(
                    s: CharSequence?,
                    start: Int,
                    before: Int,
                    count: Int
                ) {
                }

                override fun afterTextChanged(s: Editable?) {
                    //如果存在就给与数值，否则这个项目就是清空的
                    val tvItem = tvMoveTag?.tag as SortInfoItem
                    if (s.toString().isNotEmpty()) {
                        var filterText = s.toString()
                        tvItem.totalNum = (tvItem.csNum ?: BigDecimal.ZERO)
                            .multiply(tvItem.packagePara) + AppUtils.getBigDecimalValue(filterText)
                        tvItem.eaNum = AppUtils.getBigDecimalValue(filterText)
                    } else {
                        tvItem.totalNum = (tvItem.csNum ?: BigDecimal.ZERO)
                            .multiply(tvItem.packagePara)
                        tvItem.eaNum = BigDecimal.ZERO
                    }

                    if (tvItem.waitSortQty == null) {
                        tvItem.waitSortQty = BigDecimal.ZERO
                    }

                    if (tvItem.totalNum?.compareTo(tvItem.waitSortQty) == 1) {
                        val tag = eaEditTextNum?.tag as TextWatcher
                        eaEditTextNum.removeTextChangedListener(tag)
                        eaEditTextNum.setText("")
                        eaEditTextNum.addTextChangedListener(tag)

                        tvItem.totalNum = (tvItem.csNum ?: BigDecimal.ZERO)
                            .multiply(tvItem.packagePara)
                        tvItem.eaNum = BigDecimal.ZERO
                        vm?.showNotification("输入数量不能超过待分货数量", false)
                    }
                }
            }

            val check = holder?.getView<ImageView>(R.id.img_goods)
            item.isShowCs?.let {
                holder?.setGone(R.id.csNum, !it)
                holder?.setGone(R.id.tv_cs_desc, !it)
            }
            if (item.isShowCs == true) {

            }
            if (item.isSelected()) {
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    check?.setImageResource(R.drawable.select_selected_care)
                } else {
                    check?.setImageResource(R.drawable.ic_check_selected)
                }
                holder?.setEnabled(R.id.csNum, true)
                holder?.setEnabled(R.id.eaNum, true)
                if (AppUtils.isZero(item.csNum)) {
                    holder?.setText(R.id.csNum, "")
                } else {
                    holder?.setText(R.id.csNum, AppUtils.getBigDecimalValueStr(item.csNum))
                }
                if (AppUtils.isZero(item.eaNum)) {
                    holder?.setText(R.id.eaNum, "")
                } else {
                    holder?.setText(R.id.eaNum, AppUtils.getBigDecimalValueStr(item.eaNum))
                }
            } else {
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    check?.setImageResource(R.drawable.select_normal_care)
                } else {
                    check?.setImageResource(R.drawable.ic_check_unselect)
                }
                holder?.setEnabled(R.id.csNum, false)
                holder?.setText(R.id.csNum, "")
                holder?.setEnabled(R.id.eaNum, false)
                holder?.setText(R.id.eaNum, "")
            }

            //数量文本改变汇总
            csEditTextNum?.addTextChangedListener(watcherCs)
            csEditTextNum?.tag = watcherCs
            eaEditTextNum?.addTextChangedListener(watcherEa)
            eaEditTextNum?.tag = watcherEa
            tvMoveTag?.tag = item //控件绑定数据，复用的时候用到
        }
    }
}