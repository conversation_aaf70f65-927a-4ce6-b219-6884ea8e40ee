package com.midea.prestorage.function.inpool

import CheckUtil
import android.content.Intent
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.net.InPoolStorageList
import com.midea.prestoragesaas.databinding.ActivityInStoragePoolBinding
import com.midea.prestoragesaas.databinding.PopViewInPoolBinding
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.outpool.dialog.CopyDialog
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.xuexiang.xqrcode.XQRCode

// 入库订单池
class InPoolStorageActivity : BaseActivity() {

    lateinit var binding: ActivityInStoragePoolBinding
    lateinit var popBinding: PopViewInPoolBinding
    lateinit var popupWindow: PopupWindow
    private lateinit var daysDialog: FilterDialog
    lateinit var orderTypesDialog: FilterDialog
    lateinit var carNoDialog: FilterDialog
    private var vm = InPoolStorageVM(this)
    val adapter = InPoolListOrderAdapter()
    var isNeedRefresh = false
    private lateinit var copyDialog: CopyDialog
    var isLoadParamFromMainActivity = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_in_storage_pool)
        binding.vm = vm

        if (!CheckUtil.isHaveWarehouseCode()) {
            AlertDialogUtil.showOnlyOkDialog(this, "未选择仓库", AlertDialogUtil.OnOkCallback {
                finish()
            })
        }

        //初始化 入库订单类型
        DCUtils.initReceiptOrderType(this, object : DCUtils.OnInitFinish {
            override fun finish() {
                initOrderTypesDialog()
            }
        })

        //初始化 入库单状态
        DCUtils.initReceiptStatusDict(this, object : DCUtils.OnInitFinish {
            override fun finish() {
                // 只要有初始化数据就行了 这里不需要操作
            }
        })

        initPop()

        initRecycle()

        // 初始化  下拉加载更多
        initLoadMore()


        //选择日期范围对话框
        initDaysDialog()
        //选择车牌号对话框
        initCarNumberDialog()

        //初始化 字典(库存状态(正品不良品))
        DCUtils.initLot4dicts(this, null)

        //重置默认条件
        vm.resetDefault()
        //先按默认条件 查询一次
        vm.search(false)
        vm.initCarNo()

        copyDialog = CopyDialog(this)


        // 如果是从首页带查询条件跳进来的 ， 则直接按条件查询
        if (this.intent != null && this.intent.getBooleanExtra("fromMainActivity", false) != null
            && this.intent.getBooleanExtra("fromMainActivity",false).equals(true)
        ) {
            isLoadParamFromMainActivity = true

            val beginTime = this.intent.getStringExtra("beginTime")
            val endTime = this.intent.getStringExtra("endTime")
            if (beginTime != null && endTime != null) {

                val param = mutableMapOf<String, Any>()
                param.put("whCode", getWhCode())
                param.put("beginTime", beginTime)
                param.put("endTime", endTime)

                // 100 新建 ， 200到货确认 ， 300 收货中
                val statusList = mutableListOf<String>()
                statusList.add("100")
                statusList.add("200")
                statusList.add("300")
                param["statusList"] = statusList

                vm.curPageNo = 1
                // 第几页
                param.put("pageNo", vm.curPageNo)
                // 每页多少条数据
                param.put("pageSize", 1000)

                vm.loadByParam(param)
            }
        }

    }

    override fun onResume() {
        super.onResume()
        if (isNeedRefresh) {
            isNeedRefresh = false
            // 从其他界面回来后 界面上的所有勾选框默认恢复为空
            binding.cbSelectAll.isChecked = false
            binding.vm!!.search(false)
        }
    }


    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.search(true)
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = true

        /*  adapter.setOnItemChildClickListener { adapter, view, position ->
              val bean = adapter.data[position] as InPoolStorageList
              vm.onItemClick(bean)
          }*/
    }

    private fun initPop() {
        val popView = LayoutInflater.from(this).inflate(R.layout.pop_view_in_pool, null)
        popBinding = DataBindingUtil.bind(popView)!!
        popBinding.vm = vm

        popupWindow = PopupWindow(
            popView,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        popupWindow.setBackgroundDrawable(
            ColorDrawable(
                ContextCompat.getColor(
                    this,
                    R.color.colorOutlineGrey
                )
            )
        )
        popupWindow.isOutsideTouchable = true

        binding.llContainer.setOnClickListener {
            popupWindow.showAsDropDown(it)
            //20211129 需求 展示筛选框时 如果单号不为空 光标定位到单号最后面
            AppUtils.requestFocus(binding.etSearchOrderNo)

        }

        popBinding.tvPallet.setOnClickListener {
            carNoDialog.show()
        }

        popBinding.tvHideSearch.setOnClickListener {
            popupWindow.dismiss()
        }

        popupWindow.setOnDismissListener {
            vm.search(false)
        }


        popBinding.tvDayRange.setOnClickListener {
            daysDialog.show()
        }

        popBinding.tvOrderType.setOnClickListener {
            orderTypesDialog.show()
        }

        popBinding.tvResetSearch.setOnClickListener {
            vm.onResetSearch()
        }


        binding.llAllChoose.setOnClickListener {
            binding.cbSelectAll.performClick()
        }

        binding.cbSelectAll.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                adapter.allSelect(true)
            }
        }

        binding.cbSelectAll.setOnClickListener {
            if (!binding.cbSelectAll.isChecked) {
                adapter.allSelect(false)
            }
        }
    }

    fun setCbSelectChecked(isChecked: Boolean) {
        binding.cbSelectAll.isChecked = isChecked
    }

    fun initCarNoDialogData(beans: MutableList<BaseItemShowInfo>) {
        carNoDialog.addAllData(beans)
    }

    private fun initRecycle() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter
        adapter.setClickId(R.id.ll_checked)

        adapter.setOnItemClickListener { adapter, view, position ->
            val bean = adapter.data[position] as InPoolStorageList
            vm.onItemClick(bean)
        }

        adapter.setChangeSelectStatus {
            val results = adapter.data.filter { !it.isSelected }
            binding.cbSelectAll.isChecked = results.isEmpty()
        }

        adapter.setOnItemLongClickListener{adapter, _, position ->
            val bean = adapter.data[position] as InPoolStorageList
            if (TextUtils.isEmpty(bean.waveNo)) {
                CheckUtil.copyToClipboard(bean.custOrderNo)
                AppUtils.showToast(this@InPoolStorageActivity, "客户单号已复制到剪切板")
            } else {
                copyDialog.setCopyText(bean.waveNo, bean.custOrderNo)
                copyDialog.show()
            }
            true
        }
    }

    private fun initCarNumberDialog() {
        carNoDialog = FilterDialog(this)
        carNoDialog.setTitle("选择车牌号")
        carNoDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            popBinding.tvPallet.text = it.showInfo
            vm.searchCarNo.set(it.showInfo)
            // 不需要立即刷新 收起筛选框的时候再刷新
            // vm.search()
            carNoDialog.dismiss()
        })
    }

    private fun initDaysDialog() {
        //选择天数的弹框
        daysDialog = FilterDialog(this)
        daysDialog.setTitle("请选择天数")
        daysDialog.dismissEdit()
        val days = DCUtils.days
        daysDialog.addAllData(days as MutableList<BaseItemShowInfo>)
        daysDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            vm.searchDayRange.set(it.showInfo)
            daysDialog.dismiss()
            vm.initCarNo()
        })
    }

    private fun initOrderTypesDialog() {
        //选择入库单类型
        orderTypesDialog = FilterDialog(this)
        orderTypesDialog.setTitle("请选择入库单类型")
        orderTypesDialog.dismissEdit()


        val listOrderType = mutableListOf<BaseItemShowInfo>()
        DCUtils.inOrderTypeC2N.values.forEach {
            val item = BaseItemShowInfo()
            item.showInfo = it.toString()
            listOrderType.add(item)
        }

        orderTypesDialog.addAllData(listOrderType)
        orderTypesDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            if (DCUtils.inOrderTypeN2C.get(it.showInfo) != null) {
                vm.searchOrderType.set(DCUtils.inOrderTypeN2C.get(it.showInfo).toString())
                popBinding.tvOrderType.setText(it.showInfo)
                vm.displaySearchOrderType.set(it.showInfo)
            }

            orderTypesDialog.dismiss()
        })
    }

    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }

    fun showData(data: MutableList<InPoolStorageList>) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            vm.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    class InPoolListOrderAdapter : ListCheckBoxAdapter<InPoolStorageList>(R.layout.item_order_in_pool), LoadMoreModule {
        override fun convert(helper: BaseViewHolder, item: BaseItemForPopup) {
            super.convert(helper, item as InPoolStorageList)

            // 不扫码印章
            if (!item.unscanMark.isNullOrBlank()) {
                helper.itemView.findViewById<LinearLayout>(R.id.redMarkUnScan).visibility = View.VISIBLE
                if (item.unscanMark.equals("00")) {
                    helper.setText(R.id.tvUnscanText, "不扫码申请中")
                } else if (item.unscanMark.equals("01")) {
                    helper.setText(R.id.tvUnscanText, "不扫码已审核")
                }
            } else {
                helper.itemView.findViewById<LinearLayout>(R.id.redMarkUnScan).visibility = View.INVISIBLE
            }

            //订单状态
            if (!item.status.isNullOrBlank()) {
                if (DCUtils.inOrderStatusC2N.size > 0) {
                    helper.setText(R.id.tvStatus, DCUtils.inOrderStatusC2N.get(item.status).toString())
                } else {
                    helper.setText(R.id.tvStatus, item.status)
                }

                //根据状态对应字体颜色
                if (item.status.equals("100")) {  // 新建
                    helper.setTextColorRes(R.id.tvStatus, R.color.ui_font_color_info_green)
                } else if (item.status.equals("200")) {  // 到货确认
                    helper.setTextColorRes(R.id.tvStatus, R.color.ui_font_color_orange)
                } else if (item.status.equals("300")) {  //  收货中
                    helper.setTextColorRes(R.id.tvStatus, R.color.ui_font_color_blue)
                } else if (item.status.equals("350")) {  // 收货完成
                    helper.setTextColorRes(R.id.tvStatus, R.color.ui_font_color_green)
                } else if (item.status.equals("900")) {  // 关闭
                    helper.setTextColorRes(R.id.tvStatus, R.color.ui_font_color_default)
                } else if (item.status.equals("950")) {  // 拒收
                    helper.setTextColorRes(R.id.tvStatus, R.color.ui_font_color_red)
                } else if (item.status.equals("999")) {  // 取消
                    helper.setTextColorRes(R.id.tvStatus, R.color.ui_font_color_default)
                }
            } else {
                helper.setText(R.id.tvStatus, "")
            }

            if (!item.receiptType.isNullOrBlank()) {
                if (DCUtils.inOrderTypeC2N.size > 0 && DCUtils.inOrderTypeC2N.get(item.receiptType) != null) {
                    helper.setText(R.id.tvReceiptType, DCUtils.inOrderTypeC2N.get(item.receiptType).toString())
                } else {
                    helper.setText(R.id.tvReceiptType, item.receiptType)
                }
            } else {
                helper.setText(R.id.tvReceiptType, "")
            }



            if (!item.totalQty.isNullOrBlank()) {
                helper.setText(R.id.tvTotalQty, item.totalQty.toDouble().toInt().toString())
            } else {
                helper.setText(R.id.tvTotalQty, "")
            }

            val check = helper.getView<ImageView>(R.id.img_select)
            if (item.isSelected()) {
                check.setImageResource(R.drawable.ic_check_selected)
            } else {
                check.setImageResource(R.drawable.ic_check_unselect)
            }
        }
    }
}