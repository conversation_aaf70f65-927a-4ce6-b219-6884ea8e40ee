package com.midea.prestorage.function.outstorage.dialog

import CheckUtil
import android.text.TextUtils
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.AreaList
import com.midea.prestorage.dialog.MultiChooseDialog
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody

class BindShippingLocDialogVM(val dialog: BindShippingLocDialog) {

    val title = ObservableField<String>("绑定集货位")
    val goodsNo = ObservableField<String>("")
    val newLocCode = ObservableField<String>("")
    private var statusDialog: MultiChooseDialog? = null
    var statusBean = mutableListOf<DCBean>()

    fun show() {

        queryShippingZone()

        statusDialog = MultiChooseDialog(dialog.mContext)
        statusDialog?.setTitle("请选择集货区")
        statusDialog?.setBack(object : MultiChooseDialog.MultiChooseBack {
            override fun multiChooseBack(baseInfo: MutableList<BaseItemShowInfo>) {
                statusBean.clear()
                statusBean.addAll(baseInfo as MutableList<DCBean>)

                val types = baseInfo.joinToString(separator = ",") { it.value.toString() }
                newLocCode.set(types)
                statusDialog?.dismiss()
            }
        })
    }

    val goodsEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                if (!TextUtils.isEmpty(goodsNo.get().toString().trim())) {
                    assignPickLoc()
                }
            }
        }
    }


    fun close() {
        dialog.inputBack?.inputFail()
        dialog.dismiss()
    }

    fun confirm() {
        if (CheckUtil.isFastDoubleClick()) {
            if (dialog.binding.cbAppointLoc.isChecked) {
                assignPickLoc()
            }else {
                allocPickLoc()
            }
        }
    }

    private fun queryShippingZone() {
        dialog.mContext.waitingDialogHelp.showDialogUnCancel()
        RetrofitHelper.getInventoryAPI()
            .queryShippingZone(dialog.mContext.getWhCode())
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(dialog.mContext as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<AreaList>>(dialog.mContext) {
                override fun success(data: MutableList<AreaList>?) {
                    dialog.mContext.waitingDialogHelp.hidenDialog()
                    var dCBeans = mutableListOf<DCBean>()
                    data?.forEach {
                        var bean = DCBean(it.zoneCode, it.zoneName, DCBean.SHOW_VALUE)
                        dCBeans.add(bean)
                    }
                    if (!dCBeans.isNullOrEmpty() && dCBeans.size == 1) {
                        dCBeans.getOrNull(0)?.isSelected = true
                        dCBeans.getOrNull(0)?.isTempSelected = true
                        newLocCode.set(dCBeans.getOrNull(0)?.value.toString())
                        statusBean.addAll(dCBeans)
                    }
                    statusDialog?.addAllData(dCBeans as MutableList<BaseItemShowInfo>)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    dialog.mContext.waitingDialogHelp.hidenDialog()
                    AppUtils.showToast(dialog.mContext, apiErrorModel.message)
                }
            })
    }

    fun assignPickLoc() {
        if (TextUtils.isEmpty(goodsNo.get().toString().trim())) {
            AppUtils.showToast(dialog.mContext, "集货位不能为空!")
            return
        }

        dialog.mContext.waitingDialogHelp.showDialog()
        val param = mutableMapOf(
            "whCode" to  dialog.mContext.getWhCode(),
            "shippingLoc" to goodsNo.get().toString().trim(),
            "pickContainerCode" to (dialog.deleteInfo?.pickContainerCode ?: "")
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getInventoryAPI()
            .assignPickLoc(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(dialog.mContext as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(dialog.mContext) {
                override fun success(data: Any?) {
                    dialog.mContext.waitingDialogHelp.hidenDialog()
                    dialog.inputBack?.inputOk()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    dialog.mContext.waitingDialogHelp.hidenDialog()
                    AppUtils.showToast(dialog.mContext, apiErrorModel.message)
                }
            })
    }

    private fun allocPickLoc() {
        if (TextUtils.isEmpty(newLocCode.get())) {
            AppUtils.showToast(dialog.mContext, "集货区不能为空!")
            return
        }

        dialog.mContext.waitingDialogHelp.showDialog()
        val param = mutableMapOf(
            "whCode" to  dialog.mContext.getWhCode(),
            "shippingZonesList" to statusBean?.map { it.key },
            "pickContainerCode" to (dialog.deleteInfo?.pickContainerCode ?: "")
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getInventoryAPI()
            .allocPickLoc(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(dialog.mContext as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(dialog.mContext) {
                override fun success(data: Any?) {
                    dialog.mContext.waitingDialogHelp.hidenDialog()
                    dialog.inputBack?.inputOk()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    dialog.mContext.waitingDialogHelp.hidenDialog()
                    AppUtils.showToast(dialog.mContext, apiErrorModel.message)
                }
            })
    }

    fun statusClick() {
        statusDialog?.show()
    }

    val statusDC = mutableListOf(
        DCBean("100", "待收货", DCBean.SHOW_VALUE),     //green
        DCBean("150", "收货中", DCBean.SHOW_VALUE),     //blue
        DCBean("300", "收货完成", DCBean.SHOW_VALUE),   //orange
        DCBean("350", "上架中", DCBean.SHOW_VALUE),    //yellow
        DCBean("900", "关闭", DCBean.SHOW_VALUE),      // red
        DCBean("999", "取消", DCBean.SHOW_VALUE)       // red
    )
}