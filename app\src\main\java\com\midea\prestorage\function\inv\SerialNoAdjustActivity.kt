package com.midea.prestorage.function.inv

import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.widget.TextView
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.beans.net.ItemSerialAdjust
import com.midea.prestorage.beans.net.SerialScanDto
import com.midea.prestoragesaas.databinding.ActivitySerialNoAdjustBinding
import com.midea.prestoragesaas.databinding.PopViewForSelectCustemItemCodeBinding
import com.midea.prestorage.function.inv.response.ItemRfVO
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.xuexiang.xqrcode.XQRCode

// 条码采集
class SerialNoAdjustActivity : BaseActivity() {

    lateinit var binding: ActivitySerialNoAdjustBinding
    var adapter = ListSerialNoAdjustAdapter()

    //69码 查询出多个custItemCodes的时候 弹框让用户选择custItemCode
    lateinit var dlgSelectCustItemCode: AlertDialog
    private lateinit var popBindingSelectCustItemCode: PopViewForSelectCustemItemCodeBinding
    lateinit var popAdapterSelectCustItemCode: CommonAdapter<BaseItemForPopup>

    // 调账类型选项
    val adjustOptions = mutableListOf<String>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //setContentView(R.layout.activity_serial_no_adjust)

        binding = DataBindingUtil.setContentView(this, R.layout.activity_serial_no_adjust)
        binding.vm = SerialNoAdjustVM(this)

        // 加载字典： 调账类型 和 下拉选择框
        initSerialNoAdjustType()

        initRecyclerView()

        initPopWinSelectCustItemCode()

        binding.vm!!.initAdjustCode()
    }


    fun initRecyclerView() {
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter
        adapter.setOnCheckListener {
            var tip = "明细信息:   \n";
            tip += "大箱条码:  " + it.serialCs + "\n"
            tip += "小箱条码:  " + it.serialNo + "\n"
            tip += "客户商品编码:  " + it.custItemCode + "\n"
            tip += "商品描述:  " + it.itemName + "\n"

            if (DCUtils.serialNoAdjustTypeC2N.get(it.adjustType) != null) {
                tip += "调账类型:  " + DCUtils.serialNoAdjustTypeC2N.get(it.adjustType) + "\n"
            }
            ToastUtils.getInstance().toastNoSound(this, tip, Toast.LENGTH_SHORT)
        }
    }


    // 初始化选择客户商品编码的弹窗
    private fun initPopWinSelectCustItemCode() {

        val popViewSelectCustItemCode = LayoutInflater.from(this).inflate(R.layout.pop_view_for_select_custem_item_code, null)
        popBindingSelectCustItemCode = DataBindingUtil.bind(popViewSelectCustItemCode)!!

        val alertDialogBuilder = AlertDialog.Builder(this)
        alertDialogBuilder.setView(popViewSelectCustItemCode)
        dlgSelectCustItemCode = alertDialogBuilder.create()

        popAdapterSelectCustItemCode = ListCustItemCodeAdapter()
        popBindingSelectCustItemCode.recyclerViewInPop.layoutManager = LinearLayoutManager(this)
        popBindingSelectCustItemCode.recyclerViewInPop.adapter = popAdapterSelectCustItemCode

        popBindingSelectCustItemCode.closePop.setOnClickListener {
            dlgSelectCustItemCode.dismiss()
        }

    }

    // 选择custItemCode的list 的  adapter
    class ListCustItemCodeAdapter : CommonAdapter<BaseItemForPopup>(R.layout.item_pop_view_for_select_cust_item_code) {
        override fun convert(helper: BaseViewHolder, item: BaseItemForPopup) {
            super.convert(helper, item as ItemRfVO)
            // 客户商品编码
            helper.setText(R.id.tvCustItemCode, item.custItemCode)
        }
    }

    fun initSerialNoAdjustType() {
        DCUtils.initSerialNoAdjustTypeDict(this, object : DCUtils.OnInitFinish {
            override fun finish() {

                DCUtils.serialNoAdjustTypeC2N.values.forEach {
                    adjustOptions.add(it.toString())
                }
                //默认选第一个
                if (adjustOptions.size > 0) {
                    binding.vm!!.curSelectAdjustName = adjustOptions.get(0)
                }

                binding.spinnerAdjustType.setItems(adjustOptions);
                binding.spinnerAdjustType.setOnItemSelectedListener(MaterialSpinner.OnItemSelectedListener<String> { view, position, id, item ->
                    binding.vm!!.curSelectAdjustName = adjustOptions.get(binding.spinnerAdjustType.selectedIndex)
                })

            }
        })
    }


    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }



    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == BaseActivity.QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果
            binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }


    class ListSerialNoAdjustAdapter : ListChoiceClickAdapter<ItemSerialAdjust>(R.layout.item_for_serial_adjust) {

        override fun convert(helper: BaseViewHolder, item: ItemSerialAdjust) {
            super.convert(helper, item)
            helper.setText(R.id.tvNumber, (this.data.size - helper.adapterPosition).toString())
            helper.setText(R.id.tvSerialNo, item.serialNo)
        }
    }
}