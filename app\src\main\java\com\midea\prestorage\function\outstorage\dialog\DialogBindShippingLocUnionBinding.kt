package com.midea.prestorage.function.outstorage.dialog

import android.widget.CheckBox
import android.widget.EditText
import android.widget.TextView
import com.midea.prestoragesaas.databinding.DialogBindShippingLocBinding
import com.midea.prestoragesaas.databinding.DialogBindShippingLocCareBinding

sealed class DialogBindShippingLocUnionBinding{
    abstract var vm: BindShippingLocDialogVM?
    abstract val etLocCode: EditText
    abstract val cbAppointLoc: CheckBox
    abstract val cbNewLoc: CheckBox
    abstract val tvLocCode: TextView

    class V2(val binding: DialogBindShippingLocCareBinding) : DialogBindShippingLocUnionBinding() {
        override var vm: BindShippingLocDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val etLocCode = binding.etLocCode
        override val cbAppointLoc = binding.cbAppointLoc
        override val cbNewLoc = binding.cbNewLoc
        override val tvLocCode = binding.tvLocCode
    }

    class V1(val binding: DialogBindShippingLocBinding) : DialogBindShippingLocUnionBinding() {
        override var vm: BindShippingLocDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val etLocCode = binding.etLocCode
        override val cbAppointLoc = binding.cbAppointLoc
        override val cbNewLoc = binding.cbNewLoc
        override val tvLocCode = binding.tvLocCode
    }
}
