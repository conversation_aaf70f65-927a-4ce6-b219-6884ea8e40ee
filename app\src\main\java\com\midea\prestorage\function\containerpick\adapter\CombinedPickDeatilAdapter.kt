package com.midea.prestorage.function.containerpick.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.TextView
import com.midea.prestorage.beans.net.PickDetail
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R

class CombinedPickDeatilAdapter(private val mContext: Context) : BaseAdapter() {
    private val beans: MutableList<PickDetail> = mutableListOf()

    fun addData(data: MutableList<PickDetail>?) {
        if (!data.isNullOrEmpty()) {
            beans.addAll(data)
        }
    }

    override fun getCount(): Int {
        return beans.size
    }

    override fun getItem(position: Int): PickDetail {
        return beans[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(
        position: Int,
        convertView: View?,
        parent: ViewGroup
    ): View {
        val viewHolder: ViewHolder?

        var convertView = convertView
        if (convertView == null) {
            convertView =
                LayoutInflater.from(mContext).inflate(
                    if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_combined_pick_deatil_care else R.layout.item_combined_pick_deatil,
                    parent,
                    false
                )
            viewHolder = ViewHolder()
            viewHolder.tvGridNumber = convertView.findViewById(R.id.tv_gridNumber)
            viewHolder.tvSkuQty = convertView.findViewById(R.id.tv_skuQty)
            viewHolder.tvQty = convertView.findViewById(R.id.tv_qty)
            viewHolder.tvStoreName = convertView.findViewById(R.id.tv_storeName)
            convertView!!.tag = viewHolder
        } else {
            viewHolder = convertView.tag as ViewHolder
        }

        val bean = beans[position]
        viewHolder.tvGridNumber?.text = AppUtils.getBigDecimalValueStr(bean.gridNumber)
        viewHolder.tvSkuQty?.text = AppUtils.getBigDecimalValueStr(bean.skuQty)
        viewHolder.tvQty?.text = AppUtils.getBigDecimalValueStr(bean.qty)
        viewHolder.tvStoreName?.text = bean.storeName ?: ""
        return convertView
    }

    internal inner class ViewHolder {
        var tvGridNumber: TextView? = null
        var tvSkuQty: TextView? = null
        var tvQty: TextView? = null
        var tvStoreName: TextView? = null
    }
}