package com.midea.prestorage.function.outstorage.dialog

import androidx.databinding.ObservableField

class GoodsInfoChangesDialogVM(val dialog: GoodsInfoChangesDialog) {
    val title = ObservableField<String>("提示")
    val tipInfo = ObservableField<String>("")

    fun close() {
        dialog.dismiss()
    }

    fun cancel() {
        if (CheckUtil.isFastDoubleClick()) {
            dialog.backCancel()
        }
    }

    fun confirm() {
        if (CheckUtil.isFastDoubleClick()) {
            dialog.backConfirm()
        }
    }


}