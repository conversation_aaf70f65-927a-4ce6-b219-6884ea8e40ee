package com.midea.prestorage.function.main.dialog

import android.app.AlertDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestorage.beans.setting.ImplWarehouse
import com.midea.prestorage.dialog.DialogTipUnionBinding
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogSaasWhChooseBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

class SaasWhChooseDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {

    private var binding: DialogSaasWhChooseUnionBinding
    private var chooseback: ChooseBack? = null

    init {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as <PERSON><PERSON><PERSON>) {
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window?.setGravity(Gravity.CENTER)
            window?.attributes?.run {
                gravity = Gravity.CENTER
            }
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_saas_wh_choose_care, null)
            setView(contentView)
            DialogSaasWhChooseUnionBinding.V2(DataBindingUtil.bind(contentView)!!)
        } else {
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_saas_wh_choose, null)
            setView(contentView)
            DialogSaasWhChooseUnionBinding.V1(DataBindingUtil.bind(contentView)!!)
        }

        binding.vm = SaasWhChooseDialogVM(this)

//        setCanceledOnTouchOutside(false)
//        // 加上这个 确保按回退键也不关闭选择框
//        setCancelable(false)

    }

    fun setWarehouse(warehouseCode: String, warehouse: String) {
        binding.vm!!.warehouse.set(warehouse)
        binding.vm!!.warehouseCode.set(warehouseCode)
    }

    fun setTenant(tenantCode: String, tenantName: String) {
        binding.vm!!.tenant.set(tenantName)
        binding.vm!!.tenantCode.set(tenantCode)
    }

    fun getWarehouseCode(): String {
        return binding.vm!!.warehouseCode.get().toString()
    }

    fun getTenantCode(): String {
        return binding.vm!!.tenantCode.get().toString()
    }

    fun setChooseBack(backImpl: ChooseBack) {
        chooseback = backImpl
    }

    fun backChoose(type : String) {
        if (chooseback != null) {
            chooseback!!.chooseBack(type)
        }
    }

    fun backConfirm(whCode: String, whName: String) {
        if (chooseback != null) {
            chooseback!!.confirmBack(whCode, whName)
        }
    }

    interface ChooseBack {
        fun chooseBack(type : String)
        fun confirmBack(whCode: String, whName: String)
    }
}