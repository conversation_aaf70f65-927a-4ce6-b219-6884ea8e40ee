package com.midea.prestorage.dialog

import android.widget.CheckBox
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestoragesaas.databinding.DialogListClickBinding
import com.midea.prestoragesaas.databinding.DialogListClickCareBinding
import com.midea.prestoragesaas.databinding.DialogMultiChooseBinding
import com.midea.prestoragesaas.databinding.DialogMultiChooseCareBinding

sealed class DialogMultiChooseUnionBinding{
    abstract var vm: MultiChooseDialogVM?
    abstract val recycle: RecyclerView
    abstract val llAllChoose: LinearLayout
    abstract val cbSelectAll: CheckBox

    class V2(val binding: DialogMultiChooseCareBinding) : DialogMultiChooseUnionBinding() {
        override var vm: MultiChooseDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val recycle = binding.recycle
        override val llAllChoose = binding.llAllChoose
        override val cbSelectAll = binding.cbSelectAll
    }

    class V1(val binding: DialogMultiChooseBinding) : DialogMultiChooseUnionBinding() {
        override var vm: MultiChooseDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val recycle = binding.recycle
        override val llAllChoose = binding.llAllChoose
        override val cbSelectAll = binding.cbSelectAll
    }
}
