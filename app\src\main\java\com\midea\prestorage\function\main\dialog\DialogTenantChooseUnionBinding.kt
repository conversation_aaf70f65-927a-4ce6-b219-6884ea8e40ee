package com.midea.prestorage.function.main.dialog

import androidx.recyclerview.widget.RecyclerView
import com.midea.prestoragesaas.databinding.DialogTenantChooseBinding
import com.midea.prestoragesaas.databinding.DialogTenantChooseCareBinding

sealed class DialogTenantChooseUnionBinding{
    abstract var vm: TenantDialogVM?
    abstract val recycle: RecyclerView

    class V2(val binding: DialogTenantChooseCareBinding) : DialogTenantChooseUnionBinding() {
        override var vm: TenantDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val recycle = binding.recycle
    }

    class V1(val binding: DialogTenantChooseBinding) : DialogTenantChooseUnionBinding() {
        override var vm: TenantDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val recycle = binding.recycle
    }
}
