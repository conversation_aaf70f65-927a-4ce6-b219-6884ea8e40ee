package com.midea.prestorage.function.outstorage

import android.widget.EditText
import android.widget.RelativeLayout
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.databinding.ActivityBindShippingLocBinding
import com.midea.prestoragesaas.databinding.ActivityBindShippingLocCareBinding
import com.midea.prestoragesaas.databinding.ActivityOrderQueryBinding
import com.midea.prestoragesaas.databinding.ActivityOrderQueryCareBinding

sealed class ActivityOrderQueryUnionBinding{
    abstract var vm: OrderQueryVM?
    abstract val llTitleBar: RelativeLayout
    abstract val etCode: EditText
    abstract val spinnerStatus: MaterialSpinner
    abstract val recycle: RecyclerView
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: ActivityOrderQueryCareBinding) : ActivityOrderQueryUnionBinding() {
        override var vm: OrderQueryVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etCode = binding.etCode
        override val spinnerStatus = binding.spinnerStatus
        override val recycle = binding.recycle
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: ActivityOrderQueryBinding) : ActivityOrderQueryUnionBinding() {
        override var vm: OrderQueryVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etCode = binding.etCode
        override val spinnerStatus = binding.spinnerStatus
        override val recycle = binding.recycle
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
