package com.midea.prestorage.beans.net

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.math.BigDecimal

@Parcelize
data class RespTaskInfo(
    var taskQty: BigDecimal? = null,
    var storeTotalQty: BigDecimal? = null,
    var skuTotalQty: BigDecimal? = null,
    var totalQty: BigDecimal? = null,
    var totalCsQty: BigDecimal? = null,
    var totalEaQty: BigDecimal? = null,
    val pickDetailList: List<PickDetail>?
) : Parcelable

@Parcelize
data class PickDetail(
    val taskCode: String?,
    val gridNumber: BigDecimal?,
    val skuQty: BigDecimal?,
    val qty: BigDecimal?,
    val storeName: String?
) : Parcelable
