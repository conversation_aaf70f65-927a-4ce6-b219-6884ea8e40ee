package com.midea.prestorage.base.adapter;

import com.chad.library.adapter.base.viewholder.BaseViewHolder;

public class ListChoiceClickPositionAdapter<T> extends CommonAdapter<T> {

    private OnCheckListener<T> onCheckListener;

    public ListChoiceClickPositionAdapter(int layoutResId) {
        super(layoutResId);
    }

    @Override
    protected void convert(BaseViewHolder helper, final T item) {
        super.convert(helper, item);
        helper.itemView.setTag(item);

        helper.itemView.setOnClickListener(v -> {
            if (onCheckListener != null) {
                onCheckListener.onCheckListener(item, getItemPosition(item));
            }
        });
    }

    public void setOnCheckListener(OnCheckListener<T> onCheckListener) {
        this.onCheckListener = onCheckListener;
    }

    public interface OnCheckListener<T> {
        void onCheckListener(T t, int position);
    }
}
