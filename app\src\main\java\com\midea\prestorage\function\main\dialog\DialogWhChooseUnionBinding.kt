package com.midea.prestorage.function.main.dialog

import androidx.recyclerview.widget.RecyclerView
import com.midea.prestoragesaas.databinding.DialogWhChooseBinding
import com.midea.prestoragesaas.databinding.DialogWhChooseCareBinding

sealed class DialogWhChooseUnionBinding {
    abstract var vm: WhChooseDialogVM?
    abstract val recycle: RecyclerView

    class V2(val binding: DialogWhChooseCareBinding) : DialogWhChooseUnionBinding() {
        override var vm: WhChooseDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val recycle = binding.recycle
    }

    class V1(val binding: DialogWhChooseBinding) : DialogWhChooseUnionBinding() {
        override var vm: WhChooseDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val recycle = binding.recycle
    }
}
