package com.midea.prestorage.function.put

import androidx.lifecycle.MutableLiveData
import com.midea.prestorage.widgets.ViewBindingAdapter

class InStorageScanPutOperationQtyInputVM {
    val unit = MutableLiveData<String>()
    val unitName = MutableLiveData<String>()
    val qty = MutableLiveData<String>()
    var index = -1
    var nextFocus: ((Int) -> Unit)? = null

    val qtyKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                if (index >= 0) {
                    nextFocus?.invoke(index)
                }
            }
        }
    }

    fun reset() {
        qty.value = ""
    }

    fun valid() = !qty.value.isNullOrEmpty()

    fun qtyValue(): String = qty.value ?: ""

    fun unitValue(): String = unit.value ?: ""
}