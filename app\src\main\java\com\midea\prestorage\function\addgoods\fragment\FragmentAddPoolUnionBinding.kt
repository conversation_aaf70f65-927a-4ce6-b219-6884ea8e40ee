package com.midea.prestorage.function.addgoods.fragment

import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestorage.widgets.WarpLinearLayout
import com.midea.prestoragesaas.databinding.FragmentAddPoolBinding
import com.midea.prestoragesaas.databinding.FragmentAddPoolCareBinding

sealed class FragmentAddPoolUnionBinding{
    abstract var vm: AddPoolVM?
    abstract val root: View
    abstract val srl: SwipeRefreshLayout
    abstract val rv: RecyclerView
    abstract val llContainer: LinearLayout
    abstract val linOpenLaunchSelectCom: LinearLayout
    abstract val ivNoOrder: ImageView
    abstract val tvTotalNumber: TextView
    abstract val ivOpenLaunchSelectCom: ImageView
    abstract val llConfirmGet: LinearLayout
    abstract val warpLinear: WarpLinearLayout
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: FragmentAddPoolCareBinding) : FragmentAddPoolUnionBinding() {
        override var vm: AddPoolVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val srl = binding.srl
        override val rv = binding.rv
        override val llContainer = binding.llContainer
        override val linOpenLaunchSelectCom = binding.linOpenLaunchSelectCom
        override val ivNoOrder = binding.ivNoOrder
        override val tvTotalNumber = binding.tvTotalNumber
        override val ivOpenLaunchSelectCom = binding.ivOpenLaunchSelectCom
        override val llConfirmGet = binding.llConfirmGet
        override val warpLinear = binding.warpLinear
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: FragmentAddPoolBinding) : FragmentAddPoolUnionBinding() {
        override var vm: AddPoolVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val srl = binding.srl
        override val rv = binding.rv
        override val llContainer = binding.llContainer
        override val linOpenLaunchSelectCom = binding.linOpenLaunchSelectCom
        override val ivNoOrder = binding.ivNoOrder
        override val tvTotalNumber = binding.tvTotalNumber
        override val ivOpenLaunchSelectCom = binding.ivOpenLaunchSelectCom
        override val llConfirmGet = binding.llConfirmGet
        override val warpLinear = binding.warpLinear
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
