package com.midea.prestorage.function.inv

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.SortInfoItem
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivitySortGoodsDetailBinding

class SortGoodsDetailActivity : BaseViewModelActivity<SortGoodsDetailVM>() {
    private lateinit var binding: ActivitySortGoodsDetailUnionBinding
    private lateinit var adapter: ContainerPickFourAdapter

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivitySortGoodsDetailUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_sort_goods_detail_care
                )
            )
        } else {
            ActivitySortGoodsDetailUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_sort_goods_detail
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        vm = ViewModelProvider(this, ViewModelProvider.AndroidViewModelFactory(application))
            .get(SortGoodsDetailVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        vm.showDatas.observe(this, Observer<MutableList<SortInfoItem>> {
            showData(it)
        })

        initData()
        initRecycle()
    }

    fun initData() {
        vm.containerCode.set(intent.getStringExtra("containerCode"))
    }

    private fun initRecycle() {
        adapter = ContainerPickFourAdapter(vm)
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter
    }

    fun showData(data: MutableList<SortInfoItem>) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
    }

    override fun onResume() {
        super.onResume()
        vm.initList()
    }

    class ContainerPickFourAdapter(private val vm: SortGoodsDetailVM?) :
        CommonAdapter<SortInfoItem>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_sort_goods_detail_care else R.layout.item_sort_goods_detail) {
        override fun convert(holder: BaseViewHolder?, item: SortInfoItem?) {
            super.convert(holder, item)

        }
    }
}