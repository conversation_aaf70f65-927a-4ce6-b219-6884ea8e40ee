package com.midea.prestorage.function.containerpick

import android.os.Bundle
import android.view.WindowManager
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.ContainerPickDetailListV2
import com.midea.prestoragesaas.databinding.ActivityOutContainerPickFourBinding

class ContainerPickFourActivity : BaseActivity() {

    private lateinit var binding: ActivityOutContainerPickFourBinding
    private var vm = ContainerPickFourVM(this)
    val adapter = ContainerPickFourAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_out_container_pick_four)
        binding.vm = vm

        initRecycle()
    }

    override fun onResume() {
        super.onResume()
        vm.init()
    }

    private fun initRecycle() {
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun showData(data: MutableList<ContainerPickDetailListV2>) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
    }

    class ContainerPickFourAdapter :
        CommonAdapter<ContainerPickDetailListV2>(R.layout.item_container_four) {
        override fun convert(holder: BaseViewHolder?, item: ContainerPickDetailListV2?) {
            super.convert(holder, item)

            if (item?.lotAtt01.isNullOrEmpty() && item?.lotAtt05.isNullOrEmpty()) {
                holder?.setGone(R.id.ll_bottom, true)
            } else {
                holder?.setGone(R.id.ll_bottom, false)
            }
        }
    }
}