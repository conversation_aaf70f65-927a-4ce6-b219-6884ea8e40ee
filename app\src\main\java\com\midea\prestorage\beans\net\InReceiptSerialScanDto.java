package com.midea.prestorage.beans.net;


import com.midea.prestorage.function.instorage.response.InOrderDetail;
import com.midea.prestorage.function.instorage.response.InReceiptSerial;
import com.midea.prestorage.function.inv.response.BarcodeLotDto;
import com.midea.prestorage.function.inv.response.ItemRfVO;

import java.util.List;

public class InReceiptSerialScanDto {

    private String shipmentCode;

    private String receiptCode;
    /**
     * 波次号
     */
    private String waveNo;
    /**
     * 仓库编码
     */
    private String whCode;
    /**
     * 序列号
     */
    private String serialNo;
    /**
     * 客户商品编码
     */
    private String custItemCode;
    /**
     * 可用数量
     */
    private Integer qty;

    private Double totalQty;
    /**
     * 1-规则解析的条码，2-LMS接口解析的条码，3-69码 ，4-商品编码
     */
    private Integer barcodeType;
    /**
     * 是否不扫码  （rf实际没用到这个）
     */
    private String userdefined15;
    /**
     * 条码类型0 实物条码 1 虚拟条码，默认实物条码0
     */
    private String serialType;

    /**
     * 容器号
     */
    private String containerCode;

    private Integer scanQty;


    /**
     * 剩余不需扫码数
     */
    private Double unScanQty;

    // 收货渠道号 rf端 固定是101
    private String receiptChannel;

    private BarcodeLotDto barcodeLotDto;

    private String itemName;

    private String barcode; //只用于请求

    private String receiptFlag;

    private String itemCode;


    private List<InOrderDetail> inReceiptDetailInfoVOS;


    public List<InOrderDetail> getInReceiptDetailInfoVOS() {
        return inReceiptDetailInfoVOS;
    }

    public void setInReceiptDetailInfoVOS(List<InOrderDetail> inReceiptDetailInfoVOS) {
        this.inReceiptDetailInfoVOS = inReceiptDetailInfoVOS;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }



    public String getReceiptFlag() {
        return receiptFlag;
    }

    public void setReceiptFlag(String receiptFlag) {
        this.receiptFlag = receiptFlag;
    }

    /**
     * 识别的条码列表
     */
    private List<SkuBarcodeInfoDto> skuBarcodeInfoDtos;

    private List<InReceiptSerial> inReceiptSerials;

    private List<OutShipmentSerial> outShipmentSerials;

    private List<String> custItemCodes;

    private List<String> unScanItemCodes;


    public List<String> getUnScanItemCodes() {
        return unScanItemCodes;
    }

    public void setUnScanItemCodes(List<String> unScanItemCodes) {
        this.unScanItemCodes = unScanItemCodes;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public List<String> getCustItemCodes() {
        return custItemCodes;
    }

    public void setCustItemCodes(List<String> custItemCodes) {
        this.custItemCodes = custItemCodes;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getReceiptChannel() {
        return receiptChannel;
    }

    public void setReceiptChannel(String receiptChannel) {
        this.receiptChannel = receiptChannel;
    }


    public BarcodeLotDto getBarcodeLotDto() {
        return barcodeLotDto;
    }

    public void setBarcodeLotDto(BarcodeLotDto barcodeLotDto) {
        this.barcodeLotDto = barcodeLotDto;
    }

    public List<SkuBarcodeInfoDto> getSkuBarcodeInfoDtos() {
        return skuBarcodeInfoDtos;
    }

    public void setSkuBarcodeInfoDtos(List<SkuBarcodeInfoDto> skuBarcodeInfoDtos) {
        this.skuBarcodeInfoDtos = skuBarcodeInfoDtos;
    }

    public List<InReceiptSerial> getInReceiptSerials() {
        return inReceiptSerials;
    }

    public void setInReceiptSerials(List<InReceiptSerial> inReceiptSerials) {
        this.inReceiptSerials = inReceiptSerials;
    }

    public List<OutShipmentSerial> getOutShipmentSerials() {
        return outShipmentSerials;
    }

    public void setOutShipmentSerials(List<OutShipmentSerial> outShipmentSerials) {
        this.outShipmentSerials = outShipmentSerials;
    }

    public String getShipmentCode() {
        return shipmentCode;
    }

    public void setShipmentCode(String shipmentCode) {
        this.shipmentCode = shipmentCode;
    }

    public String getReceiptCode() {
        return receiptCode;
    }

    public void setReceiptCode(String receiptCode) {
        this.receiptCode = receiptCode;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public Double getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(Double totalQty) {
        this.totalQty = totalQty;
    }

    public Integer getBarcodeType() {
        return barcodeType;
    }

    public void setBarcodeType(Integer barcodeType) {
        this.barcodeType = barcodeType;
    }

    public String getUserdefined15() {
        return userdefined15;
    }

    public void setUserdefined15(String userdefined15) {
        this.userdefined15 = userdefined15;
    }

    public String getSerialType() {
        return serialType;
    }

    public void setSerialType(String serialType) {
        this.serialType = serialType;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public Integer getScanQty() {
        return scanQty;
    }

    public void setScanQty(Integer scanQty) {
        this.scanQty = scanQty;
    }


    public Double getUnScanQty() {
        return unScanQty;
    }

    public void setUnScanQty(Double unScanQty) {
        this.unScanQty = unScanQty;
    }
}