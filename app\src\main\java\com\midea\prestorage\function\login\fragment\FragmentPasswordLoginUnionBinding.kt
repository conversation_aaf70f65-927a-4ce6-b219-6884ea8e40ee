package com.midea.prestorage.function.login.fragment

import android.view.View
import android.widget.EditText
import com.midea.prestoragesaas.databinding.FragmentPasswordLoginBinding
import com.midea.prestoragesaas.databinding.FragmentPasswordLoginCareBinding

sealed class FragmentPasswordLoginUnionBinding {
    abstract var vm: PasswordLoginVM?
    abstract val root: View
    abstract val etPassword: EditText

    class V2(val binding: FragmentPasswordLoginCareBinding) : FragmentPasswordLoginUnionBinding() {
        override var vm: PasswordLoginVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val etPassword = binding.etPassword
    }

    class V1(val binding: FragmentPasswordLoginBinding) : FragmentPasswordLoginUnionBinding() {
        override var vm: PasswordLoginVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val etPassword = binding.etPassword
    }
}
