package com.midea.prestorage.function.containerpick

import CheckUtil
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.net.ContainerPickSecondPrintList
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.printer.BluetoothConnectBack
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody

class ContainerReprintVM(val activity: ContainerReprintActivity) {

    var orderNo = activity.intent.getStringExtra("receiptCode")
    val title = ObservableField<String>()
    val isDataOk = ObservableField(false)

    val isPrintOk = ObservableField(false)

    private val blueBack = object : BluetoothConnectBack {
        override fun success() {
            activity.waitingDialogHelp.hidenDialog()
            isPrintOk.set(Printer.connectUtils?.isPrintOk())
        }

        override fun fail() {
            isPrintOk.set(Printer.connectUtils?.isPrintOk())
            AppUtils.showToast(activity, "打印机连接失败，请重启打印机!")
        }

        override fun connect() {
            bluetoothOpen()
        }
    }

    fun bluetoothOpen(isAuto: Boolean = true) {
        if (!Printer.isPrintOk()) {
            Printer.openBluetooth(activity, blueBack, isAuto)
        } else {
            isPrintOk.set(true)
        }
    }

    init {
        title.set("已拣列表")
        queryOrder()
    }

    private fun queryOrder() {
        activity.waitingDialogHelp.showDialogUnCancel()
        val param = mutableMapOf(
            "whCode" to Constants.whInfo?.whCode,
            "pickUserCode" to Constants.userInfo?.name
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .queryReprintInfo(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<MutableList<ContainerPickSecondPrintList>>(activity) {
                override fun success(data: MutableList<ContainerPickSecondPrintList>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    data?.let {
                        activity.adapter.setNewInstance(data)
                        isDataOk.set(!data.isNullOrEmpty())
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    private fun printInfoSaveOrUpdate(
        containerCodeList: MutableList<String>
    ) {
        activity.waitingDialogHelp.showDialogUnCancel()
        val param = mutableMapOf(
            "whCode" to Constants.whInfo?.whCode,
            "printCode" to "pick_container_tag",
            "printType" to "pick",
            "containerCodeList" to containerCodeList
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .printInfoSaveOrUpdate(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    queryOrder()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun onReprint() {
        if (CheckUtil.isFastDoubleClick()) {
            val results = activity.adapter.returnBeans
            if (results.isEmpty()) {
                ToastUtils.getInstance()
                    .showErrorToastWithSound(activity, "请先选择拣货明细")
            } else {
                if (Printer.isPrintOk()) {
                    var containerCodeList = mutableListOf<String>()
                    results?.forEach { it ->
                        it.pickContainerCode?.let { it2 ->
                            containerCodeList.add(it2)
                        }
                    }
                    printInfoSaveOrUpdate(
                        containerCodeList.distinct()?.toMutableList()
                    )
                    Printer.printContainerNew(results)
                } else {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, "请先连接蓝牙打印机")
                }
            }
        }
    }

    fun back() {
        activity.finish()
    }
}