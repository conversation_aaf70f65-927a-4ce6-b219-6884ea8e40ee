package com.midea.prestorage.function.pickhastaskdetail

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestoragesaas.databinding.ActivityPickHasTaskDetailBinding
import com.midea.prestorage.function.inv.response.InvStockTakeTaskDetailHasPick
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import java.math.BigDecimal


class PickHasTaskDetailActivity : BaseViewModelActivity<PickHasTaskDetailVM>() {

    companion object {

        fun newIntent(context: Context, waveNo: String): Intent {
            val intent = Intent(context, PickHasTaskDetailActivity::class.java)
            intent.putExtra("waveNo", waveNo)
            return intent
        }
    }

    private lateinit var binding: ActivityPickHasTaskDetailBinding
    private lateinit var dataListAdapter: PickHasTaskDetailAdapter

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_pick_has_task_detail)
        vm = ViewModelProvider.AndroidViewModelFactory(application)
            .create(PickHasTaskDetailVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        initData()
        initRecycleView()//初始化列表
        initObjser()//初始化监听
    }

    //初始化监听
    private fun initObjser() {
        binding.vm?.isUpdataList?.observe(this, Observer<Boolean> {
            if (it) {
                dataListAdapter.setNewInstance(binding.vm?.returnDataList?.value)
                dataListAdapter.notifyDataSetChanged()
            }
        })
    }

    //初始化列表
    private fun initRecycleView() {
        dataListAdapter = PickHasTaskDetailAdapter()
        binding.rv.layoutManager = LinearLayoutManager(this)
        binding.rv.adapter = dataListAdapter
    }


    //初始化数据
    private fun initData() {
        val waveNo = intent.getStringExtra("waveNo")
        binding.vm?.waveNo?.value = waveNo

        binding.vm?.webRequest()
    }

    class PickHasTaskDetailAdapter:
        CommonAdapter<InvStockTakeTaskDetailHasPick>(R.layout.activity_pick_has_task_detail_item) {

        override fun convert(holder: BaseViewHolder?, item: InvStockTakeTaskDetailHasPick?) {
            super.convert(holder, item)

            when (item?.lotAtt04) {
                "Y" -> holder?.setBackgroundResource(R.id.tv_status, R.drawable.bg_bt_green)
                else -> holder?.setBackgroundResource(
                    R.id.tv_status,
                    R.drawable.bg_bt_red_no_circle
                )
            }
        }
    }
}