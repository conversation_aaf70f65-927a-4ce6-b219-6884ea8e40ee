package com.midea.prestorage.function.containerpick

import android.os.Bundle
import android.view.WindowManager
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.function.containerpick.adapter.CombinedPickDeatilAdapter
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityCombinedPickDeatilBinding

class CombinedPickDeatilActivity : BaseActivity() {

    lateinit var binding: ActivityCombinedPickDeatilUnionBinding
    private var vm = CombinedPickDeatilVM(this)
    val adapterNumber = CombinedPickDeatilAdapter(this)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityCombinedPickDeatilUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_combined_pick_deatil_care
                )
            )
        } else {
            ActivityCombinedPickDeatilUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_combined_pick_deatil
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        binding.vm = vm

        binding.gridNumber.adapter = adapterNumber

        vm.init()
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }


}