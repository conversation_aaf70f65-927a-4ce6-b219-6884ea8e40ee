package com.midea.prestorage.function.inv.response;

public class InvStocktakeHeader {

    /**
     * 仓库
     */
    private String whCode;
	
    /**
     * 盘点单号
     */
    private String stocktakeCode;
	
    /**
     * 货主
     */
    private String ownerCode;
	
    /**
     * 盘点类型（100:计划盘点200:实时盘点）
     */
    private String stocktakeType;
	
    /**
     * 盘点名称
     */
    private String stocktakeName;
	
    /**
     * 状态
     */
    private String status;
	
    /**
     * 是否盲盘(0：否 1：是)
     */
    private Integer isDarkCheck;
	
    /**
     * 是否库存状态盘点(0：否 1：是)
     */
    private Integer isLot04Check;
	
    /**
     * 是否生成空库位(0：否 1：是)
     */
    private Integer isEmptyLocationCheck;
	
    /**
     * 是否需要复盘(0：否 1：是)
     */
    private Integer isRepeatCheck;
	
    /**
     * 是否抽盘(0：否 1：是)
     */
    private Integer isWholeCheck;
	
    /**
     * 抽盘比例
     */
    private Integer stocktakePercent;
	
    /**
     * 空库位比例	
     */
    private Integer emptyLocationPercent;
	
    /**
     * 非空库位比例
     */
    private Integer notEmptyLocationPercent;
	
    /**
     * 总货位数
     */
    private String totalLoc;
	
    /**
     * 总SKU数
     */
    private String totalSku;
	
    /**
     * 总数量
     */
    private String totalQty;
	
    /**
     * 关闭人
     */
    private String closedBy;
	
    /**
     * 关闭时间
     */
    private String closedTime;
	
    /**
     * 库位
     */
    private String locCode;


    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getStocktakeCode() {
        return stocktakeCode;
    }

    public void setStocktakeCode(String stocktakeCode) {
        this.stocktakeCode = stocktakeCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getStocktakeType() {
        return stocktakeType;
    }

    public void setStocktakeType(String stocktakeType) {
        this.stocktakeType = stocktakeType;
    }

    public String getStocktakeName() {
        return stocktakeName;
    }

    public void setStocktakeName(String stocktakeName) {
        this.stocktakeName = stocktakeName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getIsDarkCheck() {
        return isDarkCheck;
    }

    public void setIsDarkCheck(Integer isDarkCheck) {
        this.isDarkCheck = isDarkCheck;
    }

    public Integer getIsLot04Check() {
        return isLot04Check;
    }

    public void setIsLot04Check(Integer isLot04Check) {
        this.isLot04Check = isLot04Check;
    }

    public Integer getIsEmptyLocationCheck() {
        return isEmptyLocationCheck;
    }

    public void setIsEmptyLocationCheck(Integer isEmptyLocationCheck) {
        this.isEmptyLocationCheck = isEmptyLocationCheck;
    }

    public Integer getIsRepeatCheck() {
        return isRepeatCheck;
    }

    public void setIsRepeatCheck(Integer isRepeatCheck) {
        this.isRepeatCheck = isRepeatCheck;
    }

    public Integer getIsWholeCheck() {
        return isWholeCheck;
    }

    public void setIsWholeCheck(Integer isWholeCheck) {
        this.isWholeCheck = isWholeCheck;
    }

    public Integer getStocktakePercent() {
        return stocktakePercent;
    }

    public void setStocktakePercent(Integer stocktakePercent) {
        this.stocktakePercent = stocktakePercent;
    }

    public Integer getEmptyLocationPercent() {
        return emptyLocationPercent;
    }

    public void setEmptyLocationPercent(Integer emptyLocationPercent) {
        this.emptyLocationPercent = emptyLocationPercent;
    }

    public Integer getNotEmptyLocationPercent() {
        return notEmptyLocationPercent;
    }

    public void setNotEmptyLocationPercent(Integer notEmptyLocationPercent) {
        this.notEmptyLocationPercent = notEmptyLocationPercent;
    }

    public String getTotalLoc() {
        return totalLoc;
    }

    public void setTotalLoc(String totalLoc) {
        this.totalLoc = totalLoc;
    }

    public String getTotalSku() {
        return totalSku;
    }

    public void setTotalSku(String totalSku) {
        this.totalSku = totalSku;
    }

    public String getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(String totalQty) {
        this.totalQty = totalQty;
    }

    public String getClosedBy() {
        return closedBy;
    }

    public void setClosedBy(String closedBy) {
        this.closedBy = closedBy;
    }

    public String getClosedTime() {
        return closedTime;
    }

    public void setClosedTime(String closedTime) {
        this.closedTime = closedTime;
    }

    public String getLocCode() {
        return locCode;
    }

    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }
}
