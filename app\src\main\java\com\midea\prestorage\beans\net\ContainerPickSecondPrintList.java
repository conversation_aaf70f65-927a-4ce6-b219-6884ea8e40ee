package com.midea.prestorage.beans.net;

import androidx.annotation.Nullable;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.beans.base.BaseItemForPopup;

import java.math.BigDecimal;


public class ContainerPickSecondPrintList extends BaseItemForPopup{

    @ShowAnnotation
    private String pickContainerCode;
    private String waveNo;
    @ShowAnnotation
    private String csQty;
    @ShowAnnotation
    private String eaQty;
    private String weight;
    private String volumn;
    @ShowAnnotation
    private String shipToCustomerName;
    @ShowAnnotation
    private String pickUserInfo;
    private String shippingLoc;
    @ShowAnnotation
    private String skuCount;
    private BigDecimal printCount = BigDecimal.ZERO;
    private String lastPrintDate;
    @Nullable
    private String upSystemNote;
    @Nullable
    private String custOrderNo;

    @Nullable
    public String getCustOrderNo() {
        return custOrderNo;
    }

    public void setCustOrderNo(@Nullable String custOrderNo) {
        this.custOrderNo = custOrderNo;
    }

    @Nullable
    public String getUpSystemNote() {
        return upSystemNote;
    }

    public void setUpSystemNote(@Nullable String upSystemNote) {
        this.upSystemNote = upSystemNote;
    }

    public BigDecimal getPrintCount() {
        return printCount;
    }

    public void setPrintCount(BigDecimal printCount) {
        this.printCount = printCount;
    }

    public String getLastPrintDate() {
        return lastPrintDate;
    }

    public void setLastPrintDate(String lastPrintDate) {
        this.lastPrintDate = lastPrintDate;
    }

    public String getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(String skuCount) {
        this.skuCount = skuCount;
    }

    public String getPickContainerCode() {
        return pickContainerCode;
    }

    public void setPickContainerCode(String pickContainerCode) {
        this.pickContainerCode = pickContainerCode;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getCsQty() {
        return csQty;
    }

    public void setCsQty(String csQty) {
        this.csQty = csQty;
    }

    public String getEaQty() {
        return eaQty;
    }

    public void setEaQty(String eaQty) {
        this.eaQty = eaQty;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getVolumn() {
        return volumn;
    }

    public void setVolumn(String volumn) {
        this.volumn = volumn;
    }

    public String getShipToCustomerName() {
        return shipToCustomerName;
    }

    public void setShipToCustomerName(String shipToCustomerName) {
        this.shipToCustomerName = shipToCustomerName;
    }

    public String getPickUserInfo() {
        return pickUserInfo;
    }

    public void setPickUserInfo(String pickUserInfo) {
        this.pickUserInfo = pickUserInfo;
    }

    public String getShippingLoc() {
        return shippingLoc;
    }

    public void setShippingLoc(String shippingLoc) {
        this.shippingLoc = shippingLoc;
    }
}