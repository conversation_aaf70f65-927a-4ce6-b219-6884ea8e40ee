package com.midea.prestorage.function.login.fragment

import android.content.Intent
import android.text.TextUtils
import androidx.databinding.ObservableField
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.App
import com.midea.prestorage.beans.setting.ImplWarehouse
import com.midea.prestorage.beans.setting.LoginInfo
import com.midea.prestorage.function.login.LoginActivity
import com.midea.prestorage.function.mainyg.MainYgActivity
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.DbUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable

class PasswordLoginVM(val fragment: PasswordLoginFragment) {
    val username = ObservableField<String>("")
    val password = ObservableField<String>("")
    val isRemember = ObservableField(false)
    val db = DbUtils.db

    init {
        initData()
    }

    private fun initData() {
        Observable.create<LoginInfo> {
            Constants.userInfo =
                db.selector(LoginInfo::class.java).orderBy("isFirst", true).findFirst()
            if (Constants.userInfo == null) {
                Constants.userInfo = LoginInfo()
            }
            Constants.whInfo =
                db.selector(ImplWarehouse::class.java).findFirst()

            it.onNext(Constants.userInfo!!)
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<LoginInfo> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: LoginInfo) {
                    if (!TextUtils.isEmpty(t.account)) {
                        username.set(t.account)
                        password.set(t.pwd)
                        isRemember.set(t.isRemember)
                    }
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    val userNameEnter = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                fragment.pwdFocus()
            }
        }
    }

    fun cleanUserName() {
        username.set("")
    }

    fun cleanPassWord() {
        password.set("")
    }
}