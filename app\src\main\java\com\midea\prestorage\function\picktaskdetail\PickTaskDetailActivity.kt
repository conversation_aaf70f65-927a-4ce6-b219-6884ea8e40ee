package com.midea.prestorage.function.picktaskdetail


import android.content.Context
import android.content.Intent
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestoragesaas.databinding.ActivityPickTaskDetailBinding
import com.midea.prestoragesaas.databinding.PickTaskTypeMenuBinding
import com.midea.prestorage.function.inv.response.InvStockTakeTaskDetail
import com.midea.prestorage.function.inv.response.InvStockTakeTaskHeader
import com.midea.prestorage.function.outstorage.OutStorageNewActivity
import com.midea.prestorage.function.pickhastaskdetail.PickHasTaskDetailActivity
import com.midea.prestorage.function.picktask.PickTaskDialog
import com.midea.prestorage.function.picktaskdetail.PickTaskDetailVm.Companion.checkLotAtt01
import com.midea.prestorage.function.picktaskdetail.PickTaskDetailVm.Companion.checkLotAtt02
import com.midea.prestorage.function.picktaskdetail.PickTaskDetailVm.Companion.checkLotAtt03
import com.midea.prestorage.function.picktaskdetail.PickTaskDetailVm.Companion.checkLotAtt05
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.isNull
import java.math.BigDecimal


class PickTaskDetailActivity : BaseViewModelActivity<PickTaskDetailVm>() {

    companion object {
        fun newIntent(
            context: Context,
            waveNo: String
        ): Intent {
            val intent = Intent(context, PickTaskDetailActivity::class.java)
            intent.putExtra("waveNo", waveNo)
            return intent
        }
    }

    private lateinit var binding: ActivityPickTaskDetailBinding
    private lateinit var dataListAdapter: PickTaskDetailAdapter
    private lateinit var pickTaskDialog: PickTaskDialog
    private lateinit var pickTaskFinishDialog: PickTaskFinishDialog
    lateinit var popMenuBinding: PickTaskTypeMenuBinding
    lateinit var popupMenuWindow: PopupWindow

    //执行刷新
    fun setRefresh() {
        //清空数据
        vm.returnData?.clear()
        dataListAdapter.setNewInstance(vm.returnData)
        dataListAdapter.notifyDataSetChanged()

        //网络请求重新获取
        vm.webGetData()
    }

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_pick_task_detail)
        vm = ViewModelProvider.AndroidViewModelFactory(application)
            .create(PickTaskDetailVm::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        initData()

        initObserveListen()
        initRecycleView()
        initRadiGroup()
        initDialog()
        initPopMenu()

        binding.edLocal.requestFocus()
    }

    fun initDialog() {
        pickTaskDialog = PickTaskDialog()
        pickTaskDialog.setPickTaskDialogBack(object : PickTaskDialog.PickTaskDialogBack {
            override fun pickTaskDialogBack() {
                vm.showNotification("拣货成功", true)
                setRefresh()
            }
        })

        pickTaskFinishDialog = PickTaskFinishDialog()
        pickTaskFinishDialog.setPickTaskDialogBack(object :
            PickTaskFinishDialog.PickTaskFinishDialogBack {
            override fun pickTaskFinishDialogBack() {
                vm.showNotification("拣货成功", true)
                setRefresh()
            }
        })
    }

    private fun initPopMenu() {

        val popView = LayoutInflater.from(this).inflate(R.layout.pick_task_type_menu, null)
        popMenuBinding = DataBindingUtil.bind(popView)!!
        popMenuBinding.vm = binding.vm

        popupMenuWindow = PopupWindow(
            popView,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        popupMenuWindow.setBackgroundDrawable(
            ColorDrawable(
                ContextCompat.getColor(
                    this,
                    R.color.colorOutlineGrey
                )
            )
        )

        popupMenuWindow.isOutsideTouchable = true


        popMenuBinding.btnSearchType1.setOnClickListener {
            SPUtils.put(application, checkLotAtt01, !(SPUtils[checkLotAtt01, false] as Boolean))
            popMenuBinding.cbSearchType1.isChecked = (SPUtils[checkLotAtt01, false] as Boolean)
        }
        popMenuBinding.btnSearchType2.setOnClickListener {
            SPUtils.put(application, checkLotAtt02, !(SPUtils[checkLotAtt02, false] as Boolean))
            popMenuBinding.cbSearchType2.isChecked = (SPUtils[checkLotAtt02, false] as Boolean)
        }
        popMenuBinding.btnSearchType3.setOnClickListener {
            SPUtils.put(application, checkLotAtt03, !(SPUtils[checkLotAtt03, false] as Boolean))
            popMenuBinding.cbSearchType3.isChecked = (SPUtils[checkLotAtt03, false] as Boolean)
        }
        popMenuBinding.btnSearchType4.setOnClickListener {
            SPUtils.put(application, checkLotAtt05, !(SPUtils[checkLotAtt05, false] as Boolean))
            popMenuBinding.cbSearchType4.isChecked = (SPUtils[checkLotAtt05, false] as Boolean)
        }

        popupMenuWindow.setOnDismissListener {
            setRefresh()
        }

    }

    private fun initData() {
        binding.vm?.waveNo?.value = intent.getStringExtra("waveNo")
        binding.vm?.taskDetailIdList = intent.getStringArrayListExtra("taskDetailIdList")?.toMutableList() ?: mutableListOf()
    }

    //初始化radiogroup
    private fun initRadiGroup() {
        binding.radioGroup.setOnCheckedChangeListener { group, checkedId ->
            if (checkedId == R.id.by_good) {
                binding.vm?.orderByGood?.value = 1
                binding.vm?.returnData?.forEach {
                    it.orderByGood = 1
                }
            } else {
                binding.vm?.orderByGood?.value = 0
                binding.vm?.returnData?.forEach {
                    it.orderByGood = 0
                }
            }

            vm.returnData?.sort()//排序
            dataListAdapter.setNewInstance(vm.returnData)
            dataListAdapter.notifyDataSetChanged()
        }
    }

    //初始化回收列表
    private fun initRecycleView() {
        dataListAdapter = PickTaskDetailAdapter()
        binding.rvPickTaskDetail.apply {
            layoutManager = LinearLayoutManager(context) //设置线性布局
            adapter = dataListAdapter
        }
        dataListAdapter.setOnItemClickListener { adapter, _, position ->
            pickTaskDialog.setData(adapter.data[position] as InvStockTakeTaskDetail)
            pickTaskDialog.showNow(supportFragmentManager, "PickTaskDialog")
        }

        val bean = intent.getSerializableExtra("beans") as InvStockTakeTaskHeader?
        if (bean.isNull()) {
            setRefresh()
        } else {
            vm.dealData(bean)
        }
    }

    private fun initObserveListen() {
        vm.isFilter.observe(this, Observer<Boolean> { item ->
            if (item) {
                vm.returnData = vm.oldReturnData//  先恢复

                //过滤处理
                vm.returnData =
                    vm.returnData?.filter { innerItem ->
                        innerItem.custItemCode == vm.filterValue.value.toString() || innerItem.fromLoc ==
                                vm.filterValue.value.toString() || innerItem.barcode ==
                                vm.filterValue.value.toString()
                    }?.toMutableList()

                //如果为空就获取老数据
                if (vm.filterValue.value?.trim().toString() == "") {
                    vm.returnData = vm.oldReturnData
                }

                //标志是否按商品排序,如果按商品排序就重新过滤
                if (binding.byGood.isChecked) {
                    binding.vm?.orderByGood?.value = 1
                    binding.vm?.returnData?.forEach {
                        it.orderByGood = 1
                    }
                    binding.vm?.returnData?.sort()
                }

                //刷新数据
                dataListAdapter.setNewInstance(vm.returnData)
                dataListAdapter.notifyDataSetChanged()
            }
        })

        binding.vm?.isOutStorageNew?.observe(this, Observer<Boolean> {
            if (it) {
                val intent = Intent(this, OutStorageNewActivity::class.java)
                intent.putExtra("waveNo", binding.vm?.waveNo?.value as String)
                startActivity(intent)
            }
        })

        binding.vm?.notificationDataListChange?.observe(this, Observer<Boolean> {
            if (it) {
                vm.returnData?.sort()//排序
                dataListAdapter.apply {
                    setNewInstance(binding.vm?.returnData)
                    notifyDataSetChanged()
                }
                vm.isFilter.value = true
            }
        })

        binding.vm?.isPickFinish?.observe(this, Observer<Boolean> {
            if (it && vm.totalString.value.toString().isNotEmpty()
                && vm.returnData != null
                && vm.returnData!!.size > 0
            ) {//检测数量和数据
                val results = vm.totalString.value!!.split("/")
                pickTaskFinishDialog.setData(
                    vm.returnData!!,
                    BigDecimal(results[1].toInt() - results[0].toInt())
                )
                pickTaskFinishDialog.showNow(supportFragmentManager, "PickTaskDialog")
            }
        })

        binding.vm?.isHasTaskPick?.observe(this, Observer<Boolean> {
            if (it) {
                val intent = PickHasTaskDetailActivity.newIntent(
                    this,
                    binding.vm?.waveNo?.value as String
                )
                startActivity(intent)
            }
        })

        vm.isShowSearchTypeMenu.observe(this, Observer<Boolean> { item ->
            if (item) {
                popMenuBinding.cbSearchType1.isChecked = SPUtils[checkLotAtt01, false] as Boolean
                popMenuBinding.cbSearchType2.isChecked = SPUtils[checkLotAtt02, false] as Boolean
                popMenuBinding.cbSearchType3.isChecked = SPUtils[checkLotAtt03, false] as Boolean
                popMenuBinding.cbSearchType4.isChecked = SPUtils[checkLotAtt05, false] as Boolean
                popupMenuWindow.showAsDropDown(binding.titleBtnMore)
            }
        })
    }


    class PickTaskDetailAdapter :
        CommonAdapter<InvStockTakeTaskDetail>(R.layout.activity_pick_task_list_item_detail) {

        override fun convert(holder: BaseViewHolder?, item: InvStockTakeTaskDetail?) {
            super.convert(holder, item)

            when (item?.lotAtt04) {
                "Y" -> holder?.setBackgroundResource(R.id.tv_status, R.drawable.bg_bt_green)
                else -> holder?.setBackgroundResource(
                    R.id.tv_status,
                    R.drawable.bg_bt_red_no_circle
                )
            }

            if (!item?.barcode.isNullOrEmpty()) {
                if (!item?.custItemCode.isNullOrEmpty() && item?.custItemCode == item?.barcode) {
                    holder?.setText(R.id.tv_barcode, "")
                } else {
                    holder?.setText(R.id.tv_barcode, "(" + item?.barcode + ")")
                }
            } else {
                holder?.setText(R.id.tv_barcode, "")
            }

            var line = 0
            if (SPUtils[checkLotAtt01, false] as Boolean) {
                line += 1
                holder?.setGone(R.id.tvType1, false)
                holder?.setGone(R.id.tvSearchType1, false)
                holder?.setText(R.id.tvType1, "生产日期:")
                if (item?.lotAtt01.isNullOrEmpty()) {
                    holder?.setText(R.id.tvSearchType1, "")
                } else {
                    holder?.setText(R.id.tvSearchType1, item?.lotAtt01)
                }
            }
            if (SPUtils[checkLotAtt02, false] as Boolean) {
                line += 1
                if (line == 1) {
                    holder?.setGone(R.id.tvType1, false)
                    holder?.setGone(R.id.tvSearchType1, false)
                    holder?.setText(R.id.tvType1, "失效日期:")
                    if (item?.lotAtt02.isNullOrEmpty()) {
                        holder?.setText(R.id.tvSearchType1, "")
                    } else {
                        holder?.setText(R.id.tvSearchType1, item?.lotAtt02)
                    }
                } else if (line == 2) {
                    holder?.setGone(R.id.tvType2, false)
                    holder?.setGone(R.id.tvSearchType2, false)
                    holder?.setText(R.id.tvType2, "失效日期:")
                    if (item?.lotAtt02.isNullOrEmpty()) {
                        holder?.setText(R.id.tvSearchType2, "")
                    } else {
                        holder?.setText(R.id.tvSearchType2, item?.lotAtt02)
                    }
                }
            }
            if (SPUtils[checkLotAtt03, false] as Boolean) {
                line += 1
                if (line == 1) {
                    holder?.setGone(R.id.tvType1, false)
                    holder?.setGone(R.id.tvSearchType1, false)
                    holder?.setText(R.id.tvType1, "入库日期:")
                    if (item?.lotAtt03.isNullOrEmpty()) {
                        holder?.setText(R.id.tvSearchType1, "")
                    } else {
                        holder?.setText(R.id.tvSearchType1, item?.lotAtt03)
                    }
                } else if (line == 2) {
                    holder?.setGone(R.id.tvType2, false)
                    holder?.setGone(R.id.tvSearchType2, false)
                    holder?.setText(R.id.tvType2, "入库日期:")
                    if (item?.lotAtt03.isNullOrEmpty()) {
                        holder?.setText(R.id.tvSearchType2, "")
                    } else {
                        holder?.setText(R.id.tvSearchType2, item?.lotAtt03)
                    }
                } else if (line == 3) {
                    holder?.setGone(R.id.tvType3, false)
                    holder?.setGone(R.id.tvSearchType3, false)
                    holder?.setText(R.id.tvType3, "入库日期:")
                    if (item?.lotAtt03.isNullOrEmpty()) {
                        holder?.setText(R.id.tvSearchType3, "")
                    } else {
                        holder?.setText(R.id.tvSearchType3, item?.lotAtt03)
                    }
                }
            }
            if (SPUtils[checkLotAtt05, false] as Boolean) {
                line += 1
                if (line == 1) {
                    holder?.setGone(R.id.tvType1, false)
                    holder?.setGone(R.id.tvSearchType1, false)
                    holder?.setText(R.id.tvType1, "批次:")
                    if (item?.lotAtt05.isNullOrEmpty()) {
                        holder?.setText(R.id.tvSearchType1, "")
                    } else {
                        holder?.setText(R.id.tvSearchType1, item?.lotAtt05)
                    }
                } else if (line == 2) {
                    holder?.setGone(R.id.tvType2, false)
                    holder?.setGone(R.id.tvSearchType2, false)
                    holder?.setText(R.id.tvType2, "批次:")
                    if (item?.lotAtt05.isNullOrEmpty()) {
                        holder?.setText(R.id.tvSearchType2, "")
                    } else {
                        holder?.setText(R.id.tvSearchType2, item?.lotAtt05)
                    }
                } else if (line == 3) {
                    holder?.setGone(R.id.tvType3, false)
                    holder?.setGone(R.id.tvSearchType3, false)
                    holder?.setText(R.id.tvType3, "批次:")
                    if (item?.lotAtt05.isNullOrEmpty()) {
                        holder?.setText(R.id.tvSearchType3, "")
                    } else {
                        holder?.setText(R.id.tvSearchType3, item?.lotAtt05)
                    }
                } else if (line == 4) {
                    holder?.setGone(R.id.tvType5, false)
                    holder?.setGone(R.id.tvSearchType5, false)
                    holder?.setText(R.id.tvType5, "批次:")
                    if (item?.lotAtt05.isNullOrEmpty()) {
                        holder?.setText(R.id.tvSearchType5, "")
                    } else {
                        holder?.setText(R.id.tvSearchType5, item?.lotAtt05)
                    }
                }
            }

            if (line > 0) {
                holder?.setGone(R.id.llType01, false)
                holder?.setGone(R.id.tvType1, false)
                holder?.setGone(R.id.tvSearchType1, false)
            } else {
                holder?.setGone(R.id.llType01, true)
                holder?.setGone(R.id.tvType1, true)
                holder?.setGone(R.id.tvSearchType1, true)
            }
            if (line > 1) {
                holder?.setGone(R.id.tvType2, false)
                holder?.setGone(R.id.tvSearchType2, false)
            } else {
                holder?.setGone(R.id.tvType2, true)
                holder?.setGone(R.id.tvSearchType2, true)
            }
            if (line > 2) {
                holder?.setGone(R.id.llType02, false)
                holder?.setGone(R.id.tvType3, false)
                holder?.setGone(R.id.tvSearchType3, false)
            } else {
                holder?.setGone(R.id.llType02, true)
                holder?.setGone(R.id.tvType3, true)
                holder?.setGone(R.id.tvSearchType3, true)
            }
            if (line > 3) {
                holder?.setGone(R.id.tvType5, false)
                holder?.setGone(R.id.tvSearchType5, false)
            } else {
                holder?.setGone(R.id.tvType5, true)
                holder?.setGone(R.id.tvSearchType5, true)
            }

        }
    }
}