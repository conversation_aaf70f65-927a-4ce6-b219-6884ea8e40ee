package com.midea.prestorage.beans.net;

import androidx.annotation.Nullable;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class SerialPickContainerInfo implements Serializable {

    private String id;
    private String createTime;
    private String updateTime;
    private String tenantCode;
    private String createUserCode;
    private String createUserName;
    private String updateUserCode;
    private String updateUserName;
    private String remark;
    private String version;
    private String deleteFlag;
    private String pageNo;
    private String pageSize;
    private String offset;
    private String orderBy;
    private String orderByType;
    private String ids;
    private String tenantCodes;
    private String count;
    private String startTime;
    private String endTime;
    private String pickContainerCode;
    private String packageType;
    private String containerCode;
    private String containerType;
    private String status;
    private String waveNo;
    private String closeContainerTime;
    private String sortBy;
    private String sortStartTime;
    private String sortEndTime;
    private String oqcBy;
    private String oqcStartTime;
    private String oqcEndTime;
    private String whCode;
    private String sourceSystem;
    private String pickUserCode;
    private String pickStartTime;
    private String msg;
    private int isNeedScan;
    private int resultCode;
    @Nullable
    private String pickUserName;
    @Nullable
    private BigDecimal totalQty;
    @Nullable
    private String taskCode;

    private List<SerialPickContainerActuresList> outPickContainerActures;

    @Nullable
    private List<SerialPickContainerActuresList> prePackageList;

    @Nullable
    public List<SerialPickContainerActuresList> getPrePackageList() {
        return prePackageList;
    }

    public void setPrePackageList(@Nullable List<SerialPickContainerActuresList> prePackageList) {
        this.prePackageList = prePackageList;
    }

    @Nullable
    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(@Nullable String taskCode) {
        this.taskCode = taskCode;
    }

    @Nullable
    public String getPickUserName() {
        return pickUserName;
    }

    public void setPickUserName(@Nullable String pickUserName) {
        this.pickUserName = pickUserName;
    }

    @Nullable
    public BigDecimal getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(@Nullable BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getPageNo() {
        return pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getOffset() {
        return offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderByType() {
        return orderByType;
    }

    public void setOrderByType(String orderByType) {
        this.orderByType = orderByType;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getTenantCodes() {
        return tenantCodes;
    }

    public void setTenantCodes(String tenantCodes) {
        this.tenantCodes = tenantCodes;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getPickContainerCode() {
        return pickContainerCode;
    }

    public void setPickContainerCode(String pickContainerCode) {
        this.pickContainerCode = pickContainerCode;
    }

    public String getPackageType() {
        return packageType;
    }

    public void setPackageType(String packageType) {
        this.packageType = packageType;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public String getContainerType() {
        return containerType;
    }

    public void setContainerType(String containerType) {
        this.containerType = containerType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getCloseContainerTime() {
        return closeContainerTime;
    }

    public void setCloseContainerTime(String closeContainerTime) {
        this.closeContainerTime = closeContainerTime;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public String getSortStartTime() {
        return sortStartTime;
    }

    public void setSortStartTime(String sortStartTime) {
        this.sortStartTime = sortStartTime;
    }

    public String getSortEndTime() {
        return sortEndTime;
    }

    public void setSortEndTime(String sortEndTime) {
        this.sortEndTime = sortEndTime;
    }

    public String getOqcBy() {
        return oqcBy;
    }

    public void setOqcBy(String oqcBy) {
        this.oqcBy = oqcBy;
    }

    public String getOqcStartTime() {
        return oqcStartTime;
    }

    public void setOqcStartTime(String oqcStartTime) {
        this.oqcStartTime = oqcStartTime;
    }

    public String getOqcEndTime() {
        return oqcEndTime;
    }

    public void setOqcEndTime(String oqcEndTime) {
        this.oqcEndTime = oqcEndTime;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getPickUserCode() {
        return pickUserCode;
    }

    public void setPickUserCode(String pickUserCode) {
        this.pickUserCode = pickUserCode;
    }

    public String getPickStartTime() {
        return pickStartTime;
    }

    public void setPickStartTime(String pickStartTime) {
        this.pickStartTime = pickStartTime;
    }

    public List<SerialPickContainerActuresList> getOutPickContainerActures() {
        return outPickContainerActures;
    }

    public void setOutPickContainerActures(List<SerialPickContainerActuresList> outPickContainerActures) {
        this.outPickContainerActures = outPickContainerActures;
    }

    public int getIsNeedScan() {
        return isNeedScan;
    }

    public void setIsNeedScan(int isNeedScan) {
        this.isNeedScan = isNeedScan;
    }

    public int getResultCode() {
        return resultCode;
    }

    public void setResultCode(int resultCode) {
        this.resultCode = resultCode;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}