package com.midea.prestorage.function.inv.dialog

import android.app.AlertDialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.EditText
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.OutStorageQuery
import com.midea.prestorage.function.inv.CountInTimeSettingVM
import com.midea.prestorage.function.inv.CountInTimeSettingVM.Companion.BOXES_NUM
import com.midea.prestorage.function.inv.CountInTimeSettingVM.Companion.quantityEntryMode
import com.midea.prestorage.function.inv.CountInTimeVM
import com.midea.prestorage.function.inv.response.InvStocktakeDetail
import com.midea.prestorage.function.inv.response.PackageRelation
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.FilterDigitTextWatcher
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogInputNumActualBinding
import java.math.BigDecimal

class InputNumActualDialog(
    var mContext: BaseActivity
) : AlertDialog(mContext) {

    var binding: DialogInputNumActualUnionBinding
    var deleteInfo: InvStocktakeDetail? = null
    var bean: OutStorageQuery? = null
    var inputBack: InputNumActualBack? = null
    var textWatcher: FilterDigitTextWatcher? = null
    lateinit var adapterNumber: TaskGridAdapter

    init {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window?.setGravity(Gravity.CENTER)
            window?.attributes?.run {
                gravity = Gravity.CENTER
            }
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_input_num_actual_care, null)
            setView(contentView)
            DialogInputNumActualUnionBinding.V2(DataBindingUtil.bind(contentView)!!)
        } else {
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_input_num_actual, null)
            setView(contentView)
            DialogInputNumActualUnionBinding.V1(DataBindingUtil.bind(contentView)!!)
        }
        binding.vm = InputNumActualDialogVM(this)

        setCanceledOnTouchOutside(true)
        if (textWatcher == null) {
            textWatcher = FilterDigitTextWatcher(binding.etNum, 0, true) {
                if ("只能输入正整数" == it) {
                    binding.etNum.setText(binding.etNum.text.toString().replace(".", ""))
                    if (!binding.etNum.text.isNullOrEmpty()) {
                        binding.etNum.setSelection(binding.etNum.text!!.length)
                    }
                }
                AppUtils.showToast(mContext, it)
            }
        }
        binding.etNum.removeTextChangedListener(textWatcher)
        binding.etNum.addTextChangedListener(textWatcher)
    }

    override fun show() {
        super.show()

        if (quantityEntryMode == BOXES_NUM) {
            binding.gridNumber.visibility = View.VISIBLE
            binding.etNum.visibility = View.GONE

            adapterNumber = TaskGridAdapter(mContext, deleteInfo?.isDecimal ?: 0)
            binding.gridNumber.adapter = adapterNumber
            val beans = deleteInfo?.packageRelationList
            beans?.forEach {
                it.num = null //每次弹窗都把数量置空
            }
            adapterNumber.addData(beans)
            adapterNumber.notifyDataSetChanged()
        } else {
            binding.gridNumber.visibility = View.GONE
            binding.etNum.visibility = View.VISIBLE
            binding.etNum.post {
                binding.etNum.requestFocus()
            }
        }

        if (deleteInfo == null) {
            textWatcher?.limitDecimalPlaces = 0
        } else if (deleteInfo?.isDecimal == null || deleteInfo?.isDecimal == 0) {
            textWatcher?.limitDecimalPlaces = 0
        } else if (deleteInfo?.isDecimal != 0) {
            textWatcher?.limitDecimalPlaces = 4
        }
        binding.vm!!.goodsNo.set("")
        binding.vm!!.itemCode.set("")
        binding.vm!!.itemCode69.set("")
        binding.vm!!.show()

        var str = ""
        var eaUnit = deleteInfo?.packageRelationList?.find { "EA" == it.cdprUnit }?.cdprDesc
        deleteInfo?.packageRelationList?.forEach {
            if ("EA" != it.cdprUnit) {
                str = if (str.isNullOrEmpty()) {
                    str + it.cdprQuantity + eaUnit + "/" + it.cdprDesc
                } else {
                    str + "，" + it.cdprQuantity + eaUnit + "/" + it.cdprDesc
                }
            }
        }
        binding.vm!!.explain.set(deleteInfo?.cdpaFormat ?: "")
    }

    interface InputNumActualBack {
        fun inputOk(data: InvStocktakeDetail, num: BigDecimal)
        fun inputFail()
    }

    class TaskGridAdapter(private val mContext: Context, private val isPoint: Int) : BaseAdapter() {
        private var beans: MutableList<PackageRelation> = mutableListOf()

        fun filterNonDigit(limitDecimalPlaces: Int, inputText: String): String {
            val sb = StringBuilder()
            var allowDot = limitDecimalPlaces > 0
            inputText.forEach { c ->
                if (Character.isDigit(c) || (c == '.' && allowDot)) {
                    sb.append(c)
                    if (c == '.') allowDot = false
                } else {
                    AppUtils.showToast(mContext, "只能输入正整数")
                }
            }
            return sb.toString()
        }

        fun filterNumber(limitDecimalPlaces: Int, input: String): String {
            val dotIndex = input.indexOf(".")
            return if (dotIndex == -1) {
                input
            } else {
                val integerPart = input.substring(0, dotIndex)
                var decimalPart = input.substring(dotIndex + 1)
                if (decimalPart.length > limitDecimalPlaces) {
                    AppUtils.showToast(mContext, "最高输入小数点后" + limitDecimalPlaces + "位")
                    decimalPart = decimalPart.substring(0, limitDecimalPlaces)
                    "$integerPart.$decimalPart"
                } else {
                    input
                }
            }
        }

        fun addData(data: MutableList<PackageRelation>?) {
            if (!data.isNullOrEmpty()) {
                beans = data
            }
        }

        override fun getCount(): Int {
            return beans.size
        }

        override fun getItem(position: Int): PackageRelation {
            return beans[position]
        }

        override fun getItemId(position: Int): Long {
            return position.toLong()
        }

        fun getData(): MutableList<PackageRelation> {
            return beans
        }

        override fun getView(
            position: Int,
            convertView: View?,
            parent: ViewGroup
        ): View {
            val viewHolder: ViewHolder?

            var convertView = convertView
            if (convertView == null) {
                convertView =
                    LayoutInflater.from(mContext)
                        .inflate(
                            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_plan_stock_num_care else R.layout.item_plan_stock_num,
                            parent,
                            false
                        )
                viewHolder = ViewHolder()
                viewHolder.edQty = convertView.findViewById(R.id.ed_qty)
                viewHolder.tvUnit = convertView.findViewById(R.id.tv_unit)
                convertView!!.tag = viewHolder
            } else {
                viewHolder = convertView.tag as ViewHolder
            }

            val bean = beans[position]
            viewHolder.tvUnit?.text = bean.cdprDesc

            viewHolder.setTextWatcher(bean)

            if (position == 0) {
                viewHolder.edQty?.requestFocus()
            }

            return convertView
        }

        internal inner class ViewHolder {
            var edQty: EditText? = null
            var tvUnit: TextView? = null

            var textWatcher: TextWatcher? = null

            fun setTextWatcher(bean: PackageRelation) {
                if (textWatcher == null) {
                    textWatcher = object : TextWatcher {
                        override fun beforeTextChanged(
                            s: CharSequence?,
                            start: Int,
                            count: Int,
                            after: Int
                        ) {
                        }

                        override fun onTextChanged(
                            s: CharSequence?,
                            start: Int,
                            before: Int,
                            count: Int
                        ) {
                        }

                        override fun afterTextChanged(s: Editable?) {
                            val tempStr = s.toString()
                            if (tempStr.isNotEmpty()) {
                                var filterText = filterNonDigit(
                                    if (isPoint != 0 && "EA" == bean.cdprUnit) 4 else 0,
                                    tempStr
                                )
                                if (isPoint != 0 && "EA" == bean.cdprUnit) {
                                    filterText = filterNumber(4, filterText)
                                }
                                if (filterText != tempStr) {
                                    edQty?.setText(filterText)
                                    edQty?.text?.let { edQty?.setSelection(it.length) }
                                }
                                bean.num = filterText
                            } else {
                                bean.num = tempStr
                            }
                        }
                    }
                    edQty?.addTextChangedListener(textWatcher)
                }
            }
        }
    }
}
