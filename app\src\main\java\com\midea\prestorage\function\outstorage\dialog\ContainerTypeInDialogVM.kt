package com.midea.prestorage.function.outstorage.dialog

import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData

class ContainerTypeInDialogVM(val dialog: ContainerTypeInDialog) {
    val title = ObservableField<String>("提示")
    var containerNo = ObservableField<String>("")
    var containerLockNo = ObservableField<String>("")

    fun close() {
        dialog.dismiss()
    }

    fun confirm() {
        if (CheckUtil.isFastDoubleClick()) {
            dialog.backConfirm()
        }
    }


}