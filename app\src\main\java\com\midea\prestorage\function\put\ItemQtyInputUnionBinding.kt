package com.midea.prestorage.function.put

import android.view.View
import android.widget.*
import com.midea.prestorage.widgets.TrailingZerosEditText
import com.midea.prestoragesaas.databinding.ItemPackageRelationQtyInputBinding
import com.midea.prestoragesaas.databinding.ItemPackageRelationQtyInputCareBinding

sealed class ItemQtyInputUnionBinding {
    abstract var vm: InStorageScanPutOperationQtyInputVM?
    abstract val root: View
    abstract val edQty: TrailingZerosEditText

    class V2(val binding: ItemPackageRelationQtyInputCareBinding) : ItemQtyInputUnionBinding() {
        override var vm: InStorageScanPutOperationQtyInputVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val edQty = binding.edQty
    }

    class V1(val binding: ItemPackageRelationQtyInputBinding) : ItemQtyInputUnionBinding() {
        override var vm: InStorageScanPutOperationQtyInputVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val edQty = binding.edQty
    }
}
