package com.midea.prestorage.function.planstockold

import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.FuShipmentStatue
import com.midea.prestorage.beans.net.PlanStockList
import com.midea.prestoragesaas.databinding.ActivityPlanStockOldBinding
import com.xuexiang.xqrcode.XQRCode

class PlanStockOldActivity : BaseActivity() {

    lateinit var binding: ActivityPlanStockOldBinding
    private var vm = PlanStockOldVM(this)
    val adapter = OutPoolStorageAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_plan_stock_old)
        binding.vm = vm

        initRecycle()
        initLoadMore()
        initSpinner()
    }

    override fun onResume() {
        super.onResume()

        binding.vm!!.onRefreshCommand.onRefresh()
    }

    private fun initRecycle() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter

        adapter.setOnItemClickListener { adapter, _, position ->
            val bean = adapter.data[position] as PlanStockList
            vm.onItemClick(bean)
        }
    }

    fun initSpinner() {
        var list = mutableListOf(
            FuShipmentStatue("100", "新建"),
            FuShipmentStatue("200", "执行中")
        )
        val beans = mutableListOf<String>()
        list.forEach {
            beans.add(it.name)
        }
        binding.spinnerStatus.setItems(beans)
        binding.spinnerStatus.selectedIndex = 0

        binding.spinnerStatus.setOnItemSelectedListener { _, position, _, _ ->
            binding.vm!!.onChangeStatue(list[position])
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.startSearch()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }

    fun showData(data: MutableList<PlanStockList>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            vm.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    class OutPoolStorageAdapter :
        CommonAdapter<PlanStockList>(R.layout.item_plan_stock),
        LoadMoreModule {
    }
}