package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class OutStorageDeleteQuery implements Serializable {

    private List<OutStorageDeleteList> list;
    private int totalCount;
    private int totalPage;
    private int pageNo;
    private int pageSize;
    private int offset;

    public List<OutStorageDeleteList> getList() {
        return list;
    }

    public void setList(List<OutStorageDeleteList> list) {
        this.list = list;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public class OutStorageDeleteList implements Serializable {
        @ShowAnnotation
        private String serialNo;
        @ShowAnnotation
        private String custItemCode;
        @ShowAnnotation
        private String itemName;
        @ShowAnnotation
        private BigDecimal qty;

        private String id;
        private String createTime;
        private String updateTime;
        private String tenantCode;
        private String createUserCode;
        private String createUserName;
        private String updateUserCode;
        private String updateUserName;
        private String version;
        private String deleteFlag;
        private String pageSize;
        private String serialCs;
        private String status;
        private String shipmentCode;
        private String shipmentDetailId;
        private String serialType;
        private String whCode;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getTenantCode() {
            return tenantCode;
        }

        public void setTenantCode(String tenantCode) {
            this.tenantCode = tenantCode;
        }

        public String getCreateUserCode() {
            return createUserCode;
        }

        public void setCreateUserCode(String createUserCode) {
            this.createUserCode = createUserCode;
        }

        public String getCreateUserName() {
            return createUserName;
        }

        public void setCreateUserName(String createUserName) {
            this.createUserName = createUserName;
        }

        public String getUpdateUserCode() {
            return updateUserCode;
        }

        public void setUpdateUserCode(String updateUserCode) {
            this.updateUserCode = updateUserCode;
        }

        public String getUpdateUserName() {
            return updateUserName;
        }

        public void setUpdateUserName(String updateUserName) {
            this.updateUserName = updateUserName;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getDeleteFlag() {
            return deleteFlag;
        }

        public void setDeleteFlag(String deleteFlag) {
            this.deleteFlag = deleteFlag;
        }

        public String getPageSize() {
            return pageSize;
        }

        public void setPageSize(String pageSize) {
            this.pageSize = pageSize;
        }

        public String getSerialNo() {
            return serialNo;
        }

        public void setSerialNo(String serialNo) {
            this.serialNo = serialNo;
        }

        public String getSerialCs() {
            return serialCs;
        }

        public void setSerialCs(String serialCs) {
            this.serialCs = serialCs;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getShipmentCode() {
            return shipmentCode;
        }

        public void setShipmentCode(String shipmentCode) {
            this.shipmentCode = shipmentCode;
        }

        public String getShipmentDetailId() {
            return shipmentDetailId;
        }

        public void setShipmentDetailId(String shipmentDetailId) {
            this.shipmentDetailId = shipmentDetailId;
        }

        public String getCustItemCode() {
            return custItemCode;
        }

        public void setCustItemCode(String custItemCode) {
            this.custItemCode = custItemCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getSerialType() {
            return serialType;
        }

        public void setSerialType(String serialType) {
            this.serialType = serialType;
        }

        public int getQty() {
            if (qty == null) {
                return 0;
            }
            return qty.intValue();
        }

        public BigDecimal getQtyBig(){
            return qty;
        }

        public void setQty(BigDecimal qty) {
            this.qty = qty;
        }

        public String getWhCode() {
            return whCode;
        }

        public void setWhCode(String whCode) {
            this.whCode = whCode;
        }
    }
}
