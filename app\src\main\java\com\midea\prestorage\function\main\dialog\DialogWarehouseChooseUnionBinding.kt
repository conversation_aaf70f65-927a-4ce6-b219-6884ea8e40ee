package com.midea.prestorage.function.main.dialog

import androidx.recyclerview.widget.RecyclerView
import com.midea.prestoragesaas.databinding.DialogWarehouseChooseBinding
import com.midea.prestoragesaas.databinding.DialogWarehouseChooseCareBinding

sealed class DialogWarehouseChooseUnionBinding {
    abstract var vm: WhDialogVM?
    abstract val recycle: RecyclerView

    class V2(val binding: DialogWarehouseChooseCareBinding) : DialogWarehouseChooseUnionBinding() {
        override var vm: WhDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val recycle = binding.recycle
    }

    class V1(val binding: DialogWarehouseChooseBinding) : DialogWarehouseChooseUnionBinding() {
        override var vm: WhDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val recycle = binding.recycle
    }
}
