package com.midea.prestorage.function.inv

import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.daimajia.swipe.SimpleSwipeListener
import com.daimajia.swipe.SwipeLayout
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.PrintBean
import com.midea.prestorage.beans.net.ReceiveInfo
import com.midea.prestorage.dialog.DialogTipUnionBinding
import com.midea.prestoragesaas.databinding.ActivityInventorySearchBinding
import com.midea.prestoragesaas.databinding.InvSearchTypeMenuBinding
import com.midea.prestoragesaas.databinding.PopViewForSelectCustemItemCodeBinding
import com.midea.prestorage.function.inv.InventorySearchVM.Companion.checkQty
import com.midea.prestorage.function.inv.dialog.PrintNumDialog
import com.midea.prestorage.function.inv.fragment.TransByGoodVM
import com.midea.prestorage.function.inv.response.FuInvLocationInventory
import com.midea.prestorage.function.inv.response.ItemRfVO
import com.midea.prestorage.function.inv.response.PackageRelation
import com.midea.prestorage.function.main.ProfileUnionBinding
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.receivecpkx.dialog.SimpleLoadMoreView
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.*
import com.midea.prestorage.widgets.CareLoadMoreView
import com.xuexiang.xqrcode.XQRCode
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.*

// 库存查询
class InventorySearchActivity : BaseActivity() {


    lateinit var binding: InventorySearchUnionBinding
    var adapter = ListLocInventoryAdapter()

    //69码或sn码 查询出多个custItemCodes的时候 弹框让用户选择custItemCode
    lateinit var dlgSelectCustItemCode: AlertDialog
    private lateinit var popBindingSelectCustItemCode: PopViewForUnionBinding
    lateinit var popAdapterSelectCustItemCode: PopListAdapter

    lateinit var popMenuBinding: InvSearchTypeMenuBinding
    lateinit var popupMenuWindow: PopupWindow

    lateinit var printNumDialog: PrintNumDialog

    companion object {
        fun newIntent(
            context: Context,
            custItemCode: String?
        ): Intent {
            val intent = Intent(context, InventorySearchActivity::class.java)
            intent.putExtra("custItemCode", custItemCode)
            return intent
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //setContentView(R.layout.activity_inventory_search)

        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            InventorySearchUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_inventory_search_care
                )
            )
        } else {
            InventorySearchUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_inventory_search
                )
            )
        }
        binding.vm = InventorySearchVM(this)

        DCUtils.goodsStatue(this, object : DCUtils.DCBack {

            override fun dcBack(statusDC: MutableList<DCBean>) {
            }
        })

        initData()
        initRecycleView()
        initLoadMore()
        initPopWinSelectCustItemCode()

        initPopMenu()

        AppUtils.requestFocus(binding.etLocCode)

        val adapter = FilterAdapter<String>(
            this,
            R.layout.item_dropdown_line
        )
        binding.etLocCode.setAdapter(adapter)

        initDialog()

        if (Constants.isShowInventorySearch) {
            binding.vm?.bluetoothOpen(true)
        }
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    private fun initData() {
        if (intent.getStringExtra("custItemCode") != null) {
            binding.etCustItemCode.setText(intent.getStringExtra("custItemCode"))
        }
    }

    private fun initDialog() {

        //打印条码dialog
        printNumDialog = PrintNumDialog(this)
        printNumDialog.setTitle("请输入打印数量")
        printNumDialog.setPrintBack(object : PrintNumDialog.PrintBarcodeBack {
            override fun printBarcodeBack(printBean: PrintBean) {
                printNumDialog.dismiss()
                Printer.printBarcode(printBean)
            }
        })

        printNumDialog.setOnDismissListener {
            adapter.notifyItemChanged(binding.vm?.currentItemFlag?.get()!!)
        }

    }

    private fun initPopMenu() {

        val popView = LayoutInflater.from(this).inflate(R.layout.inv_search_type_menu, null)
        popMenuBinding = DataBindingUtil.bind(popView)!!
        popMenuBinding.vm = binding.vm

        popupMenuWindow = PopupWindow(
            popView,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        popupMenuWindow.setBackgroundDrawable(
            ColorDrawable(
                ContextCompat.getColor(
                    this,
                    R.color.colorOutlineGrey
                )
            )
        )

        popupMenuWindow.isOutsideTouchable = true

        popMenuBinding.btnSearchType1.setOnClickListener {
            popupMenuWindow.dismiss()
            checkQty = true
            binding.vm!!.toSearch()
        }
        popMenuBinding.btnSearchType2.setOnClickListener {
            popupMenuWindow.dismiss()
            checkQty = false
            binding.vm!!.toSearch()
        }

        binding.mSwitch.setOnCheckedChangeListener { _, isChecked ->
            if (binding.mSwitch.isEnabled) {
                binding.vm!!.toSearch()
                binding.mSwitch.isEnabled = false
                // 2秒后将isSwitchClickable设置为true
                Handler(Looper.getMainLooper()).postDelayed({
                    binding.mSwitch.isEnabled = true
                }, 2000)
            } else {
                // Switch的点击事件被忽略
            }
        }
    }

    // 初始化选择客户商品编码的弹窗
    private fun initPopWinSelectCustItemCode() {

        val alertDialogBuilder = AlertDialog.Builder(this)
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            val popViewSelectCustItemCode =
                LayoutInflater.from(this)
                    .inflate(R.layout.pop_view_for_select_custem_item_code_care, null)
            popBindingSelectCustItemCode =
                PopViewForUnionBinding.V2(DataBindingUtil.bind(popViewSelectCustItemCode)!!)
            alertDialogBuilder.setView(popViewSelectCustItemCode)
        } else {
            val popViewSelectCustItemCode =
                LayoutInflater.from(this)
                    .inflate(R.layout.pop_view_for_select_custem_item_code, null)
            popBindingSelectCustItemCode =
                PopViewForUnionBinding.V1(DataBindingUtil.bind(popViewSelectCustItemCode)!!)
            alertDialogBuilder.setView(popViewSelectCustItemCode)
        }

        dlgSelectCustItemCode = alertDialogBuilder.create()

        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            dlgSelectCustItemCode.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            dlgSelectCustItemCode.window?.setGravity(Gravity.CENTER)
            dlgSelectCustItemCode.window?.attributes?.run {
                gravity = Gravity.CENTER
            }
        }

        popAdapterSelectCustItemCode = PopListAdapter()
        popBindingSelectCustItemCode.recyclerViewInPop.layoutManager = LinearLayoutManager(this)
        popBindingSelectCustItemCode.recyclerViewInPop.adapter = popAdapterSelectCustItemCode

        popAdapterSelectCustItemCode.setOnItemClickListener { adapter, view, position ->
            val item = adapter.getItem(position) as ItemRfVO
            binding.vm?.itemCode = item.itemCode ?: ""
            dlgSelectCustItemCode.dismiss()
            binding.vm?.onRefreshCommand?.onRefresh()
        }

        popBindingSelectCustItemCode.closePop.setOnClickListener {
            dlgSelectCustItemCode.dismiss()
        }

    }


    fun initRecycleView() {
        binding.srl.setOnRefreshListener(binding.vm?.onRefreshCommand)

        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter

        adapter.setOnItemChildClickListener { adapter, view, position ->
            val item = adapter.data[position] as FuInvLocationInventory

            when (view.id) {
                //移库
                R.id.ll_move -> {
                    val it = Intent(this, TransferActivity::class.java)
                    it.putExtra("loc", item.locCode)
                    it.putExtra("itemCode", item.custItemCode)
                    startActivity(it)
                }

                //打印条码
                R.id.ll_print -> {
                    val list = adapter.data
                    val bean = list[position] as FuInvLocationInventory
                    var printBean = PrintBean()
                    printBean.itemCode = bean.itemCode
                    printBean.itemName = bean.itemName
                    printBean.cdpaFormat = bean.cdpaFormat
                    printBean.printCount = 1
                    binding.vm?.currentItemFlag?.set(position) //标记是哪一行的打印
                    printNumDialog.setData(printBean)
                    printNumDialog.show()
                }
            }
        }

        if (!TextUtils.isEmpty(binding.etCustItemCode.text.toString())) {
            binding.vm?.toSearch(false)
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.loadMore()
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            adapter.loadMoreModule.loadMoreView = CareLoadMoreView()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果
            binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    fun setData(data: MutableList<String>) {
        val adapter = binding.etLocCode.adapter as FilterAdapter<String>
        adapter.clear()
        adapter.addAll(data)
        adapter.notifyDataSetChanged()
        binding.etLocCode.showDropDown()
    }

    override fun onDestroy() {
        super.onDestroy()
        Printer.closeBluetooth()
    }

    class PopListAdapter :
        CommonAdapter<ItemRfVO>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_pop_view_for_select_cust_item_code_pop_care else R.layout.item_pop_view_for_select_cust_item_code_pop) {
        override fun convert(holder: BaseViewHolder?, item: ItemRfVO) {
            super.convert(holder, item)

            holder?.setGone(R.id.tv_item_name, item.itemName.isNullOrEmpty())
        }
    }

    class ListLocInventoryAdapter :
        ListCheckBoxAdapter<FuInvLocationInventory>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_for_inventory_search_care else R.layout.item_for_inventory_search),
        LoadMoreModule {

        private var currentSwipeLayout: SwipeLayout? = null

        init {
            addChildClickViewIds(R.id.ll_move, R.id.ll_print)
        }

        override fun convert(helper: BaseViewHolder, item: BaseItemForPopup) {
            super.convert(helper, item as FuInvLocationInventory)

            val swipeLayout = helper?.itemView?.findViewById<SwipeLayout>(R.id.sample)
            if (swipeLayout?.openStatus == SwipeLayout.Status.Open) {
                swipeLayout?.close(true)
            }

            //如果生产日期，和失效日期都是空就不要显示
            if (item.lotAtt01 == null && item.lotAtt02 == null) {
                helper.itemView.findViewById<LinearLayout>(R.id.ll_make_lose_attr).visibility =
                    View.GONE
            } else {
                if (item.lotAtt01 != null) helper.setText(R.id.lotAtt01, item.lotAtt01)
                if (item.lotAtt02 != null) helper.setText(R.id.lotAtt02, item.lotAtt02)
            }

            //货品描述
            if (!item.itemName.isNullOrEmpty()) {
                helper.setText(R.id.tvItemName, item.itemName)
            } else {
                helper.setText(R.id.tvItemName, "")
            }

            //货主名称
            if (!item.ownerName.isNullOrEmpty()) {
                helper.setText(R.id.tvOwnerName, item.ownerName)
            } else {
                helper.setText(R.id.tvOwnerName, "")
            }

            //商品编码
            helper.setText(R.id.tvCustItemCode, item.custItemCode)

            //69码
            helper.setText(
                R.id.tvWhBarcode69,
                LotAttUnit.formatWhBarcode69(item?.whCsBarcode69, item?.whBarcode69)
            )

            //库位编码
            if (!item.locCode.isNullOrEmpty()) {
                helper.setText(R.id.tvLocCode, item.locCode)
            } else {
                helper.setText(R.id.tvLocCode, "")
            }

            //  库存
            if (item.onHandQty != null) {
                if (item.isVisible == 0) {
                    helper.setText(R.id.tvOnHandQty, item.onHandQtyStr)
                } else {
                    helper.setText(R.id.tvOnHandQty, "***")
                }
            } else {
                helper.setText(R.id.tvOnHandQty, "")
            }

            // 占用
            if (item.occupyQty != null) {
                // 2022年4月20日 星期三 占用数 = allocated_qty + in_transit_qty + locked_qty + rep_out_qty
                //var wrapQty = item.allocatedQty.plus(item.inTransitQty).plus(item.lockedQty).plus(item.repOutQty)
                if (item.occupyQty.compareTo(BigDecimal.ZERO) == 0) {
                    helper.setText(R.id.tvAllocatedQty, "0")
                } else {
                    helper.setText(
                        R.id.tvAllocatedQty,
                        item.occupyQty.stripTrailingZeros().toPlainString()
                    )
                }
            } else {
                helper.setText(R.id.tvAllocatedQty, "")
            }

            //可用
            if (item.usableQty != null) {
//                var useAbleQty = BigDecimal.ZERO
//                useAbleQty = if (item.allocatedQty != null) {
//                    item.onHandQty.subtract(item.allocatedQty).subtract(item.inTransitQty)
//                        .subtract(item.lockedQty).subtract(item.repOutQty)
//                } else {
//                    item.onHandQty.subtract(item.inTransitQty).subtract(item.lockedQty)
//                        .subtract(item.repOutQty)
//                }
                if (item.usableQty.compareTo(BigDecimal.ZERO) == 0) {
                    helper.setText(R.id.tvUseAbleQty, "0")
                } else {
                    helper.setText(
                        R.id.tvUseAbleQty,
                        item.usableQty.stripTrailingZeros().toPlainString()
                    )
                }
            } else {
                helper.setText(R.id.tvUseAbleQty, "")
            }

            //在途
            if (item.inTransitQty != null) {
                if (item.inTransitQty.compareTo(BigDecimal.ZERO) == 0) {
                    helper.setGone(R.id.ll_inTransitQty, true)
                    helper.setText(R.id.tvInTransitQty, "0")
                } else {
                    helper.setGone(R.id.ll_inTransitQty, false)
                    helper.setText(
                        R.id.tvInTransitQty,
                        item.inTransitQty.stripTrailingZeros().toPlainString()
                    )
                }
            } else {
                helper.setGone(R.id.ll_inTransitQty, true)
                helper.setText(R.id.tvInTransitQty, "")
            }

            //  属性5: 批次
            if (!item.lotAtt05.isNullOrEmpty()) {
                helper.setText(R.id.tvLot5, item.lotAtt05)
            } else {
                helper.setText(R.id.tvLot5, "")
            }

            //20211129 如果是按  货品+状态查询，则不显示批次 (checkQty=true)  不显示库位
            if (checkQty) {
                helper.setGone(R.id.layoutLot5, true)
            } else {
                helper.setGone(R.id.layoutLot5, false)
            }

            // 属性4  状态
            if (!item.lotAtt04.isNullOrEmpty()) {
                if (DCUtils.goodsStatueC2N.get(item.lotAtt04) != null) {
                    helper.setText(
                        R.id.tvLot4,
                        DCUtils.goodsStatueC2N.get(item.lotAtt04).toString()
                    )
                } else {
                    helper.setText(R.id.tvLot4, item.lotAtt04)
                }
            } else {
                helper.setText(R.id.tvLot4, "")
            }
        }

        override fun createBaseViewHolder(view: View): BaseViewHolder {
            val swipeLayout = view.findViewById<SwipeLayout>(R.id.sample)

            swipeLayout?.addSwipeListener(object : SimpleSwipeListener() {
                override fun onStartOpen(layout: SwipeLayout?) {
                    // 如果当前已经有 item 处于打开状态，则关闭该 item
                    if (currentSwipeLayout != null && currentSwipeLayout != layout) {
                        currentSwipeLayout?.close()
                    }
                    // 记录当前打开的 item
                    currentSwipeLayout = layout
                }

                override fun onOpen(layout: SwipeLayout?) {
                    // 如果当前已经有 item 处于打开状态，则关闭该 item
                    if (currentSwipeLayout != null && currentSwipeLayout != layout) {
                        currentSwipeLayout?.close()
                    }
                    // 记录当前打开的 item
                    currentSwipeLayout = layout
                }

                override fun onClose(layout: SwipeLayout?) {
                    // 如果当前关闭的 item 与当前打开的 item 相同，则清空记录
                    if (currentSwipeLayout == layout) {
                        currentSwipeLayout = null
                    }
                }
            })
            return BaseViewHolder(view)
        }
    }
}