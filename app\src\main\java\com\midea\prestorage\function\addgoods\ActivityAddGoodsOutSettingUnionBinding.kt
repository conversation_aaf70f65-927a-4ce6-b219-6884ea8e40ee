package com.midea.prestorage.function.addgoods

import android.widget.CheckBox
import android.widget.LinearLayout
import android.widget.TextView
import com.midea.prestoragesaas.databinding.ActivityAddGoodsOutSettingBinding
import com.midea.prestoragesaas.databinding.ActivityAddGoodsOutSettingCareBinding

sealed class ActivityAddGoodsOutSettingUnionBinding{
    abstract var vm: SettingOutVM?
    abstract val cbDefault: CheckBox
    abstract val cbRemember: CheckBox
    abstract val llDefault: LinearLayout
    abstract val llRemember: LinearLayout
    abstract val tvNotification: TextView

    class V2(val binding: ActivityAddGoodsOutSettingCareBinding) : ActivityAddGoodsOutSettingUnionBinding() {
        override var vm: SettingOutVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val cbDefault = binding.cbDefault
        override val cbRemember = binding.cbRemember
        override val llDefault = binding.llDefault
        override val llRemember = binding.llRemember
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityAddGoodsOutSettingBinding) : ActivityAddGoodsOutSettingUnionBinding() {
        override var vm: SettingOutVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val cbDefault = binding.cbDefault
        override val cbRemember = binding.cbRemember
        override val llDefault = binding.llDefault
        override val llRemember = binding.llRemember
        override val tvNotification = binding.tvNotification
    }
}
