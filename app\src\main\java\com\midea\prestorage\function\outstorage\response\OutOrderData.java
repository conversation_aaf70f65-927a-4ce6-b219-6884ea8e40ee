package com.midea.prestorage.function.outstorage.response;

import java.io.Serializable;

public class OutOrderData implements Serializable, Comparable<OutOrderData> {

    int status = 0;
    boolean isScan = false;

    public OutOrderData(int status, boolean isScan) {
        this.status = status;
        this.isScan = isScan;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public boolean isScan() {
        return isScan;
    }

    public void setScan(boolean scan) {
        isScan = scan;
    }

    @Override
    public int compareTo(OutOrderData o) {
        return Integer.compare(this.status, o.status);
    }
}
