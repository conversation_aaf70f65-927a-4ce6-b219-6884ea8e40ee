package com.midea.prestorage.function.containerpick.fragment

import android.app.Application
import androidx.databinding.ObservableField
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.base.ErrorToaster
import com.midea.prestorage.base.SuccessToaster

abstract class BulkPackVM(application: Application) :
    BaseViewModel(application), ErrorToaster {

    lateinit var errorToaster: ErrorToaster
    lateinit var successToaster: SuccessToaster

    override fun init() {

    }

    open fun refresh() {

    }
}