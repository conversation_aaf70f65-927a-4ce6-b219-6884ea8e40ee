package com.midea.prestorage.function.collection

import android.text.TextUtils
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.CollectionInfo
import com.midea.prestorage.beans.net.FuShipmentStatue
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody

class CollectionDetailVM(val activity: CollectionDetailActivity) {

    val title = ObservableField("入库扫码")
    val num = ObservableField("")
    val totalQty = ObservableField("")
    val qty = ObservableField("")
    val notQty = ObservableField("")
    val waveNo = ObservableField("")
    val custOrderNo = ObservableField("")
    val orderStatueStr = ObservableField("")

    var bean: CollectionInfo? = null

    //网点分拨商品状态
    var goodsStatue: MutableList<DCBean>? = null
    var orderStatue: MutableList<DCBean>? = null

    init {
        initDC()
        initDCOrderStatue()
    }

    private fun initDC() {
        RetrofitHelper.getDirectionAPI()
            .fuShipmentStatus("CL_SHIPPING_CONTAINER_STATUS")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<FuShipmentStatue>>(activity) {
                override fun success(data: MutableList<FuShipmentStatue>?) {
                    if (data != null) {
                        val beans = mutableListOf<DCBean>()
                        data.removeAll { it.enableFlag == 0 }
                        data.forEach {
                            beans.add(DCBean(it.name, it.code, DCBean.SHOW_KEY))
                        }
                        goodsStatue = beans

                        val shipmentCode = activity.intent.getStringExtra("shipmentCode")
                        checkType(shipmentCode)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    AppUtils.showToast(activity, apiErrorModel.message)
                }
            })
    }

    private fun initDCOrderStatue() {
        DCUtils.shipmentStatus(activity as RxAppCompatActivity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                orderStatue = statusDC
                initOrderStatue()
            }
        })
    }

    private fun checkType(shipmentCode: String?) {
        activity.waitingDialogHelp.showDialog()
        val map = mutableMapOf(
            "shipmentCode" to shipmentCode,
            "whCode" to activity.getWhCode()
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        RetrofitHelper.getAddGoodsService()
            .dispatchInfo(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<CollectionInfo>(activity) {
                override fun success(data: CollectionInfo?) {
                    if (data != null) {
                        bean = data
                        num.set(data.num.toString())
                        totalQty.set(data.totalQty.toString())
                        qty.set(data.qty.toString())
                        notQty.set(data.notQty.toString())
                        if (data.waveNo.isNullOrEmpty()) {
                            waveNo.set(data.shipmentCode)
                        } else {
                            waveNo.set(data.waveNo)
                        }
                        custOrderNo.set(data.custOrderNo)

                        data.despatchInfoDtos.forEach {
                            val dcBean = goodsStatue?.find { item ->
                                item.value.toString() == it.status.toString()
                            }
                            it.statusStr = dcBean?.key
                        }
                        activity.adapter.setNewInstance(data.despatchInfoDtos)
                        activity.adapter.notifyDataSetChanged()

                        initOrderStatue()
                        if (!TextUtils.isEmpty(bean?.tips)) {
                            ToastUtils.getInstance().showErrorToastWithSound(activity, bean?.tips)
                        }
                    }
                    activity.waitingDialogHelp.hidenDialog()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    private fun initOrderStatue() {
        if (bean == null || orderStatue == null) {
            return
        }
        val result = orderStatue?.find {
            it.value.toString() == bean!!.status.toString()
        }
        orderStatueStr.set(result?.key)
    }

    fun back() {
        activity.finish()
    }
}

