package com.midea.prestorage.dialog;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.drawable.ColorDrawable;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.PopupWindow;

import androidx.appcompat.app.AlertDialog;

import java.util.Map;

public class AlertDialogUtil {

    //只有一个确定按钮的提示性弹框
    public static void showOnlyOkDialog(Activity activity, String msg, OnOkCallback onOkCallback) {
        if (activity == null) return;
        AlertDialog.Builder alertDialogBuilder = new AlertDialog.Builder(activity);
        alertDialogBuilder.setMessage(msg);
        if (onOkCallback != null) {
            alertDialogBuilder.setCancelable(false);
        }
        alertDialogBuilder.setPositiveButton("确定", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (onOkCallback != null) {
                    onOkCallback.OnOk();
                }
                if (dialog != null) {
                    dialog.dismiss();
                }
            }
        });
        //alertdialogbuilder.setNeutralButton("取消", null);
        final AlertDialog dlg = alertDialogBuilder.create();
        dlg.show();
    }

    //只有一个确定按钮的提示性弹框
    public static void showOnlyOkDialog(Activity activity, String msg, OnOkCallback onOkCallback, String btnOkText) {
        if (activity == null) return;
        AlertDialog.Builder alertDialogBuilder = new AlertDialog.Builder(activity);
        alertDialogBuilder.setMessage(msg);
        alertDialogBuilder.setCancelable(false);
        alertDialogBuilder.setPositiveButton(btnOkText, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (onOkCallback != null) {
                    onOkCallback.OnOk();
                }
                if (dialog != null) {
                    dialog.dismiss();
                }
            }
        });
        //alertdialogbuilder.setNeutralButton("取消", null);
        final AlertDialog dlg = alertDialogBuilder.create();
        dlg.show();
    }


    // 有确定和取消按钮的对话框，  需要传入 确定和取消 按钮的事件回调监听器
    public static void showOkAndCancelDialog(Context context, String msg,
                                             DialogInterface.OnClickListener callBackOk,
                                             DialogInterface.OnClickListener callBackCancel) {
        if (context == null) return;
        AlertDialog.Builder alertDialogBuilder = new AlertDialog.Builder(context);
        alertDialogBuilder.setMessage(msg);
        alertDialogBuilder.setPositiveButton("确定", callBackOk);
        alertDialogBuilder.setNeutralButton("取消", callBackCancel);
        final AlertDialog dlg = alertDialogBuilder.create();
        dlg.show();
    }


    // 列表选项 对话框， 类似spinner
    // 需要传入 一个map ，  map的key将会显示在界面  ， 在item被点击时的事件回调里自行获取 map的key和 val
    // 需要传入 列表item被点击时的事件监听器
    public static void showSelectListDialog(Context context, String title,
                                            Map<String, Object> map,
                                            OnSelectKey onSelectKey) {
        if (context == null) return;
        AlertDialog.Builder alertDialogBuilder = new AlertDialog.Builder(context);
        alertDialogBuilder.setTitle(title);
        final String[] displayKeys = map.keySet().toArray(new String[map.size()]);

        alertDialogBuilder
                .setSingleChoiceItems(displayKeys, 0, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        if (onSelectKey != null) {
                            onSelectKey.onSelect(displayKeys[which], map.get(displayKeys[which]));
                            if (dialog != null) {
                                dialog.dismiss();
                            }
                        }
                    }
                });
        /*alertDialogBuilder.setPositiveButton("确定", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {

            }
        });
        alertDialogBuilder.setNegativeButton("取消", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });*/
        final AlertDialog dlg = alertDialogBuilder.create();
        dlg.show();
    }


    // https://github.com/Bigkoo/Android-PickerView
    /*  年月日时分秒选择器
    public static TimePickerView buildDlgSelectDateTime(Activity activity,OnTimeSelectListener onTimeSelectListener){
        TimePickerView pvTime = new TimePickerBuilder(activity, onTimeSelectListener)
                .setType(new boolean[]{true, true, true, true, true, true})//选择级别， 前三个年月日 后三个时分秒
                //.setCancelText("Cancel")//取消按钮文字
                //.setSubmitText("Sure")//确认按钮文字
                //.setContentSize(18)//滚轮文字大小
                //.setTitleSize(20)//标题文字大小
                //.setTitleText("Title")//标题文字
                //.setOutSideCancelable(false)//点击屏幕，点在控件外部范围时，是否取消显示
                //.isCyclic(true)//是否循环滚动
                //.setTitleColor(Color.BLACK)//标题文字颜色
                //.setSubmitColor(Color.BLUE)//确定按钮文字颜色
                //.setCancelColor(Color.BLUE)//取消按钮文字颜色
                //.setTitleBgColor(0xFF666666)//标题背景颜色 Night mode
                //.setBgColor(0xFF333333)//滚轮背景颜色 Night mode
                //.setDate(selectedDate)// 如果不设置的话，默认是系统时间
                //.setRangDate(startDate,endDate)//起始终止年月日设定
                .setLabel("年","月","日","时","分","秒")//默认设置为年月日时分秒
                //.isCenterLabel(false) //是否只显示中间选中项的label文字，false则每项item全部都带有label。
                //.isDialog(true)//是否显示为对话框样式
                .build();
        return pvTime;
    }
    */


    /**
     * @param cx    activity
     * @param view  传入需要显示在什么控件下
     * @param view1 传入内容的view
     */
    public static PopupWindow buildPopWin(Context cx, View view, View view1, int color) {
        DisplayMetrics dm = new DisplayMetrics();
        WindowManager wmManager = (WindowManager) cx.getSystemService(Context.WINDOW_SERVICE);
        wmManager.getDefaultDisplay().getMetrics(dm);
        int Hight = dm.heightPixels;

        PopupWindow mPopupWindow = new PopupWindow(cx);

        mPopupWindow.setBackgroundDrawable(new ColorDrawable(color));
        view1.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));
        // 设置PopupWindow的大小（宽度和高度）
        mPopupWindow.setWidth(view.getWidth());
        mPopupWindow.setHeight((Hight - view.getBottom()) * 2 / 3);
        // 设置PopupWindow的内容view
        mPopupWindow.setContentView(view1);
        mPopupWindow.setFocusable(true); // 设置PopupWindow可获得焦点
        mPopupWindow.setTouchable(true); // 设置PopupWindow可触摸
        mPopupWindow.setOutsideTouchable(true); // 设置非PopupWindow区域可触摸
        return mPopupWindow;
    }

    public interface OnSelectKey {
        void onSelect(String key, Object value);
    }

    public interface OnOkCallback {
        void OnOk();
    }



  /*  void test(){
        Map<String,Object> map = new HashMap<>();
        AlertDialogUtil.showSelectListDialog(null, "test", map, new OnSelectKey() {
            @Override
            public void onSelect(String key, Object val) {

            }
        });
    }
*/
}
