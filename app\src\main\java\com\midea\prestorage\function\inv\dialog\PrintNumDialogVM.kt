package com.midea.prestorage.function.inv.dialog

import CheckUtil
import androidx.databinding.ObservableField
import com.midea.prestorage.beans.net.PrintBean

class PrintNumDialogVM(val dialog: PrintNumDialog) {
    val title = ObservableField<String>("请输入打印数量")
    val etInfo = ObservableField("")
    var setCode = ObservableField("")
    var bean: PrintBean? = null

    fun onEnterAnyCode() {
        if (CheckUtil.isFastDoubleClick()) {
            if (etInfo.get().isNullOrBlank()) {
                return
            }
            etInfo.get()?.let { dialog.backDeleteBarcode(it) }
        }
    }

    fun close() {
        etInfo.set("")
        dialog.dismiss()
    }

    fun confirm() {
        if (CheckUtil.isFastDoubleClick()) {
            if (etInfo.get().isNullOrBlank()) {
                return
            }
            etInfo.get()?.let { dialog.backDeleteBarcode(it) }
        }
    }

}