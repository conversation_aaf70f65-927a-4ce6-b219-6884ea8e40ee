package com.midea.prestorage.function.mainyg

import android.content.Intent
import android.os.Bundle
import android.widget.AdapterView
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.setting.ImplWarehouse
import com.midea.prestoragesaas.databinding.ActivityMainYgBinding
import com.midea.prestorage.function.main.ImageAdapter
import com.midea.prestorage.function.main.ProfileUnionBinding
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.main.dialog.WhChooseDialog
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.ToastUtils

class MainYgActivity : BaseActivity(), WhChooseDialog.ServerRefresh {

    lateinit var binding: MainYgUnionBinding
    private lateinit var whDialog: WhChooseDialog

    val adapterIn = ImageAdapter(this)
    val adapterOut = ImageAdapter(this)
    val adapterStorage = ImageAdapter(this)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            MainYgUnionBinding.V2(DataBindingUtil.setContentView(this, R.layout.activity_main_yg_care))
        } else {
            MainYgUnionBinding.V1(DataBindingUtil.setContentView(this, R.layout.activity_main_yg))
        }

        binding.vm = MainYgVM(this)

        whDialog = WhChooseDialog(this)
        whDialog.setTitle("请选择仓库")
        whDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            binding.vm!!.whInfoCheck(it)
            binding.vm!!.uploadDevicesInfo()
            binding.vm!!.initAuth()
            whDialog.dismiss()
        })

        binding.vm!!.checkUpGrade(false)

        binding.gridIn.adapter = adapterIn
        binding.gridOut.adapter = adapterOut
        binding.gridStorage.adapter = adapterStorage

        binding.gridIn.onItemClickListener = listener
        binding.gridOut.onItemClickListener = listener
        binding.gridStorage.onItemClickListener = listener
        binding.vm!!.init()
    }

    var listener = AdapterView.OnItemClickListener { parent, _, position, _ ->
        val adapter = parent.adapter as ImageAdapter
        val item = adapter.getItem(position)
        if (CheckUtil.isFastDoubleClick()) {
            binding.vm!!.onItemClick(item)
        }
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun showStorageInfo(list: MutableList<ImplWarehouse>?) {
        if (!list.isNullOrEmpty()) {
            whDialog.setNewData(list)
            whDialog.notifyDataChange(list)
            if (!whDialog.isShowing) {
                whDialog.show()
            }
        } else {
            ToastUtils.getInstance().showErrorToastWithSound(this, "暂无数据!")
        }
    }

    fun whCancelAble(cancelAble: Boolean) {
        whDialog.isAbleCancel(cancelAble)
    }

    override fun refreshByServer() {
        binding.vm!!.resetWhCodeInfo(true)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        binding.vm!!.init()
    }
}