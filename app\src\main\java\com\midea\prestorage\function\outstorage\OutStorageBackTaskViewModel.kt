package com.midea.prestorage.function.outstorage

import android.app.Application
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.MutableLiveData
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.OutStorageBackTaskResp
import com.midea.prestorage.event.LiveEvent
import com.midea.prestorage.function.planstock.PlanStockDetailVM
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.withContext

class OutStorageBackTaskViewModel(application: Application) : BaseViewModel(application) {

    companion object {
        val dataFormatRegex = Regex("(\\d{4}-\\d{2}-\\d{2}) \\d{2}:\\d{2}:\\d{2}")
    }

    val barcode = MutableLiveData<String>()

    val isRefreshing = MutableLiveData<Boolean>()
    val showRecycleView = MutableLiveData<Boolean>()
    val originTaskList = mutableListOf<OutStorageBackTaskResp>()
    val taskListLiveData = MutableLiveData<MutableList<OutStorageBackTaskResp>>()
    val scanCodeResult = MutableLiveData<OutStorageBackTaskResp>()

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        isRefreshing.value = true
        requestTaskList()
    }

    val scanEvent = LiveEvent<Unit>()
    val deleteEvent = LiveEvent<Unit>()

    override fun init() {
    }

    fun scan() {
        if (CheckUtil.isFastDoubleClick()) {
            scanEvent.value = Unit
        }
    }

    fun delete() {
        if (CheckUtil.isFastDoubleClick()) {
            deleteEvent.value = Unit
        }
    }

    fun scanResult(result: String?) {
        barcode.value = result ?: ""
        onEnterCustItemCode()
    }

    fun onEnterCustItemCode() {
        if (CheckUtil.isFastDoubleClick()) {
            scanCode()
        }
    }

    private fun requestTaskList(): Job =
        launch(showDialog = true, finish = { isRefreshing.value = false }) {
            barcode.value = ""
            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getOutStorageAPI().backTaskPage()
            }
            if (result.code == 0L) {
                originTaskList.clear()
                originTaskList.addAll(result.data.orEmpty().onEach {
                    kotlin.runCatching {
                        it.lotAtt01 = dataFormatRegex.replace(it.lotAtt01 ?: "", "$1")
                    }
                })
                taskListLiveData.value = originTaskList
                showRecycleView.value = taskListLiveData.value?.isNotEmpty() == true
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }

    @VisibleForTesting
    fun scanCode(): Job = launch(showDialog = true, error = {}) {
        checkScanCode()
        val code = barcode.value.orEmpty()
        val filtered = originTaskList.filter {
            it.custItemCode == code ||
                    it.whBarcode69 == code ||
                    it.whIpBarcode69 == code ||
                    it.whCsBarcode69 == code ||
                    it.whMaxBarcode69 == code
        }
        if (filtered.isEmpty()) {
            barcode.value = ""
            showNotification("商品不在待返库任务范围", false)
            return@launch
        }
        if (filtered.size == 1) {
            scanCodeResult.value = filtered[0]
        } else {
            taskListLiveData.value = filtered.toMutableList()
        }
    }

    @VisibleForTesting
    @Throws(IllegalArgumentException::class)
    fun checkScanCode() {
        if (barcode.value.isNullOrBlank()) {
            throw IllegalArgumentException("商品编码/条码为空")
        }
    }

}