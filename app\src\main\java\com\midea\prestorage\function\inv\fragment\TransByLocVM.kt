package com.midea.prestorage.function.inv.fragment

import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import androidx.core.content.ContextCompat
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.net.BsOwner
import com.midea.prestorage.beans.net.RespRecommendLoc
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.inv.TransferActivity
import com.midea.prestorage.function.inv.response.BsLocation
import com.midea.prestorage.function.inv.response.FuInvLocationInventory
import com.midea.prestorage.function.inv.response.PackageRelation
import com.midea.prestorage.function.inv.response.RespGetInventoryByLocation
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal
import java.util.HashMap

class TransByLocVM(val fragment: TransByLocFragment) {

    val bgColor = ObservableField<Drawable>()
    var bsOwner: BsOwner? = null

    // 货主
    var textOwnerCode = ObservableField<String>("")
    var fromLoc = ObservableField("")
    var isEnableClick = ObservableField(true)

    //件数  后端返回的onHandQtySum  显示在界面（件数）上
    var onHandQtySum = ObservableField<String>("")

    //后端返回的 sku数  显示在界面(sku数)上
    var sku = ObservableField<String>("")

    private var ownerDialog: FilterDialog  //货主选择对话框

    // 是否只有一个货主
    var isOnlyOneOwner = false


//    var oriFromLocCode = ""//记录原来库位信息


    init {

        //目标库位  只有 确认来源库位没问题后才能编辑
        fragment.binding.etTargetLocCode.isEnabled = false

        ownerDialog = FilterDialog(fragment.activity as RxAppCompatActivity)
        // 隐藏搜索框
        ownerDialog.dismissEdit()
        ownerDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            it as BsOwner
            bsOwner = it
            textOwnerCode.set(it.ownerCode)
            ownerDialog.dismiss()
            factEnterFromLocCode(false)
        })

//        initOwnerList()
    }

    // 重置界面
    fun resetDefault() {

        textOwnerCode.set("")
        var ownerBsOwnerArray = mutableListOf<BsOwner>()//货主对象列表
        ownerDialog.changeDataNotify(ownerBsOwnerArray)//清空
        fragment.binding.etFromLocCode.setText("")
        fragment.binding.etTargetLocCode.setText("")
        sku.set("")
        onHandQtySum.set("")
        // 清空查询列表
        fragment.adapter.data.clear()
        fragment.adapter.notifyDataSetChanged()

    }


    fun onEnterToLocCode() {
        val fromLocCode = fragment.binding.etFromLocCode.text.toString().trim()
        val toLocCode = fragment.binding.etTargetLocCode.text.toString().trim()

        if (toLocCode.isEmpty()) {
            ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "目标库位编码不能为空")
        } else if (fromLocCode.equals(toLocCode)) {
            ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "源库位与目标库位不能相同")
            fragment.binding.etTargetLocCode.setText("")
            fragment.binding.etTargetLocCode.requestFocus()
            return
        } else {
            validateLocationCode("to", toLocCode)
        }
    }

    //获取推荐库位
    fun getRecommendLoc(bean: FuInvLocationInventory) {
        (fragment.activity as BaseActivity).waitingDialogHelp.showDialog()
        val map = mutableMapOf(
            "whCode" to (fragment.activity as BaseActivity).getWhCode(),
            "ownerCode" to bean.ownerCode,
            "scenario" to "move",
            "orderType" to "",
            "qty" to AppUtils.getBigDecimalValue(onHandQtySum.get().toString()),
            "packageUnit" to "EA",
            "lotAtt01" to bean.lotAtt01,
            "lotAtt02" to bean.lotAtt02,
            "lotAtt04" to bean.lotAtt04,
            "lotAtt05" to bean.lotAtt05,
            "lotAtt06" to bean.lotAtt06,
            "containerCode" to "",
            "itemCode" to bean.itemCode,
            "locCode" to fragment.binding.etFromLocCode.text.toString().trim()
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        RetrofitHelper.getAddGoodsService()
            .getRecommendLoc(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<RespRecommendLoc>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: RespRecommendLoc?) {
                    data?.locCode?.let {
                        if (!it.isNullOrEmpty()) {
                            fragment.binding.etTargetLocCode.setText(it)
                            onEnterToLocCode()
                        }
                    } ?: run {
                        (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(fragment.activity, apiErrorModel.message)
                }
            })
    }

    //type :  from 表示 来源库位     to表示目标库位
    fun validateLocationCode(type: String, locCode: String) {
        // 检查 库位编码是否正确 (即检查仓库里是否有该库位 库位数量>0表示存在)
        RetrofitHelper.getBasicDataAPI()
            .countLocation((fragment.activity as BaseActivity).getWhCode(), locCode, "")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<BsLocation>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: BsLocation?) {
                    data.let {

                        val fromLocCode = fragment.binding.etFromLocCode.text.toString().trim()
                        val toLocCode = fragment.binding.etTargetLocCode.text.toString().trim()

                        if (fromLocCode.equals(toLocCode)) {

                            ToastUtils.getInstance()
                                .showErrorToastWithSound(fragment.activity, "源库位与目标库位不能相同")
                            fragment.binding.etTargetLocCode.setText("")

                        } else if (type.equals("from")) {

                            if (data!!.count < 1) {
                                ToastUtils.getInstance().showErrorToastWithSound(
                                    fragment.activity,
                                    "来源库位[" + locCode + "]不存在"
                                )
                                fragment.binding.etFromLocCode.setText("")
                                fragment.binding.etFromLocCode.requestFocus()
                            }

                        } else if (type.equals("to")) {
                            if (data!!.count < 1) {
                                fragment.binding.etTargetLocCode.setText("")
                                fragment.binding.etTargetLocCode.requestFocus()
                                ToastUtils.getInstance().showErrorToastWithSound(
                                    fragment.activity,
                                    "目标库位[" + locCode + "]不存在"
                                )
                            } else {
                                //fragment.binding.etTargetLocCode.clearFocus()
                                fragment.binding.llyCountNumber.requestFocus()
                            }

                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(fragment.activity, apiErrorModel.message)
                }

            })
    }

    fun onSelectOwner() {
        if (ownerDialog.getData().size == 0) return//一个货主不用弹出选择
        ownerDialog.show()
    }

    //处理货主
    private fun dealWithOwner(data: MutableList<FuInvLocationInventory>?) {
        var ownerCodeArray = mutableListOf<String>()//货主编码列表
        var ownerBsOwnerArray = mutableListOf<BsOwner>()//货主对象列表

        //获取所有货主信息
        data?.let {
            for (it in data) {
                if (!ownerCodeArray.contains((it as FuInvLocationInventory).ownerCode)) {
                    ownerCodeArray.add((it as FuInvLocationInventory).ownerCode)
                    var bsOwner = BsOwner()
                    bsOwner.ownerCode = (it as FuInvLocationInventory).ownerCode
                    bsOwner.ownerName = (it as FuInvLocationInventory).ownerName//之后处理
                    ownerBsOwnerArray.add(bsOwner)
                }
            }
        }

        ownerBsOwnerArray.let {
            ownerBsOwnerArray.forEach {
                it.showInfo = it.ownerCode + " (" + it.ownerName + ")"
            }

            if (ownerBsOwnerArray.size == 1) {//如果一个货主直接调用处理
                isOnlyOneOwner = true
                val owner = ownerBsOwnerArray[0]
                bsOwner = owner
                textOwnerCode.set(owner.ownerCode)
                factEnterFromLocCode(false)//一个货主马上处理
            } else {//如果两个货主以上
                isOnlyOneOwner = false
                ownerDialog.changeDataNotify(ownerBsOwnerArray)
                onSelectOwner()//两个以上货主,弹出选择
            }
        }
    }

    //实质扫码处理
    private fun factEnterFromLocCode(isEnter: Boolean) {
        //查询前 先清空上次的查询结果
        sku.set("")
        onHandQtySum.set("")
        // 清空查询列表
        if (fragment.adapter != null && fragment.adapter.data != null) {
            fragment.adapter.data.clear()
            fragment.adapter.notifyDataSetChanged()
        }

//        if (textOwnerCode.get().isNullOrEmpty()) {
//            AlertDialogUtil.showOnlyOkDialog(fragment.activity, "未输入货主编码", null)
//            return
//        }

        if (fragment.binding.etFromLocCode.text.isNullOrEmpty()) {
            ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "未输入来源库位编码")
            return
        }


        val fmLocCode = fragment.binding.etFromLocCode.text.toString().trim()
        (fragment.activity as BaseActivity).waitingDialogHelp.showDialog()

        RetrofitHelper.getWareManageAPI()
            .getWareByLocation(
                textOwnerCode.get()?.trim(),
                (fragment.activity as BaseActivity).getWhCode(),
                fmLocCode, 1, 100
            )
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<RespGetInventoryByLocation>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: RespGetInventoryByLocation?) {
                    (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()

                    if (isEnter) {//如果是回车进入就获取货主信息并处理货主信息
                        dealWithOwner(data?.fuInvLocationInventoryList)
                        return
                    }


                    if (data != null) {
                        fragment.binding.etTargetLocCode.isEnabled = true
                        fragment.binding.etTargetLocCode.requestFocus()

                        sku.set(data.sku.toString())
                        onHandQtySum.set(AppUtils.getBigDecimalValueStr(data.onHandQtySum))

                        if (data.fuInvLocationInventoryList != null) {
                            fragment.adapter.data.clear()
                            data.fuInvLocationInventoryList.forEach {
                                if (it.packageRelationList.isNullOrEmpty()) {
                                    it.onHandQtyStr = ""
                                } else {
                                    it.onHandQtyStr =
                                        handleNum(it.onHandQty, it.packageRelationList)
                                }
                                fragment.adapter.addData(it)
                            }
                            fragment.adapter.notifyDataSetChanged()
                            data.fuInvLocationInventoryList.getOrNull(0)?.let {
                                getRecommendLoc(
                                    it
                                )
                            }
                        }


                    } else {
                        fragment.binding.etTargetLocCode.isEnabled = false
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(fragment.activity, apiErrorModel.message)
                    fragment.binding.etFromLocCode.setText("")
                    fragment.binding.etTargetLocCode.isEnabled = false
                    textOwnerCode.set("")
                    ownerDialog.changeDataNotify(mutableListOf<BsOwner>())
                }
            })
    }

    private fun handleNum(
        sumQtyBig: BigDecimal,
        packageRelationList: List<PackageRelation>?
    ): String {
        val csUnit = packageRelationList?.find { it.cdprUnit == "CS" }?.cdprDesc
        val ipUnit = packageRelationList?.find { it.cdprUnit == "IP" }?.cdprDesc
        val eaUnit = packageRelationList?.find { it.cdprUnit == "EA" }?.cdprDesc ?: ""

        val packageParaCs =
            packageRelationList?.find { it.cdprUnit == "CS" }?.cdprQuantity
                ?: BigDecimal.ZERO
        val packageParaIp =
            packageRelationList?.find { it.cdprUnit == "IP" }?.cdprQuantity
                ?: BigDecimal.ZERO

        val sumCsQty = if (packageParaCs > BigDecimal.ZERO) {
            sumQtyBig.divide(packageParaCs, BigDecimal.ROUND_DOWN)
                .setScale(0, BigDecimal.ROUND_DOWN)
        } else {
            BigDecimal.ZERO
        }

        var sumQty = sumQtyBig.subtract(sumCsQty.multiply(packageParaCs))

        val sumIpQty = if (packageParaIp > BigDecimal.ZERO) {
            sumQty.divide(packageParaIp, BigDecimal.ROUND_DOWN).setScale(0, BigDecimal.ROUND_DOWN)
        } else {
            BigDecimal.ZERO
        }

        sumQty = sumQty.subtract(sumIpQty.multiply(packageParaIp))

        val sumEaQty = sumQty

        val qtyInfo = mutableListOf<String>()

        if (sumCsQty > BigDecimal.ZERO) {
            qtyInfo.add("${sumCsQty.stripTrailingZeros().toPlainString()}$csUnit")
        }

        if (sumIpQty > BigDecimal.ZERO) {
            qtyInfo.add("${sumIpQty.stripTrailingZeros().toPlainString()}$ipUnit")
        }

        if (sumEaQty > BigDecimal.ZERO) {
            qtyInfo.add("${sumEaQty.stripTrailingZeros().toPlainString()}$eaUnit")
        }

        return if (qtyInfo.isEmpty()) {
            ""
        } else {
            "(${qtyInfo.joinToString("")})"
        }
    }

    fun onEnterFromLocCode() {
        if (CheckUtil.isFastDoubleClick()) {
//            if (fromLoc.get()?.trim() == "IRA") {
//                fromLoc.set("")
//                ToastUtils.getInstance()
//                    .showErrorToastWithSound(fragment.activity, "IRA库位调出需走移库申请单审批")
//                return
//            }

            ownerDialog.getData().clear()
            textOwnerCode.set("")

            factEnterFromLocCode(true)
        }
    }

    fun submitTransfer() {
        if (CheckUtil.isFastDoubleClick()) {
            isEnableClick.set(false)

            val fromLocCode = fragment.binding.etFromLocCode.text.toString()
            val toLocCode = fragment.binding.etTargetLocCode.text.toString()

            if (fromLocCode.isBlank()) {
                ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "未输入来源库位编码")
                isEnableClick.set(true)
                return
            }

            if (textOwnerCode.get().isNullOrEmpty()) {
                ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "未输入货主编码")
                isEnableClick.set(true)
                return
            }

            if (toLocCode.isBlank()) {
                ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "未输入目标库位编码")
                isEnableClick.set(true)
                return
            }

            if (fromLocCode.equals(toLocCode)) {
                ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "源库位与目标库位不能相同")
                fragment.binding.etTargetLocCode.setText("")
                fragment.binding.etTargetLocCode.requestFocus()
                isEnableClick.set(true)
                return
            }

            val param: HashMap<String, String?> = HashMap()
            param["ownerCode"] = textOwnerCode.get().toString().trim()
            param["ownerName"] = bsOwner?.ownerName
            param["toLocCode"] = toLocCode.trim().toUpperCase()
            param["fmLocCode"] = fromLocCode.trim().toUpperCase()
            param["whCode"] = (fragment.activity as BaseActivity).getWhCode()

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            (fragment.activity as BaseActivity).waitingDialogHelp.showDialog()
            fragment.binding.btnSubmitTransfer.setBackgroundResource(R.drawable.bg_bt_gray)
            fragment.binding.btnSubmitTransfer.isClickable = false

            RetrofitHelper.getWareManageAPI()
                .movWareByLocation(requestBody)
                .compose(NetworkScheduler.compose())
                .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
                .subscribe(object : RequestCallback<Any>(fragment.activity as RxAppCompatActivity) {
                    override fun success(data: Any?) {
                        fragment.binding.btnSubmitTransfer.setBackgroundResource(R.drawable.bg_bt_blue)
                        fragment.binding.btnSubmitTransfer.isClickable = true
                        (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()

                        ToastUtils.getInstance()
                            .showSuccessToastWithSound(fragment.activity, "移库成功")
                        //移库完了 重置界面
                        resetDefault()

                        isEnableClick.set(true)
                    }

                    override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                        fragment.binding.btnSubmitTransfer.setBackgroundResource(R.drawable.bg_bt_blue)
                        fragment.binding.btnSubmitTransfer.isClickable = true
                        (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(fragment.activity, apiErrorModel.message)

                        isEnableClick.set(true)
                    }
                })
        }
    }


    fun startScanFromLoc() {
        if (fragment.binding.etFromLocCode.isEnabled) {
            (fragment.activity as TransferActivity).currentStep =
                (fragment.activity as TransferActivity).STEP_TRANS_LOC_FROM_LOC
            XQRCode.startScan(fragment.activity, BaseActivity.QR_CODE_BACK)
        }
    }

    fun startScanToLoc() {
        if (fragment.binding.etTargetLocCode.isEnabled) {
            (fragment.activity as TransferActivity).currentStep =
                (fragment.activity as TransferActivity).STEP_TRANS_LOC_TO_LOC
            XQRCode.startScan(fragment.activity, BaseActivity.QR_CODE_BACK)
        }
    }


    fun scanResult(result: String?, step: Int) {
        result?.let {
            if (step == (fragment.activity as TransferActivity).STEP_TRANS_LOC_FROM_LOC) {
                // 扫来源库位
                fragment.binding.etFromLocCode.setText(result)
                onEnterFromLocCode()
            } else if (step == (fragment.activity as TransferActivity).STEP_TRANS_LOC_TO_LOC) {
                // 扫目标库位
                fragment.binding.etTargetLocCode.setText(result)
                onEnterToLocCode()
            }
        }
    }

    init {
        val position = fragment.arguments?.getInt("position")
        if (position == 0) {
            bgColor.set(
                ColorDrawable(
                    ContextCompat.getColor(
                        fragment.requireActivity(),
                        R.color.bg_orange
                    )
                )
            )
        } else {
            bgColor.set(
                ColorDrawable(
                    ContextCompat.getColor(
                        fragment.requireActivity(),
                        R.color.btn_red
                    )
                )
            )
        }
    }
}