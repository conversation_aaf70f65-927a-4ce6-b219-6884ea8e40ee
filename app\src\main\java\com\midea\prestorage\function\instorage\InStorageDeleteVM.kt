package com.midea.prestorage.function.instorage

import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.RuleDataList
import com.midea.prestorage.beans.net.SerialScanDeleteDto
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.function.instorage.response.InReceiptSerial
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody


class InStorageDeleteVM(val activity: InStorageDeleteActivity) {

    val title = ObservableField("删除条码记录")
    val anyCode = ObservableField("")

    //单号类型
    var curOrderReceiveType = ObservableField("")   // receipt 入库单  wave波次单


    // 单号 (入库单号或彼此单号)
    private var curOrderNo = ObservableField("")

    // 条码规则
    var ruleList: MutableList<RuleDataList>? = null

    var curDeleteInReceiptSerial = InReceiptSerial()

    var containerCode: String? = null

    init {

        //这个界面是 加载 收货容器号 对应的扫码记录
        // 所以肯定是有其他activity传了个containerCode 过来才能进到这里
        val orderNo = activity.intent.getStringExtra("orderNo")
        val orderReceiveType = activity.intent.getStringExtra("orderReceiveType")
        containerCode = activity.intent.getStringExtra("containerCode")

        if (containerCode.isNullOrEmpty()) {
            AlertDialogUtil.showOnlyOkDialog(activity, "容器号为空") { activity.finish() }
        } else if (orderNo.isNullOrEmpty()) {
            AlertDialogUtil.showOnlyOkDialog(activity, "单号为空") { activity.finish() }
        } else if (orderReceiveType.isNullOrEmpty()) {
            AlertDialogUtil.showOnlyOkDialog(activity, "单号类型为空") { activity.finish() }
        } else {
            curOrderNo.set(orderNo)
            curOrderReceiveType.set(orderReceiveType)
            loadContainerData()
        }

    }


    // 编辑框 输入了要删除的条码并按下回车
    // maxDelete 最大可删除数量
    fun enterSerialNoToDelete() {

        //‘货品条码’回车事件处理：
        // 假定输入的是sn，如果不是，也通过本地解析尝试把它转成sn
        //var serialNo = CheckUtil.localParseCode(anyCode.get().toString(), ruleList)
        // 相关验证逻辑 基本由后端处理掉了?
        anyCode.set(anyCode.get().toString())

        val strAnyCode = anyCode.get().toString()

        if (strAnyCode.isBlank()) {
            loadContainerData()
            return
        }


        val filterResultList = mutableListOf<InReceiptSerial>()
        // 1.先在列表中 查找是否有和该码相同的客户商品编码 并且条码类型为1
        activity.adapter.data.forEach {
            if (!it.custItemCode.isNullOrBlank() && !it.serialType.isNullOrBlank()
                && it.custItemCode.equals(strAnyCode) && it.serialType.equals("1")
            ) {
                filterResultList.add(it)
            }
        }

        if (filterResultList.size == 0) {
            // 如果没有找到相同的custItemCode 则判断为sn码  调用query接口 走自动删除逻辑
            querySnAndDelete(strAnyCode)
        } else if (filterResultList.size == 1) {
            //如果匹配到相同的customItemCode只有1条 则弹框编辑删除数量进行删除
            curDeleteInReceiptSerial = filterResultList.get(0)

            // 根据 条码类型0或1 决定是否弹窗编辑删除数量
            onDeleteSerial(curDeleteInReceiptSerial)
        } else {
            //"存在" + filterResultList.size + "条相同商品编码的记录"
            //如果匹配到相同的customItemCode 有多条 则当前列表显示 过滤相同customItemCode 的列表
            activity.adapter.data.clear()
            filterResultList.forEach {
                activity.adapter.data.add(it)
            }
            activity.adapter.notifyDataSetChanged()
        }


    }


    fun onClearSn() {
        loadContainerData()
    }


    fun querySnAndDelete(sn: String) {
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            //"receiptCode" to
            "serialNo" to sn
        )


        if (curOrderReceiveType.get().equals("receipt")) {
            // 按入库单收货
            param["receiptCode"] = curOrderNo.get().toString()
        } else if (curOrderReceiveType.get().equals("wave")) {
            // 按波次单收货
            param["waveNo"] = curOrderNo.get().toString()
        }

        param["containerCode"] = containerCode!!


        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getAppAPI()
            .queryByBarCode(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<SerialScanDeleteDto>(activity) {
                override fun success(data: SerialScanDeleteDto?) {
                    activity.waitingDialogHelp.hidenDialog()
                    data?.let {
                        val inReceiptSerial = InReceiptSerial()
                        inReceiptSerial.serialType = data.serialType
                        inReceiptSerial.receiptCode = data.receiptCode
                        inReceiptSerial.itemCode = data.itemCode
                        if (data.custItemCode != null) {
                            inReceiptSerial.custItemCode = data.custItemCode
                        } else if (!anyCode.get().isNullOrBlank()) {
                            inReceiptSerial.custItemCode = anyCode.get()
                        }

                        // 类型0  删除接口传 serialNo=xxx
                        if (data.serialType.equals("1")) {
                            // 对于要弹框编辑删除数量的码 serialNo = 空  然后在onDeleteSerial方法中会serialNo= itemCode 或custItemCode
                            data.serialNo = ""
                        } else if (inReceiptSerial.serialType.equals("0")) {
                            // 对于不用弹框编辑删除数量的条码 serialNo = 编辑框中输入的条码
                            inReceiptSerial.serialNo = anyCode.get()
                        }


                        // 根据 条码类型0或1 决定是否弹窗编辑删除数量
                        onDeleteSerial(inReceiptSerial)
                    }

                    anyCode.set("")

                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    anyCode.set("")
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)

                }
            })
    }

    // 根据容器号 加载商品信息
    fun loadContainerData() {

        anyCode.set("")
        activity.waitingDialogHelp.showDialog()

        RetrofitHelper.getAppAPI()
            .loadContainerDeleteData(Constants.whInfo?.whCode)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<InReceiptSerial>>(activity) {
                override fun success(data: MutableList<InReceiptSerial>?) {

                    activity.waitingDialogHelp.hidenDialog()

                    data?.let {
                        activity.adapter.data.clear()
                        data.forEach {
                            activity.adapter.data.add(it)
                        }
                        activity.adapter.notifyDataSetChanged()
                        loadServerRuleList()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })

    }


    //后退
    fun back() {
        activity.finish()
    }

    fun onDeleteSerial(inReceiptSerial: InReceiptSerial) {


        // serialType 为 0 传 serialNo
        // serialType 为 1 传 itemCode
        if (inReceiptSerial.serialType.equals("0")) {
            // 条码类型为‘0’的通过扫描条码删除
            AlertDialogUtil.showOkAndCancelDialog(activity,
                "确定是否删除此序列号条码记录:" + inReceiptSerial.serialNo + "?",
                { _, i ->  //点了确定
                    deleteSerial("0", inReceiptSerial.serialNo, inReceiptSerial.receiptCode, 1, "")
                }
            ) { _, i ->  //点了取消

            }

        } else if (inReceiptSerial.serialType.equals("1")) {
            // 条码类型为‘1’的通过单击明细行弹出数量录入框删除
            if (!inReceiptSerial.itemCode.isNullOrEmpty()) {
                // 界面上要显示的是
                /* activity.deleteDialog.showDlg(
                     "确认删除数量", inReceiptSerial.itemCode,
                     inReceiptSerial.receiptCode,
                     maxDelete,
                     inReceiptSerial.custItemCode
                 )*/
                activity.deleteDialog.showDlg(inReceiptSerial)
            }

        }
    }


    fun deleteSerial(serialType: String, code: String, receiptCode: String, qty: Int, id: String) {

        val param = mutableMapOf<String, Any>()
        param.put("whCode", activity.getWhCode())
        param.put("serialType", serialType)

        if (curOrderReceiveType.get().equals("receipt")) {
            // 按入库单收货
            param.put("receiptCode", curOrderNo.get().toString())
        } else if (curOrderReceiveType.get().equals("wave")) {
            // 按波次单收货
            param.put("waveNo", curOrderNo.get().toString())
        }

        param["containerCode"] = containerCode!!

        if (id.isNotBlank()) {
            param.put("id", id.toLong())
        }

        // serialType 为 0 传 serialNo
        // serialType 为 1 时 传 itemCode
        if (serialType.equals("0")) {
            param.put("serialNo", code)
        } else if (serialType.equals("1")) {
            param.put("itemCode", code)
        }

        // 如果是类型1 还要指定数量
        if (serialType.equals("1")) {
            param.put("qty", qty.toString())
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()

        RetrofitHelper.getAppAPI()
            .deleteInReceiptSerial(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showSuccessToastWithSound(activity, "删除成功")
                    //删除成功 刷新数据
                    loadContainerData()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                    loadContainerData()
                }
            })
    }


    //根据 itemCode 列表  去查询条码规则  (留着用来后面做条码解析)
    private fun loadServerRuleList() {

        // 如果已经查过了，就不用查了
        if (ruleList != null && ruleList!!.size > 0) {
            return
        }


        val param = mutableListOf<String>()

        if (activity.adapter.data.size == 0) {
            return
        }

        activity.adapter.data.forEach {
            param.add(it.itemCode)
        }


        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .barcodeRule(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<RuleDataList>>(activity) {
                override fun success(data: MutableList<RuleDataList>?) {
                    if (data != null) {
                        ruleList = data
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    //activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }


    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        result?.let {
            anyCode.set(result)
            enterSerialNoToDelete()
        }

    }


}