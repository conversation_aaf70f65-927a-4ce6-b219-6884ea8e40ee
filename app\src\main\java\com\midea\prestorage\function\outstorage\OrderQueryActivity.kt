package com.midea.prestorage.function.outstorage

import android.content.Intent
import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.outstorage.response.RespOrderQuery
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.databinding.ActivityOrderQueryBinding

class OrderQueryActivity : BaseViewModelActivity<OrderQueryVM>()  {
    private lateinit var binding: ActivityOrderQueryUnionBinding
    private lateinit var adapter: OrderQueryAdapter

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityOrderQueryUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_order_query_care
                )
            )
        } else {
            ActivityOrderQueryUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_order_query
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        vm = ViewModelProvider.AndroidViewModelFactory(application)
            .create(OrderQueryVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        vm.showDatas.observe(this, Observer<MutableList<RespOrderQuery>> {
            showData(it)
        })

        initSpinner()
        initRecycle()

        AppUtils.requestFocus(binding.etCode)
    }

    fun initSpinner() {
        val beans = mutableListOf<String>(
            "客户订单号",
            "出库单号"
        )
        binding.spinnerStatus.setItems(beans)
        binding.spinnerStatus.selectedIndex = 0

        binding.spinnerStatus.setOnItemSelectedListener { _, position, _, _ ->
            if (position == 0) {
                binding.etCode.hint = "请输入客户订单号"
            }else {
                binding.etCode.hint = "请输入出库单号"
            }
            vm.searchType.set(beans[position])
        }
    }

    private fun initRecycle() {
        adapter = OrderQueryAdapter(vm)
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter
    }

    fun showData(data: MutableList<RespOrderQuery>) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
    }

    class OrderQueryAdapter(private val vm: OrderQueryVM?) :
        CommonAdapter<RespOrderQuery>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_order_query_care else R.layout.item_order_query) {
        override fun convert(holder: BaseViewHolder?, item: RespOrderQuery?) {
            super.convert(holder, item)


        }
    }
}