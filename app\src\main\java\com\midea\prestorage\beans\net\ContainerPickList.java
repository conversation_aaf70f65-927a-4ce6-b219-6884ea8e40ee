package com.midea.prestorage.beans.net;

import androidx.annotation.Nullable;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.beans.base.BaseItemForPopup;
import com.midea.prestorage.utils.AppUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class ContainerPickList extends BaseItemForPopup implements Serializable {

    @ShowAnnotation
    private int index;
    @ShowAnnotation
    private String taskCode;
    @ShowAnnotation
    private String waveNo;
    @ShowAnnotation
    private String taskTypeStr;
    @ShowAnnotation
    private String statusStr;
    @ShowAnnotation
    private String numCount;
    @ShowAnnotation
    private String volumeCount;
    @ShowAnnotation
    private String releasedTime;
    @ShowAnnotation
    private String dispatchNo;
    @ShowAnnotation
    private String taskStartTime;
    @ShowAnnotation
    private String confirmedBy;
    @ShowAnnotation
    private String confirmedByName;
    @ShowAnnotation
    private String shipToCustomerName;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal sumQty;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal sumCsQty;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal sumEaQty;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal sumWeight;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal sumVolume;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal sumLines;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal sumOrders;
    @ShowAnnotation
    private String csUnit;
    @ShowAnnotation
    private String eaUnit;

    private String id;
    private String createTime;
    private String updateTime;
    private String tenantCode;
    private String createUserCode;
    private String createUserName;
    private String updateUserCode;
    private String updateUserName;
    private String remark;
    private String version;
    private String deleteFlag;
    private String pageNo;
    private String pageSize;
    private String offset;
    private String orderBy;
    private String orderByType;
    private String ids;
    private String tenantCodes;
    private String count;
    private String startTime;
    private String endTime;
    private String whCode;
    private String appointedBy;
    private String appointedTime;
    private String assignedBy;
    private String taskEndTime;
    private String logicContainerId;
    private String locked;
    private String shipToCustomerCode;
    private String taskHeaderIds;
    private String taskType;
    private int status;
    private int waitCount;
    private String fromZone;
    @Nullable
    private String commonFlag;
    @ShowAnnotation
    @Nullable
    private String comPickNo;//合拣号
    @ShowAnnotation
    @Nullable
    private String shippingLoc;
    @Nullable
    private List<String> shippingLocList;

    @Nullable
    public List<String> getShippingLocList() {
        return shippingLocList;
    }

    public void setShippingLocList(@Nullable List<String> shippingLocList) {
        this.shippingLocList = shippingLocList;
    }

    @Nullable
    public String getShippingLoc() {
        return shippingLoc;
    }

    public void setShippingLoc(@Nullable String shippingLoc) {
        this.shippingLoc = shippingLoc;
    }

    @Nullable
    public String getComPickNo() {
        return comPickNo;
    }

    public void setComPickNo(@Nullable String comPickNo) {
        this.comPickNo = comPickNo;
    }

    @Nullable
    public String getCommonFlag() {
        return commonFlag;
    }

    public void setCommonFlag(@Nullable String commonFlag) {
        this.commonFlag = commonFlag;
    }

    public String getConfirmedByName() {
        return confirmedByName;
    }

    public void setConfirmedByName(String confirmedByName) {
        this.confirmedByName = confirmedByName;
    }

    public String getFromZone() {
        return fromZone;
    }

    public void setFromZone(String fromZone) {
        this.fromZone = fromZone;
    }

    public int getWaitCount() {
        return waitCount;
    }

    public void setWaitCount(int waitCount) {
        this.waitCount = waitCount;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getPageNo() {
        return pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getOffset() {
        return offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderByType() {
        return orderByType;
    }

    public void setOrderByType(String orderByType) {
        this.orderByType = orderByType;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getTenantCodes() {
        return tenantCodes;
    }

    public void setTenantCodes(String tenantCodes) {
        this.tenantCodes = tenantCodes;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getAppointedBy() {
        return appointedBy;
    }

    public void setAppointedBy(String appointedBy) {
        this.appointedBy = appointedBy;
    }

    public String getAppointedTime() {
        return appointedTime;
    }

    public void setAppointedTime(String appointedTime) {
        this.appointedTime = appointedTime;
    }

    public String getAssignedBy() {
        return assignedBy;
    }

    public void setAssignedBy(String assignedBy) {
        this.assignedBy = assignedBy;
    }

    public String getConfirmedBy() {
        return confirmedBy;
    }

    public void setConfirmedBy(String confirmedBy) {
        this.confirmedBy = confirmedBy;
    }

    public String getTaskStartTime() {
        return taskStartTime;
    }

    public void setTaskStartTime(String taskStartTime) {
        this.taskStartTime = taskStartTime;
    }

    public String getTaskEndTime() {
        return taskEndTime;
    }

    public void setTaskEndTime(String taskEndTime) {
        this.taskEndTime = taskEndTime;
    }

    public String getLogicContainerId() {
        return logicContainerId;
    }

    public void setLogicContainerId(String logicContainerId) {
        this.logicContainerId = logicContainerId;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getLocked() {
        return locked;
    }

    public void setLocked(String locked) {
        this.locked = locked;
    }

    public String getReleasedTime() {
        return releasedTime;
    }

    public void setReleasedTime(String releasedTime) {
        this.releasedTime = releasedTime;
    }

    public String getDispatchNo() {
        return dispatchNo;
    }

    public void setDispatchNo(String dispatchNo) {
        this.dispatchNo = dispatchNo;
    }

    public String getShipToCustomerCode() {
        return shipToCustomerCode;
    }

    public void setShipToCustomerCode(String shipToCustomerCode) {
        this.shipToCustomerCode = shipToCustomerCode;
    }

    public String getShipToCustomerName() {
        return shipToCustomerName;
    }

    public void setShipToCustomerName(String shipToCustomerName) {
        this.shipToCustomerName = shipToCustomerName;
    }

    public String getTaskHeaderIds() {
        return taskHeaderIds;
    }

    public void setTaskHeaderIds(String taskHeaderIds) {
        this.taskHeaderIds = taskHeaderIds;
    }

    public String getNumCount() {
        return numCount;
    }

    public void setNumCount(String numCount) {
        this.numCount = numCount;
    }

    public String getVolumeCount() {
        return volumeCount;
    }

    public void setVolumeCount(String volumeCount) {
        this.volumeCount = volumeCount;
    }

    public String getSumQty() {
        if (sumQty == null) {
            return "";
        }
        return AppUtils.getBigDecimalValue(sumQty).toPlainString();
    }

    public void setSumQty(BigDecimal sumQty) {
        this.sumQty = sumQty;
    }

    public BigDecimal getSumCsQty() {
        return sumCsQty;
    }

    public void setSumCsQty(BigDecimal sumCsQty) {
        this.sumCsQty = sumCsQty;
    }

    public BigDecimal getSumEaQty() {
        return sumEaQty;
    }

    public void setSumEaQty(BigDecimal sumEaQty) {
        this.sumEaQty = sumEaQty;
    }

    public String getSumWeight() {
        if (sumWeight == null) {
            return "";
        }
        return AppUtils.getBigDecimalValue(sumWeight).toPlainString();
    }

    public void setSumWeight(BigDecimal sumWeight) {
        this.sumWeight = sumWeight;
    }

    public String getSumVolume() {
        if (sumVolume == null) {
            return "";
        }
        return AppUtils.getBigDecimalValue(sumVolume).toPlainString();
    }

    public void setSumVolume(BigDecimal sumVolume) {
        this.sumVolume = sumVolume;
    }

    public String getSumLines() {
        if (sumLines == null) {
            return "";
        }
        return AppUtils.getBigDecimalValue(sumLines).toPlainString();
    }

    public void setSumLines(BigDecimal sumLines) {
        this.sumLines = sumLines;
    }

    public String getSumOrders() {
        if (sumOrders == null) {
            return "";
        }
        return AppUtils.getBigDecimalValue(sumOrders).toPlainString();
    }

    public void setSumOrders(BigDecimal sumOrders) {
        this.sumOrders = sumOrders;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getTaskTypeStr() {
        return taskTypeStr;
    }

    public void setTaskTypeStr(String taskTypeStr) {
        this.taskTypeStr = taskTypeStr;
    }

    public String getCsUnit() {
        return csUnit;
    }

    public void setCsUnit(String csUnit) {
        this.csUnit = csUnit;
    }

    public String getEaUnit() {
        return eaUnit;
    }

    public void setEaUnit(String eaUnit) {
        this.eaUnit = eaUnit;
    }
}