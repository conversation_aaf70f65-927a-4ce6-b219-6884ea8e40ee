package com.midea.prestorage.function.containerpick

import android.annotation.SuppressLint
import android.content.Intent
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.ContainerConfigBean
import com.midea.prestorage.beans.net.ContainerPickList
import com.midea.prestorage.beans.net.RespTaskInfo
import com.midea.prestorage.function.containerpick.dialog.CombinedSettingDialog
import com.midea.prestorage.function.containerpick.dialog.ErrorTipDialog
import com.midea.prestorage.function.inv.InvReconciliationDetailActivity
import com.midea.prestorage.function.inv.InventorySearchVM
import com.midea.prestorage.function.inv.dialog.CountInTimeSettingDialog
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.printer.BluetoothConnectBack
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody
import java.text.SimpleDateFormat
import java.util.*

class CombinedPickVM(val activity: CombinedPickActivity) {

    val isNoData = ObservableBoolean(false)

    var orderNo = ObservableField("")
    var totalNum = ObservableField("0")
    var waitNum = ObservableField("0")

    var orderNoStr: String = ""

    val isPrintOk = ObservableField(false)
    var jumpFlag = false //判断是否跳转到扫码页面
    var sortCondition = 1 //排序条件
    var sortMode = 1 //排序方式
    var oldSortCondition = 1 //排序条件
    var oldSortMode = 1 //排序方式

    var isFirstEnter = true

    companion object {
        var refreshEvent = false
    }

    private val blueBack = object : BluetoothConnectBack {
        override fun success() {
            activity.waitingDialogHelp.hidenDialog()
            isPrintOk.set(true)
        }

        override fun fail() {
            isPrintOk.set(false)
            AppUtils.showToast(activity, "打印机连接失败，请重启打印机!")
        }

        override fun connect() {
            bluetoothOpen()
        }
    }

    fun bluetoothOpen(isAuto: Boolean = true) {
        if (!Printer.isPrintOk()) {
            Printer.openBluetooth(activity, blueBack, isAuto)
        } else {
            isPrintOk.set(true)
        }
    }

    fun refreshCommand() {
        orderNoStr = ""
        isFirstEnter = true
        initList(orderNoStr, true)
    }

    @SuppressLint("SimpleDateFormat")
    val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

    @SuppressLint("SimpleDateFormat")
    fun onEnterOrderNo() {
        if (CheckUtil.isFastDoubleClick()) {
            if (orderNo.get().isNullOrEmpty()) {
                return
            }

            activity.waitingDialogHelp.showDialog()

            orderNoStr = orderNo.get().toString()
            initList(orderNoStr, false)
        }
    }

    fun initList(orderStr: String, isRefresh: Boolean = false) {
        val cal = Calendar.getInstance()
        cal.add(Calendar.DATE, -30)
        val format = SimpleDateFormat("yyyy-MM-dd")

        val startDate = "${format.format(cal.time)} 00:00:00"
        val endDate = "${format.format(Date())} 23:59:59"

        RetrofitHelper.getOutStorageAPI()
            .queryPickList(orderStr.trim(), startDate, endDate, mobile = Constants.mobile)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<ContainerPickList>>(activity) {
                override fun success(data: MutableList<ContainerPickList>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    orderNo.set("")

                    if (!isRefresh && isFirstEnter) {
                        isFirstEnter = false
                        activity.adapter.data.clear()
                    }

                    data?.let {
                        it.forEachIndexed { index, item ->
                            item.statusStr = outTaskStatus[item.status]
                            item.taskTypeStr = outTaskType[item.taskType]

                            if (isRefresh) {
                                item.index = index + 1
                                item.isSelected = false
                            } else {
                                item.index = activity.adapter.data.size + index + 1
                                item.isSelected = true
                            }
                        }

                        if (isRefresh) {
                            activity.showData(it)
                        } else {
                            activity.addData(it)
                        }
                        isNoData.set(activity.adapter.data.isEmpty())
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    if (isRefresh) {
                        totalNum.set("0")
                        waitNum.set("0")
                    }
                    orderNo.set("")
                    orderNoStr = ""
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun clearOrderNo() {
        orderNo.set("")
    }

    fun back() {
        activity.finish()
    }

    fun onItemClick(bean: ContainerPickList) {
        if (!bean.confirmedBy.isNullOrEmpty() && bean.confirmedBy != Constants.userInfo?.name) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "不是您的任务不允许操作!")
            return
        }
        refreshEvent = true
        val it = Intent(activity, CombinedPickDeatilListActivity::class.java)
        it.putExtra("comPickNo", bean.comPickNo)
        activity.startActivity(it)
    }

    fun startScan() {
        jumpFlag = true
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        orderNo.set(result)
        onEnterOrderNo()
    }

    fun bottomClick() {
        if (CheckUtil.isFastDoubleClick()) {
            val results = activity.adapter.returnBeans
            if (results.isEmpty() || results.size == 1 || results.size > 10) {
                ToastUtils.getInstance()
                    .showErrorToastWithSound(activity, "请选择两个及以上，十个以下任务！")
            } else {
                var taskCodeList = mutableListOf<String>()
                results?.forEach { it ->
                    it.taskCode?.let { it2 ->
                        taskCodeList.add(it2)
                    }
                }
                selectTaskInfoList(taskCodeList)
            }
        }
    }

    fun rePrint() {
        if (CheckUtil.isFastDoubleClick()) {
            val it = Intent(activity, ContainerReprintActivity::class.java)
            activity.startActivity(it)
        }
    }

    private fun selectTaskInfoList(codes: MutableList<String>?) {
        activity.waitingDialogHelp.showDialogUnCancel()
        val param = mutableMapOf(
            "whCode" to Constants.whInfo?.whCode,
            "taskCodeList" to codes
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .selectTaskInfoList(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<RespTaskInfo>(activity) {
                override fun success(data: RespTaskInfo?) {
                    activity.waitingDialogHelp.hidenDialog()
                    refreshEvent = false
                    val it = Intent(activity, CombinedPickDeatilActivity::class.java)
                    it.putExtra("RespTaskInfo", data)
                    activity.startActivity(it)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun showTypeMenu() {
        if (CheckUtil.isFastDoubleClick()) {
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                val settingDialog = CombinedSettingDialog(activity)
                settingDialog.setCurrentStatus(sortCondition, sortMode)
                settingDialog.setOnSettingBack(object : CombinedSettingDialog.OnSettingBack {
                    override fun onConfirmClick(condition: Int, mode: Int) {
                        sortCondition = condition
                        sortMode = mode
                        activity.showData(activity.adapter.data)
                    }
                })
                settingDialog.show()
            } else {
                activity.popMenuBinding.cbSearchType1.isChecked = sortCondition == 1
                activity.popMenuBinding.cbSearchType2.isChecked = sortCondition == 2
                activity.popMenuBinding.cbSearchType3.isChecked = sortCondition == 3
                activity.popMenuBinding.cbSearchType4.isChecked = sortMode == 1
                activity.popMenuBinding.cbSearchType5.isChecked = sortMode == 2
                activity.popupMenuWindow.showAsDropDown(activity.binding.titleBtnMore)
            }
        }
    }

    //数据字典CL_SHIPMENT_STATUS
    val outTaskStatus = mutableMapOf(
        100 to "未执行",
        300 to "已指派",
        600 to "已领取",
        750 to "执行中",
        900 to "已完成"
    )

    //数据字典CL_PICK_TASK_TYPE
    val outTaskType = mutableMapOf(
        "CSPICK" to "整拣",
        "EAPICK" to "零拣",
        "BACKPICK" to "拣货反库",
        "NORMAL" to "通用",
        "SORTPICK" to "边拣边分"
    )
}