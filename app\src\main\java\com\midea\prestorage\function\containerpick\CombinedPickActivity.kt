package com.midea.prestorage.function.containerpick

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageView
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.App
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.beans.net.ContainerPickList
import com.midea.prestorage.beans.net.ContainerPickSecondPrintList
import com.midea.prestorage.beans.net.ReplenishmentBean
import com.midea.prestorage.function.containerpick.CombinedPickVM.Companion.refreshEvent
import com.midea.prestorage.function.inv.InventorySearchVM
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityCombinedPickBinding
import com.midea.prestoragesaas.databinding.InvSearchSortMenuBinding
import com.xuexiang.xqrcode.XQRCode
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class CombinedPickActivity : BaseActivity() {

    lateinit var binding: ActivityCombinedPickUnionBinding
    private var vm = CombinedPickVM(this)
    val adapter = OutStorageListAdapter()

    lateinit var popMenuBinding: InvSearchSortMenuBinding
    lateinit var popupMenuWindow: PopupWindow

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityCombinedPickUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_combined_pick_care
                )
            )
        } else {
            ActivityCombinedPickUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_combined_pick
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        binding.vm = vm

        lifecycleScope.launch {
            while (Constants.isShowContainerPick) {
                delay(60 * 1000)
                binding.vm?.bluetoothOpen(true)
            }
        }
        initRecycle()
        initPopMenu()
        inputRequest()

        vm.refreshCommand()
    }

    private fun initPopMenu() {

        val popView = LayoutInflater.from(this).inflate(R.layout.inv_search_sort_menu, null)
        popMenuBinding = DataBindingUtil.bind(popView)!!
        popMenuBinding.vm = binding.vm

        popupMenuWindow = PopupWindow(
            popView,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        popupMenuWindow.setBackgroundDrawable(
            ColorDrawable(
                ContextCompat.getColor(
                    this,
                    R.color.colorOutlineGrey
                )
            )
        )

        popupMenuWindow.isOutsideTouchable = true

        popMenuBinding.btnSearchType1.setOnClickListener {
            vm.oldSortCondition = 1
            popMenuBinding.cbSearchType1.isChecked = true
            popMenuBinding.cbSearchType2.isChecked = false
            popMenuBinding.cbSearchType3.isChecked = false
        }
        popMenuBinding.btnSearchType2.setOnClickListener {
            vm.oldSortCondition = 2
            popMenuBinding.cbSearchType1.isChecked = false
            popMenuBinding.cbSearchType2.isChecked = true
            popMenuBinding.cbSearchType3.isChecked = false
        }
        popMenuBinding.btnSearchType3.setOnClickListener {
            vm.oldSortCondition = 3
            popMenuBinding.cbSearchType1.isChecked = false
            popMenuBinding.cbSearchType2.isChecked = false
            popMenuBinding.cbSearchType3.isChecked = true
        }
        popMenuBinding.btnSearchType4.setOnClickListener {
            vm.oldSortMode = 1
            popMenuBinding.cbSearchType4.isChecked = true
            popMenuBinding.cbSearchType5.isChecked = false
        }
        popMenuBinding.btnSearchType5.setOnClickListener {
            vm.oldSortMode = 2
            popMenuBinding.cbSearchType4.isChecked = false
            popMenuBinding.cbSearchType5.isChecked = true
        }
        popMenuBinding.tvBack.setOnClickListener {
            popupMenuWindow.dismiss()
        }
        popMenuBinding.tvSure.setOnClickListener {
            popupMenuWindow.dismiss()
            vm.sortCondition = vm.oldSortCondition
            vm.sortMode = vm.oldSortMode
            showData(adapter.data)
        }

    }

    override fun onResume() {
        super.onResume()
        if (Constants.isShowContainerPick) {
            binding.vm?.bluetoothOpen(true)
        }
        if (!vm.jumpFlag && refreshEvent) {
            vm.refreshCommand()
            refreshEvent = false
        } else {
            vm.jumpFlag = false
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Printer.closeBluetooth()
    }

    private fun initRecycle() {
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter
        adapter.setClickId(R.id.ll_click)

        adapter.setOnDataChangeListenerPosition<ContainerPickList> { bean: ContainerPickList, isSelected: Boolean ->

        }

        adapter.setOnItemClickListener { adapter, _, position ->
            val bean = adapter.data[position] as ContainerPickList
            //已领取（600）和执行中（750）的任务必带合拣号，可以直接点击这个任务进入合并拣货
            if (bean.status == 600 || bean.status == 750) {
                vm.onItemClick(bean)
            }
        }
    }

    fun inputRequest() {
        binding.etSearchOrderNo.requestFocus()
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun showData(data: MutableList<ContainerPickList>) {
        if (vm.sortMode == 1) {//升序
            data.sortWith(compareBy(
                { -it.status },
                {
                    if (vm.sortCondition == 1) it.releasedTime.takeIf { data -> data.isNotEmpty() }
                        ?: Char.MAX_VALUE else if (vm.sortCondition == 2) it.sumEaQty else it.sumCsQty
                }
            ))
        } else {//降序
            data.sortWith(
                compareByDescending<ContainerPickList> { it.status }
                    .thenByDescending {
                        if (vm.sortCondition == 1) it.releasedTime.takeIf { data -> data.isNotEmpty() }
                            ?: Char.MAX_VALUE else if (vm.sortCondition == 2) it.sumEaQty else it.sumCsQty
                    }
            )
        }

        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
        vm.totalNum.set(adapter.data.size.toString())
        vm.waitNum.set(adapter.data.count { it.status == 100 || it.status == 300 || it.status == 600 }
            .toString())
    }

    fun addData(data: MutableList<ContainerPickList>) {
        val newData = (adapter.data.filter { obj ->
            data.none {
                it.taskCode == obj.taskCode
            }
        } + data).toMutableList()

        if (vm.sortMode == 1) {//升序
            newData.sortWith(compareBy(
                { -it.status },
                {
                    if (vm.sortCondition == 1) it.releasedTime.takeIf { data -> data.isNotEmpty() }
                        ?: Char.MAX_VALUE else if (vm.sortCondition == 2) it.sumEaQty else it.sumCsQty
                }
            ))
        } else {//降序
            newData.sortWith(
                compareByDescending<ContainerPickList> { it.status }
                    .thenByDescending {
                        if (vm.sortCondition == 1) it.releasedTime.takeIf { data -> data.isNotEmpty() }
                            ?: Char.MAX_VALUE else if (vm.sortCondition == 2) it.sumEaQty else it.sumCsQty
                    }
            )
        }

        adapter.setNewInstance(newData)
        adapter.notifyDataSetChanged()
        vm.totalNum.set(adapter.data.size.toString())
        vm.waitNum.set(adapter.data.count { it.status == 100 || it.status == 300 || it.status == 600 }
            .toString())
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            vm.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    class OutStorageListAdapter :
        ListCheckBoxAdapter<ContainerPickList>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_combined_pick_care else R.layout.item_combined_pick) {

        @SuppressLint("SetTextI18n")
        override fun convert(holder: BaseViewHolder, item: BaseItemForPopup) {
            super.convert(holder, item as ContainerPickList)

            if (item.isSelected()) {
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    holder.setImageResource(R.id.img_goods, R.drawable.select_selected_care)
                } else {
                    holder.setImageResource(R.id.img_goods, R.drawable.ic_check_selected)
                }
            } else {
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    holder.setImageResource(R.id.img_goods, R.drawable.select_normal_care)
                } else {
                    holder.setImageResource(R.id.img_goods, R.drawable.ic_check_unselect)
                }
            }

            //只能勾选待执行（100）和已指派（300）任务去组成合并拣货
            if (item.status == 100 || item.status == 300) {
                holder.setGone(R.id.img_goods, false)
            } else {
                holder.setGone(R.id.img_goods, true)
            }

            if (item.taskStartTime.isNullOrEmpty()) {
                holder.setGone(R.id.ll_pick, true)
            } else {
                holder.setGone(R.id.ll_pick, false)
            }

            if (item.fromZone.isNullOrEmpty()) {
                holder.setText(R.id.tv_from_zone, "库区:")
            } else {
                holder.setText(R.id.tv_from_zone, "库区:" + item.fromZone)
            }

            if (item.status == 600 || item.status == 750) {
                holder.setGone(R.id.tv_comPickNo, false)
                holder.setText(R.id.tv_comPickNo, "合拣号:" + item.comPickNo)
            } else {
                holder.setGone(R.id.tv_comPickNo, true)
            }

            when (item.status) {
                100 -> {
                    holder.setTextColor(
                        R.id.tv_status,
                        ContextCompat.getColor(App.mInstance, R.color.colorTextGray)
                    )
                    holder.setBackgroundResource(R.id.tv_status, R.drawable.bg_bt_litter_gray)
                }
                300 -> {
                    holder.setTextColor(
                        R.id.tv_status,
                        ContextCompat.getColor(App.mInstance, R.color.colorTextGray)
                    )
                    holder.setBackgroundResource(R.id.tv_status, R.drawable.bg_bt_litter_gray)
                }
                600 -> {
                    holder.setTextColor(
                        R.id.tv_status,
                        ContextCompat.getColor(App.mInstance, R.color.colorWhite)
                    )
                    holder.setBackgroundResource(R.id.tv_status, R.drawable.bg_bt_green)
                }
                750 -> {
                    holder.setTextColor(
                        R.id.tv_status,
                        ContextCompat.getColor(App.mInstance, R.color.colorWhite)
                    )
                    holder.setBackgroundResource(R.id.tv_status, R.drawable.bg_bt_blue)
                }
                900 -> {
                    holder.setTextColor(
                        R.id.tv_status,
                        ContextCompat.getColor(App.mInstance, R.color.colorWhite)
                    )
                    holder.setBackgroundResource(R.id.tv_status, R.drawable.bg_bt_red)
                }
            }

            holder?.setGone(R.id.ll_label, "LABEL" != item.commonFlag)
        }
    }
}