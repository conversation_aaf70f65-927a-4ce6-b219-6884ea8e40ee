package com.midea.prestorage.function.planstockold

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.PlanStockDetailList
import com.midea.prestoragesaas.databinding.ActivityPlanStockDetailOldBinding

class PlanStockDetailActivity : BaseActivity() {

    lateinit var binding: ActivityPlanStockDetailOldBinding
    private var vm: PlanStockDetailVM? = null
    val adapter = OutPoolStorageAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding =
            DataBindingUtil.setContentView(this, R.layout.activity_plan_stock_detail_old)
        binding.vm = PlanStockDetailVM(this)
        vm = binding.vm

        initRecycle()
        initLoadMore()
    }

    private fun initRecycle() {
        binding.rv.layoutManager = LinearLayoutManager(this)
        binding.rv.adapter = adapter

        adapter.setOnItemClickListener { adapter, view, position ->
            val item = adapter.data[position] as PlanStockDetailList
            binding.vm!!.setOnItemClick(item)
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.startSearch()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    fun initSpinner(list: MutableList<DCBean>) {
        val beans = mutableListOf<String>()
        list.forEach {
            beans.add(it.key)
        }
        binding.spinnerStatus.setItems(beans)

        binding.spinnerStatus.setOnItemSelectedListener { _, position, _, _ ->
            binding.vm!!.onChangeStatue(list[position])
        }
    }

    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果
            //binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    fun goodsFocus() {
        binding.edGoods.requestFocus()
    }

    fun qtyFocus() {
        binding.edQty.requestFocus()
    }

    class OutPoolStorageAdapter :
        CommonAdapter<PlanStockDetailList>(R.layout.item_plan_stock_detail_old),
        LoadMoreModule {
        override fun convert(holder: BaseViewHolder?, item: PlanStockDetailList) {
            super.convert(holder, item)

            //货品编码或69码显示
            val custItemCode = holder?.itemView?.findViewWithTag<TextView>("custItemCode")
            custItemCode?.text =
                if (item.whBarcode69 == null || item.whBarcode69.trim() == "") item.custItemCode else item.whBarcode69

            // 已完成的 标记为绿色背景色  未完成的白色背景色
            if (!TextUtils.isEmpty(item.firstQty) && item == data[0]) {
                holder?.setBackgroundColor(R.id.ll_item, Color.parseColor("#8de0cc"))
                holder?.setBackgroundColor(R.id.ll_item_name, Color.parseColor("#8de0cc"))
            } else {
                holder?.setBackgroundColor(R.id.ll_item, Color.parseColor("#ffffff"))
                holder?.setBackgroundColor(R.id.ll_item_name, Color.parseColor("#ffffff"))
            }
        }
    }
}