package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.beans.base.BaseItemForPopup;
import com.midea.prestorage.beans.base.BaseItemShowInfo;
import com.midea.prestorage.function.inv.response.PackageRelation;
import com.midea.prestorage.function.inv.response.PackageRelationList;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Created by LIXK5 on 2019/4/28.
 */

public class ReplenishmentBean implements Serializable {


    private int totalCount;
    private int pageNo;
    private int totalPage;
    private int pageSize;
    private int offset;

    private List<ReplenishmentListBean> list;

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public List<ReplenishmentListBean> getList() {
        return list;
    }

    public void setList(List<ReplenishmentListBean> list) {
        this.list = list;
    }

    public class ReplenishmentListBean extends BaseItemForPopup {

        @ShowAnnotation
        private String itemCode;
        @ShowAnnotation
        private String custItemCode;
        @ShowAnnotation
        private String itemName;
        @ShowAnnotation
        private String fromLoc;
        @ShowAnnotation
        private String createTime;
        @ShowAnnotation
        private String fromZone;
        @ShowAnnotation
        private String orderByValue;
        @ShowAnnotation
        private String assignedUser;
        @ShowAnnotation
        private String agingDate;
        @ShowAnnotation
        private String fromArea;
        @ShowAnnotation
        private String updateTime;
        @ShowAnnotation
        private String toLoc;
        @ShowAnnotation
        private String toZone;

        private String toZoneGroupName;
        private String id;
        private String pageSize;
        private String taskCode;
        private String companyCode;
        private String totalQty;
        private String unitQty;
        private String unit;
        private String fromZoneGroup;
        private String toZoneGroup;
        private int taskLevel;//1紧急
        private String status;
        private String barCode;
        private String orderByAttribute;
        private String subTaskType;
        private String manufactureDate;
        private String toArea;
        private String basicUnitQty;
        private String lotNum;
        @ShowAnnotation
        private String toZoneName;
        private boolean isFinish;
        private String whCsBarcode69;
        private String whMaxBarcode69;
        private List<PackageRelationList> packageRelationList;
        private BigDecimal availableQty;
        private String cdpaFormat;
        private int isEditable;
        private String ownerCode;
        private String lotAtt01;
        private String lotAtt02;
        private String lotAtt04;
        private String lotAtt05;
        private String lotAtt06;

        private int isDecimal;

        public int getIsDecimal() {
            return isDecimal;
        }

        public void setIsDecimal(int isDecimal) {
            this.isDecimal = isDecimal;
        }

        public String getLotAtt01() {
            return lotAtt01;
        }

        public void setLotAtt01(String lotAtt01) {
            this.lotAtt01 = lotAtt01;
        }

        public String getLotAtt02() {
            return lotAtt02;
        }

        public void setLotAtt02(String lotAtt02) {
            this.lotAtt02 = lotAtt02;
        }

        public String getLotAtt04() {
            return lotAtt04;
        }

        public void setLotAtt04(String lotAtt04) {
            this.lotAtt04 = lotAtt04;
        }

        public String getLotAtt05() {
            return lotAtt05;
        }

        public void setLotAtt05(String lotAtt05) {
            this.lotAtt05 = lotAtt05;
        }

        public String getLotAtt06() {
            return lotAtt06;
        }

        public void setLotAtt06(String lotAtt06) {
            this.lotAtt06 = lotAtt06;
        }

        public String getOwnerCode() {
            return ownerCode;
        }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
        }

        public int getIsEditable() {
            return isEditable;
        }

        public void setIsEditable(int isEditable) {
            this.isEditable = isEditable;
        }

        public String getCdpaFormat() {
            return cdpaFormat;
        }

        public void setCdpaFormat(String cdpaFormat) {
            this.cdpaFormat = cdpaFormat;
        }

        public BigDecimal getAvailableQty() {
            return availableQty;
        }

        public void setAvailableQty(BigDecimal availableQty) {
            this.availableQty = availableQty;
        }

        public List<PackageRelationList> getPackageRelationList() {
            return packageRelationList;
        }

        public void setPackageRelationList(List<PackageRelationList> packageRelationList) {
            this.packageRelationList = packageRelationList;
        }

        public String getWhCsBarcode69() {
            return whCsBarcode69;
        }

        public void setWhCsBarcode69(String whCsBarcode69) {
            this.whCsBarcode69 = whCsBarcode69;
        }

        public String getWhMaxBarcode69() {
            return whMaxBarcode69;
        }

        public void setWhMaxBarcode69(String whMaxBarcode69) {
            this.whMaxBarcode69 = whMaxBarcode69;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getPageSize() {
            return pageSize;
        }

        public void setPageSize(String pageSize) {
            this.pageSize = pageSize;
        }

        public String getTaskCode() {
            return taskCode;
        }

        public void setTaskCode(String taskCode) {
            this.taskCode = taskCode;
        }

        public String getCompanyCode() {
            return companyCode;
        }

        public void setCompanyCode(String companyCode) {
            this.companyCode = companyCode;
        }

        public String getFromLoc() {
            return fromLoc;
        }

        public void setFromLoc(String fromLoc) {
            this.fromLoc = fromLoc;
        }

        public String getItemCode() {
            return itemCode;
        }

        public void setItemCode(String itemCode) {
            this.itemCode = itemCode;
        }

        public String getTotalQty() {
            return totalQty;
        }

        public void setTotalQty(String totalQty) {
            this.totalQty = totalQty;
        }

        public String getUnitQty() {
            return unitQty;
        }

        public void setUnitQty(String unitQty) {
            this.unitQty = unitQty;
        }

        public String getUnit() {
            if ("CS".equals(unit)) {
                return "箱";
            } else if ("EA".equals(unit)) {
                return "个";
            } else {
                return "托盘";
            }
        }

        public String getOriginalUnit() {
            return  unit;
        }

        public String getUnitValue() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public String getFromZoneGroup() {
            return fromZoneGroup;
        }

        public void setFromZoneGroup(String fromZoneGroup) {
            this.fromZoneGroup = fromZoneGroup;
        }

        public String getToZoneGroup() {
            return toZoneGroup;
        }

        public void setToZoneGroup(String toZoneGroup) {
            this.toZoneGroup = toZoneGroup;
        }

        public String getToZoneGroupName() {
            return toZoneGroupName;
        }

        public void setToZoneGroupName(String toZoneGroupName) {
            this.toZoneGroupName = toZoneGroupName;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getBarCode() {
            return barCode;
        }

        public void setBarCode(String barCode) {
            this.barCode = barCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public boolean isFinish() {
            return isFinish;
        }

        public void setFinish(boolean finish) {
            isFinish = finish;
        }

        public int getTaskLevel() {
            return taskLevel;
        }

        public void setTaskLevel(int taskLevel) {
            this.taskLevel = taskLevel;
        }

        public String getOrderByValue() {
            return orderByValue;
        }

        public void setOrderByValue(String orderByValue) {
            this.orderByValue = orderByValue;
        }

        public String getToLoc() {
            return toLoc;
        }

        public void setToLoc(String toLoc) {
            this.toLoc = toLoc;
        }

        public String getSubTaskType() {
            return subTaskType;
        }

        public void setSubTaskType(String subTaskType) {
            this.subTaskType = subTaskType;
        }

        public String getAssignedUser() {
            return assignedUser;
        }

        public void setAssignedUser(String assignedUser) {
            this.assignedUser = assignedUser;
        }

        public String getAgingDate() {
            return agingDate;
        }

        public void setAgingDate(String agingDate) {
            this.agingDate = agingDate;
        }

        public String getManufactureDate() {
            return manufactureDate;
        }

        public void setManufactureDate(String manufactureDate) {
            this.manufactureDate = manufactureDate;
        }

        public String getOrderByAttribute() {
            if ("lot_att01".equals(orderByAttribute)) {
                return "生产日期";
            } else if ("lot_att02".equals(orderByAttribute)) {
                return "失效日期";
            } else if ("lot_att03".equals(orderByAttribute)) {
                return "入库日期";
            } else if ("lot_att04".equals(orderByAttribute)) {
                return "状态";
            } else if ("lot_att05".equals(orderByAttribute)) {
                return "批次";
            } else {
                return orderByAttribute == null ? "" : orderByAttribute;
            }
        }

        public void setOrderByAttribute(String orderByAttribute) {
            this.orderByAttribute = orderByAttribute;
        }

        public String getFromZone() {
            return fromZone;
        }

        public void setFromZone(String fromZone) {
            this.fromZone = fromZone;
        }

        public String getToZone() {
            return toZone;
        }

        public void setToZone(String toZone) {
            this.toZone = toZone;
        }

        public String getBasicUnitQty() {
            return basicUnitQty;
        }

        public void setBasicUnitQty(String basicUnitQty) {
            this.basicUnitQty = basicUnitQty;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getCustItemCode() {
            return custItemCode;
        }

        public void setCustItemCode(String custItemCode) {
            this.custItemCode = custItemCode;
        }

        public String getFromArea() {
            return fromArea;
        }

        public void setFromArea(String fromArea) {
            this.fromArea = fromArea;
        }

        public String getLotNum() {
            return lotNum;
        }

        public void setLotNum(String lotNum) {
            this.lotNum = lotNum;
        }

        public String getToArea() {
            return toArea;
        }

        public void setToArea(String toArea) {
            this.toArea = toArea;
        }

        public String getToZoneName() {
            return toZoneName;
        }

        public void setToZoneName(String toZoneName) {
            this.toZoneName = toZoneName;
        }
    }
}
