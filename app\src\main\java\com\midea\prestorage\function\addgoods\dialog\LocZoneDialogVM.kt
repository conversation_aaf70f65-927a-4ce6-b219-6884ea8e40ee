package com.midea.prestorage.function.addgoods.dialog

import androidx.databinding.ObservableField
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.function.instorage.response.RespZonePage
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.kotlin.bindUntilEvent

class LocZoneDialogVM(val dialog: LocZoneDialog) {

    val title = ObservableField<String>("请选择上架库位")
    val type = ObservableField<String>("from")
    var pageNo = 1
    var totalPage = 10

    val filterInfo = ObservableField("")
    val inputTextChange = object : ViewBindingAdapter.TextChangedListener {

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            initPalletInfo(true, s.toString())
        }
    }

    fun initPalletInfo(isSearch: Boolean, searchInfo: String? = null) {
        if (isSearch) {
            dialog.adapter.setNewInstance(mutableListOf())
            pageNo = 1
        }
        if (pageNo > totalPage) {
            return
        }

        RetrofitHelper.getAppAPI()
            .getZonePage(
                pageNo,
                10,
                searchInfo,
                (dialog.mContext as BaseActivity).getWhCode(),
                if ("from" == type.get().toString()) null else "PICK",
                if ("from" == type.get().toString()) "1" else null
            )
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(dialog.mContext, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PageResult<RespZonePage>>(dialog.mContext) {
                override fun success(data: PageResult<RespZonePage>?) {
                    data?.let { result ->

                        val info = mutableListOf<BaseItemShowInfo>()
                        if (pageNo == 1 && searchInfo.isNullOrEmpty()) {
                            info.add(BaseItemShowInfo("全部").also {
                                it.payload = ""
                            })
                        }
                        result.list?.forEach { item ->
                            info.add(BaseItemShowInfo(item.zoneName).also {
                                it.payload = item.zoneCode
                            })
                        }

                        dialog.addData(info)

                        totalPage = if (result.totalCount == 0) 1 else result.totalPage
                        pageNo = result.pageNo + 1

                        if (pageNo > result.totalPage) {
                            dialog.endLoad()
                        } else {
                            dialog.stopLoad()
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    AppUtils.showToast(dialog.mContext, apiErrorModel.message)
                }
            })
    }

    fun close() {
        dialog.dismiss()
    }
}