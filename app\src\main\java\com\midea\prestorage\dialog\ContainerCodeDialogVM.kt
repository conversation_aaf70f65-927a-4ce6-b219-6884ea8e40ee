package com.midea.prestorage.dialog

import android.view.View
import androidx.databinding.ObservableField

class ContainerCodeDialogVM(var dialog: ContainerCodeDialog) {
    val titleName = ObservableField<String>("")
    val msg = ObservableField<String>("")
    val containerCode = ObservableField<String>("")
    val taskCode = ObservableField<String>("")

    var listener: ContainerCodeDialog.OnTipBack? = null

    /**
     * 确认按钮
     */
    val confirmClick = View.OnClickListener {
        dialog?.dismiss()
        listener?.onConfirmClick()
    }
}