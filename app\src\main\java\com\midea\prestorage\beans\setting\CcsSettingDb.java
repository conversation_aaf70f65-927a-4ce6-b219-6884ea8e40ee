package com.midea.prestorage.beans.setting;

import org.xutils.db.annotation.Column;
import org.xutils.db.annotation.Table;

@Table(name = "CcsSettingDb")
public class CcsSettingDb {
    @Column(name = "id", isId = true, autoGen = true, property = "NOT NULL")
    private int id;

    @Column(name = "isChecked")
    public boolean isChecked;

    public CcsSettingDb() {
    }

    public CcsSettingDb(boolean isChecked) {
        this.isChecked = isChecked;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setChecked(boolean checked) {
        isChecked = checked;
    }

    public boolean isChecked() {
        return isChecked;
    }
}
