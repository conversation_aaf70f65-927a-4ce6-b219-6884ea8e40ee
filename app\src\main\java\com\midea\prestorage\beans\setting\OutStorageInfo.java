package com.midea.prestorage.beans.setting;

import org.xutils.db.annotation.Column;
import org.xutils.db.annotation.Table;

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/9/23$
 */
@Table(name = "OutStorageInfo")
public class OutStorageInfo {

    @Column(name = "id", isId = true, autoGen = true, property = "NOT NULL")
    private int id;

    //记录订单详情是否汇总的字段  0为明细显示，1为汇总显示(出库订单池详情)
    @Column(name = "orderDetailShowMode")
    private int orderDetailShowMode;

    //拣货任务池显示字段,true为按订单，false为按波次
    @Column(name = "orderPoolShowMode")
    private boolean orderPoolShowMode;

    //拣货任务池显示字段,true为按订单，false为按波次
    @Column(name = "doNotNotice")
    private boolean doNotNotice;


    public OutStorageInfo() {
    }

    public int getOrderDetailShowMode() {
        return orderDetailShowMode;
    }

    public void setOrderDetailShowMode(int orderDetailShowMode) {
        this.orderDetailShowMode = orderDetailShowMode;
    }

    public boolean isOrderPoolShowMode() {
        return orderPoolShowMode;
    }

    public void setOrderPoolShowMode(boolean orderPoolShowMode) {
        this.orderPoolShowMode = orderPoolShowMode;
    }

    public boolean isDoNotNotice() {
        return doNotNotice;
    }

    public void setDoNotNotice(boolean doNotNotice) {
        this.doNotNotice = doNotNotice;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
}
