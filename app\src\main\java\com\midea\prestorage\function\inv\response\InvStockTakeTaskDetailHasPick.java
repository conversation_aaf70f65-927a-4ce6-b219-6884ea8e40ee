package com.midea.prestorage.function.inv.response;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.math.BigDecimal;

public class InvStockTakeTaskDetailHasPick {

    @ShowAnnotation
    String custItemCode;

    @ShowAnnotation
    String fromLoc;

    @ShowAnnotation
    String itemName;

    @ShowAnnotation(isDecimal = true)
    BigDecimal toQty;

    @ShowAnnotation
    String taskEndTime;

    @ShowAnnotation(isDecimal = true)
    BigDecimal fromQty;

    @ShowAnnotation
    private String lotAtt04Str;

    String lotAtt04;

    private String barcode;

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getFromLoc() {
        return fromLoc;
    }

    public void setFromLoc(String fromLoc) {
        this.fromLoc = fromLoc;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public BigDecimal getToQty() {
        return toQty;
    }

    public void setToQty(BigDecimal toQty) {
        this.toQty = toQty;
    }

    public String getTaskEndTime() {
        return taskEndTime;
    }

    public void setTaskEndTime(String taskEndTime) {
        this.taskEndTime = taskEndTime;
    }

    public BigDecimal getFromQty() {
        return fromQty;
    }

    public void setFromQty(BigDecimal fromQty) {
        this.fromQty = fromQty;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getLotAtt04Str() {
        return lotAtt04Str;
    }

    public void setLotAtt04Str(String lotAtt04Str) {
        this.lotAtt04Str = lotAtt04Str;
    }
}
