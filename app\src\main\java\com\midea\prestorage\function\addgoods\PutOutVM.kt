package com.midea.prestorage.function.addgoods

import android.text.TextUtils
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.PackageResp
import com.midea.prestorage.beans.net.ReplenishmentBean
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.MySoundUtils
import com.midea.prestorage.utils.TTSUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody

class PutOutVM(val activity: PutOutActivity) {

    val goods = ObservableField<String>("")
    val location = ObservableField<String>("")
    val inputNum = ObservableField<String>("")
    var pageNo = 1 //当前第一页

    val custItemCode = ObservableField<String>()
    val itemName = ObservableField<String>()
    val fromZoneGroup = ObservableField<String>()
    val fromLoc = ObservableField<String>()
    val createTime = ObservableField<String>()
    val unitInfo = ObservableField<String>()
    val totalQty = ObservableField<String>()
    val unitQty = ObservableField<String>()
    val orderByValue = ObservableField<String>()
    val batchResp = ObservableField<String>()
    val isHaveData = ObservableField(true)
    val isEnable = ObservableField(true)
    var isGoodsOk: Boolean = false

    private var packageResp = activity.intent.getSerializableExtra("packageResp") as PackageResp?
    var batchDistrictResp = activity.intent.getSerializableExtra("batchResp") as PackageResp?

    var currentSearchBean: ReplenishmentBean.ReplenishmentListBean? = null
    var allShowData = mutableListOf<ReplenishmentBean.ReplenishmentListBean>()
    var index = 0

    var totalCount = 0

    var isDefaultNum = false
    var isDefaultNext = false

    val isShowDialogClickable = ObservableField(true)

    val isPoint = MutableLiveData(9999)

    init {
        val queryCode = activity.intent.getStringExtra("queryCode")
        goods.set(queryCode)
        searchData(true)
    }

    fun onLocationClick() {
        activity.showDialog()
    }

    val enterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            pageNo = 1
            searchData(true, isSearch = true)
        }
    }

    fun putOut() {
        val intent = PutInActivity.newIntent(activity, "补货上架", packageResp, batchDistrictResp)
        activity.startActivity(intent)
        activity.finish()
    }

    fun confirmDown() {
        if (!isGoodsOk) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "请先扫描商品!")
            activity.goodsRequest()
            return
        }
        if (TextUtils.isEmpty(inputNum.get()) || inputNum.get() == "0") {
            inputNum.set("")
            ToastUtils.getInstance().showErrorToastWithSound(activity, "请输入数量!")
            return
        }

        putOnShelf()
    }

    private fun putOnShelf() {
        activity.waitingDialogHelp.showDialog()
        val map = mutableMapOf<String, Any?>(
            "whCode" to activity.getWhCode()
        )

        val list = mutableListOf<MutableMap<String, String?>>()
        val mapTo = mutableMapOf<String, String?>()
        mapTo["taskDetailId"] = currentSearchBean!!.id
        mapTo["qty"] = inputNum.get()
        mapTo["locationCode"] = currentSearchBean!!.fromLoc
        list.add(mapTo)

        map["details"] = list

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        val response = if (Constants.whInfo?.bearingSystem == "3") {
            RetrofitHelper.getAddGoodsService()
                .offShelfBwms(requestBody)
        } else {
            RetrofitHelper.getAddGoodsService()
                .offShelf(requestBody)
        }
        response
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showSuccessToast(activity, "下架成功!")
                    TTSUtils.startAuto("下架成功")
                    currentSearchBean!!.isFinish = true
                    val results = allShowData.filter { !it.isFinish }

                    if (results.isEmpty()) {
                        activity.finish()
                        AppUtils.showToast(activity, "所有任务下架完成!")
                        return
                    }

                    //去除已经完成了的库位
                    val set = mutableSetOf<String>()
                    results.forEach {
                        set.add(it.fromLoc)
                    }
                    val beans = mutableListOf<DCBean>()
                    set.forEach {
                        beans.add(DCBean(it, it))
                    }
                    activity.resetLocation(beans)

                    if (isDefaultNext) {
                        onNext()
                    } else {
                        inputNum.set("")
                    }
//                    if (isDefaultNum) {
                    activity.goodsRequest()
//                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    inputNum.set("")
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun setMode() {
        if (isDefaultNum) {
            inputNum.set(currentSearchBean?.totalQty)
            isEnable.set(false)
        } else {
            if (currentSearchBean?.isEditable == 0) {
                inputNum.set(currentSearchBean?.totalQty)
                isEnable.set(false)
            }else {
                inputNum.set("")
                isEnable.set(true)
            }
        }
    }

    fun searchData(
        isRefresh: Boolean,
        isAllFinish: Boolean = false,
        isAudio: Boolean = true,
        isSearch: Boolean = false
    ) {
        activity.waitingDialogHelp.showDialog()
        val map = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "day" to "30",
            "queryType" to 2,
            "pageNo" to pageNo,
            "pageSize" to 10,
            "subTaskType" to "PICK",
            "queryCode" to goods.get(),
            "locationCode" to "",
            "itemCodes" to mutableListOf<String>(),
            "orderBy" to "task_level,id",
            "orderByType" to "asc"
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        val response = if (Constants.whInfo?.bearingSystem == "3") {
            RetrofitHelper.getAddGoodsService()
                .rfQueryNew(requestBody)
        } else {
            RetrofitHelper.getAddGoodsService()
                .rfQuery(requestBody)
        }
        response
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ReplenishmentBean>(activity!!) {
                override fun success(data: ReplenishmentBean?) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (data != null && data.list != null && data.list.isNotEmpty()) {
                        data.list.sortBy {
                            it.fromLoc
                        }

                        val set = mutableSetOf<String>()
                        data.list.forEach {
                            set.add(it.fromLoc)
                        }
                        val beans = mutableListOf<DCBean>()
                        set.forEach {
                            beans.add(DCBean(it, it))
                        }
                        activity.initLocation(beans)

                        if (isRefresh) {
                            index = 0
                            allShowData.clear()
                            totalCount = data.totalCount

                            if (isAllFinish) {
                                if (totalCount == 0) {
                                    ToastUtils.getInstance()
                                        .showErrorToastWithSound(activity, "所有任务已完成!")
                                }
                            }
                        }

                        if (isSearch) {
                            isGoodsOk = true
                            activity.qtyRequest()
                        }

                        if (isSearch && !isDefaultNum) {
                            activity.qtyRequest()
                        }

                        allShowData.addAll(data.list)
                        allShowData.forEachIndexed { i, replenishmentListBean ->
                            if (location.get() == replenishmentListBean.fromLoc) {
                                index = i
                                return@forEachIndexed
                            }
                        }
                        currentSearchBean = allShowData[index]
                        isPoint.value = currentSearchBean?.isDecimal
                        setIndexInfo()
                        if (isAudio) {
                            MySoundUtils.getInstance(activity).dingSound()
                        }
                    } else {
                        if (isRefresh) {
                            isHaveData.set(false)
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(activity, "您当前没有下架任务!")
                            activity.showNoDataInfo()
                            MySoundUtils.getInstance(activity).errorSound()
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (index > 0 && !isRefresh) {
                        index--
                    }
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun setIndexToLocal(local: String) {
        allShowData.forEachIndexed { i, replenishmentListBean ->
            if (local == replenishmentListBean.fromLoc) {
                index = i
                return@forEachIndexed
            }
        }
        currentSearchBean = allShowData[index]
        isPoint.value = currentSearchBean?.isDecimal
        setIndexInfo()
    }

    fun setIndexInfo() {
        val results = packageResp?.list?.filter { it.code == currentSearchBean?.unit }
        if (results != null && results.isNotEmpty()) {
            currentSearchBean?.unit = results[0].name
        }
        val resultsBatch =
            batchDistrictResp?.list?.filter { it.code == currentSearchBean?.orderByAttribute }
        if (resultsBatch != null && resultsBatch.isNotEmpty()) {
            currentSearchBean?.orderByAttribute = resultsBatch[0].name
        }
        custItemCode.set(currentSearchBean?.custItemCode)
        itemName.set(currentSearchBean?.itemName)
        fromLoc.set(currentSearchBean?.fromLoc)
        createTime.set(currentSearchBean?.createTime)
        unitInfo.set(currentSearchBean?.basicUnitQty + "/箱")
        totalQty.set(currentSearchBean?.totalQty)
        fromZoneGroup.set(currentSearchBean?.barCode)
        orderByValue.set(currentSearchBean?.orderByValue)
        unitQty.set(currentSearchBean?.unitQty + currentSearchBean?.unit)
        batchResp.set(currentSearchBean?.orderByAttribute)
        if (isDefaultNum) {
            inputNum.set(currentSearchBean?.totalQty)
            isEnable.set(false)
        } else {
            if (currentSearchBean?.isEditable == 0) {
                inputNum.set(currentSearchBean?.totalQty)
                isEnable.set(false)
            }else {
                inputNum.set("")
                isEnable.set(true)
            }
        }
        location.set(currentSearchBean?.fromLoc)
        isShowDialogClickable.set(currentSearchBean?.isEditable == 1)
        goods.set("")
    }

    fun setting() {
        var intent = SettingOutActivity.newIntent(activity, "下架设置")
        activity.startActivity(intent)
    }

    fun cleanGoods() {
        goods.set("")
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun onNext() {
        if (allShowData.isNullOrEmpty()) {
            return
        }
        index++
        if (index > allShowData.size - 1) {
            if (index > totalCount - 1) {
                index = 0
                if (allShowData[index].isFinish) {
                    onNext()
                    return
                }
            } else {
                pageNo++
                searchData(false)
                return
            }
        } else {
            if (allShowData[index].isFinish) {
                onNext()
                return
            }
        }
        currentSearchBean = allShowData[index]
        isPoint.value = currentSearchBean?.isDecimal
        setIndexInfo()
        activity.goodsRequest()
    }

    fun back() {
        activity.finish()
    }

    fun scanResult(result: String?) {
        if (!TextUtils.isEmpty(result)) {
            goods.set(result)
            enterKeyPress.onEnterKey()
        }
    }
}