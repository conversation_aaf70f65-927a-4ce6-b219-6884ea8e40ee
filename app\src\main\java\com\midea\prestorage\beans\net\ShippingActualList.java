package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;
import java.math.BigDecimal;

public class ShippingActualList implements Serializable {

    private String waveNo;
    private String ownerCode;
    private BigDecimal oqcCsQty;
    private BigDecimal oqcEaQty;
    private BigDecimal packagePara;

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public BigDecimal getOqcCsQty() {
        return oqcCsQty;
    }

    public void setOqcCsQty(BigDecimal oqcCsQty) {
        this.oqcCsQty = oqcCsQty;
    }

    public BigDecimal getOqcEaQty() {
        return oqcEaQty;
    }

    public void setOqcEaQty(BigDecimal oqcEaQty) {
        this.oqcEaQty = oqcEaQty;
    }

    public BigDecimal getPackagePara() {
        return packagePara;
    }

    public void setPackagePara(BigDecimal packagePara) {
        this.packagePara = packagePara;
    }
}