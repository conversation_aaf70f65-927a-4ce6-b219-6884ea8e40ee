package com.midea.prestorage.function.pointjoin.fragment

import android.app.Dialog
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.midea.prestoragesaas.R
import com.midea.prestorage.beans.net.PointDivideDetailBean
import com.midea.prestoragesaas.databinding.DialogPointJoinBinding
import com.midea.prestorage.widgets.ZoomOutPageTransformer
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/6/26$
 */
class PointJoinDialog(var mContext: RxAppCompatActivity) : Dialog(mContext) {

    private var binding: DialogPointJoinBinding

    //    private val adapter = ListChoiceClickAdapter<BaseItemShowInfo>(R.layout.dialog_list_item_click)
    val pointJoinDialog = this@PointJoinDialog

    init {
        val view = LayoutInflater.from(mContext).inflate(R.layout.dialog_point_join, null)
        setCanceledOnTouchOutside(true)

        binding = DataBindingUtil.bind(view)!!
        binding.vm = PointJoinDialogVM(this)
        setContentView(view)
        setCancelable(false)
        initViewPager()
    }

    private fun initViewPager() {
        val recyclerView = binding.viewPager.getChildAt(0) as RecyclerView
        recyclerView.setPadding(70, 0, 70, 0)
        recyclerView.clipToPadding = false
        //设置适配器
        binding.viewPager.setPageTransformer(ZoomOutPageTransformer()) //设置画廊模式
    }

    class MyAdapter(
        activity: FragmentActivity,
        var dialog: PointJoinDialog,
        private val items: MutableList<PointDivideDetailBean>
    ) : FragmentStateAdapter(activity) {
        val fragments = mutableListOf<PointJoinDialogFragment>()

        override fun getItemCount(): Int {
            return items.size
        }

        override fun createFragment(position: Int): Fragment {
//            return PointJoinDialogFragment.newInstance(items[position])
            return Fragment()
        }
    }

    fun setData(data: MutableList<PointDivideDetailBean>) {
        val adapter = MyAdapter(mContext, this, data)
        binding.viewPager.adapter = adapter
    }
}
