package com.midea.prestorage.function.containerpick

import android.widget.EditText
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestoragesaas.databinding.ActivityCombinedPickDeatilListBinding
import com.midea.prestoragesaas.databinding.ActivityCombinedPickDeatilListCareBinding

sealed class ActivityCombinedPickDeatilListUnionBinding {
    abstract var vm: CombinedPickDeatilListVM?
    abstract val llTitleBar: RelativeLayout
    abstract val edLocation: EditText
    abstract val edGoodsCode: EditText
    abstract val recycle: RecyclerView
    abstract val tvNotification: TextView

    class V2(val binding: ActivityCombinedPickDeatilListCareBinding) :
        ActivityCombinedPickDeatilListUnionBinding() {
        override var vm: CombinedPickDeatilListVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val edLocation = binding.edLocation
        override val edGoodsCode = binding.edGoodsCode
        override val recycle = binding.recycle
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityCombinedPickDeatilListBinding) :
        ActivityCombinedPickDeatilListUnionBinding() {
        override var vm: CombinedPickDeatilListVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val edLocation = binding.edLocation
        override val edGoodsCode = binding.edGoodsCode
        override val recycle = binding.recycle
        override val tvNotification = binding.tvNotification
    }
}
