package com.midea.prestorage.function.inv.fragment

import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestoragesaas.databinding.FragmentTransByLocBinding
import com.midea.prestoragesaas.databinding.FragmentTransByLocCareBinding

sealed class FragmentTransByLocUnionBinding{
    abstract var vm: TransByLocVM?
    abstract val root: View
    abstract val etFromLocCode: EditText
    abstract val recyclerView: RecyclerView
    abstract val etTargetLocCode: EditText
    abstract val btnSubmitTransfer: Button
    abstract val llyCountNumber: LinearLayout

    class V2(val binding: FragmentTransByLocCareBinding) : FragmentTransByLocUnionBinding() {
        override var vm: TransByLocVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val etFromLocCode = binding.etFromLocCode
        override val recyclerView = binding.recyclerView
        override val etTargetLocCode = binding.etTargetLocCode
        override val btnSubmitTransfer = binding.btnSubmitTransfer
        override val llyCountNumber = binding.llyCountNumber
    }

    class V1(val binding: FragmentTransByLocBinding) : FragmentTransByLocUnionBinding() {
        override var vm: TransByLocVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val etFromLocCode = binding.etFromLocCode
        override val recyclerView = binding.recyclerView
        override val etTargetLocCode = binding.etTargetLocCode
        override val btnSubmitTransfer = binding.btnSubmitTransfer
        override val llyCountNumber = binding.llyCountNumber
    }
}
