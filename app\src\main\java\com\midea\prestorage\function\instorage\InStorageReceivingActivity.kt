package com.midea.prestorage.function.instorage

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestoragesaas.databinding.ActivityInStorageReceivingBinding
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.function.instorage.request.ReqContainerReceive
import com.midea.prestorage.function.instorage.response.RespNoReceived
import com.midea.prestorage.function.instorage.response.RespReceiptHeader
import com.midea.prestorage.function.mainyg.MainYgActivity
import com.midea.prestorage.function.outstorage.dialog.SendTipDialog
import com.midea.prestorage.function.picktaskdetail.PickTaskDetailActivity
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.ToastUtilsCare

class InStorageReceivingActivity : BaseViewModelActivity<InStorageReceivingVM>() {
    private lateinit var binding: ActivityInStorageReceivingBinding
    private lateinit var adapter: ContentPagerAdapter
    private val datas = mutableListOf<RespNoReceived>()
    private lateinit var sendTipDialog: SendTipDialog

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_in_storage_receiving)
        vm = ViewModelProvider.AndroidViewModelFactory(application).create(InStorageReceivingVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        //返回
        vm.finishActivity.observe(this, Observer<String> {
            if(!it.isNullOrBlank()) {
                ToastUtilsCare.toastBig(this, it, Toast.LENGTH_SHORT)
                startActivity(Intent(this, InStorageNewActivity::class.java)) //回到入库订单列表页面
            }
        })

        //上一条数据
        vm.previousP.observe(this, Observer<Boolean> {
            if (it) {
                if(binding.viewPager.currentItem != 0) {
                    binding.viewPager.currentItem = binding.viewPager.currentItem - 1
                }
            }
        })

        //下一条数据
        vm.nextP.observe(this, Observer<Boolean> {
            if (it) {
                if(binding.viewPager.currentItem < datas.size -1) {
                    binding.viewPager.currentItem = binding.viewPager.currentItem + 1
                }
            }
        })

        //收货记录
        vm.toReceivingRecord.observe(this, Observer<Boolean> {
            if (it) {
                val intent = Intent(this, InStorageReceivingRecordActivity::class.java)
                // 这里塞一个 后端返回的波次号或入库单号
                intent.putExtra("orderNo", vm.curOrderNo.value)
                intent.putExtra("orderReceiveType", vm.curOrderReceiveType.get())
                startActivity(intent)
            }
        })

        vm.notReceivedDatas.observe(this, Observer<MutableList<RespNoReceived>> { result ->
            datas.clear()
            result?.forEach {
                datas.add(it)
            }
            vm.isNoData.value = datas.size <= 0
            if(datas.size > 0) {
                binding.viewFirst.visibility = View.VISIBLE
            }else {
                binding.viewFirst.visibility = View.GONE
                binding.viewLast.visibility = View.GONE
                binding.viewMid.visibility = View.GONE
            }
            if(datas.size > 1) {
                binding.viewLast.visibility = View.VISIBLE
            }else {
                binding.viewLast.visibility = View.GONE
                binding.viewMid.visibility = View.GONE
            }
            if(datas.size > 2) {
                binding.viewMid.visibility = View.VISIBLE
            }else {
                binding.viewMid.visibility = View.GONE
            }
            adapter.notifyDataSetChanged()
        })

        //收货完成
        vm.receiptCompleted.observe(this, Observer<Boolean> { result ->
            if(datas != null && datas.size >0) {
                if (result) {
                    if(TextUtils.isEmpty(datas[binding.viewPager.currentItem].datas[datas[binding.viewPager.currentItem].datas.lastIndex - 2].itemValue)) { //校验数字是否为空
                        vm.showErrorNotification("收货数量不能为空，请重新输入", false)
                        return@Observer
                    }
                    if(!checkNum(datas[binding.viewPager.currentItem].datas[datas[binding.viewPager.currentItem].datas.lastIndex - 2].itemValue)) { //校验数字的合法性
                        vm.showErrorNotification("收货数量格式不对，请重新输入", false)
                        return@Observer
                    }
                    if (datas[binding.viewPager.currentItem].datas[datas[binding.viewPager.currentItem].datas.lastIndex - 2].itemValue.toInt() >
                        AppUtils.getBigDecimalValueStr(getFragment(binding.viewPager.currentItem).itemInfo.totalQty).toInt()) { //校验收货数量是否大于应收数量
                        vm.showErrorNotification("收货数量不能超过应收数量，请重新输入", false)
                        return@Observer
                    }
                    if (datas[binding.viewPager.currentItem].datas[datas[binding.viewPager.currentItem].datas.lastIndex - 2].itemValue.toInt() < 1) { //校验收货数量是否大于0
                        vm.showErrorNotification("收货数量要大于0，请重新输入", false)
                        return@Observer
                    }
//                    if(TextUtils.isEmpty(datas[binding.viewPager.currentItem].datas[datas[binding.viewPager.currentItem].datas.lastIndex].itemValue)) { //校验上架库位是否为空
//                        vm.showErrorNotification("上架库位不能为空，请重新输入", false)
//                        return@Observer
//                    }

                    var tempLotAtt = LinkedHashMap<String, String>()
                    datas[binding.viewPager.currentItem].datas.forEach {
                        tempLotAtt[it.lotAtt] = it.itemValue
                    }

                    val requestParam = mutableListOf<ReqContainerReceive>()
                    val param = ReqContainerReceive()
                    var lotAtt04Result = getFragment(binding.viewPager.currentItem).itemInfo.cdWhLotDetail?.filter {
                        it.lotAtt == "LOT_ATT04"
                    }
                    getFragment(binding.viewPager.currentItem).itemInfo.cdWhLotDetail?.forEachIndexed { index, cdWhLotDetailDTO ->
                        if("R" == cdWhLotDetailDTO.inputControl && tempLotAtt[cdWhLotDetailDTO.lotAtt].isNullOrBlank()) {
                            vm.showErrorNotification(cdWhLotDetailDTO.title + "不能为空", false)
                            return@Observer
                        }
                    }

                    if(lotAtt04Result != null && lotAtt04Result.isNotEmpty()) {
                        if (getGoodsStatus(tempLotAtt["LOT_ATT04"]!!).isNullOrBlank()) {
                            vm.showErrorNotification(datas[binding.viewPager.currentItem].datas[0].itemName + "填写不正确，请重新输入", false)
                            return@Observer
                        }
                    }

                    param.ids = getFragment(binding.viewPager.currentItem).itemInfo.ids
                    param.cdWhLotDetail = getFragment(binding.viewPager.currentItem).itemInfo.cdWhLotDetail
                    param.whCode = getWhCode()
                    param.itemCode = getFragment(binding.viewPager.currentItem).itemInfo.itemCode
                    param.locCode = "RECV01"
                    param.planLocCode = tempLotAtt["PLANLOC"]
                    param.qty = tempLotAtt["QTY"]?.toInt()!!
                    param.putWayFlag = if (tempLotAtt["PLANLOC"].isNullOrEmpty()) "N" else "Y"
                    param.operateChannel = "WMS_RF"
                    param.lotAtt04 = tempLotAtt["LOT_ATT04"]?.let { getGoodsStatus(it) }
                    param.lotAtt01 = tempLotAtt["LOT_ATT01"]
                    param.lotAtt02 = tempLotAtt["LOT_ATT02"]
                    param.lotAtt03 = tempLotAtt["LOT_ATT03"]
                    param.lotAtt05 = tempLotAtt["LOT_ATT05"]
                    param.lotAtt06 = tempLotAtt["LOT_ATT06"]
                    param.lotAtt07 = tempLotAtt["LOT_ATT07"]
                    param.lotAtt08 = tempLotAtt["LOT_ATT08"]
                    param.lotAtt09 = tempLotAtt["LOT_ATT09"]
                    param.lotAtt10 = tempLotAtt["LOT_ATT10"]
                    param.lotAtt11 = tempLotAtt["LOT_ATT11"]
                    param.lotAtt12 = tempLotAtt["LOT_ATT12"]

                    requestParam.add(param)

                    vm.containerReceive(requestParam)
                }
            }
        })

        //关闭订单
        vm.closeOrder.observe(this, Observer<Boolean> { result ->
            if(datas != null && datas.size >0) {
                if (result) {
                    sendTipDialog.setContent("是否确认关闭订单？")
                    sendTipDialog.show()
                }
            }
        })

        vm.loadMoreDatas.observe(this, Observer<MutableList<RespNoReceived>> {result ->
            result?.forEach {
                datas.add(it)
            }
            vm.isNoData.value = datas.size <= 0
            if(datas.size > 0) {
                binding.viewFirst.visibility = View.VISIBLE
            }else {
                binding.viewFirst.visibility = View.GONE
                binding.viewLast.visibility = View.GONE
                binding.viewMid.visibility = View.GONE
            }
            if(datas.size > 1) {
                binding.viewLast.visibility = View.VISIBLE
            }else {
                binding.viewLast.visibility = View.GONE
                binding.viewMid.visibility = View.GONE
            }
            if(datas.size > 2) {
                binding.viewMid.visibility = View.VISIBLE
            }else {
                binding.viewMid.visibility = View.GONE
            }
            adapter.notifyDataSetChanged()
        })

        initData()
        initDialog()
    }

    fun getFragment(position: Int): ReceivingContentFragment{
        return (supportFragmentManager.findFragmentByTag("f" + adapter.getItemId(position)) as ReceivingContentFragment)
    }

    fun getGoodsStatus(lotAtt: String):String {
        DCUtils.goodsStatue?.forEach {
            if(it.key == lotAtt) {
                return it.value.toString()
            }
        }
        return ""
    }

    fun checkNum(str: String):Boolean {

        return try {
            var num = str.toInt()
            true
        }catch (e: Exception) {
            false
        }
    }

    fun initData() {
        val orderNo = intent.getStringExtra("orderNo")
        val orderReceiveType = intent.getStringExtra("orderReceiveType")
        if (orderNo.isNullOrEmpty()) {
            AlertDialogUtil.showOnlyOkDialog(this, "单号为空", AlertDialogUtil.OnOkCallback { })
        } else {
            vm.curOrderNo.value = orderNo
        }
        if (!orderReceiveType.isNullOrEmpty()) {
            vm.curOrderReceiveType.set(orderReceiveType)  //单号类型
        }

        //binding.viewPager.offscreenPageLimit = 2

        adapter = ContentPagerAdapter(this, datas)
        binding.viewPager.adapter = adapter
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                if(vm.loadMoreComplete.get() == 0) {
                    vm.isLastItem.value = false
                    vm.isMidItem.value = position != 0
                    if(position == (adapter.itemCount - 1)) {
                        vm.queryNotReceived(true)
                    }
                }else {
                    vm.isLastItem.value = position == (adapter.itemCount - 1)
                    vm.isMidItem.value = position != 0 && position != (adapter.itemCount - 1)
                }
                vm.isFirstItem.value = position == 0
            }
        })
    }

    fun initDialog() {
        //关闭订单的弹窗dialog
        sendTipDialog = SendTipDialog(this)
        sendTipDialog.setTitle("提示")
        sendTipDialog.setConfirmBack(object: SendTipDialog.ConfirmBack {
            override fun confirmBack() {
                sendTipDialog.dismiss()

                val requestParam = mutableListOf<String>()
                var hashSetId = HashSet<String>() //排重
                datas.forEachIndexed { index, respNoReceived ->
                    if (respNoReceived.receiptHeaderId != null && !hashSetId.contains(respNoReceived.receiptHeaderId)) {
                        requestParam.add(respNoReceived.receiptHeaderId)
                        hashSetId.add(respNoReceived.receiptHeaderId)
                    }
                }
                vm.orderClose(requestParam)
            }

        })
    }

    override fun onResume() {
        super.onResume()
    }

    class ContentPagerAdapter(activity: FragmentActivity, private val datas: MutableList<RespNoReceived>) :FragmentStateAdapter(activity) {

        override fun getItemCount(): Int {
            return datas.size
        }

        override fun createFragment(position: Int): Fragment {
            return ReceivingContentFragment(datas[position])
        }

        override fun getItemId(position: Int): Long {
            return datas[position].hashCode().toLong()
        }
    }
}