package com.midea.prestorage.function.containerpick

import android.app.Application
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.base.ErrorToaster
import com.midea.prestorage.base.SuccessToaster
import com.midea.prestorage.beans.net.CdWhLotDetailDto
import com.midea.prestorage.beans.net.PickingContainer
import com.midea.prestorage.function.containerpick.fragment.BulkAlreadyPackedVM
import com.midea.prestorage.function.containerpick.fragment.BulkPackVM
import com.midea.prestorage.function.containerpick.fragment.BulkToBePackedVM

class BulkPackingVM(application: Application) : BaseViewModel(application),
    ErrorToaster, SuccessToaster {

    val title = MutableLiveData("散件打包")

    val toBePackedVM = BulkToBePackedVM(application).also {
        it.errorToaster = this
        it.successToaster = this
    }

    val alreadyPackedVM = BulkAlreadyPackedVM(application).also {
        it.errorToaster = this
        it.successToaster = this
    }

    private var currentViewModel: BulkPackVM = toBePackedVM

    val isPrintOk = ObservableField(false)
    var mBluetoothOpen = MutableLiveData(false)

    var taskCode = ObservableField<String>()
    var containerCode = ObservableField<String>()

    var pickingContainerList: MutableList<PickingContainer>? = null

    fun updateCurrent(index: Int) {
        currentViewModel = when (index) {
            0 -> toBePackedVM
            1 -> alreadyPackedVM
            else -> toBePackedVM
        }
        currentViewModel.refresh()
    }

    override fun init() {

    }

    fun bluetoothOpen() {
        if (CheckUtil.isFastDoubleClick()) {
            mBluetoothOpen.value = true
        }
    }

    override fun showError(msg: String) {
        showNotification(msg, false)
    }

    fun refresh() {
        currentViewModel.refresh()
    }

    override fun showSuccess(msg: String) {
        showNotification(msg, true)
    }

}