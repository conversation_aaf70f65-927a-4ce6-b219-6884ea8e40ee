package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.beans.base.BaseItemForPopup;


/**
 * Created by LUCY6 on 2017-5-23.
 */

public class InPoolStorageList extends BaseItemForPopup {

    @ShowAnnotation
    private String receiptCode;
    @ShowAnnotation
    private String waveNo;
    @ShowAnnotation
    private String custOrderNo;
    @ShowAnnotation
    private String receiptType;
    @ShowAnnotation
    private String carNo;
    @ShowAnnotation
    private String dispatchNo;
    @ShowAnnotation
    private String status;
    private String pickupTime;
    private String unscanMark;
    private String totalVolume;
    private String totalWeight;
    private String receiptDetailVOList;
    private String totalQty;

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getCustOrderNo() {
        return custOrderNo;
    }

    public void setCustOrderNo(String custOrderNo) {
        this.custOrderNo = custOrderNo;
    }

    public String getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(String totalQty) {
        this.totalQty = totalQty;
    }

    public String getReceiptType() {
        return receiptType;
    }

    public void setReceiptType(String receiptType) {
        this.receiptType = receiptType;
    }

    public String getReceiptCode() {
        return receiptCode;
    }

    public void setReceiptCode(String receiptCode) {
        this.receiptCode = receiptCode;
    }

    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }

    public String getDispatchNo() {
        return dispatchNo;
    }

    public void setDispatchNo(String dispatchNo) {
        this.dispatchNo = dispatchNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPickupTime() {
        return pickupTime;
    }

    public void setPickupTime(String pickupTime) {
        this.pickupTime = pickupTime;
    }

    public String getUnscanMark() {
        return unscanMark;
    }

    public void setUnscanMark(String unscanMark) {
        this.unscanMark = unscanMark;
    }

    public String getTotalVolume() {
        return totalVolume;
    }

    public void setTotalVolume(String totalVolume) {
        this.totalVolume = totalVolume;
    }

    public String getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(String totalWeight) {
        this.totalWeight = totalWeight;
    }

    public String getReceiptDetailVOList() {
        return receiptDetailVOList;
    }

    public void setReceiptDetailVOList(String receiptDetailVOList) {
        this.receiptDetailVOList = receiptDetailVOList;
    }

    public static boolean isChinese(char c) {
        return c >= 0x4E00 && c <= 0x9FA5;// 根据字节码判断
    }
}
