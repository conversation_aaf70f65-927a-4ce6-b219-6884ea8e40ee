package com.midea.prestorage.function.containerpick

import android.widget.RelativeLayout
import androidx.lifecycle.LifecycleOwner
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.midea.prestoragesaas.databinding.ActivityBulkPackingBinding
import com.midea.prestoragesaas.databinding.ActivityBulkPackingCareBinding

sealed class ActivityBulkPackingUnionBinding {
    abstract var vm: BulkPackingVM?
    abstract val llTitleBar: RelativeLayout
    abstract val viewPager: ViewPager2
    abstract val tabLayout: TabLayout
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: ActivityBulkPackingCareBinding) : ActivityBulkPackingUnionBinding() {
        override var vm: BulkPackingVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val viewPager = binding.viewPager
        override val tabLayout = binding.tabLayout
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: ActivityBulkPackingBinding) : ActivityBulkPackingUnionBinding() {
        override var vm: BulkPackingVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val viewPager = binding.viewPager
        override val tabLayout = binding.tabLayout
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
