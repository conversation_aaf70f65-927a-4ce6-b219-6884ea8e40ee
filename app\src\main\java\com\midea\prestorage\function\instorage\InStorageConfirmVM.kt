package com.midea.prestorage.function.instorage

import android.content.Intent
import android.util.Log
import android.widget.Toast
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.WhInfo
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.function.instorage.InStorageScanActivity.Companion.CODE_LOAD_CONTAINER
import com.midea.prestorage.function.instorage.response.InReceiptSerial
import com.midea.prestorage.function.inv.response.BsLocation
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.MySoundUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody


class InStorageConfirmVM(val activity: InStorageConfirmActivity) {


    // 容器号
    //private var curContainerCode = ObservableField<String>("")

    // 要上架的库位
    var locCode = ObservableField<String>("")

    //装卸组名称
    var handlingGroupName = ""

    //装卸组编码
    var handlingGroupCode = ""

    var locationList = mutableListOf<BsLocation>()

    var curOrderReceiveType = ""

    var containerCode: String? = null

    init {
        //这个界面是 加载 收货容器号 对应的扫码记录
        // 所以肯定是有其他activity传了个containerCode 过来才能进到这里
        containerCode = activity.intent.getStringExtra("containerCode")
        if (!containerCode.isNullOrEmpty()) {
            loadContainerData()
        } else {
            activity.finish()
        }

        if (!activity.intent.getStringExtra("handlingGroupName").isNullOrBlank()) {
            handlingGroupName = activity.intent.getStringExtra("handlingGroupName")!!
        }

        if (!activity.intent.getStringExtra("handlingGroupCode").isNullOrBlank()) {
            handlingGroupCode = activity.intent.getStringExtra("handlingGroupCode")!!
        }

        if (!activity.intent.getStringExtra("curOrderReceiveType").isNullOrBlank()) {
            curOrderReceiveType = activity.intent.getStringExtra("curOrderReceiveType")!!
        }


    }


    fun showListLocation() {
        activity.spinnerAdapter.copyOriginDataAsResult()
        activity.binding.etLocationCode.showDropDown()
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        result?.let {
            activity.binding.etLocationCode.setText(result.trim())
            onConfirmReceipt()
        }

    }


    //点击 "确认收货完成"  按钮 事件
    fun onConfirmReceipt() {

        if (!activity.binding.btnConfirmReceipt.isClickable) {
            ToastUtils.getInstance().taostWithErrorSound(activity, "上次操作未完成，请稍后重试", Toast.LENGTH_SHORT)
            return
        }

        activity.binding.btnConfirmReceipt.setBackgroundResource(R.drawable.bg_bt_gray)
        activity.binding.btnConfirmReceipt.isClickable = false

        if (locCode.get().isNullOrEmpty()) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "库位编码不能为空")
            activity.binding.btnConfirmReceipt.setBackgroundResource(R.drawable.bg_bt_blue)
            activity.binding.btnConfirmReceipt.isClickable = true
            return
        }


        // 先检查 输入的库位编码是否 在可供上架库位列表中
        val locCodeEntered = locCode.get().toString()
        if (locationList.size > 0) {
            var isLocationOK = false
            locationList.forEach {
                // 只有匹配到相同locCode 和 类型RS的 库位才能上架
                if (it.locCode.equals(locCodeEntered)) {
                    isLocationOK = true
                }
            }

            if (isLocationOK) {
                // 库位正确才调用后端接口执行上架操作
                confirmReceipt()
            } else {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "请输入正确的库位编码")
                locCode.set("")
                activity.binding.btnConfirmReceipt.setBackgroundResource(R.drawable.bg_bt_blue)
                activity.binding.btnConfirmReceipt.isClickable = true
            }

        } else {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "无可供上架的库位，操作失败")
            locCode.set("")
            activity.binding.btnConfirmReceipt.setBackgroundResource(R.drawable.bg_bt_blue)
            activity.binding.btnConfirmReceipt.isClickable = true
        }
    }

    // 根据容器号 加载商品信息
    fun loadContainerData() {
        Log.e("tao", "收货确认界面，根据容器号:" + containerCode + "加载扫码记录")
        RetrofitHelper.getAppAPI()
            .loadContainerData(Constants.whInfo?.whCode)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<InReceiptSerial>>(activity) {
                override fun success(data: MutableList<InReceiptSerial>?) {
                    data?.let {
                        activity.adapter.data.clear()
                        activity.adapter.addData(data)
                        activity.adapter.notifyDataSetChanged()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })

    }


    fun initLocationList() {


        val locUseType = "RS"
        val locCodeLike = ""

        activity.waitingDialogHelp.showDialog()


        RetrofitHelper.getBasicDataAPI()
            .queryLocationToPutAway(activity.getWhCode(), locUseType, locCodeLike)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<BsLocation>>(activity) {
                override fun success(data: MutableList<BsLocation>?) {

                    activity.waitingDialogHelp.hidenDialog()

                    locationList.clear()

                    if (data != null && data.size > 0) {
                        data.forEach {
                            //locationList.add(it.locCode)
                            it.locCodeAndZoneName = it.locCode + "  [" + it.zoneName + "]"
                        }
                        locationList = data
                        activity.spinnerAdapter.setData(data)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }


    // 上架(把容器对应的商品信息 放到指定库位)
    fun confirmReceipt() {


        val param = mutableMapOf(
            "containerCode" to containerCode,
            "locCode" to locCode.get().toString(),
            "whCode" to activity.getWhCode(),
            "handingGroupCode" to handlingGroupCode,
            "handingGroupName" to handlingGroupName
        )

        // receiptFlag  0:按单，1：组单
        // receipt 入库单  wave波次单
        if (curOrderReceiveType.equals("wave")) {
            param.put("receiptFlag", "1")
        } else if (curOrderReceiveType.equals("receipt")) {
            param.put("receiptFlag", "0")
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        activity.binding.btnConfirmReceipt.setBackgroundResource(R.drawable.bg_bt_gray)
        activity.binding.btnConfirmReceipt.isClickable = false

        RetrofitHelper.getAppAPI()
            .confirmReceipt(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    activity.binding.btnConfirmReceipt.setBackgroundResource(R.drawable.bg_bt_blue)
                    activity.binding.btnConfirmReceipt.isClickable = true
                    MySoundUtils.getInstance().dingSound()
                    AlertDialogUtil.showOnlyOkDialog(activity, "上架成功") {
                        // 把 上架成功的标记 返回至上一级activity
                        val intent = Intent()
                        //当前activity销毁时，data会传递给上一级activity
                        Log.e("tao", "上架成功，传参数: confirmSuccess=1 返回给扫码界面")
                        intent.putExtra("confirmSuccess", "1")
                        activity.setResult(CODE_LOAD_CONTAINER, intent)
                        activity.finish()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.binding.btnConfirmReceipt.setBackgroundResource(R.drawable.bg_bt_blue)
                    activity.binding.btnConfirmReceipt.isClickable = true
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                    loadContainerData()
                }
            })
    }


    //点击后退键
    fun back() {
        activity.finish()
    }


}