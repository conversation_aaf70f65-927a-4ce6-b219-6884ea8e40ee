package com.midea.prestorage.function.inv.dialog

import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData

class LotAttDialogVM(val title: String?, val showArrow: Boolean = true, val canEdit: Boolean = true, val fieldType: String?, val lotAtt: String?, val key: String?) {
    var content: ObservableField<String> = ObservableField("")
    var clickItem = MutableLiveData(false)
    var hintStr: ObservableField<String> = ObservableField("")

    fun onClickItem() {
        if (CheckUtil.isFastDoubleClick()) {
            if (!canEdit) {
                clickItem.value = true
            }
        }
    }
}