package com.midea.prestorage.function.put

import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestoragesaas.databinding.ActivityInStorageScanBinding
import com.midea.prestoragesaas.databinding.ActivityInStorageScanCareBinding

sealed class InStorageScanUnionBinding {
    abstract var vm: InStorageScanPutVM?
    abstract val edOrderNo: EditText
    abstract val recycle: RecyclerView
    abstract val ivLocalInfo: ImageView
    abstract val ivAreaInfo: ImageView
    abstract val srl: SwipeRefreshLayout
    abstract val tvNotification: TextView

    class V2(val binding: ActivityInStorageScanCareBinding) : InStorageScanUnionBinding() {
        override var vm: InStorageScanPutVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val edOrderNo = binding.edOrderNo
        override val recycle = binding.recycle
        override val ivLocalInfo = binding.ivLocalInfo
        override val ivAreaInfo = binding.ivAreaInfo
        override val srl = binding.srl
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityInStorageScanBinding) : InStorageScanUnionBinding() {
        override var vm: InStorageScanPutVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val edOrderNo = binding.edOrderNo
        override val recycle = binding.recycle
        override val ivLocalInfo = binding.ivLocalInfo
        override val ivAreaInfo = binding.ivAreaInfo
        override val srl = binding.srl
        override val tvNotification = binding.tvNotification
    }
}
