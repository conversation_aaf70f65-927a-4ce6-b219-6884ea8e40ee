package com.midea.prestorage.function.outstorage.dialog

import android.widget.Toast
import androidx.databinding.ObservableField
import com.midea.prestorage.function.instorage.response.InReceiptSerial
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils

class DeleteSerialDialogVM(val dialog: DeleteSerialDialog) {

    val title = ObservableField<String>("确认删除数量")
    val textCustItemCode = ObservableField<String>("")
    val textItemCode = ObservableField<String>("")
    val deleteNum = ObservableField<String>("")
    val receiptCode = ObservableField<String>("")
    var maxDelete = 0
    val id = ObservableField<String>("")


    fun show(inReceiptSerial: InReceiptSerial) {
        deleteNum.set("")

        // 删除时 后端接口要求传 itemCode
        textItemCode.set(inReceiptSerial.itemCode)
        // 删除时 前端界面要求显示 custItemCode
        textCustItemCode.set(inReceiptSerial.custItemCode)
        // 删除时 20211122 测试 要求title 显示  custItemCode
        if (!textCustItemCode.get().isNullOrBlank()) {
            title.set("删除确认:  " + textCustItemCode.get())
        }
        //扫码记录id
        if (inReceiptSerial.id != null) {
            id.set(inReceiptSerial.id)
        }
        // 入库单
        if (inReceiptSerial.receiptCode != null) {
            receiptCode.set(inReceiptSerial.receiptCode)
        }
        // 最大删除数
        if (inReceiptSerial.qty != null) {
            maxDelete = inReceiptSerial.qty.toInt()
        }

        AppUtils.requestFocus(dialog.binding.etDeleteNum)
    }


    fun close() {
        dialog.dismiss()
    }

    fun confirm() {

        try {

            if (!deleteNum.get().isNullOrBlank() && deleteNum.get()!!.toInt() > 0) {
                if (deleteNum.get().toString().toInt() > maxDelete) {
                    ToastUtils.getInstance().taostWithErrorSound(dialog.activity, "超过最大可删除数量: " + maxDelete, Toast.LENGTH_SHORT)
                } else {
                    dialog.activity.binding.vm!!.deleteSerial(
                        "1",
                        //删除时 后端接口要求传 itemCode
                        textItemCode.get().toString(),
                        receiptCode.get().toString(),
                        deleteNum.get()!!.toInt(),
                        id.get().toString()
                    )
                    dialog.dismiss()
                }

            } else {
                ToastUtils.getInstance().taostWithErrorSound(dialog.activity, "删除数量必须为正整数", Toast.LENGTH_SHORT)
            }

        } catch (e: NumberFormatException) {
            ToastUtils.getInstance().taostWithErrorSound(dialog.activity, "数量格式错误或超过最大值", Toast.LENGTH_SHORT)
            return
        }


    }
}