package com.midea.prestorage.function.outstorage.dialog

import android.app.AlertDialog
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.OutStorageDeleteQueryBean
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogOutStorageDeleteTipBinding

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/6/26$
 */
class DeleteTipDialog(
    var mContext: BaseActivity
) : AlertDialog(mContext) {

    private var binding: DialogOutStorageDeleteTipBinding
    var deleteInfo: OutStorageDeleteQueryBean? = null
    var queryType = 1
    var deleteBack: DeleteBack? = null

    init {
        val contentView =
            LayoutInflater.from(mContext).inflate(R.layout.dialog_out_storage_delete_tip, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = DeleteTipDialogVM(this)

        setCanceledOnTouchOutside(true)
    }

    override fun show() {
        super.show()
        binding.vm!!.goodsNo.set("1")
        binding.etNum.post {
            binding.etNum.requestFocus()
            binding.etNum.setSelection(1)
        }
        binding.vm!!.show()
    }

    interface DeleteBack {
        fun deleteOk()
        fun deleteFail(errorMsg: String)
    }
}
