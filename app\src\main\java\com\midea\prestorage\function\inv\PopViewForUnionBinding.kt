package com.midea.prestorage.function.inv

import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestorage.dialog.DialogTipUnionBinding
import com.midea.prestorage.dialog.TipDialogVM
import com.midea.prestoragesaas.databinding.DialogTipBinding
import com.midea.prestoragesaas.databinding.DialogTipCareBinding
import com.midea.prestoragesaas.databinding.PopViewForSelectCustemItemCodeBinding
import com.midea.prestoragesaas.databinding.PopViewForSelectCustemItemCodeCareBinding

sealed class PopViewForUnionBinding {
    abstract val recyclerViewInPop: RecyclerView
    abstract val closePop: ImageView

    class V2(val binding: PopViewForSelectCustemItemCodeCareBinding) : PopViewForUnionBinding() {
        override val recyclerViewInPop = binding.recyclerViewInPop
        override val closePop = binding.closePop
    }

    class V1(val binding: PopViewForSelectCustemItemCodeBinding) : PopViewForUnionBinding() {
        override val recyclerViewInPop = binding.recyclerViewInPop
        override val closePop = binding.closePop
    }
}
