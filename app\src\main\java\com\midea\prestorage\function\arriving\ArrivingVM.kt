package com.midea.prestorage.function.arriving

import CheckUtil
import android.annotation.SuppressLint
import android.content.Intent
import android.text.TextUtils
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.ArrivingBean
import com.midea.prestorage.beans.net.ArrivingDetailInfo
import com.midea.prestorage.beans.net.ArrivingPhoneBean
import com.midea.prestorage.beans.net.FuShipmentStatue
import com.midea.prestorage.function.arriving.dialog.ArrivingDialog
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody
import java.text.SimpleDateFormat
import java.util.*


@Suppress("UNCHECKED_CAST")
class ArrivingVM(val activity: ArrivingActivity) {

    val format = SimpleDateFormat("yyyy-MM-dd")

    val isRefreshing = ObservableBoolean(false)
    var filter = ObservableField("筛选")
    val isNoData = ObservableBoolean(false)
    var orderStatus: MutableList<DCBean>? = null

    var dayInfo = DCUtils.days[1]
    var typeStatue: MutableList<DCBean>? = null

    var orderNo = ObservableField<String>("")
    var typeInfo = ObservableField<String>("")
    var taskInfo = ObservableField<String>("")
    var taskTypeV2 = ObservableField<String>("")
    var dayInfoTv = ObservableField<String>(dayInfo.key)

    var taskStatue: DCBean? = null
    var taskType: DCBean? = null
    var shStatue: DCBean? = null

    var orderStatueBeans: MutableList<DCBean>? = null

    private lateinit var dialog: ArrivingDialog

    // 当前页码
    var pageNo = 1

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        isRefreshing.set(true)
        pageNo = 1
        initOrderList()
    }

    fun init() {
        onRefreshCommand.onRefresh()
        initFilterInfo()
        mobileDriverQueues()

        dialog = ArrivingDialog(activity)
        initTaskType()
        initTaskStatus()
        initShStatus()

        dialog.setOnDismissListener {
            onRefreshCommand.onRefresh()
        }
    }


    private fun initShStatus() {
        RetrofitHelper.getDirectionAPI()
            .fuShipmentStatus("EXCUTE_STATUS")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<FuShipmentStatue>>(activity) {
                override fun success(data: MutableList<FuShipmentStatue>?) {
                    if (data != null) {
                        val beans = mutableListOf<DCBean>()
                        data.removeAll { it.enableFlag == 0 }
                        data.forEach {
                            beans.add(DCBean(it.name, it.code, DCBean.SHOW_KEY))
                        }
                        activity.shStatues.addAllData(beans as MutableList<BaseItemShowInfo>)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    AppUtils.showToast(activity, apiErrorModel.message)
                }
            })
    }

    private fun initTaskType() {
        RetrofitHelper.getDirectionAPI()
            .fuShipmentStatus("TASK_TYPE")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<FuShipmentStatue>>(activity) {
                override fun success(data: MutableList<FuShipmentStatue>?) {
                    if (data != null) {
                        val beans = mutableListOf<DCBean>()
                        data.removeAll { it.enableFlag == 0 }
                        data.forEach {
                            beans.add(DCBean(it.name, it.code, DCBean.SHOW_KEY))
                        }
                        activity.taskType.addAllData(beans as MutableList<BaseItemShowInfo>)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    AppUtils.showToast(activity, apiErrorModel.message)
                }
            })
    }

    private fun initTaskStatus() {
        RetrofitHelper.getDirectionAPI()
            .fuShipmentStatus("ORDER_STATUS")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<FuShipmentStatue>>(activity) {
                override fun success(data: MutableList<FuShipmentStatue>?) {
                    if (data != null) {
                        val beans = mutableListOf<DCBean>()
                        data.removeAll { it.enableFlag == 0 }
                        data.forEach {
                            beans.add(DCBean(it.name, it.code, DCBean.SHOW_KEY))
                        }
                        orderStatueBeans = beans
                        combineStatue()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    AppUtils.showToast(activity, apiErrorModel.message)
                }
            })
    }

    val goodsEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                activity.popDismiss()
            }
        }
    }

    fun initFilterInfo() {
        val filters = mutableListOf<String>()
        filters.add(dayInfo.key)

        if (!TextUtils.isEmpty(orderNo.get())) {
            filters.add(orderNo.get()!!)
        }

        if (taskStatue != null) {
            filters.add(taskStatue!!.key)
        }

        if (taskType != null) {
            filters.add(taskType!!.key)
        }

        activity.resetFilterInfo(filters)
    }

    @SuppressLint("SimpleDateFormat")
    fun initOrderList(isLoadMore: Boolean = false) {
        val cal = Calendar.getInstance()
        cal.time = Date()
        cal.add(Calendar.DATE, -dayInfo.value.toString().toInt())
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "custOrderNo" to orderNo.get()!!,
            "createTimeFrom" to "${format.format(cal.time)} 00:00:00",
            "createTimeTo" to "${format.format(Date())} 23:59:59",
            "taskStatus" to shStatue?.value, //审核状态
            "taskType" to taskType?.value,
            "isSearchAllFlag" to taskStatue?.value,
            "pageSize" to 30,
            "pageNo" to pageNo
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getAppAPI()
            .searchCarArrivedTask(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PageResult<ArrivingBean>>(activity) {
                override fun success(data: PageResult<ArrivingBean>?) {
                    if (data != null) {
                        activity.setCbSelectChecked(false)
                        if (isLoadMore) {
                            activity.loadMoreData(data.list)
                        } else {
                            activity.showData(data.list)
                        }
                        combineStatue()
                        filter.set("筛选(${data.totalCount})")
                    } else {
                        filter.set("筛选(0)")
                    }
                    if (pageNo >= data!!.totalPage) {
                        //加载到了最后一页
                        activity.adapter.loadMoreModule.loadMoreEnd()
                    } else {
                        pageNo = data.pageNo + 1
                    }
                    isRefreshing.set(false)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isRefreshing.set(false)
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun combineStatue() {
        if (orderStatueBeans != null && activity.adapter.data.isNotEmpty()) {
            activity.adapter.data.forEach {
                it.orderStatusStr = orderStatueBeans?.find { item ->
                    item.value.toString() == it.orderStatus
                }?.key.toString()
            }
            activity.adapter.notifyDataSetChanged()
        }
    }

    fun back() {
        activity.finish()
    }

    fun popShow() {
        orderNo.set("")
    }

    fun loadMore() {
        initOrderList(true)
    }

    fun dayClick() {
        val days = days
        activity.showDaysDialog(days as MutableList<BaseItemShowInfo>)
    }

    // 重置按钮，点击
    fun resetClick() {
        orderNo.set("")

        shStatue = null
        typeInfo.set("")

        taskStatue = null
        taskInfo.set("")

        taskType = null
        taskTypeV2.set("")

        dayInfo = DCUtils.days[1]
        dayInfoTv.set(dayInfo.key)
    }

    private fun mobileDriverQueues() {
        RetrofitHelper.getAppAPI()
            .mobileDriverQueues(activity.getWhCode())
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PageResult<ArrivingPhoneBean>>(activity) {
                override fun success(data: PageResult<ArrivingPhoneBean>?) {
                    if (data != null && !data.list.isNullOrEmpty()) {
                        data.list.forEach {
                            it.showInfo = it.carNo
                        }
                        dialog.addPhoneData(data.list as MutableList<BaseItemShowInfo>)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    /**
     * 按单收货
     */
    fun orderReceive() {
        if (CheckUtil.isFastDoubleClick()) {
            val checkBeans = activity.adapter.returnBeans

            if (checkBeans.isEmpty()) {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "请勾选任务!")
                return
            }

            dialog.tag = checkBeans
            dialog.show()
        }
    }

    fun onItemClick(bean: ArrivingBean) {
        val it = Intent(activity, ArrivingDetailActivity::class.java)
        it.putExtra("bean", bean)
        activity.startActivity(it)
    }

    fun showType() {
        val status = StringBuffer("")
        typeStatue?.forEach {
            status.append(it.key.toString() + ",")
        }
        typeInfo.set(status.toString())
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        orderNo.set(result)
    }

    // 天数
    var days = mutableListOf(
        DCBean("3天", 3, DCBean.SHOW_KEY),
        DCBean("5天", 5, DCBean.SHOW_KEY),
        DCBean("7天", 7, DCBean.SHOW_KEY)
    )
}