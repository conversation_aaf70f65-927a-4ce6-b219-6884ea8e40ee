package com.midea.prestorage.function.inv.response;

import com.midea.prestorage.function.outstorage.response.RespShipmentDetail;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

public class AdjustDetailSearchResp implements Serializable {


    private String custItemCode;
    private String itemCode;
    private String itemName;
    private String fmInventorySts;
    private BigDecimal planQty;
    private BigDecimal allocatedQty;
    private BigDecimal scanNum;
    private String adjustCode;
    private String whCode;
    private String scanFlag;
    private String id;
    private String headerId;
    private String ownerCode;

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getFmInventorySts() {
        return fmInventorySts;
    }

    public void setFmInventorySts(String fmInventorySts) {
        this.fmInventorySts = fmInventorySts;
    }

    public BigDecimal getPlanQty() {
        return planQty;
    }

    public void setPlanQty(BigDecimal planQty) {
        this.planQty = planQty;
    }

    public BigDecimal getAllocatedQty() {
        return allocatedQty;
    }

    public void setAllocatedQty(BigDecimal allocatedQty) {
        this.allocatedQty = allocatedQty;
    }

    public BigDecimal getScanNum() {
        return scanNum;
    }

    public void setScanNum(BigDecimal scanNum) {
        this.scanNum = scanNum;
    }

    public String getAdjustCode() {
        return adjustCode;
    }

    public void setAdjustCode(String adjustCode) {
        this.adjustCode = adjustCode;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getScanFlag() {
        return scanFlag;
    }

    public void setScanFlag(String scanFlag) {
        this.scanFlag = scanFlag;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getHeaderId() {
        return headerId;
    }

    public void setHeaderId(String headerId) {
        this.headerId = headerId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AdjustDetailSearchResp that = (AdjustDetailSearchResp) o;
        return Objects.equals(custItemCode, that.custItemCode) &&
                Objects.equals(itemName, that.itemName) &&
                Objects.equals(fmInventorySts, that.fmInventorySts);
    }
}
