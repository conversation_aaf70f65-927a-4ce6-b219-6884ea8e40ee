package com.midea.prestorage.function.addgoods

import android.widget.*
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestoragesaas.databinding.ActivityPutInGoodsBinding
import com.midea.prestoragesaas.databinding.ActivityPutInGoodsCareBinding
import com.midea.prestoragesaas.databinding.ActivityPutOutGoodsBinding
import com.midea.prestoragesaas.databinding.ActivityPutOutGoodsCareBinding

sealed class ActivityPutInGoodsUnionBinding{
    abstract var vm: PutInVM?
    abstract val llTitleBar: RelativeLayout
    abstract val edGoods: EditText
    abstract val llDataInfo: LinearLayout
    abstract val ivNoOrder: ImageView
    abstract val edQty: EditText
    abstract val cbSelectAll: CheckBox
    abstract val tvNotification: TextView
    abstract val rv: RecyclerView
    abstract val edLocation: EditText
    abstract val llSelectAll: LinearLayout

    class V2(val binding: ActivityPutInGoodsCareBinding) : ActivityPutInGoodsUnionBinding() {
        override var vm: PutInVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val edGoods = binding.edGoods
        override val llDataInfo = binding.llDataInfo
        override val ivNoOrder = binding.ivNoOrder
        override val edQty = binding.edQty
        override val tvNotification = binding.tvNotification
        override val cbSelectAll = binding.cbSelectAll
        override val rv = binding.rv
        override val edLocation = binding.edLocation
        override val llSelectAll = binding.llSelectAll
    }

    class V1(val binding: ActivityPutInGoodsBinding) : ActivityPutInGoodsUnionBinding() {
        override var vm: PutInVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val edGoods = binding.edGoods
        override val llDataInfo = binding.llDataInfo
        override val ivNoOrder = binding.ivNoOrder
        override val edQty = binding.edQty
        override val tvNotification = binding.tvNotification
        override val cbSelectAll = binding.cbSelectAll
        override val rv = binding.rv
        override val edLocation = binding.edLocation
        override val llSelectAll = binding.llSelectAll
    }
}
