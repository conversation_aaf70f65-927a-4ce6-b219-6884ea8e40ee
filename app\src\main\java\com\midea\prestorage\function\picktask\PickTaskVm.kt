package com.midea.prestorage.function.picktask

import android.app.Application
import android.content.Intent
import androidx.databinding.ObservableBoolean
import androidx.lifecycle.MutableLiveData
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson

import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.function.inv.response.InvStockTakeTask
import com.midea.prestorage.function.picktaskdetail.PickTaskDetailActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody


class PickTaskVm(application: Application) : BaseViewModel(application) {
    var drowDownBool = MutableLiveData<Boolean>(false)
    var notificationDataListChange = MutableLiveData<Boolean>(false)
    var boWave = MutableLiveData<String>("")
    var returnData = MutableLiveData<MutableList<InvStockTakeTask>>()
    var dropDownDay = MutableLiveData<String>("")
    var isRecyleView = MutableLiveData<Boolean>(false)
    val isRefreshing = MutableLiveData(false)

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        isRefreshing.value = true
        webRequest()
    }

    override fun init() {
    }

    fun onDrowDown() {
        drowDownBool.value = true
    }

    fun webRequest() {
        launch(showDialog = false, error = {
        }, finish = {
            isRefreshing.value = false
        }) {
            val param = mutableMapOf(
                "waveNo" to boWave.value,
                "currentDay" to dropDownDay.value?.substring(0, 1) as String,
                "whCode" to Constants.whInfo?.whCode as String,
                "confirmedBy" to Constants.userInfo?.name,
                "mobile" to Constants.mobile
            )
            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getInventoryAPI().getPickTask(requestBody)
            }

            if (result.code == 0L) {//如果正常了
                returnData.value = result.data
                notificationDataListChange.value = true//通知改变列表

                //如果存在数据就返回一个真
                isRecyleView.value = result.data?.size as Int > 0
            } else {//如果异常
                showNotification(result.msg as String, false)
                isRecyleView.value = false
            }
        }
    }

    fun getDetails(bean: InvStockTakeTask) {
        launch(showDialog = true, error = {
        }, finish = {}) {
            val param = mutableMapOf(
                "waveNo" to bean.waveNo,
                "whCode" to Constants.whInfo?.whCode,
                "taskDetailIdList" to bean.taskDetailIdList
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val status = withContext(Dispatchers.Default) {
                    RetrofitHelper.getDirectionAPI().searchDictNew("CL_INVENTORY_STS")
                }
                val details = withContext(Dispatchers.Default) {
                    RetrofitHelper.getInventoryAPI().getPickTaskDetail(requestBody)
                }

                if (status.code == 0L) {
                    details.data?.bwmsRfPickTaskDetailResponses?.forEach {
                        it.lotAtt04Str = status.data?.find { item ->
                            item.code == it.lotAtt04
                        }?.name
                    }
                }
                details
            }

            if (result.code == 0L) {
                val it = Intent()
                it.putExtra("beans", result.data)
                it.putExtra("waveNo", bean.waveNo)
                it.putStringArrayListExtra("taskDetailIdList", bean.taskDetailIdList.toCollection(ArrayList()))
                toActivity(it, PickTaskDetailActivity::class.java)
            } else {
                showNotification(result.msg as String, false)
            }
        }
    }

    fun clearOrderNo() {
        boWave.value = ""
        onRefreshCommand.onRefresh()
    }

    fun onEnterBoKeyDown() {
        //波次号校验位数
        if (CheckUtil.isFastDoubleClick()) {
            if (boWave.value?.trim()!!.length < 4) {
                showNotification("波次号必须大于3位", false)
                return
            }
            webRequest()
        }
    }
}