package com.midea.prestorage.function.pick.fragment

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.beans.net.OutPickPoolStorageDetail
import com.midea.prestoragesaas.databinding.FragmentPickPoolBinding
import com.midea.prestorage.utils.WaitingDialogHelp
import com.trello.rxlifecycle2.components.support.RxFragment

class PickPoolStartFragment : RxFragment() {

    companion object {
        fun newInstance(
            beans: ArrayList<OutPickPoolStorageDetail.DetailResponses>,
            mode: Int,//模式
            currentItem: Int,//当前页
            allItem: Int //总页面
        ): PickPoolStartFragment {
            val bundle = Bundle()
            bundle.putSerializable("beans", beans)
            bundle.putInt("mode", mode) // 0为按波次，1为按单号
            bundle.putInt("currentItem", currentItem)
            bundle.putInt("allItem", allItem)
            val fragment = PickPoolStartFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    private lateinit var binding: FragmentPickPoolBinding
    private var vm: PickPoolStartFragmentVM? = null
    lateinit var adapter: PickPoolAdapter
    private var isStart: Boolean = false
    var isStarted: Boolean = false//是否已经开始

    var waitingDialogHelp: WaitingDialogHelp? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = inflater.let { FragmentPickPoolBinding.inflate(it, container, false) }!!
        initView()
        return binding.root
    }

    private fun initView() {
        waitingDialogHelp = WaitingDialogHelp(activity)
        if (vm == null) {
            vm = PickPoolStartFragmentVM(this)
            binding.vm = vm
        }
        initRecycle()
        vm!!.init()
        if (isStart) {
            setHeadColorStart()
        }
    }

    private fun initRecycle() {
        adapter = PickPoolAdapter(
            activity,
            R.layout.item_order_pick_pool_start
        )
        adapter.setStart(isStart)
        adapter.setStarted(isStarted)
        binding.recycle.layoutManager = LinearLayoutManager(activity)
        binding.recycle.adapter = adapter

        adapter.setOnItemChildClickListener { adapter, _, position ->
            val bean = adapter.data[position] as OutPickPoolStorageDetail.DetailResponses
            vm!!.btnClick(bean)
        }
    }

    fun setInfo(detailResponses: OutPickPoolStorageDetail.DetailResponses) {
        binding.tvOrderNo.text = getNotNumTv(detailResponses.referenceCode)
        binding.tvItemCode.text = getNotNumTv(detailResponses.custItemCode)
    }

    fun setPageInfo(str: String) {
        binding.tvPageInfo.text = getNotNumTv(str)
    }

    private fun getNotNumTv(str: String?): String {
        return if (TextUtils.isEmpty(str)) "" else str!!
    }

    fun setAdapter(beans: ArrayList<OutPickPoolStorageDetail.DetailResponses>) {
        adapter.setNewInstance(beans)
        adapter.notifyDataSetChanged()
    }

    fun startUp() {
        isStart = true
        isStarted = false
        if (this::binding.isInitialized) {
            setHeadColorStart()
        }
        notifyDataChange()
    }

    fun startedUp() {
        isStart = true
        isStarted = true
        if (this::binding.isInitialized) {
            setHeadColorStart()
        }
        notifyDataChange()
    }

    private fun setHeadColorStart() {
        binding.llHead.setBackgroundColor(
            ContextCompat.getColor(
                requireActivity(),
                R.color.colorWhite
            )
        )
    }

    private fun notifyDataChange() {
        if (this::adapter.isInitialized) {
            adapter.setStart(true)
            adapter.setStarted(isStarted)
            adapter.notifyDataSetChanged()
        }
    }

    fun isAllPick(): Boolean {//是否全部拣货
        val beans = arguments?.getSerializable("beans")
                as ArrayList<OutPickPoolStorageDetail.DetailResponses>?
        val results = beans?.filter { it.status < 900 }
        if (results != null && results.isNotEmpty()) {
            return false
        }
        return true
    }

    fun setIsShowHead(b: Boolean) {
        if (b) {
            binding.llHead.visibility = View.GONE
        } else {
            binding.llHead.visibility = View.VISIBLE
        }
    }
}