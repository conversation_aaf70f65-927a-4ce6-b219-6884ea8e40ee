package com.midea.prestorage.function.outstorage.response;

import com.midea.prestorage.base.annotation.ShowAnnotation;

public class RespOrderQuery {
    @ShowAnnotation
    private String custOrderNo;
    @ShowAnnotation
    private String shipmentCode;
    @ShowAnnotation
    private String waybillNo;
    @ShowAnnotation
    private String dispatchNo;
    @ShowAnnotation
    private String waveNo;
    @ShowAnnotation
    private String shipToAttentionTo;
    @ShowAnnotation
    private String shipToPhoneNum;
    @ShowAnnotation
    private String shipToAddress;
    @ShowAnnotation
    private String shippingLoc;
    @ShowAnnotation
    private String statusStr;
    private String status;

    public String getCustOrderNo() {
        return custOrderNo;
    }

    public void setCustOrderNo(String custOrderNo) {
        this.custOrderNo = custOrderNo;
    }

    public String getShipmentCode() {
        return shipmentCode;
    }

    public void setShipmentCode(String shipmentCode) {
        this.shipmentCode = shipmentCode;
    }

    public String getWaybillNo() {
        return waybillNo;
    }

    public void setWaybillNo(String waybillNo) {
        this.waybillNo = waybillNo;
    }

    public String getDispatchNo() {
        return dispatchNo;
    }

    public void setDispatchNo(String dispatchNo) {
        this.dispatchNo = dispatchNo;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getShipToAttentionTo() {
        return shipToAttentionTo;
    }

    public void setShipToAttentionTo(String shipToAttentionTo) {
        this.shipToAttentionTo = shipToAttentionTo;
    }

    public String getShipToPhoneNum() {
        return shipToPhoneNum;
    }

    public void setShipToPhoneNum(String shipToPhoneNum) {
        this.shipToPhoneNum = shipToPhoneNum;
    }

    public String getShipToAddress() {
        return shipToAddress;
    }

    public void setShipToAddress(String shipToAddress) {
        this.shipToAddress = shipToAddress;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getShippingLoc() {
        return shippingLoc;
    }

    public void setShippingLoc(String shippingLoc) {
        this.shippingLoc = shippingLoc;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }
}
