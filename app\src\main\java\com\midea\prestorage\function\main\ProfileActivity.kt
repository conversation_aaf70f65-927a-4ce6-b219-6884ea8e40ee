package com.midea.prestorage.function.main

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import com.midea.prestorage.base.App
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.net.TenantResp
import com.midea.prestorage.beans.setting.ImplWarehouse
import com.midea.prestorage.dialog.TipDialog
import com.midea.prestorage.function.main.ProfileVM.Companion.CARE_MODE
import com.midea.prestorage.function.main.dialog.SaasWhChooseDialog
import com.midea.prestorage.function.main.dialog.TenantDialog
import com.midea.prestorage.function.main.dialog.WhDialog
import com.midea.prestorage.function.mainyg.MainYgActivity
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.http.constants.PickModes
import com.midea.prestorage.http.constants.PrinterModes
import com.midea.prestorage.http.constants.Printers
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityProfileBinding

class ProfileActivity : BaseActivity() {

    lateinit var binding: ProfileUnionBinding
    private lateinit var profileVM: ProfileVM

    val hourOptions = mutableListOf<String>()
    val dayDicts = mutableMapOf<String, Int>()
    val dayOptions = mutableListOf<String>()
    val dayOptionVals = mutableListOf<Int>()

    private lateinit var chooseDialog: SaasWhChooseDialog
    private lateinit var tenantDialog: TenantDialog
    private lateinit var whDialog: WhDialog

    companion object {
        //  默认开始时间:  3天前的 20:00
        val defaultStartDay = 2  // 看板取数 默认开始日 x天前
        val defaultStartHour = "20:00" // 看板取数 默认开始时分

        //  默认结束时间:  1天前(昨天)的  20:00
        val defaultEndDay = 0    // 看板取数 默认结束日 x天前 (0表示结束日为当天)
        val defaultEndHour = "20:00" // 看板取数 默认结束时分
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = if (SPUtils[CARE_MODE, false] as Boolean) {
            ProfileUnionBinding.V2(DataBindingUtil.setContentView(this, R.layout.activity_profile_gh))
        } else {
            ProfileUnionBinding.V1(DataBindingUtil.setContentView(this, R.layout.activity_profile))
        }

        profileVM = ProfileVM(this, binding)
        binding.vm = profileVM

        initSwitch()
        initSpinner()
        initDialog()

        //从本地db加载上一次数据
        binding.vm!!.loadSettingFromDB()
    }

    private fun initSwitch() {
        binding.mSwitch.isChecked = SPUtils[CARE_MODE, false] as Boolean
        binding.mSwitch.setOnCheckedChangeListener { _, isChecked ->
            if (binding.mSwitch.isEnabled) {
                SPUtils.put(application, CARE_MODE, isChecked)
                showCareDialog(isChecked)
                binding.mSwitch.isEnabled = false
                // 2秒后将isSwitchClickable设置为true
                Handler(Looper.getMainLooper()).postDelayed({
                    binding.mSwitch.isEnabled = true
                }, 2000)
            }
        }
    }

    private fun showCareDialog(isChecked: Boolean) {
        var tipDialog = TipDialog(this)
        tipDialog.setUnCancel()
        tipDialog.setTitle("提示")
        tipDialog.setMsg("关怀模式相关设置需要重启应用才能生效")
        tipDialog.setOnTipBackListener(object : TipDialog.OnTipBack {
            override fun onConfirmClick() {
                val intent = baseContext.packageManager.getLaunchIntentForPackage(baseContext.packageName)
                intent?.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                startActivity(intent)
            }

            override fun onDismissClick() {
                SPUtils.put(application, CARE_MODE, !isChecked)
                binding.mSwitch.isEnabled = false
                binding.mSwitch.isChecked = !isChecked
                binding.mSwitch.isEnabled = true
            }
        })
        tipDialog.show()
    }

    private fun initSpinner() {
        dayDicts["当天"] = 0
        dayDicts["前1天"] = 1
        dayDicts["前2天"] = 2
        dayDicts["前3天"] = 3

        // 从 00:01 到  23:59
        hourOptions.add("00:01")
        for (i in 1..23) {
            var hour = String.format("%02d", i)
            hourOptions.add("$hour:00")
        }
        hourOptions.add("23:59")

        binding.spinnerStartHour.setItems(hourOptions)
        binding.spinnerEndHour.setItems(hourOptions)

        dayDicts.keys.forEach {
            dayOptions.add(it)
        }

        dayDicts.values.forEach{
            dayOptionVals.add(it)
        }

        binding.spinnerStartDay.setItems(dayOptions)
        binding.spinnerEndDay.setItems(dayOptions)

        binding.spinnerStartHour.setOnItemSelectedListener({ view, position, id, item ->
            binding.vm!!.onChangeStartHour(hourOptions[position])
        })

        binding.spinnerEndHour.setOnItemSelectedListener(MaterialSpinner.OnItemSelectedListener<String> { view, position, id, item ->
            binding.vm!!.onChangeEndHour(hourOptions[position])
        })

        binding.spinnerStartDay.setOnItemSelectedListener(MaterialSpinner.OnItemSelectedListener<String> { view, position, id, item ->
            binding.vm!!.onChangeStartDay(dayDicts.get(dayOptions[position])!!)
        })

        binding.spinnerEndDay.setOnItemSelectedListener(MaterialSpinner.OnItemSelectedListener<String> { view, position, id, item ->
            binding.vm!!.onChangeEndDay(dayDicts.get(dayOptions[position])!!)
        })

        val values = Printers.values()
        val printers = mutableListOf<String>()
        values.forEach {
            printers.add(it.serverName)
        }
        binding.spinnerPrinter.setItems(printers)

        binding.spinnerPrinter.setOnItemSelectedListener { _, position, _, _ ->
            binding.vm!!.onChangePrinter(values[position])
        }

        val valueModes = PrinterModes.values()
        val printerModes = mutableListOf<String>()
        valueModes.forEach {
            printerModes.add(it.modeName)
        }
        binding.spinnerIsAutoConnect.setItems(printerModes)

        binding.spinnerIsAutoConnect.setOnItemSelectedListener { _, position, _, _ ->
            binding.vm!!.onChangePrinterModes(valueModes[position])
        }

        val pickValueModes = PickModes.values()
        val pickModes = mutableListOf<String>()
        pickValueModes.forEach {
            pickModes.add(it.modeName)
        }
        binding.spinnerPickMode.setItems(pickModes)

        binding.spinnerPickMode.setOnItemSelectedListener { _, position, _, _ ->
            binding.vm!!.onChangePickModes(pickValueModes[position])
        }
    }

    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }

    private fun initDialog() {

        //选择租户和仓库dialog
        chooseDialog = SaasWhChooseDialog(this)
        chooseDialog.setChooseBack(object : SaasWhChooseDialog.ChooseBack {
            override fun chooseBack(type : String) {
                if(type == "1") {
                    if (!tenantDialog.isShowing) {
                        tenantDialog.show()
                    }
                }else if(type == "2") {
                    if (!whDialog.isShowing) {
                        whDialog.show()
                    }
                }
            }

            override fun confirmBack(whCode: String, whName: String) {
                chooseDialog.dismiss()
                binding.vm?.saveWhInfoList(binding.vm?.whInfoList)
                binding.vm?.whInfo?.let { binding.vm?.saveWhInfo(it) }
                Constants.tenantCode = binding.vm?.tenantInfo?.tenantCode
                Constants.tenantName = binding.vm?.tenantInfo?.tenantName
                SPUtils.put(application, "tenantCode", binding.vm?.tenantInfo?.tenantCode as String)
                SPUtils.put(application, "tenantName", binding.vm?.tenantInfo?.tenantName as String)
                App.tenantCode = binding.vm?.tenantInfo?.tenantCode
                Constants.accessToken = binding.vm?.accessToken
                SPUtils.put(application, "accessToken", binding.vm?.accessToken as String)
                startActivity(Intent(this@ProfileActivity, MainYgActivity::class.java))
            }
        })

        //选择租户dialog
        tenantDialog = TenantDialog(this)
        tenantDialog.setTitle("请选择租户")
        tenantDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            chooseDialog.setTenant(it.tenantCode, it.tenantName)
            chooseDialog.setWarehouse("", "")
            binding.vm?.switchTenant(it.tenantCode)
            binding.vm?.tenantInfo = it
            tenantDialog.dismiss()
        })

        //选择仓库dialog
        whDialog = WhDialog(this)
        whDialog.setTitle("请选择仓库")
        whDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            chooseDialog.setWarehouse(it.whCode, it.cdwhName)
            binding.vm?.whInfo = it
            whDialog.dismiss()
        })
    }

    fun resetWarehouse() {
        chooseDialog.setWarehouse("", "")
    }

    fun showWhDialog(tenantCode: String, tenantName: String) {
        if (!chooseDialog.isShowing) {
            chooseDialog.setTenant(tenantCode, tenantName)
            chooseDialog.setWarehouse("", "")
            chooseDialog.show()
        }
    }

    fun setTenant(tenantCode: String, tenantName: String) {
        chooseDialog.setTenant(tenantCode, tenantName)
    }

    fun setTenantInfo(list: MutableList<TenantResp.TenantsDTO>?) {
        tenantDialog.setNewData(list)
        tenantDialog.notifyDataChange(list)
    }

    fun setWareHouse(whCode: String, whName: String) {
        chooseDialog.setWarehouse(whCode, whName)
    }

    fun setWhInfo(list: MutableList<ImplWarehouse>?) {
        whDialog.setNewData(list)
        whDialog.notifyDataChange(list)
    }
}