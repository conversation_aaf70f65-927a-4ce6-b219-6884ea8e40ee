package com.midea.prestorage.function.inv

import android.widget.*
import androidx.lifecycle.LifecycleOwner
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.databinding.ActivityInfoCollectionBinding
import com.midea.prestoragesaas.databinding.ActivityInfoCollectionCareBinding

sealed class ActivityInfoCollectionUnionBinding{
    abstract var vm: InfoCollectionVM?
    abstract val llTitleBar: RelativeLayout
    abstract val etIpQty: EditText
    abstract val etEaQty: EditText
    abstract val spinnerExpiryDate: MaterialSpinner
    abstract val llExpiryDate: LinearLayout
    abstract val spinnerExpiryDateUnit: MaterialSpinner
    abstract val addSwitch: Switch
    abstract val flIp: FrameLayout
    abstract val edItemLength: EditText
    abstract val edItemWidth: EditText
    abstract val edItemHeight: EditText
    abstract val spinnerCs: MaterialSpinner
    abstract val spinnerIp: MaterialSpinner
    abstract val spinnerEa: MaterialSpinner
    abstract val rg: RadioGroup
    abstract val rbA: RadioButton
    abstract val rbB: RadioButton
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: ActivityInfoCollectionCareBinding) : ActivityInfoCollectionUnionBinding() {
        override var vm: InfoCollectionVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etIpQty = binding.etIpQty
        override val etEaQty = binding.etEaQty
        override val spinnerExpiryDate = binding.spinnerExpiryDate
        override val llExpiryDate = binding.llExpiryDate
        override val spinnerExpiryDateUnit = binding.spinnerExpiryDateUnit
        override val addSwitch = binding.addSwitch
        override val flIp = binding.flIp
        override val edItemLength = binding.edItemLength
        override val edItemWidth = binding.edItemWidth
        override val edItemHeight = binding.edItemHeight
        override val spinnerCs = binding.spinnerCs
        override val spinnerIp = binding.spinnerIp
        override val spinnerEa = binding.spinnerEa
        override val rg = binding.rg
        override val rbA = binding.rbA
        override val rbB = binding.rbB
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: ActivityInfoCollectionBinding) : ActivityInfoCollectionUnionBinding() {
        override var vm: InfoCollectionVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etIpQty = binding.etIpQty
        override val etEaQty = binding.etEaQty
        override val spinnerExpiryDate = binding.spinnerExpiryDate
        override val llExpiryDate = binding.llExpiryDate
        override val spinnerExpiryDateUnit = binding.spinnerExpiryDateUnit
        override val addSwitch = binding.addSwitch
        override val flIp = binding.flIp
        override val edItemLength = binding.edItemLength
        override val edItemWidth = binding.edItemWidth
        override val edItemHeight = binding.edItemHeight
        override val spinnerCs = binding.spinnerCs
        override val spinnerIp = binding.spinnerIp
        override val spinnerEa = binding.spinnerEa
        override val rg = binding.rg
        override val rbA = binding.rbA
        override val rbB = binding.rbB
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
