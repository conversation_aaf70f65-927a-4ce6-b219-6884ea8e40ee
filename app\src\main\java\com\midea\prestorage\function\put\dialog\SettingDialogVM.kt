package com.midea.prestorage.function.put.dialog

import android.view.View
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogSettingBinding
import com.midea.prestoragesaas.databinding.DialogSettingPutBinding
import com.midea.prestorage.http.constants.Constants

class SettingDialogVM(
    var dialog: SettingDialog,
    val binding: DialogSettingPutBinding
) {
    fun init() {
        if (Constants.userInfo?.scanPutMode == 0) {
            binding.rbQueue.isChecked = true
        } else {
            binding.rbAll.isChecked = true
        }
    }

    var listener: SettingDialog.SettingBack? = null

    /**
     * 确认按钮
     */
    val confirmClick = View.OnClickListener {
        val checkedRadioButtonId = binding.rgReceiveMode.checkedRadioButtonId
        if (checkedRadioButtonId == R.id.rb_queue) {
            listener?.onConfirmClick(0)
        } else {
            listener?.onConfirmClick(1)
        }
        dialog.dismiss()
    }

    /**
     * 取消按钮
     */
    var cancelClick = View.OnClickListener {
        dialog.dismiss()
    }
}