package com.midea.prestorage.function.containerpick.adapter

import com.chad.library.adapter.base.BaseNodeAdapter
import com.chad.library.adapter.base.entity.node.BaseNode
import com.chad.library.adapter.base.module.LoadMoreModule
import com.midea.prestorage.function.containerpick.fragment.BulkToBePackedVM
import com.midea.prestorage.function.containerpick.provider.BulkPickDetailProvider
import com.midea.prestorage.function.containerpick.provider.BulkPickHeaderProvider
import com.midea.prestorage.function.containerpick.provider.BulkPickToBeWrap

class BulkToBePackedAdapter(vm: BulkToBePackedVM) :
    BaseNodeAdapter() {

    companion object {
        const val TYPE_HEADER = 1
        const val TYPE_DETAIL = 2
    }

    init {
        addNodeProvider(BulkPickHeaderProvider(vm))
        addNodeProvider(BulkPickDetailProvider(vm))
    }

    override fun getItemType(data: List<BaseNode>, position: Int): Int {
        return if (data[position] is BulkPickToBeWrap) TYPE_HEADER else TYPE_DETAIL
    }

}