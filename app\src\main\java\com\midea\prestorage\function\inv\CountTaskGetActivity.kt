package com.midea.prestorage.function.inv

import android.content.Intent
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestoragesaas.databinding.ActivityCountTaskGetBinding
import com.midea.prestoragesaas.databinding.ActivityLotSearchBinding

// 获取盘点任务
class CountTaskGetActivity : BaseActivity() {

    private lateinit var binding: ActivityCountTaskGetBinding
    private var adapter: CommonAdapter<CountTaskGet> = CommonAdapter(R.layout.item_count_task_get)


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //setContentView(R.layout.activity_count_task_get)

        binding = DataBindingUtil.setContentView(this, R.layout.activity_count_task_get)
        binding.vm = CountTaskGetVM(this)

        initRecycleView()
        // todo 测试数据
        addTestData()
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }


    fun initRecycleView(){
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter

        adapter.setOnItemClickListener { adapter, view, position ->
            //val intent = Intent(this, XXXActivity::class.java)
            //startActivity(intent)
        }
    }

    fun addTestData(){
        for(i in 1..20) {
            var countTask = CountTaskGet()
            countTask.test1 = "Z01" // 库区
            countTask.test2 = "010101" // 库位
            countTask.test3 = "任务未开始"  //任务状态
            adapter.addData(countTask)
        }

        adapter.notifyDataSetChanged()
    }


    class CountTaskGet : BaseItemForPopup() {
        var test1:String = ""  //库区
        var test2:String = ""  //库位
        var test3:String = ""  //任务状态
    }
}