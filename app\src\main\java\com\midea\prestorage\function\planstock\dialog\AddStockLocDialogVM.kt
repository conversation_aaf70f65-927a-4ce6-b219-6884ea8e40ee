package com.midea.prestorage.function.planstock.dialog

import android.app.Application
import android.text.TextUtils
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.widgets.ViewBindingAdapter

class AddStockLocDialogVM(application: Application) : BaseViewModel(application) {
    val locCode = ObservableField<String>("")
    var isDissmiss = MutableLiveData<Boolean>(false)
    var content = MutableLiveData<String>("")
    var startScan = MutableLiveData<Boolean>(false)

    companion object{
        const val QRCODE_BACK = 6001
    }

    fun close() {
        isDissmiss.value = true
    }

    val goodsEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (!TextUtils.isEmpty(locCode.get())) {
                confirm()
            }
        }
    }

    fun startScan() {
        if (CheckUtil.isFastDoubleClick()) {
            startScan.value = true
        }
    }

    fun confirm() {
        if (CheckUtil.isFastDoubleClick()) {
            if (locCode.get().isNullOrBlank()) {
                return
            }
            locCode.get()?.let { content.value = it }
        }
    }

    override fun init() {

    }

}