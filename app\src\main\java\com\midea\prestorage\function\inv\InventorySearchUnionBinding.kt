package com.midea.prestorage.function.inv

import android.widget.*
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestorage.function.main.ProfileUnionBinding
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.databinding.ActivityInventorySearchBinding
import com.midea.prestoragesaas.databinding.ActivityInventorySearchCareBinding
import com.midea.prestoragesaas.databinding.ActivityProfileBinding
import com.midea.prestoragesaas.databinding.ActivityProfileGhBinding

sealed class InventorySearchUnionBinding {
    abstract var vm: InventorySearchVM?
    abstract val etLocCode: AutoCompleteTextView
    abstract val etCustItemCode: EditText
    abstract val mSwitch: Switch
    abstract val srl: SwipeRefreshLayout
    abstract val recyclerView: RecyclerView
    abstract val titleBtnMore: ImageButton
    abstract val llToolbar: LinearLayout
    abstract val tvNotification: TextView

    class V2(val binding: ActivityInventorySearchCareBinding) : InventorySearchUnionBinding() {
        override var vm: InventorySearchVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val etLocCode = binding.etLocCode
        override val etCustItemCode = binding.etCustItemCode
        override val mSwitch = binding.mSwitch
        override val srl = binding.srl
        override val recyclerView = binding.recyclerView
        override val titleBtnMore = binding.titleBtnMore
        override val llToolbar = binding.llToolbar
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityInventorySearchBinding) : InventorySearchUnionBinding() {
        override var vm: InventorySearchVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val etLocCode = binding.etLocCode
        override val etCustItemCode = binding.etCustItemCode
        override val mSwitch = binding.mSwitch
        override val srl = binding.srl
        override val recyclerView = binding.recyclerView
        override val titleBtnMore = binding.titleBtnMore
        override val llToolbar = binding.llToolbar
        override val tvNotification = binding.tvNotification
    }
}
