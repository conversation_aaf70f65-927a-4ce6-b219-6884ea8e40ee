package com.midea.prestorage.function.planstock

import android.widget.EditText
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.databinding.ActivityPlanStockBinding
import com.midea.prestoragesaas.databinding.ActivityPlanStockCareBinding

sealed class ActivityPlanStockUnionBinding{
    abstract var vm: PlanStockVM?
    abstract val llTitleBar: RelativeLayout
    abstract val etSearchOrderNo: EditText
    abstract val srl: SwipeRefreshLayout
    abstract val recycle: RecyclerView
    abstract val spinnerStatus: MaterialSpinner
    abstract val tvNotification: TextView

    class V2(val binding: ActivityPlanStockCareBinding) : ActivityPlanStockUnionBinding() {
        override var vm: PlanStockVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etSearchOrderNo = binding.etSearchOrderNo
        override val srl = binding.srl
        override val recycle = binding.recycle
        override val spinnerStatus = binding.spinnerStatus
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityPlanStockBinding) : ActivityPlanStockUnionBinding() {
        override var vm: PlanStockVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etSearchOrderNo = binding.etSearchOrderNo
        override val srl = binding.srl
        override val recycle = binding.recycle
        override val spinnerStatus = binding.spinnerStatus
        override val tvNotification = binding.tvNotification
    }
}
