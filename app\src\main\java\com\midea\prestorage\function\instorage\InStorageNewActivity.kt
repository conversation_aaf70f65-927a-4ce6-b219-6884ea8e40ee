package com.midea.prestorage.function.instorage

import android.content.Intent
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickPositionAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestoragesaas.databinding.ActivityInStorageNewBinding
import com.midea.prestoragesaas.databinding.PopViewInStorageNewBinding
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.dialog.MultiChooseDialog
import com.midea.prestorage.function.instorage.response.RespReceiptHeader
import com.midea.prestorage.utils.AppUtils

class InStorageNewActivity : BaseViewModelActivity<InStorageNewVM>() {
    private lateinit var binding: ActivityInStorageNewBinding
    private lateinit var popBinding: PopViewInStorageNewBinding
    private lateinit var popupWindow: PopupWindow
    private lateinit var driverDialog: FilterDialog
    private lateinit var carNoDialog: FilterDialog
    private lateinit var receiptTypeDialog: MultiChooseDialog
    private lateinit var orderStatusDialog: MultiChooseDialog
    private lateinit var adapter: InStorageOrderAdapter

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_in_storage_new)
        vm = ViewModelProvider.AndroidViewModelFactory(application).create(InStorageNewVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        //返回
        vm.finishActivity.observe(this, Observer<Boolean> {
            if (it) {
                finish()
            }
        })

        vm.showDatas.observe(this, Observer<MutableList<RespReceiptHeader>> {
            showData(it)
        })

        vm.inOrderType.observe(this, Observer<MutableList<DCBean>> { result ->
            receiptTypeDialog.addAllData(result as MutableList<BaseItemShowInfo>)
        })

        vm.initReceiptOrderType()

        vm.carNoBeans.observe(this, Observer<MutableList<BaseItemShowInfo>> { result ->
            if(result.size > 0) {
                val bean = BaseItemShowInfo()
                bean.showInfo = "全部"
                result.add(0, bean)
            }
            carNoDialog.addAllData(result)

        })

        vm.driverNoBeans.observe(this, Observer<MutableList<BaseItemShowInfo>> { result ->
            if(result.size > 0) {
                val bean = BaseItemShowInfo()
                bean.showInfo = "全部"
                result.add(0, bean)
            }
            driverDialog.addAllData(result)
        })

        vm.initCarNo(vm.dayInfo.value.toString().split("天")[0], getWhCode())
        vm.initDriver(vm.dayInfo.value.toString().split("天")[0], getWhCode())

        vm.inOrderStatus.observe(this, Observer<MutableList<DCBean>> { result ->
            if (result.size > 0 && (result[0].value == "待收货" || result[0].value == "收货中")) {
                result[0].isSelected = true
                vm.statusList.add(result[0].key)
            }
            if (result.size > 1 && (result[1].value == "待收货" || result[1].value == "收货中")) {
                result[1].isSelected = true
                vm.statusList.add(result[1].key)
            }
            orderStatusDialog.addAllData(result as MutableList<BaseItemShowInfo>)

            vm.onRefreshCommand.onRefresh()
        })
        vm.initReceiptOrderStatus()

        initPop()
        initDialog()
        initRecycleView()
        initLoadMore()

        //弹出筛选框
        vm.showFilter.observe(this, Observer<Boolean> {
            if (it) {
                popupWindow.showAsDropDown(binding.llContainer)
            }
        })

        //弹出选择司机
        vm.isShowDriverDialog.observe(this, Observer<Boolean> {
            if (it) {
                driverDialog.show()
            }
        })

        //弹出选择车牌号
        vm.isShowCarNoDialog.observe(this, Observer<Boolean> {
            if (it) {
                carNoDialog.show()
            }
        })

        //弹出选择单据类型
        vm.isShowReceiptTypeDialog.observe(this, Observer<Boolean> {
            if (it) {
                receiptTypeDialog.show()
            }
        })

        //弹出选择订单状态
        vm.isShowOrderStatusDialog.observe(this, Observer<Boolean> {
            if (it) {
                orderStatusDialog.show()
            }
        })

        //重置筛选
        vm.resetData.observe(this, Observer<Boolean> { result ->
            if (result) {
                vm.driverInfo.value = ""
                vm.carNoInfo.value = ""
                vm.orderStatus.value = "待收货,收货中"
                vm.receiptType.value = ""

                vm.receiptTypeList.clear()
                vm.statusList.clear()
                vm.inOrderType.value?.forEach {
                    it.isSelected = false
                }
                receiptTypeDialog.addAllData(vm.inOrderType.value as MutableList<BaseItemShowInfo>)
                vm.inOrderStatus.value?.forEach {
                    if(it.value == "待收货" || it.value == "收货中") {
                        vm.statusList.add(it.key)
                        it.isSelected = true
                    }else {
                        it.isSelected = false
                    }
                }
                orderStatusDialog.addAllData(vm.inOrderStatus.value as MutableList<BaseItemShowInfo>)
            }
        })

        //刷新显示筛选的条件
        vm.resetFilterInfo?.observe(this, Observer<MutableList<String>> {
            resetFilterInfo(it)
        })

        //进入扫码页面
        vm.toScanActivity.observe(this, Observer<MutableList<String>> {
            if(it.size > 0) {
                val intent = Intent(this, InStorageScanNewActivity::class.java)
                // 这里塞一个 后端返回的波次号或入库单号
                intent.putExtra("orderNo", it[0])
                intent.putExtra("orderReceiveType", it[1])
                intent.putExtra("containerCode", "11111") //容器号
                startActivity(intent)
            }
        })

        vm.loadMoreDatas.observe(this, Observer<MutableList<RespReceiptHeader>> {
            loadMoreData(it)
        })

        vm.loadMoreComplete.observe(this, Observer<Int> {
            if (it == 1) {
                adapter.loadMoreModule.loadMoreComplete()
            } else if (it == 2) {
                adapter.loadMoreModule.loadMoreEnd()
            }
        })

        initSpinner()
    }

    private fun initSpinner() {
        val beans = mutableListOf<String>(
            "1天",
            "3天",
            "7天",
            "30天"
        )
        binding.spinnerStatus.setItems(beans)
        binding.spinnerStatus.selectedIndex = 1

        binding.spinnerStatus.setOnItemSelectedListener { _, position, _, _ ->
            vm.dayInfo.value = beans[position]
            vm.initFilterInfo()
            vm.driverInfo.value = ""
            vm.carNoInfo.value = ""
            vm.initCarNo(vm.dayInfo.value.toString().split("天")[0], getWhCode())
            vm.initDriver(vm.dayInfo.value.toString().split("天")[0], getWhCode())
            vm.onRefreshCommand.onRefresh()
        }
    }

    private fun initPop() {
        val popView = LayoutInflater.from(this).inflate(R.layout.pop_view_in_storage_new, null)
        popBinding = DataBindingUtil.bind(popView)!!
        popBinding.lifecycleOwner = this
        popBinding.vm = vm

        popupWindow = PopupWindow(
            popView,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        popupWindow.setBackgroundDrawable(
            ColorDrawable(
                ContextCompat.getColor(
                    this,
                    R.color.colorOutlineGrey
                )
            )
        )

        popupWindow.setOnDismissListener {
            vm.initFilterInfo()
            vm.onRefreshCommand.onRefresh()
        }

        popupWindow.isOutsideTouchable = true

        popBinding.llFilterClose.setOnClickListener {
            popupWindow.dismiss()
        }

    }

    private fun initDialog() {
        //司机dialog
        driverDialog = FilterDialog(this)
        driverDialog.setTitle("请选择司机")
        driverDialog.dismissEdit()
        driverDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            vm.driverInfo.value = it.showInfo
            //vm.initEngineerList()
            driverDialog.dismiss()
        })

        //车牌号dialog
        carNoDialog = FilterDialog(this)
        carNoDialog.setTitle("请选择车牌号")
        carNoDialog.dismissEdit()
        carNoDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            vm.carNoInfo.value = it.showInfo
            //vm.initEngineerList()
            carNoDialog.dismiss()
        })

        //单据类型dialog
        receiptTypeDialog = MultiChooseDialog(this)
        receiptTypeDialog.setTitle("请选择单据类型")
        receiptTypeDialog.setBack(object : MultiChooseDialog.MultiChooseBack {
            override fun multiChooseBack(baseInfo: MutableList<BaseItemShowInfo>) {
                vm.receiptTypeInfo = baseInfo as MutableList<DCBean>
                vm.showReceiptTypeInfo()
                //vm.initEngineerList()
                receiptTypeDialog.dismiss()
            }
        })

        //订单状态dialog
        orderStatusDialog = MultiChooseDialog(this)
        orderStatusDialog.setTitle("请选择订单状态")
        orderStatusDialog.setBack(object : MultiChooseDialog.MultiChooseBack {
            override fun multiChooseBack(baseInfo: MutableList<BaseItemShowInfo>) {
                vm.orderStatusInfo = baseInfo as MutableList<DCBean>
                vm.showOrderStatusInfo()
                //vm.initEngineerList()
                orderStatusDialog.dismiss()
            }
        })
    }

    fun resetFilterInfo(filters: MutableList<String>) {
        binding.warpLinear.removeAllViews()
        filters.forEach {
            binding.warpLinear.addView(getShowView(it))
        }
    }

    private fun getShowView(text: String): View {
        var view: View = LayoutInflater.from(this).inflate(R.layout.item_filter, null)
        view.findViewById<TextView>(R.id.tv_item).text = text
        return view
    }

    fun initRecycleView() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        adapter = InStorageOrderAdapter(vm)
        binding.recycle.layoutManager = LinearLayoutManager(this)
//        binding.recycle.addItemDecoration(DividerItemDecoration(this, DividerItemDecoration.VERTICAL))
        binding.recycle.adapter = adapter

        adapter.setOnCheckListener { t, position ->
            val intent = Intent(this, InStorageScanNewActivity::class.java)
            // 这里塞一个 后端返回的波次号或入库单号
            if(!t.waveNo.isNullOrBlank()) {
                intent.putExtra("orderNo", t.waveNo)
                intent.putExtra("orderReceiveType", "wave")
            }else {
                if(!t.receiptCode.isNullOrBlank()) {
                    intent.putExtra("orderNo", t.receiptCode)
                    intent.putExtra("orderReceiveType", "receipt")
                }
            }
            intent.putExtra("containerCode", "11111") //容器号
            startActivity(intent)
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.loadMore()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    fun showData(data: MutableList<RespReceiptHeader>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()

        vm.isNoData.value = adapter.data.isNullOrEmpty()
        vm.isRefreshing.value = false
    }

    fun loadMoreData(data: MutableList<RespReceiptHeader>?) {
        data?.let { adapter.addData(it) }
        adapter.notifyDataSetChanged()
    }

    override fun onResume() {
        super.onResume()
        if(!vm.isFirstEnter.value!!) {
            vm.onRefreshCommand.onRefresh()
        }
    }

    class InStorageOrderAdapter(private val vm: InStorageNewVM?) : ListChoiceClickPositionAdapter<RespReceiptHeader>(R.layout.item_in_storage_order),
        LoadMoreModule {

        override fun convert(helper: BaseViewHolder, item: RespReceiptHeader) {
            super.convert(helper, item)

            if(!item.receiptCode.isNullOrBlank()) {
                helper.setText(R.id.tv_receipt_code, item.receiptCode)
            }else {
                helper.setText(R.id.tv_receipt_code, "")
            }

            if(!item.waveNo.isNullOrBlank()) {
                helper.setText(R.id.tv_wave_no, item.waveNo)
            }else {
                helper.setText(R.id.tv_wave_no, "")
            }

            if(!AppUtils.getBigDecimalValueStr(item.totalQty).isNullOrBlank()) {
                helper.setText(R.id.tv_total_qty, AppUtils.getBigDecimalValueStr(item.totalQty))
            }else {
                helper.setText(R.id.tv_total_qty, "")
            }

            if(!item.custOrderNo.isNullOrBlank()) {
                helper.setText(R.id.tv_order_no, item.custOrderNo)
            }else {
                helper.setText(R.id.tv_order_no, "")
            }

            if(!item.dispatchNo.isNullOrBlank()) {
                helper.setText(R.id.tv_dispatch_no, item.dispatchNo)
            }else {
                helper.setText(R.id.tv_dispatch_no, "")
            }

            if(!item.carNo.isNullOrBlank()) {
                helper.setText(R.id.tv_car_no, item.carNo)
            }else {
                helper.setText(R.id.tv_car_no, "")
            }

            vm?.inOrderType?.value?.forEach {
                if (it.key == item?.receiptType) {
                    helper.setText(R.id.tv_receipt_type, it.value.toString())
                }
            }

            vm?.inOrderStatus?.value?.forEach {
                if (it.key == item?.status) {
                    helper.setText(R.id.tv_status, it.value.toString())
                }
            }

        }
    }

}