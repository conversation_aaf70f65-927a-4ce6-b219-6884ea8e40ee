package com.midea.prestorage.function.outstorage.dialog

import android.app.AlertDialog
import android.text.TextUtils
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogDeleteBarcodeBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

class DeleteBarcodeDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {
    private var binding: DialogDeleteBarcodeBinding
    private var deleteback: DeleteBarcodeBack? = null

    init {
        val contentView = LayoutInflater.from(mContext).inflate(R.layout.dialog_delete_barcode, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = DeleteBarcodeDialogVM(this)

        setCanceledOnTouchOutside(true)
    }

    fun setTitle(title: String) {
        if (!TextUtils.isEmpty(title)) {
            binding!!.vm!!.title.set(title)
        }
    }

    fun setDeleteBack(backImpl: DeleteBarcodeBack) {
        deleteback = backImpl
    }

    fun backDeleteBarcode(barcode : String) {
        if (deleteback != null) {
            deleteback!!.deleteBarcodeBack(barcode)
            binding!!.vm!!.etInfo.set("")
        }
    }

    interface DeleteBarcodeBack {
        fun deleteBarcodeBack(barcode : String)
    }
}