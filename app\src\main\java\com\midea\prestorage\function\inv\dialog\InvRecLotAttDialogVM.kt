package com.midea.prestorage.function.inv.dialog

import android.annotation.SuppressLint
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.bigkoo.pickerview.TimePickerView
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.function.inv.response.LotDetail
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.isNull
import com.midea.prestorage.utils.launch
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ItemLotAttEditBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*

class InvRecLotAttDialogVM(val dialog: InvRecLotAttDialog) :
    BaseViewModel(dialog.mContext.application) {

    @SuppressLint("SimpleDateFormat")
    var format = SimpleDateFormat("yyyy-MM-dd")
    var templates: MutableList<LotDetail>? = null
    var backDataListener: InvRecLotAttDialog.BackData? = null
    val lotAttVMList = mutableListOf<LotAttDialogVM>()
    var lotAttStr = MutableLiveData<MutableList<String>>()

    fun initData() {
        //先移除 LinearLayout 中的所有子 View
        dialog.binding.llContent.removeAllViews()
        lotAttVMList.clear()
        templates?.filter { "LOT_ATT04" != it.lotAtt }?.forEach {
            val contentView = getShowView()
            dialog.binding.llContent.addView(contentView)
            val binding: ItemLotAttEditBinding = DataBindingUtil.bind(contentView)!!
            val viewModel = initDialogVM(it)
            if (viewModel.canEdit) {
                viewModel.hintStr.set("请输入${it.title}")
            }else {
                viewModel.content.set("请选择${it.title}")
            }
            if ("LOT_ATT03" == it.lotAtt) {
                viewModel.content.set(format.format(Date()))
            }
            binding.vm = viewModel
            lotAttVMList.add(viewModel)
        }
    }

    private fun getShowView(): View {
        return LayoutInflater.from(dialog.mContext).inflate(R.layout.item_lot_att_edit, null)
    }

    private fun initDialogVM(data: LotDetail): LotAttDialogVM {
        return LotAttDialogVM(
            data.title,
            showArrow = false,
            canEdit = !checkTimePick(data.fieldType) && !checkPopField(data.fieldType),
            data.fieldType,
            data.lotAtt,
            data.key
        )
    }

    fun checkTimePick(fieldType: String?): Boolean {
        return "DateField" == fieldType || "DateTimeField" == fieldType
    }

    fun checkPopField(fieldType: String?): Boolean {
        return "PopField" == fieldType || "ComboBox" == fieldType
    }

    fun showTimePick(tag: Int) {
        val pvTime = TimePickerView.Builder(
            dialog.mContext,
            TimePickerView.OnTimeSelectListener { date2, _ ->
                when (tag) {
                    1 -> {
                        lotAttVMList.find { it.lotAtt == "LOT_ATT01" }?.content?.set(
                            format.format(
                                date2
                            )
                        )
                        calculateDate(format.format(date2))
                    }
                    2 -> {
                        lotAttVMList.find { it.lotAtt == "LOT_ATT02" }?.content?.set(
                            format.format(
                                date2
                            )
                        )
                    }
                    3 -> lotAttVMList.find { it.lotAtt == "LOT_ATT03" }?.content?.set(
                        format.format(
                            date2
                        )
                    )
                }
            })
        var title: String? = null
        when (tag) {
            1 -> title = "生产日期"
            2 -> title = "失效日期"
            3 -> title = "入库日期"
        }
        pvTime.setType(TimePickerView.Type.YEAR_MONTH_DAY)//默认全部显示
            .setCancelText("取消")//取消按钮文字
            .setSubmitText("确定")//确认按钮文字
            .setContentSize(20)//滚轮文字大小
            .setTitleSize(20)//标题文字大小
            .setOutSideCancelable(true)//点击屏幕，点在控件外部范围时，是否取消显示
            .setTitleText(title)
            .setTextColorCenter(Color.BLACK)//设置选中项的颜色
            .setTitleColor(Color.BLACK)//标题文字颜色
            .setSubmitColor(ContextCompat.getColor(dialog.mContext, R.color.colorBlue))//确定按钮文字颜色
            .setCancelColor(ContextCompat.getColor(dialog.mContext, R.color.colorOrange))//取消按钮文字颜色
            .isDialog(true)
            .setDate(Calendar.getInstance())
            .isCenterLabel(false) //是否只显示中间选中项的label文字，false则每项item全部都带有label。
            .build()

        //生产日期
        when (tag) {
            1 -> {
                val startDate = Calendar.getInstance()
                startDate.set(1900, 10, 30)
                val endDate = Calendar.getInstance()
                pvTime.setRangDate(startDate, endDate)
            }
            2 -> {
                val startDate = Calendar.getInstance()
                val endDate = Calendar.getInstance()
                endDate.add(Calendar.YEAR, 100)
                pvTime.setRangDate(startDate, endDate)
            }
            3 -> {
                val startDate = Calendar.getInstance()
                startDate.set(1900, 10, 30)
                val endDate = Calendar.getInstance()
                endDate.add(Calendar.YEAR, 100)
                pvTime.setRangDate(startDate, endDate)
            }
            else -> {
                return
            }
        }

        pvTime.build().show()
    }

    private fun calculateDate(lotAtt01: String?) {
        dialog.materialList?.let { bean ->
            if (bean?.periodOfValidity.isNull()) {
                return
            }
            if (bean?.isValidity?.toLowerCase(Locale.ROOT) == "y") {
                if (!bean.validityUnit.isNullOrEmpty()) {
                    if (!lotAtt01.isNullOrEmpty()) {
                        val cale = Calendar.getInstance()
                        cale.time = format.parse(lotAtt01)
                        when {
                            bean.validityUnit?.toLowerCase(Locale.ROOT) == "y" -> {
                                cale.add(
                                    Calendar.YEAR,
                                    AppUtils.getBigDecimalValue(bean.periodOfValidity).toInt()
                                )
                            }
                            bean.validityUnit?.toLowerCase(Locale.ROOT) == "m" -> {
                                cale.add(
                                    Calendar.MONTH,
                                    AppUtils.getBigDecimalValue(bean.periodOfValidity).toInt()
                                )
                            }
                            bean.validityUnit?.toLowerCase(Locale.ROOT) == "w" -> {
                                cale.add(
                                    Calendar.WEEK_OF_YEAR,
                                    AppUtils.getBigDecimalValue(bean.periodOfValidity).toInt()
                                )
                            }
                            bean.validityUnit?.toLowerCase(Locale.ROOT) == "d" -> {
                                cale.add(
                                    Calendar.DATE,
                                    AppUtils.getBigDecimalValue(bean.periodOfValidity).toInt()
                                )
                            }
                        }
                        lotAttVMList.find { it.lotAtt == "LOT_ATT02" }?.content?.set(
                            format.format(cale.time)
                        )
                    }
                }
            }
        }
    }

    fun getLotAttStr(dictCode: String): Job {
        return launch(showDialog = true,
            error = {
            }, finish = {
            }) {
            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getDirectionAPI().searchDictNew(dictCode)
            }

            if (result.code == 0L) {
                val datas = mutableListOf<String>()
                result.data?.forEach {
                    datas.add(it.name)
                }
                lotAttStr.value = datas
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    /**
     * 确认按钮
     */
    val confirmClick = View.OnClickListener {
        confirm()
    }

    private fun confirm() {
        if (CheckUtil.isFastDoubleClick()) {
            templates?.filter { "LOT_ATT04" != it.lotAtt }?.forEachIndexed { index, lotDetail ->
                //校验必填项不能为空
                if ("R" == lotDetail.inputControl && (lotAttVMList.getOrNull(index)?.content?.get()
                        .isNullOrBlank() || lotAttVMList.getOrNull(index)?.content?.get()?.startsWith("请选择") == true)
                ) {
                    AppUtils.showToast(dialog.mContext, lotDetail.title + "不能为空")
                    return
                } else {
                    lotDetail.value = if (lotAttVMList.getOrNull(index)?.content?.get()?.startsWith("请选择") == true) null
                    else lotAttVMList.getOrNull(index)?.content?.get()
                }
            }

            //20240704 校验日期，生产日期不能大于今天，失效日期不能小于今天
            if (lotAttVMList.find { it.lotAtt == "LOT_ATT01" }?.content?.get()?.isNotBlank() == true) {
                kotlin.runCatching {
                    val manufactureDate = format.parse(lotAttVMList.find { it.lotAtt == "LOT_ATT01" }?.content?.get())
                    val calendar = Calendar.getInstance()
                    calendar.set(Calendar.HOUR_OF_DAY, 0)
                    calendar.set(Calendar.MINUTE, 0)
                    calendar.set(Calendar.SECOND, 0)
                    calendar.set(Calendar.MILLISECOND, 0)
                    val now = calendar.time
                    if (manufactureDate?.after(now) == true) {
                        AppUtils.showToast(dialog.mContext, "生产日期不能大于当前日期！")
                        return
                    }
                }
            }
            if (lotAttVMList.find { it.lotAtt == "LOT_ATT01" }?.content?.get()?.isNotBlank() == true && lotAttVMList.find { it.lotAtt == "LOT_ATT02" }?.content?.get()?.isNotBlank() == true) {
                kotlin.runCatching {
                    val manufactureDate = format.parse(lotAttVMList.find { it.lotAtt == "LOT_ATT01" }?.content?.get())
                    val expireDate = format.parse(lotAttVMList.find { it.lotAtt == "LOT_ATT02" }?.content?.get())
                    if (manufactureDate != null && expireDate != null && manufactureDate.after(expireDate)) {
                        AppUtils.showToast(dialog.mContext, "失效日期不能小于生产日期!")
                        return
                    }
                }
            }

            dialog.dismiss()
            backDataListener?.onConfirmClick(templates)
        }
    }

    /**
     * 取消按钮
     */
    var cancelClick = View.OnClickListener {
        backDataListener?.onCancelClick()
        dialog.dismiss()
    }

    override fun init() {

    }
}