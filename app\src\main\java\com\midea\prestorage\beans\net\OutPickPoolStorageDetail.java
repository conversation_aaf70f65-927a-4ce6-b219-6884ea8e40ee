package com.midea.prestorage.beans.net;

import androidx.annotation.NonNull;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class OutPickPoolStorageDetail implements Serializable, Cloneable {
    private BigDecimal toQty;
    private BigDecimal requstQty;
    private OutTaskHeaderDto outTaskHeaderDto;
    private List<DetailResponses> detailResponses;

    public BigDecimal getToQty() {
        if (toQty == null) {
            return new BigDecimal(0);
        }
        return toQty;
    }

    public int getToQtyInt() {
        return getToQty().intValue();
    }

    public void setToQty(BigDecimal toQty) {
        this.toQty = toQty;
    }

    public int getRequstQty() {
        if (requstQty == null) {
            return 0;
        }
        return requstQty.intValue();
    }

    public void setRequstQty(BigDecimal requstQty) {
        this.requstQty = requstQty;
    }

    public OutTaskHeaderDto getOutTaskHeaderDto() {
        return outTaskHeaderDto;
    }

    public void setOutTaskHeaderDto(OutTaskHeaderDto outTaskHeaderDto) {
        this.outTaskHeaderDto = outTaskHeaderDto;
    }

    public List<DetailResponses> getDetailResponses() {
        return detailResponses;
    }

    public void setDetailResponses(List<DetailResponses> detailResponses) {
        this.detailResponses = detailResponses;
    }

    public class OutTaskHeaderDto implements Serializable {
        private String id;
        private String taskCode;
        private int status;
        private String createUserName;
        private String appointedBy;
        private String assignedBy;
        private String confirmedBy;
        private String waveNo;
        private String locked;
        private String taskModel;

        public String getTaskCode() {
            return taskCode;
        }

        public void setTaskCode(String taskCode) {
            this.taskCode = taskCode;
        }

        public String getAppointedBy() {
            return appointedBy;
        }

        public void setAppointedBy(String appointedBy) {
            this.appointedBy = appointedBy;
        }

        public String getAssignedBy() {
            return assignedBy;
        }

        public void setAssignedBy(String assignedBy) {
            this.assignedBy = assignedBy;
        }

        public String getConfirmedBy() {
            return confirmedBy;
        }

        public void setConfirmedBy(String confirmedBy) {
            this.confirmedBy = confirmedBy;
        }

        public String getWaveNo() {
            return waveNo;
        }

        public void setWaveNo(String waveNo) {
            this.waveNo = waveNo;
        }

        public String getLocked() {
            return locked;
        }

        public void setLocked(String locked) {
            this.locked = locked;
        }

        public String getTaskModel() {
            return taskModel;
        }

        public void setTaskModel(String taskModel) {
            this.taskModel = taskModel;
        }

        public String getCreateUserName() {
            return createUserName;
        }

        public void setCreateUserName(String createUserName) {
            this.createUserName = createUserName;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }
    }

    public class DetailResponses implements Serializable, Cloneable {
        @ShowAnnotation
        private String itemName;
        @ShowAnnotation
        private BigDecimal requstQty;
        @ShowAnnotation
        private String custItemCode;
        @ShowAnnotation
        private String fromZone;
        @ShowAnnotation
        private String fromLoc;
        @ShowAnnotation
        private String lotAtt05;
        @ShowAnnotation
        private String statusStr;
        @ShowAnnotation
        private String lotAtt04Str;

        private String lotAtt04;
        private String lotNum;
        private String id;
        private String orderBy;
        private String confirmBy;
        private String count;
        private String taskCode;
        private String ownerCode;
        private String itemCode;
        private String waveNo;
        private String updateUserCode;
        private BigDecimal toQty;
        private int status;
        private String referenceCode;//出库单号
        private String custOrderCode;
        private List<String> ids;

        public String getOrderBy() {
            return orderBy;
        }

        public void setOrderBy(String orderBy) {
            this.orderBy = orderBy;
        }

        public String getCount() {
            return count;
        }

        public void setCount(String count) {
            this.count = count;
        }

        public String getTaskCode() {
            return taskCode;
        }

        public void setTaskCode(String taskCode) {
            this.taskCode = taskCode;
        }

        public String getOwnerCode() {
            return ownerCode;
        }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
        }

        public String getCustItemCode() {
            return custItemCode;
        }

        public void setCustItemCode(String custItemCode) {
            this.custItemCode = custItemCode;
        }

        public String getItemCode() {
            return itemCode;
        }

        public void setItemCode(String itemCode) {
            this.itemCode = itemCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public BigDecimal getToQty() {
            if (toQty == null) {
                return new BigDecimal(0);
            }
            return toQty;
        }

        public void setToQty(BigDecimal toQty) {
            this.toQty = toQty;
        }

        public String getFromZone() {
            return fromZone;
        }

        public void setFromZone(String fromZone) {
            this.fromZone = fromZone;
        }

        public String getFromLoc() {
            return fromLoc;
        }

        public void setFromLoc(String fromLoc) {
            this.fromLoc = fromLoc;
        }

        public String getReferenceCode() {
            return referenceCode;
        }

        public void setReferenceCode(String referenceCode) {
            this.referenceCode = referenceCode;
        }

        public String getCustOrderCode() {
            return custOrderCode;
        }

        public void setCustOrderCode(String custOrderCode) {
            this.custOrderCode = custOrderCode;
        }

        public String getLotNum() {
            return lotNum;
        }

        public void setLotNum(String lotNum) {
            this.lotNum = lotNum;
        }

        public void setLotAtt04Str(String lotAtt04Str) {
            this.lotAtt04Str = lotAtt04Str;
        }

        public String getLotAtt04Str() {
            return "(" + lotAtt04Str + ")";
        }

        public String getLotAtt04() {
            return lotAtt04 ;
        }

        public void setLotAtt04(String lotAtt04) {
            this.lotAtt04 = lotAtt04;
        }

        public void setRequstQty(BigDecimal requstQty) {
            this.requstQty = requstQty;
        }

        public int getRequstQty() {
            if (requstQty == null) {
                return 0;
            }
            return requstQty.intValue();
        }

        public BigDecimal getRequestQtyBig() {
            if (requstQty == null) {
                return new BigDecimal(0);
            }
            return requstQty;
        }

        public String getStatusStr() {
            return statusStr;
        }

        public void setStatusStr(String statusStr) {
            this.statusStr = statusStr;
        }

        public String getTag() {
            return fromZone + fromLoc + lotAtt05 + custItemCode + lotAtt04;
        }

        public List<String> getIds() {
            if (ids == null) {
                ids = new ArrayList<>();
            }
            return ids;
        }

        public void addId(String id) {
            getIds().add(id);
        }

        public void removeAllIds() {
            ids.clear();
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        @NonNull
        @Override
        public DetailResponses clone() throws CloneNotSupportedException {
            return (DetailResponses) super.clone();
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getWaveNo() {
            return waveNo;
        }

        public void setWaveNo(String waveNo) {
            this.waveNo = waveNo;
        }

        public String getLotAtt05() {
            return lotAtt05;
        }

        public void setLotAtt05(String lotAtt05) {
            this.lotAtt05 = lotAtt05;
        }

        public String getUpdateUserCode() {
            return updateUserCode;
        }

        public void setUpdateUserCode(String updateUserCode) {
            this.updateUserCode = updateUserCode;
        }

        public String getConfirmBy() {
            return confirmBy;
        }

        public void setConfirmBy(String confirmBy) {
            this.confirmBy = confirmBy;
        }
    }
}
