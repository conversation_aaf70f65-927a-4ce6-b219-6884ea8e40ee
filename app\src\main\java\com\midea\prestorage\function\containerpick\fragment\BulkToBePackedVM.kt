package com.midea.prestorage.function.containerpick.fragment

import CheckUtil
import android.app.Application
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.chad.library.adapter.base.entity.node.BaseNode
import com.midea.prestorage.base.ErrorToaster
import com.midea.prestorage.beans.net.*
import com.midea.prestorage.event.LiveEvent
import com.midea.prestorage.function.containerpick.provider.BulkPickToBeDetailWrap
import com.midea.prestorage.function.containerpick.provider.BulkPickToBeWrap
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*

class BulkToBePackedVM(application: Application) :
    BulkPackVM(application),
    ErrorToaster {

    val isNoData = MutableLiveData(false)
    val showData = MutableLiveData<MutableList<ContainerPickSecondList>>()
    val taskListMutableLiveData = MutableLiveData<MutableList<BaseNode>>()
    val refreshEvent = LiveEvent<Unit>()
    var taskCode = ObservableField<String>()
    var containerCode = ObservableField<String>()
    val packHandleEvent = LiveEvent<Unit>()
    val packAndPrintEvent = LiveEvent<Unit>()
    var pickingContainerList: MutableList<PickingContainer>? = null

    override fun refresh() {
        //initList()
    }

    fun initList() {
        launch(true, error = {}, finish = {}) {
            val resp = withContext(Dispatchers.IO) {
                if (containerCode.get().isNullOrEmpty()) {
                    RetrofitHelper.getOutStorageAPI().queryComWaitList(
                        ReqQueryEaPick(
                            taskCode = taskCode.get().toString(),
                            pickingContainerList = pickingContainerList
                        )
                    )
                }else {
                    RetrofitHelper.getOutStorageAPI().queryWaitList(
                        ReqQueryEaPick(
                            taskCode = taskCode.get().toString(),
                            containerCode = containerCode.get().toString()
                        )
                    )
                }
            }
            if (resp.code == 0L) {
                isNoData.value = resp.data?.isNotEmpty() != true
                resp.data?.forEachIndexed { index, it ->
                    it.index = index + 1
                    it.details?.forEach { it1->
                        it1.packQty = it1.waitPackQty
                    }
                }
                val nodeList = mapToNodeList(resp.data).toMutableList()
                nodeList.forEach {  }
                taskListMutableLiveData.value = nodeList
            } else {
                resp.msg?.let { errorToaster.showError(it) }
            }
        }
    }

    fun packHandle(list: List<BulkPackingDetail>?, autoPrint: Boolean) {
        launch(true, error = {}, finish = {}) {
            val resp = withContext(Dispatchers.IO) {
                if (containerCode.get().isNullOrEmpty()) {
                    RetrofitHelper.getOutStorageAPI().comPackHandle(
                        ReqPackHandle(
                            pickContainerId = null,
                            operateUserCode = Constants.userInfo!!.name,
                            operateUserName = Constants.userInfo!!.userName,
                            operateTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date()),
                            details = list ?: mutableListOf()
                        )
                    )
                }else {
                    RetrofitHelper.getOutStorageAPI().packHandle(
                        ReqPackHandle(
                            pickContainerId = list?.getOrNull(0)?.pickContainerId ?: "",
                            operateUserCode = Constants.userInfo!!.name,
                            operateUserName = Constants.userInfo!!.userName,
                            operateTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date()),
                            details = list ?: mutableListOf()
                        )
                    )
                }
            }
            if (resp.code == 0L) {
                initList() //执行成功刷新当前待打包页面
                if (autoPrint) {
                    resp.data?.let { Printer.printPackedInfo(it) }
                }
            } else {
                resp.msg?.let { errorToaster.showError(it) }
            }
        }
    }

    override fun init() {
    }

    override fun showError(msg: String) {
        showNotification(msg, false)
    }

    fun confirmPacking() {
        if (CheckUtil.isFastDoubleClick()) {
            packHandleEvent.value = Unit
        }
    }

    fun packAndPrint() {
        if (CheckUtil.isFastDoubleClick()) {
            packAndPrintEvent.value = Unit
        }
    }

    private fun mapToNodeList(list: List<RespBulkPacking>?): MutableList<BaseNode> {
        return list?.map {
            BulkPickToBeWrap(it,
                it.details?.map { detail -> BulkPickToBeDetailWrap(it, detail) }
                    ?.toMutableList()
            )
        }?.toMutableList() ?: mutableListOf()
    }

    fun onParentCheckedChang(item: BulkPickToBeWrap) {
        val flatMap = taskListMutableLiveData.value?.flatMap {
            (it as BulkPickToBeWrap).childNodes ?: emptyList()
        } ?: emptyList()
        flatMap.filter { (it as? BulkPickToBeDetailWrap)?.parent != item.respBulkPacking }.forEach { (it as? BulkPickToBeDetailWrap)?.selected = false }
        flatMap.filter { (it as? BulkPickToBeDetailWrap)?.parent == item.respBulkPacking }.forEach { (it as? BulkPickToBeDetailWrap)?.selected = item.selected }
        taskListMutableLiveData.value?.forEach {
            if (it == item) {
                (it as? BulkPickToBeWrap)?.selected = item.selected
            }else {
                (it as? BulkPickToBeWrap)?.selected = false
            }
        }
        refreshEvent.value = Unit
    }

    fun onItemCheckedChang(item: BulkPickToBeDetailWrap) {
        val flatMap = taskListMutableLiveData.value?.flatMap {
            (it as BulkPickToBeWrap).childNodes ?: emptyList()
        } ?: emptyList()
        flatMap.filter { (it as? BulkPickToBeDetailWrap)?.parent != item.parent }.forEach { (it as? BulkPickToBeDetailWrap)?.selected = false }
        val find = flatMap.find { it == item }
        (find as? BulkPickToBeDetailWrap)?.selected = item.selected
        if (flatMap.filter { (it as? BulkPickToBeDetailWrap)?.parent == item.parent }.none { (it as? BulkPickToBeDetailWrap)?.selected == false }) {
            taskListMutableLiveData.value?.forEach {
                if ((it as? BulkPickToBeWrap)?.respBulkPacking == item.parent) {
                    it.selected = true
                }else {
                    (it as? BulkPickToBeWrap)?.selected = false
                }
            }
        }else {
            taskListMutableLiveData.value?.forEach {
                if ((it as? BulkPickToBeWrap)?.respBulkPacking == item.parent) {
                    it.selected = false
                }else {
                    (it as? BulkPickToBeWrap)?.selected = false
                }
            }
        }
        refreshEvent.value = Unit
    }

    fun detailFromSelected(): List<BulkPackingDetail> =
        taskListMutableLiveData.value?.flatMap {
            (it as BulkPickToBeWrap).childNodes ?: emptyList()
        }?.filter {
            (it as BulkPickToBeDetailWrap).selected
        }?.map {
            (it as BulkPickToBeDetailWrap).task
        } ?: emptyList()

}