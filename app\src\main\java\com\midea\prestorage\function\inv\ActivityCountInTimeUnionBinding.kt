package com.midea.prestorage.function.inv

import android.widget.*
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.databinding.ActivityCountInTimeBinding
import com.midea.prestoragesaas.databinding.ActivityCountInTimeCareBinding

sealed class ActivityCountInTimeUnionBinding{
    abstract var vm: CountInTimeVM?
    abstract val llTitleBar: RelativeLayout
    abstract val etLocationCode: EditText
    abstract val spinnerLot4: MaterialSpinner
    abstract val etCustItemCode: EditText
    abstract val recyclerView: RecyclerView
    abstract val layoutLocInvStatus: LinearLayout
    abstract val cbStatus: CheckBox
    abstract val tvNotification: TextView

    class V2(val binding: ActivityCountInTimeCareBinding) : ActivityCountInTimeUnionBinding() {
        override var vm: CountInTimeVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etLocationCode = binding.etLocationCode
        override val spinnerLot4 = binding.spinnerLot4
        override val etCustItemCode = binding.etCustItemCode
        override val recyclerView = binding.recyclerView
        override val layoutLocInvStatus = binding.layoutLocInvStatus
        override val cbStatus = binding.cbStatus
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityCountInTimeBinding) : ActivityCountInTimeUnionBinding() {
        override var vm: CountInTimeVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etLocationCode = binding.etLocationCode
        override val spinnerLot4 = binding.spinnerLot4
        override val etCustItemCode = binding.etCustItemCode
        override val recyclerView = binding.recyclerView
        override val layoutLocInvStatus = binding.layoutLocInvStatus
        override val cbStatus = binding.cbStatus
        override val tvNotification = binding.tvNotification
    }
}
