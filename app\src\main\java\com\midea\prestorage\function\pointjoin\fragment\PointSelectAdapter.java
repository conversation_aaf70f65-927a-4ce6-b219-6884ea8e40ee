package com.midea.prestorage.function.pointjoin.fragment;

import android.view.View;

import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.midea.prestoragesaas.R;
import com.midea.prestorage.base.adapter.BaseSelectAdapter;
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter;
import com.midea.prestorage.beans.base.BaseItemForPopup;

import java.util.List;

public class PointSelectAdapter<T extends BaseItemForPopup> extends BaseSelectAdapter<T> {

    private boolean showMode;
    private View.OnClickListener itemClick;

    public PointSelectAdapter(int layoutResId) {
        super(layoutResId);
    }

    @Override
    protected void convert(BaseViewHolder helper, BaseItemForPopup item) {
        super.convert(helper, (T) item);

        View selectImg = helper.itemView.findViewById(R.id.img_select);
        if (showMode) {
            selectImg.setVisibility(View.VISIBLE);
        } else {
            selectImg.setVisibility(View.GONE);
            if (itemClick != null) {
                helper.itemView.setTag(item);
                helper.itemView.setOnClickListener(itemClick);
            }
        }
    }

    public boolean isShowMode() {
        return showMode;
    }

    public void setShowMode(boolean showMode) {
        if (showMode) {
            List<T> data = getData();
            for (int i = 0; i < data.size(); i++) {
                data.get(i).setSelected(false);
            }
        }
        this.showMode = showMode;
    }

    public void setItemClick(View.OnClickListener itemClick) {
        this.itemClick = itemClick;
    }
}