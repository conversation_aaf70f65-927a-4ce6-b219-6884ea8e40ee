package com.midea.prestorage.function.inv

import android.widget.EditText
import android.widget.GridLayout
import android.widget.RelativeLayout
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestoragesaas.databinding.ActivitySortGoodsBinding
import com.midea.prestoragesaas.databinding.ActivitySortGoodsCareBinding

sealed class ActivitySortGoodsUnionBinding {
    abstract var vm: SortGoodsVM?
    abstract val llTitleBar: RelativeLayout
    abstract val etSearchOrderNo: EditText
    abstract val recycle: RecyclerView
    abstract val gridLayout: GridLayout
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: ActivitySortGoodsCareBinding) : ActivitySortGoodsUnionBinding() {
        override var vm: SortGoodsVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etSearchOrderNo = binding.etSearchOrderNo
        override val recycle = binding.recycle
        override val gridLayout = binding.gridLayout
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: ActivitySortGoodsBinding) : ActivitySortGoodsUnionBinding() {
        override var vm: SortGoodsVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etSearchOrderNo = binding.etSearchOrderNo
        override val recycle = binding.recycle
        override val gridLayout = binding.gridLayout
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
