package com.midea.prestorage.function.login.fragment

import android.content.Intent
import android.text.TextUtils
import androidx.databinding.ObservableField
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.App
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.setting.ImplWarehouse
import com.midea.prestorage.beans.setting.LoginInfo
import com.midea.prestorage.function.login.LoginActivity
import com.midea.prestorage.function.mainyg.MainYgActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.CountDownTimeUtil
import com.midea.prestorage.utils.DbUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import java.util.*


class SmsLoginVM(val fragment: SmsLoginFragment) {
    val username = ObservableField<String>("")
    val password = ObservableField<String>("")
    val db = DbUtils.db

    init {
        initData()
    }

    private fun initData() {
        Observable.create<LoginInfo> {
            Constants.userInfo =
                db.selector(LoginInfo::class.java).orderBy("isFirst", true).findFirst()
            if (Constants.userInfo == null) {
                Constants.userInfo = LoginInfo()
            }
            Constants.whInfo =
                db.selector(ImplWarehouse::class.java).findFirst()

            it.onNext(Constants.userInfo!!)
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<LoginInfo> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: LoginInfo) {
                    if (!TextUtils.isEmpty(t.account)) {
                        username.set(t.account)
                    }
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    val userNameEnter = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                fragment.pwdFocus()
            }
        }
    }

    fun onGetCodeClick() {
        if(CheckUtil.isFastDoubleClick()) {
            val userName = username.get().toString().trim().toLowerCase(Locale.ROOT)
            if (userName.isNullOrEmpty()) {
                ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "手机号不能为空!")
                return
            }

            if (!userName.startsWith("1") || userName.length != 11) {
                ToastUtils.getInstance()
                    .showErrorToastWithSound(fragment.activity, "手机号输入不合法")
                return
            }

            sendVerificationCode(userName) //发送验证码

        }
    }

    //发送验证码
    fun sendVerificationCode(mobile: String) {
        //弹出等待框
        (fragment.activity as BaseActivity).waitingDialogHelp.showDialog()
        RetrofitHelper.getAppAPI()
            .sendVerificationCode(mobile)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: Any?) {
                    (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()
                    fragment.mCountDownTimeUtil = CountDownTimeUtil(fragment.binding.tvGetVerificationCode)
                    fragment.mCountDownTimeUtil!!.runTimer()
                    ToastUtils.getInstance().showSuccessToastWithSound(fragment.activity, "验证码已发送")
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(fragment.activity, apiErrorModel.message)
                    (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun cleanUserName() {
        username.set("")
    }

    fun cleanPassWord() {
        password.set("")
    }
}