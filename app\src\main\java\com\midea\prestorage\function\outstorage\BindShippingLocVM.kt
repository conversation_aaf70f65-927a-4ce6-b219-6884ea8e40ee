package com.midea.prestorage.function.outstorage

import android.app.Application
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.RespPickContainerInfo
import com.midea.prestorage.event.LiveEvent
import com.midea.prestorage.function.inv.response.ItemRfVO
import com.midea.prestorage.function.inv.response.RespMaterial
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class BindShippingLocVM(application: Application) : BaseViewModel(application) {
    var mScanCode = MutableLiveData(false)
    var mResetSearch = MutableLiveData(false)
    var searchType = ObservableField("货品编码")
    var curCode = ObservableField("")
    var showDatas = MutableLiveData<MutableList<RespPickContainerInfo>>()
    val isNoData = MutableLiveData(false)
    val showBottom = MutableLiveData(false)
    var curSearchCode = ""
    var searchMode: Int? = 0
    var showPopDatas = MutableLiveData<MutableList<ItemRfVO>>()
    var isFirst = true
    var curSearchMode: Int? = 0
    var oldSearchCode = ""
    val isPrintOk = ObservableField(false)
    var mBluetoothOpen = MutableLiveData(false)
    var currentItemFlag = ObservableField<Int>(0)
    val bindLocLiveEvent = LiveEvent<Unit>()

    override fun init() {

    }

    fun bluetoothOpen() {
        if (CheckUtil.isFastDoubleClick()) {
            mBluetoothOpen.value = true
        }
    }

    fun onEnterCode() {
        if (CheckUtil.isFastDoubleClick()) {
            if (curCode.get().toString().trim().isNullOrBlank()) {
                showNotification("容器号、拣货箱号不能为空", false)
                return
            }
            // 清除前后空格
            curSearchCode = curCode.get().toString().trim()
            startSearch()
        }
    }

    //点击了查询按钮
    fun startSearch() {

        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getInventoryAPI()
                    .queryPickContainerInfo(Constants.whInfo?.whCode.toString(), curSearchCode)
            }

            if (result.code == 0L) {
                if (result.data != null) {
                    showDatas.value = result.data
                }
            } else {
                result.msg?.let { showNotification(it, false) }
                val emptyList = mutableListOf<RespPickContainerInfo>()
                showDatas.value = emptyList
            }
        }
    }

    fun showInfoNotification(msg: String, isSuccess: Boolean) {
        showNotification(msg, isSuccess)
    }

    fun clearCode() {
        curCode.set("")
    }

    fun scanCode() {
        mScanCode.value = true
    }

    fun scanResult(code: String?) {
        code?.let {
            curCode.set(code)
            onEnterCode()
        }
    }

    //重置查询条件
    fun resetSearch() {
        if (CheckUtil.isFastDoubleClick()) {
            curCode.set("")
            curSearchCode = ""
            isNoData.value = false
            mResetSearch.value = true
        }
    }

    fun toSearch() {
        onEnterCode()
    }

    fun bindShippingLoc() {
        if (CheckUtil.isFastDoubleClick()) {
            bindLocLiveEvent.value = Unit
        }
    }
}