package com.midea.prestorage.function.addgoods

import android.widget.EditText
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestoragesaas.databinding.ActivityReplenishmentBinding
import com.midea.prestoragesaas.databinding.ActivityReplenishmentCareBinding

sealed class ActivityReplenishmentUnionBinding{
    abstract var vm: ReplenishmentActivityVM?
    abstract val llTitleBar: RelativeLayout
    abstract val edOrderNo: EditText
    abstract val srl: SwipeRefreshLayout
    abstract val recycle: RecyclerView
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: ActivityReplenishmentCareBinding) : ActivityReplenishmentUnionBinding() {
        override var vm: ReplenishmentActivityVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val edOrderNo = binding.edOrderNo
        override val srl = binding.srl
        override val recycle = binding.recycle
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: ActivityReplenishmentBinding) : ActivityReplenishmentUnionBinding() {
        override var vm: ReplenishmentActivityVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val edOrderNo = binding.edOrderNo
        override val srl = binding.srl
        override val recycle = binding.recycle
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
