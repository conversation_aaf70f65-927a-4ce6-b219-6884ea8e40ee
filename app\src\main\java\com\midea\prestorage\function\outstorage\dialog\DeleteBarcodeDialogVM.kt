package com.midea.prestorage.function.outstorage.dialog

import androidx.databinding.ObservableField

class DeleteBarcodeDialogVM(val dialog: DeleteBarcodeDialog) {
    val title = ObservableField<String>("提示")
    val etInfo = ObservableField("")

    fun onEnterAnyCode() {
        if (CheckUtil.isFastDoubleClick()) {
            if (etInfo.get().isNullOrBlank()) {
                return
            }
            etInfo.get()?.let { dialog.backDeleteBarcode(it) }
        }
    }

    fun close() {
        dialog.dismiss()
    }

    fun confirm() {
        if (CheckUtil.isFastDoubleClick()) {
            if (etInfo.get().isNullOrBlank()) {
                return
            }
            etInfo.get()?.let { dialog.backDeleteBarcode(it) }
        }
    }

}