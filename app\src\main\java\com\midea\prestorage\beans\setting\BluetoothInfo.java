package com.midea.prestorage.beans.setting;

import org.xutils.db.annotation.Column;
import org.xutils.db.annotation.Table;

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/9/23$
 */
@Table(name = "BluetoothInfo")
public class BluetoothInfo {

    @Column(name = "id", isId = true, autoGen = true, property = "NOT NULL")
    private int id;

    //打印机链接地址
    @Column(name = "bluetoothAddress")
    private String bluetoothAddress;

    //打印机品牌
    @Column(name = "printBrand")
    private int printBrand;

    //打印机连接方式
    @Column(name = "printMode")
    private int printMode; //0为自动连接，1为手动连接

    public BluetoothInfo() {
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getBluetoothAddress() {
        return bluetoothAddress;
    }

    public void setBluetoothAddress(String bluetoothAddress) {
        this.bluetoothAddress = bluetoothAddress;
    }

    public int getPrintBrand() {
        return printBrand;
    }

    public void setPrintBrand(int printBrand) {
        this.printBrand = printBrand;
    }

    public int getPrintMode() {
        return printMode;
    }

    public void setPrintMode(int printMode) {
        this.printMode = printMode;
    }
}
