package com.midea.prestorage.function.addgoods

import android.content.Intent
import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.net.ContainerPickSecondList
import com.midea.prestorage.beans.net.InvSetDetailList
import com.midea.prestorage.beans.net.ReplenishmentBean
import com.midea.prestorage.beans.net.ReplenishmentBean.ReplenishmentListBean
import com.midea.prestorage.function.addgoods.dialog.LocZoneDialog
import com.midea.prestorage.function.instorage.dialog.LocDialog
import com.midea.prestorage.function.inv.response.RespMaterial
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.send.ActivitySendSecondUnionBinding
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.MathUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.CareLoadMoreView
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityReplenishmentBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.xuexiang.xqrcode.XQRCode

class ReplenishmentActivity : BaseViewModelActivity<ReplenishmentActivityVM>() {
    private lateinit var binding: ActivityReplenishmentUnionBinding
    private lateinit var adapter: InStorageOrderAdapter

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityReplenishmentUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_replenishment_care
                )
            )
        } else {
            ActivityReplenishmentUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_replenishment
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        vm = ViewModelProvider.AndroidViewModelFactory(application)
            .create(ReplenishmentActivityVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        vm.loadMoreComplete.observe(this, {
            if (it == 1) {
                adapter.loadMoreModule.loadMoreComplete()
            } else if (it == 2) {
                adapter.loadMoreModule.loadMoreEnd()
            }
        })

        vm.showDatas.observe(this, {
            showData(it)
        })

        vm.loadMoreDatas.observe(this, {
            loadMoreData(it)
        })

        vm?.scanLiveEvent?.observe(this, Observer {
            XQRCode.startScan(this, BaseActivity.QR_CODE_BACK)
        })

        vm?.showFromZoneLiveEvent?.observe(this, Observer {
            showZoneDialog(0)
        })

        vm?.showToZoneLiveEvent?.observe(this, Observer {
            showZoneDialog(1)
        })

        initData()
        initRecycleView()
        initLoadMore()

        AppUtils.requestFocus(binding.edOrderNo)
    }

    fun initData() {
        vm.containerPickSecondList =
            intent.getSerializableExtra("ContainerPickSecondList") as ContainerPickSecondList?
        vm.toFlag = intent.getStringExtra("toFlag")
    }

    fun initRecycleView() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        adapter = InStorageOrderAdapter()
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.itemAnimator = null
        binding.recycle.adapter = adapter

        adapter.setOnItemClickListener { adapter, _, position ->
            val bean = adapter.data[position] as ReplenishmentBean.ReplenishmentListBean
            vm.onItemClick(bean)
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.initOrderList()
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            adapter.loadMoreModule.loadMoreView = CareLoadMoreView()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    fun showData(data: MutableList<ReplenishmentListBean>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()

        vm.isNoData.set(adapter.data.isNullOrEmpty())
        vm.isRefreshing.set(false)
    }

    fun loadMoreData(data: MutableList<ReplenishmentListBean>?) {
        data?.let { adapter.addData(it) }
        adapter.notifyDataSetChanged()
    }

    override fun onResume() {
        super.onResume()
        vm.onRefreshCommand.onRefresh()
    }

    private fun showZoneDialog(position: Int) {
        var zoneDialog = LocZoneDialog(this)
        when (position) {
            0 -> {
                zoneDialog.setTitle("请选择来源库区")
                zoneDialog.setType("from")
            }
            else -> {
                zoneDialog.setTitle("请选择目标库区")
                zoneDialog.setType("to")
            }
        }
        zoneDialog?.setOnItemClick(object : LocZoneDialog.LocBack {
            override fun locBack(info: BaseItemShowInfo) {
                when (position) {
                    0 -> {
                        vm.fromInfo.set(info.payload as? String ?: "")
                        vm.fromInfoStr.set(info.showInfo)
                    }
                    else -> {
                        vm.toInfo.set(info.payload as? String ?: "")
                        vm.toInfoStr.set(info.showInfo)
                    }
                }
                vm.onRefreshCommand.onRefresh()
                zoneDialog?.dismiss()
            }
        })
        zoneDialog.show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            vm.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    class InStorageOrderAdapter :
        CommonAdapter<ReplenishmentListBean>(
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_replenishment_care else R.layout.item_replenishment
        ), LoadMoreModule {

        private fun joinString(str1: String?, str2: String?): String {
            return when {
                str1.isNullOrEmpty() -> str2 ?: ""
                str2.isNullOrEmpty() -> str1
                else -> "$str1 / $str2"
            }
        }

        override fun convert(holder: BaseViewHolder?, item: ReplenishmentListBean?) {
            super.convert(holder, item)

            when (item?.unitValue) {
                "EA" -> holder?.setText(R.id.tv_barCode, item?.barCode ?: "")
                "CS" -> {
                    if (!item?.whCsBarcode69.isNullOrEmpty()) {
                        holder?.setText(R.id.tv_barCode, item?.whCsBarcode69 ?: "")
                    } else {
                        holder?.setText(R.id.tv_barCode, item?.barCode ?: "")
                    }
                }
                "PL" -> {
                    if (!item?.whMaxBarcode69.isNullOrEmpty()) {
                        holder?.setText(R.id.tv_barCode, item?.whMaxBarcode69 ?: "")
                    } else {
                        holder?.setText(R.id.tv_barCode, item?.barCode ?: "")
                    }
                }
                else -> holder?.setText(R.id.tv_barCode, item?.barCode ?: "")
            }

            holder?.setVisible(R.id.img_status, item?.taskLevel != 0)

            if (item?.packageRelationList.isNullOrEmpty()) {
                holder?.setText(R.id.tv_unit_info, item?.totalQty)
            } else {
                holder?.setText(
                    R.id.tv_unit_info,
                    MathUtils.calculateUnits(
                        AppUtils.getBigDecimalValue(item?.totalQty),
                        item?.packageRelationList!!
                    )
                )
            }
        }
    }

}