package com.midea.prestorage.function.login.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import com.midea.prestorage.function.login.ActivityLoginUnionBinding
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.FragmentPasswordLoginBinding
import com.midea.prestoragesaas.databinding.FragmentPasswordLoginCareBinding
import com.trello.rxlifecycle2.components.support.RxFragment

class PasswordLoginFragment : RxFragment() {
    companion object {
        const val TAG = "PasswordLoginFragment"

        fun newInstance(position: Int): PasswordLoginFragment {
            val bundle = Bundle()
            bundle.putInt("position", position)
            val fragment = PasswordLoginFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    lateinit var binding: FragmentPasswordLoginUnionBinding
    private var vm: PasswordLoginVM? = null

    fun getVm(): PasswordLoginVM? {
        return vm
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            FragmentPasswordLoginUnionBinding.V2(inflater.let {
                FragmentPasswordLoginCareBinding.inflate(
                    it,
                    container,
                    false
                )
            })
        } else {
            FragmentPasswordLoginUnionBinding.V1(inflater.let {
                FragmentPasswordLoginBinding.inflate(
                    it,
                    container,
                    false
                )
            })
        }

        initView()

        return binding.root
    }

    //初始化
    private fun initView() {
        if (vm == null) {
            vm = PasswordLoginVM(this)
            binding.vm = vm
        }
    }

    /**
     * 密码获取焦点
     */
    fun pwdFocus() {
        binding.etPassword.requestFocus()
    }
}