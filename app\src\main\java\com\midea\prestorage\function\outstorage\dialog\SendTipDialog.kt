package com.midea.prestorage.function.outstorage.dialog

import android.app.AlertDialog
import android.text.TextUtils
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogSendTipBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

class SendTipDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {
    private var binding: DialogSendTipBinding
    private var confirmBack: ConfirmBack? = null

    init {
        val contentView = LayoutInflater.from(mContext).inflate(R.layout.dialog_send_tip, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = SendTipDialogVM(this)

        setCanceledOnTouchOutside(true)
    }

    fun setTitle(title: String) {
        if (!TextUtils.isEmpty(title)) {
            binding!!.vm!!.title.set(title)
        }
    }

    fun setContent(content: String) {
        if (!TextUtils.isEmpty(content)) {
            binding!!.vm!!.tipInfo.set(content)
        }
    }

    fun setConfirmBack(backImpl: ConfirmBack) {
        confirmBack = backImpl
    }

    fun backConfirm() {
        if (confirmBack != null) {
            confirmBack!!.confirmBack()
        }
    }

    interface ConfirmBack {
        fun confirmBack()
    }

}