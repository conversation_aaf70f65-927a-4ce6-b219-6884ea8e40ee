package com.midea.prestorage.function.addgoods

import android.text.TextUtils
import android.util.Log
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.PackageResp
import com.midea.prestorage.beans.net.ReceiptListInfo
import com.midea.prestorage.beans.net.ReplenishmentBean
import com.midea.prestorage.beans.net.RespRecommendLoc
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.*
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import okhttp3.MediaType
import okhttp3.RequestBody

/**
 * Created by LIXK5 on 2019/4/9.
 */
class PutInVM(val activity: PutInActivity) {

    val isRefreshing = ObservableBoolean(false)

    val title = ObservableField<String>()
    val goods = ObservableField<String>("")
    val location = ObservableField<String>("")
    val inputNum = ObservableField<String>("")
    var pageNo = 1 //当前第一页

    val itemCode = ObservableField<String>()
    val itemName = ObservableField<String>()
    val fromLoc = ObservableField<String>()
    val createTime = ObservableField<String>()
    val unitInfo = ObservableField<String>()
    val totalQty = ObservableField<String>()
    val fromZoneGroup = ObservableField<String>()
    val orderByValue = ObservableField<String>()
    val unitQty = ObservableField<String>()
    val batchResp = ObservableField<String>()
    val taskCode = ObservableField<String>()

    val isShowSearchData = ObservableField(false)
    val isLocalEnabled = ObservableField(true)
    val isShowLocal = ObservableField(true)

    var currentSearchBean: ReplenishmentBean.ReplenishmentListBean? = null

    private var packageResp = activity.intent.getSerializableExtra("packageResp") as PackageResp?
    var batchDistrictResp = activity.intent.getSerializableExtra("batchResp") as PackageResp?

    var defaultStorage: Int? = null
    var scanMode = 0 //0为勾选模式，1为单个模式

    val db = DbUtils.db

    var firstSelectedPosition = -1
    var isEnter = true

    val isPoint = MutableLiveData(9999)

    init {
//        queryOnShelfCompanyCodes()
        refreshData()
    }

    fun refreshData() {
        pageNo = 1
        isRefreshing.set(true)
        initData(true)
    }

    val enterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                if (TextUtils.isEmpty(goods.get())) {
                    return
                }
                if (TextUtils.isEmpty(location.get()) && defaultStorage == 1) {
                    activity.localRequest()
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "请先扫描货位编码!")
                    return
                }
                searchData()
            }
        }
    }

    val localPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick(isEnter)) {
                checkPickLocation(isEnter)
                isEnter = true
            }
        }
    }

    val inputTextChange = object : ViewBindingAdapter.TextChangedListener {

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            if (!s.isNullOrEmpty() && currentSearchBean != null) {
                val inputNumStr = AppUtils.getBigDecimalValue(s.toString())
                if (inputNumStr > AppUtils.getBigDecimalValue(currentSearchBean!!.totalQty)) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "数量超出!")
                    inputNum.set("")
                }
            }
        }
    }

    fun setMode() {
        when (defaultStorage) {
            0 -> {
                location.set("")
                activity.localUnable()
                activity.goodsRequest()
                isShowLocal.set(false)
            }
            1 -> {
                val local = Constants.userInfo?.rememberLoc
                if (TextUtils.isEmpty(local)) {
                    activity.localEnable()
                    activity.localRequest()
                    location.set("")
                    isLocalEnabled.set(true)
                } else {
                    location.set(local)
                    isLocalEnabled.set(false)
                    activity.goodsRequest()
                }
                isShowLocal.set(true)
            }
            else -> {
                activity.localEnable()
                activity.localRequest()
                location.set("")
                isShowLocal.set(true)
            }
        }
    }

    fun setting() {
        val intent = SettingInActivity.newIntent(activity, "上架设置")
        activity.startActivity(intent)
    }

    fun checkChange(isCheck: Boolean) {
        activity.adapter.data.forEach {
            it.isSelected = isCheck
        }
        activity.adapter.setIsShowSelected(isCheck)
        activity.adapter.notifyDataSetChanged()
    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        refreshData()
    }

    //获取推荐库位
    fun getRecommendLoc(bean: ReplenishmentBean.ReplenishmentListBean) {
        activity.waitingDialogHelp.showDialog()
        val map = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "ownerCode" to bean.ownerCode,
            "scenario" to "replenishment",
            "orderType" to "",
            "qty" to bean.totalQty,
            "packageUnit" to bean.originalUnit,
            "lotAtt01" to bean.lotAtt01,
            "lotAtt02" to bean.lotAtt02,
            "lotAtt04" to bean.lotAtt04,
            "lotAtt05" to bean.lotAtt05,
            "lotAtt06" to bean.lotAtt06,
            "containerCode" to "",
            "itemCode" to bean.itemCode
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        RetrofitHelper.getAddGoodsService()
            .getRecommendLoc(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<RespRecommendLoc>(activity) {
                override fun success(data: RespRecommendLoc?) {
                    data?.locCode?.let {
                        if (!it.isNullOrEmpty()) {
                            change()
                            location.set(it)
                            localPress.onEnterKey()
                        }
                    } ?: run {
                        activity.waitingDialogHelp.hidenDialog()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun putOut() {
        val intent = PutOutActivity.newIntent(
            activity,
            "补货下架",
            "",
            ""
        )
        activity.startActivity(intent)
        activity.finish()
    }

    fun confirmUp() {
        if (isLocalEnabled.get()!! && scanMode == 1 && defaultStorage == 1) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "请扫描货位!")
            return
        }

        if ((TextUtils.isEmpty(inputNum.get()) ||  AppUtils.isZero(AppUtils.getBigDecimalValue(inputNum.get()))) && scanMode == 1) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "请输入数量!")
            inputNum.set("")
            return
        }

        if (scanMode == 0) {
            val results = activity.adapter.data.filter { it.isSelected }
            if (results.isEmpty()) {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "请勾选上架任务!")
                return
            }
            putOnShelfBatch(results)
        } else {
            if (isLocalEnabled.get()!! && defaultStorage == 1) {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "请扫描货位码!")
                return
            }
            putOnShelf()
        }
    }

    private fun putOnShelfBatch(results: List<ReplenishmentBean.ReplenishmentListBean>) {
        val map = mutableMapOf<String, Any?>(
            "whCode" to activity.getWhCode()
        )

        val list = mutableListOf<MutableMap<String, String?>>()
        results.forEach {
            val localInfo = if (defaultStorage == 0) {
                it.toLoc
            } else {
                location.get()
            }
            val mapTo = mutableMapOf<String, String?>()
            mapTo["taskDetailId"] = it.id
            mapTo["qty"] = it.totalQty
            mapTo["locationCode"] = localInfo
            mapTo["subTaskType"] = "PUTAWAY"
            list.add(mapTo)
        }
        map["details"] = list

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        val response = if (Constants.whInfo?.bearingSystem == "3") {
            RetrofitHelper.getAddGoodsService()
                .putOnShelfBwms(requestBody)
        } else {
            RetrofitHelper.getAddGoodsService()
                .putOnShelf(requestBody)
        }
        response
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    isShowSearchData.set(false)
                    activity.adapter.setIsSearchResult(false)
                    inputNum.set("")
                    goods.set("")
                    setMode()

                    scanMode = 0
                    activity.showAllSelect()
                    activity.setUnSelectAll()

                    ToastUtils.getInstance()
                        .showSuccessToastWithSound(activity, "补货上架成功!")
                    TTSUtils.startAuto("补货上架成功!")
                    refreshData()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun putOnShelf() {
        activity.waitingDialogHelp.showDialog()
        val localInfo = if (defaultStorage == 0) {
            currentSearchBean?.toLoc
        } else {
            location.get()
        }

        val map = mutableMapOf<String, Any?>(
            "whCode" to activity.getWhCode()
        )

        val list = mutableListOf<MutableMap<String, String?>>()
        val mapTo = mutableMapOf<String, String?>()
        mapTo["taskDetailId"] = currentSearchBean!!.id
        mapTo["qty"] = inputNum.get()
        mapTo["locationCode"] = localInfo
        mapTo["subTaskType"] = "PUTAWAY"
        list.add(mapTo)

        map["details"] = list

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        val response = if (Constants.whInfo?.bearingSystem == "3") {
            RetrofitHelper.getAddGoodsService()
                .putOnShelfBwms(requestBody)
        } else {
            RetrofitHelper.getAddGoodsService()
                .putOnShelf(requestBody)
        }
        response
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    isShowSearchData.set(false)
                    activity.adapter.setIsSearchResult(false)
                    inputNum.set("")
                    goods.set("")
                    setMode()

                    scanMode = 0
                    activity.showAllSelect()
                    activity.setUnSelectAll()

                    ToastUtils.getInstance().showSuccessToastWithSound(activity, "补货上架成功!")
                    TTSUtils.startAuto("补货上架成功!")
                    refreshData()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun change() {
        if (defaultStorage == 0) {
            return
        }
        location.set("")
        isLocalEnabled.set(true)
        activity.localRequest()
    }

    fun initData(isRefresh: Boolean) {
        val map = mutableMapOf(
            "whCode" to Constants.whInfo?.whCode,
            "day" to "30",
            "queryType" to 2,
            "pageNo" to pageNo,
            "pageSize" to 10,
            "subTaskType" to "PUTAWAY",
            "zoneCode" to "",
            "orderBy" to "task_level,id",
            "orderByType" to "asc"
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        val response = if (Constants.whInfo?.bearingSystem == "3") {
            RetrofitHelper.getAddGoodsService()
                .rfQueryNew(requestBody)
        } else {
            RetrofitHelper.getAddGoodsService()
                .rfQuery(requestBody)
        }
        response
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ReplenishmentBean>(activity) {
                override fun success(data: ReplenishmentBean?) {
                    if (isRefresh) {
                        activity.stopRefresh()
                    }
                    if (data != null && data.list != null && data.list.isNotEmpty()) {
                        data.list.forEach {
                            val results =
                                packageResp?.list?.filter { item -> item.code == it?.unit }
                            if (results != null && results.isNotEmpty()) {
                                it.unit = results[0].name
                            }
                            val resultsBatch =
                                batchDistrictResp?.list?.filter { item -> item.code == it?.orderByAttribute }
                            if (resultsBatch != null && resultsBatch.isNotEmpty()) {
                                it?.orderByAttribute = resultsBatch[0].name
                            }
                        }
                        if (isRefresh) {
                            activity.adapter.setNewInstance(data.list)
                        } else {
                            activity.adapter.addData(data.list)
                        }
                        activity.adapter.notifyDataSetChanged()
                        activity.showDataInfo()
                        isPoint.value = data.list.getOrNull(0)?.isDecimal
                    } else {
                        if (isRefresh) {
                            ToastUtils.getInstance().showErrorToastWithSound(activity, "您当前没有上架任务!")
                            activity.showNoDataInfo()
                        }
                    }

                    if (pageNo >= data?.totalPage!!) {
                        //加载到了最后一页
                        activity.adapter.loadMoreModule.loadMoreEnd()
                    } else {
                        pageNo += 1
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isRefreshing.set(false)
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun searchData() {
        activity.waitingDialogHelp.showDialog()
        val map = mutableMapOf(
            "whCode" to Constants.whInfo?.whCode,
            "day" to "30",
            "zoneCode" to "",
            "queryType" to 2,
            "pageNo" to 1,
            "pageSize" to 10,
            "subTaskType" to "PUTAWAY",
            "queryCode" to goods.get(),
            "orderBy" to "task_level,id",
            "orderByType" to "asc"
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        val response = if (Constants.whInfo?.bearingSystem == "3") {
            RetrofitHelper.getAddGoodsService()
                .rfQueryNew(requestBody)
        } else {
            RetrofitHelper.getAddGoodsService()
                .rfQuery(requestBody)
        }
        response
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ReplenishmentBean>(activity) {
                override fun success(data: ReplenishmentBean?) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (data != null && data.list != null && data.list.isNotEmpty()) {
                        isShowSearchData.set(true)
                        activity.adapter.setIsSearchResult(true)
                        itemCode.set(data.list[0]?.custItemCode)
                        itemName.set(data.list[0]?.itemName)
                        fromLoc.set(data.list[0]?.toLoc)
                        createTime.set(data.list[0]?.createTime)
                        val results =
                            packageResp?.list?.filter { item -> item.code == data.list[0]?.unit }
                        if (results != null && results.isNotEmpty()) {
                            data.list[0]?.unit = results[0].name
                        }
                        unitInfo.set(data.list[0]?.basicUnitQty + "/箱")
                        totalQty.set(data.list[0]?.totalQty)
                        fromZoneGroup.set(data.list[0]?.fromZoneGroup)
                        orderByValue.set(data.list[0]?.orderByValue)
                        unitQty.set(data.list[0]?.unitQty + data.list[0]?.unit)
                        val resultsBatch =
                            batchDistrictResp?.list?.filter { item -> item.code == data.list[0]?.orderByAttribute }
                        if (resultsBatch != null && resultsBatch.isNotEmpty()) {
                            data.list[0]?.orderByAttribute = resultsBatch[0].name
                        }
                        batchResp.set(data.list[0]?.orderByAttribute)
                        taskCode.set(data.list[0]?.barCode)

                        currentSearchBean = data.list[0]
                        scanMode = 1
                        activity.dismissAllSelect()
                        activity.adapter.setIsShowSelected(false)
                        activity.adapter.notifyDataSetChanged()
                        activity.qtyRequest()

                        MySoundUtils.getInstance(activity).dingSound()

                        //获取推荐库位
                        data.list.getOrNull(0)?.let {
                            if (it.isEditable != 0) {
                                isEnter = false
                                getRecommendLoc(it)
                            }
                        }
                    } else {
                        ToastUtils.getInstance().showErrorToastWithSound(activity, "没有此商品的上架任务!")
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    isRefreshing.set(false)
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun checkPickLocation(isEnter: Boolean = true) {
        activity.waitingDialogHelp.showDialog()
        if (TextUtils.isEmpty(location.get())) {
            return
        }
        val map = mutableMapOf(
            "whCode" to Constants.whInfo?.whCode,
            "locCode" to location.get()!!.trim()
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        RetrofitHelper.getAddGoodsService()
            .checkPickLocation(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    MySoundUtils.getInstance(activity).dingSound()
                    isLocalEnabled.set(false)
                    if (isEnter) {
                        activity.goodsRequest()
                    }else {
                        activity.qtyRequest()
                    }
                    if (defaultStorage == 1) {
                        saveUserInfo(location.get()!!)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    location.set("")
                }
            })
    }

    fun saveUserInfo(location: String) {
        Observable.create<String> {
            try {
                Constants.userInfo?.rememberLoc = location
                db.saveOrUpdate(Constants.userInfo)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    fun cleanGoods() {
        goods.set("")
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun back() {
        activity.finish()
    }

    fun scanResult(result: String?) {
        if (!TextUtils.isEmpty(result)) {
            goods.set(result)
            enterKeyPress.onEnterKey()
        }
    }
}