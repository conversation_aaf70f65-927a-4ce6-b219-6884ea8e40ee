package com.midea.prestorage.beans.net;

import androidx.annotation.Nullable;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;
import java.math.BigDecimal;

public class ContainerReceiveDetailListResp implements Serializable {

    @ShowAnnotation
    private String ownerCode;
    @ShowAnnotation
    private String lotAtt04Str;
    @ShowAnnotation
    private String custItemCode;
    @ShowAnnotation
    private String custItemName;
    @ShowAnnotation
    private String itemName;
    @ShowAnnotation
    private String whBarcode69;
    @ShowAnnotation
    private String receiveInfo;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal planQty;

    private BigDecimal realQty;
    private BigDecimal csQty;
    private BigDecimal eaQty;
    @Nullable
    private BigDecimal otQty;
    @Nullable
    private BigDecimal ipQty;
    private String csUnit;
    private String eaUnit;
    @Nullable
    private String ipUnit;
    @Nullable
    private String otUnit;
    private String lotAtt04;
    private String whCsBarcode69;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal totalQty;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal receiptQty;

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public BigDecimal getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public BigDecimal getReceiptQty() {
        return receiptQty;
    }

    public void setReceiptQty(BigDecimal receiptQty) {
        this.receiptQty = receiptQty;
    }

    public String getWhCsBarcode69() {
        return whCsBarcode69;
    }

    public void setWhCsBarcode69(String whCsBarcode69) {
        this.whCsBarcode69 = whCsBarcode69;
    }

    @Nullable
    public BigDecimal getOtQty() {
        return otQty;
    }

    public void setOtQty(@Nullable BigDecimal otQty) {
        this.otQty = otQty;
    }

    @Nullable
    public BigDecimal getIpQty() {
        return ipQty;
    }

    public void setIpQty(@Nullable BigDecimal ipQty) {
        this.ipQty = ipQty;
    }

    @Nullable
    public String getIpUnit() {
        return ipUnit;
    }

    public void setIpUnit(@Nullable String ipUnit) {
        this.ipUnit = ipUnit;
    }

    @Nullable
    public String getOtUnit() {
        return otUnit;
    }

    public void setOtUnit(@Nullable String otUnit) {
        this.otUnit = otUnit;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getCustItemName() {
        return custItemName;
    }

    public void setCustItemName(String custItemName) {
        this.custItemName = custItemName;
    }

    public BigDecimal getPlanQty() {
        return planQty;
    }

    public void setPlanQty(BigDecimal planQty) {
        this.planQty = planQty;
    }

    public BigDecimal getRealQty() {
        return realQty;
    }

    public void setRealQty(BigDecimal realQty) {
        this.realQty = realQty;
    }

    public BigDecimal getCsQty() {
        return csQty;
    }

    public void setCsQty(BigDecimal csQty) {
        this.csQty = csQty;
    }

    public BigDecimal getEaQty() {
        return eaQty;
    }

    public void setEaQty(BigDecimal eaQty) {
        this.eaQty = eaQty;
    }

    public String getCsUnit() {
        return csUnit;
    }

    public void setCsUnit(String csUnit) {
        this.csUnit = csUnit;
    }

    public String getEaUnit() {
        return eaUnit;
    }

    public void setEaUnit(String eaUnit) {
        this.eaUnit = eaUnit;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getLotAtt04Str() {
        return lotAtt04Str;
    }

    public void setLotAtt04Str(String lotAtt04Str) {
        this.lotAtt04Str = lotAtt04Str;
    }

    public String getReceiveInfo() {
        return receiveInfo;
    }

    public void setReceiveInfo(String receiveInfo) {
        this.receiveInfo = receiveInfo;
    }

    public String getWhBarcode69() {
        return whBarcode69;
    }

    public void setWhBarcode69(String whBarcode69) {
        this.whBarcode69 = whBarcode69;
    }
}
