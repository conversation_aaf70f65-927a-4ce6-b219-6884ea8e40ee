package com.midea.prestorage.beans.net

import com.midea.prestorage.http.constants.Constants
import java.math.BigDecimal

data class ReqSortConfirm(
    val whCode: String = Constants.whInfo?.whCode ?: "",
    val containerCode: String ? = null,
    val shippingLoc: String ? = null,
    val operatorCode: String ? = null,
    val operator: String ? = null,
    val operateTime: String ? = null,
    val itemList: List<ReqSortItemList>?
)

data class ReqSortItemList(
    var itemCode: String ? = null,
    var custItemCode: String ? = null,
    var packagePara: BigDecimal? = null,
    var csSortQty: BigDecimal ? = null,
    var eaSortQty: BigDecimal ? = null,
)
