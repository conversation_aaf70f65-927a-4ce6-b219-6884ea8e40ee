package com.midea.prestorage.function.addgoods.fragment

import android.text.TextUtils
import androidx.databinding.ObservableBoolean
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.mideadspda.module.electro.fragment.AddMyTaskFragment
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.AreaList
import com.midea.prestorage.beans.net.ReplenishmentBean
import com.midea.prestorage.function.addgoods.AddGoodsActivity
import com.midea.prestorage.function.addgoods.PutInActivity
import com.midea.prestorage.function.addgoods.PutOutActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.MySoundUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody

/**
 * Created by LIXK5 on 2019/4/11.
 */
class AddMyTaskVM(val fragment: AddMyTaskFragment) {

    val isRefreshing = ObservableBoolean(false)
    var pageNo = 1 //当前第一页

    var dayType: DCBean = DCBean("全部", "")
    var areaArgs: AreaList? = null

    var isEnterGoods = false//用于控制是否输入搜索的参数

    fun putOut() {
        var intent = PutOutActivity.newIntent(
            fragment.requireActivity(),
            "补货下架",
            fragment.goodsInfo,
            areaArgs?.zoneCode
        )
        fragment.requireActivity().startActivity(intent)
    }

    fun putIn() {
        var intent = PutInActivity.newIntent(
            fragment.requireActivity(),
            "补货上架",
            (fragment.activity as AddGoodsActivity).packageResp,
            (fragment.activity as AddGoodsActivity).batchResp
        )
        fragment.requireActivity().startActivity(intent)
    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        refreshData()
    }

    fun refreshData() {
        pageNo = 1
        isRefreshing.set(true)
        initData(true)
    }

    fun unitOk() {
        (fragment.activity as AddGoodsActivity).vm.areaArgs?.let { fragment.initArea(it) }
        (fragment.activity as AddGoodsActivity).vm.areaArgsIn?.let { fragment.initPutaway(it) }
    }

    fun initData(isRefresh: Boolean) {
        val queryType = if (dayType.value == "ALL") {
            if (areaArgs?.taskType == 2) 3 else 2
        } else if (dayType.value == "PICK") {
            2
        } else {
            3
        }

        val map = mutableMapOf(
            "whCode" to Constants.whInfo?.whCode,
            "day" to "30",
            "queryType" to queryType,
            "pageNo" to pageNo,
            "pageSize" to 10,
            "queryCode" to fragment.goodsInfo,
            "subTaskType" to if (dayType.value == "ALL") "" else dayType.value,
            "orderBy" to "task_level,id",
            "orderByType" to "asc"
        )
        if (!TextUtils.isEmpty(areaArgs?.zoneCode)) {
            map["zoneCodes"] = mutableListOf(areaArgs?.zoneCode)
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        val response = if (Constants.whInfo?.bearingSystem == "3") {
            RetrofitHelper.getAddGoodsService()
                .rfQueryNew(requestBody)
        } else {
            RetrofitHelper.getAddGoodsService()
                .rfQuery(requestBody)
        }

        response
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ReplenishmentBean>(fragment.requireActivity()) {
                override fun success(data: ReplenishmentBean?) {
                    if (isRefresh) {
                        isRefreshing.set(false)
                    } else {
                        fragment.adapter.loadMoreModule.loadMoreEnd()
                    }
                    if (data != null && data.list != null && data.list.isNotEmpty()) {
                        data.list.forEach {
                            if ((fragment.activity as AddGoodsActivity).packageResp != null) {
                                val results = (fragment.activity as AddGoodsActivity)
                                    .packageResp?.list?.filter { item -> item.code == it.unit }
                                val resultBatch = (fragment.activity as AddGoodsActivity)
                                    .batchResp?.list?.filter { item -> item.code == it.orderByAttribute }
                                if (results != null && results.isNotEmpty()) {
                                    it.unit = results[0].name
                                }
                                if (resultBatch != null && resultBatch.isNotEmpty()) {
                                    it.orderByAttribute = resultBatch[0].name
                                }
                            }
                        }
                        if (isRefresh) {
                            fragment.adapter.setNewInstance(data.list)
                        } else {
                            fragment.adapter.addData(data.list)
                        }
                        fragment.showDataInfo()
                    } else {
                        if (isRefresh) {
                            fragment.showNoDataInfo()
                        }
                    }

                    if (pageNo >= data?.totalPage!!) {
                        //加载到了最后一页
                        fragment.adapter.loadMoreModule.isEnableLoadMore = false
                    } else {
                        pageNo += 1
                    }
                    (fragment.activity as AddGoodsActivity).setMyDataNum(data.totalCount)

                    if (isEnterGoods) {
                        MySoundUtils.getInstance().dingSound()
                        isEnterGoods = false
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isRefreshing.set(false)
                    fragment.showErrorMsg(apiErrorModel.message)
                    if (statusCode == 605025L) {
                        fragment.cleanOrderNo()
                    } else if (statusCode == 605006L) {
                        fragment.cleanOrderNoV2()
                    } else {
                        (fragment.activity as AddGoodsActivity).setMyDataNum(0)
                        fragment.showNoDataInfo()
                    }
                }
            })
    }

    val days = mutableListOf(
        DCBean("全部", "ALL", DCBean.SHOW_KEY),
        DCBean("补货下架", "PICK", DCBean.SHOW_KEY),
        DCBean("补货上架", "PUTAWAY", DCBean.SHOW_KEY)
    )
}