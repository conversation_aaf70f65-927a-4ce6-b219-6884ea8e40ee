package com.midea.prestorage.function.outstorage.response

import androidx.databinding.ObservableField
import com.midea.prestorage.function.instorage.response.InOrderData
import com.midea.prestorage.function.instorage.response.InOrderDataSort
import com.midea.prestorage.utils.AppUtils

class ShipmentDetailSort(var item: RespShipmentDetail): Comparable<ShipmentDetailSort> {

    val vs = ViewStyle()

    inner class ViewStyle {
        val sortFlag = ObservableField<Int>(0)
    }

    init {
        vs.sortFlag.set(if (AppUtils.getBigDecimalValueStr(item.scannedQty) == "0" && AppUtils.getBigDecimalValueStr(item.allocatedQty) == "0") 2 else if (AppUtils.getBigDecimalValueStr(item.scannedQty) == "0") 1 else if (AppUtils.getBigDecimalValueStr(item.allocatedQty).toInt() > AppUtils.getBigDecimalValueStr(item.scannedQty).toInt()) 0 else 2)
    }

    override fun compareTo(other: ShipmentDetailSort): Int {
        return vs.sortFlag.get()!!.compareTo(other.vs.sortFlag.get()!!)
    }
}