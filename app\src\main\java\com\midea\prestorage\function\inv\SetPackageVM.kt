package com.midea.prestorage.function.inv

import android.annotation.SuppressLint
import android.content.Intent
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.ContainerPickList
import com.midea.prestorage.beans.net.InvSetDetailList
import com.midea.prestorage.beans.net.InvSetList
import com.midea.prestorage.beans.net.SetPackagePrintBean
import com.midea.prestorage.function.inv.response.FuInvLocationInventory
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.printer.BluetoothConnectBack
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody
import java.text.SimpleDateFormat
import java.util.*
import kotlin.Comparator

class SetPackageVM(val activity: SetPackageActivity) {

    val isNoData = ObservableBoolean(false)
    val isRefreshing = ObservableBoolean(false)
    var orderNo = ObservableField("")
    var totalNum = ObservableField("0")
    val isPrintOk = ObservableField(false)

    fun init() {
        bluetoothOpen() //进入页面就去连接蓝牙
        activity.waitingDialogHelp.showDialog()
        initList(orderNo.get().toString())
    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        isRefreshing.set(true)
        activity.waitingDialogHelp.showDialog()
        initList(orderNo.get().toString())
    }

    private val blueBack = object : BluetoothConnectBack {
        override fun success() {
            activity.waitingDialogHelp.hidenDialog()
            isPrintOk.set(true)
        }

        override fun fail() {
            isPrintOk.set(false)
            AppUtils.showToast(activity, "打印机连接失败，请重启打印机!")
        }

        override fun connect() {
            bluetoothOpen()
        }
    }

    fun bluetoothOpen() {
        if (CheckUtil.isFastDoubleClick()) {
            if(!Printer.isPrintOk()) {
                Printer.openBluetooth(activity, blueBack)
            }else {
                isPrintOk.set(true)
            }
        }
    }

    @SuppressLint("SimpleDateFormat")
    val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

    @SuppressLint("SimpleDateFormat")
    fun onEnterOrderNo() {
        if (CheckUtil.isFastDoubleClick()) {
            if (orderNo.get().isNullOrEmpty()) {
                ToastUtils.getInstance()
                    .showErrorToastWithSound(activity, "集托号不能为空")
                return
            }
            activity.waitingDialogHelp.showDialog()
            initList(orderNo.get()!!)
        }
    }

    /**
     * 我要集托
     */
    fun toSetPackage() {
        if (CheckUtil.isFastDoubleClick()) {
            val it = Intent(activity, WaitSetPackageActivity::class.java)
            it.putExtra("flag", "main") //标识是从主列表页面进去的
            activity.startActivity(it)
        }
    }

    /**
     * 集托完成
     */
    fun setPackageFinish() {
        if (CheckUtil.isFastDoubleClick()) {
            if (totalNum.get().toString() == "0") {
                ToastUtils.getInstance()
                    .showErrorToastWithSound(activity, "请选择需要完成的集托信息！")
                return
            }
            activity.sendTipDialog.setContent("确定要完成集托吗？")
            activity.sendTipDialog.show()
        }
    }

    private fun initList(setCode: String) {
        val param = mutableMapOf(
            "setCode" to setCode,
            "whCode" to Constants.whInfo?.whCode,
            "setUserCode" to Constants.userInfo?.name
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getInventoryAPI()
            .loadSetList(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<InvSetList>>(activity) {
                override fun success(data: MutableList<InvSetList>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    isRefreshing.set(false)
                    if (data != null) {
                        if (data.size > 0) {
                            isNoData.set(false)
                        } else {
                            isNoData.set(true)
                        }
                        data.sort()
                        data.forEachIndexed { index, invSetList ->
                            invSetList.index = index + 1
                            if(invSetList.status == "150") {
                                invSetList.status = "集托中"
                            }else if (invSetList.status == "200") {
                                invSetList.status = "集托完成"
                            }
                        }
                        activity.showData(data)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isRefreshing.set(false)
                    orderNo.set("")
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun finishSetPackage(data: MutableList<String>?) {
        val param = mutableMapOf(
            "setCodeList" to data,
            "whCode" to Constants.whInfo?.whCode,
            "setUserCode" to Constants.userInfo?.name
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getInventoryAPI()
            .setFinish(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<SetPackagePrintBean>>(activity) {
                override fun success(data: MutableList<SetPackagePrintBean>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showSuccessToastWithSound(activity, "集托完成！")
                    initList(orderNo.get().toString()) //接口处理之后，刷新当前页面
                    data?.forEach {
                        it.totalNum = (it.wholeNum.toInt() + it.partNum.toInt()).toString()
                        it.waveNum = it.waveNo.split(",").size.toString()
                    }
                    data?.let { Printer.printSetPackage(it) } //集托完成则去自动做打印操作

                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun clearOrderNo() {
        orderNo.set("")
    }

    fun back() {
        activity.finish()
    }

    fun onItemClick(bean: InvSetList) {
        val it = Intent(activity, SetPackageDetailActivity::class.java)
        it.putExtra("flag", "main") //标识是从主列表页面进去的
        it.putExtra("status", bean.status) //标识集托的状态
        it.putExtra("setCode", bean.setCode) //集托号
        it.putExtra("bean", bean)
        activity.startActivity(it)
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        orderNo.set(result)
        onEnterOrderNo()
    }
}