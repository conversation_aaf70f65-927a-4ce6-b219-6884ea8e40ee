package com.midea.prestorage.function.inv.response

import java.math.BigDecimal

data class RespAttributeList(
    val lotDetails: List<LotDetail>?,
    val packageDetails: List<PackageDetail>?,
    val isDecimal: Int
)

data class LotDetail(
    val createTime: String?,
    val creator: String?,
    val fieldType: String?,
    val inputControl: String?,
    val isPrint: String?,
    val jobId: Int?,
    val key: String?,
    val lotAtt: String?,
    val lotCode: String?,
    val modifier: String?,
    val modifyTime: String?,
    val orgId: String?,
    val pageSize: Int?,
    val recStatus: Int?,
    val recVer: Int?,
    val timeZone: String?,
    val title: String?,
    var value: String?
)

data class PackageDetail(
    val cdpaCode: String?,
    val cdpaFormat: String?,
    val qty: BigDecimal?,
    val seqNo: Int?,
    val unitCode: String?,
    val unitName: String?,
    var num: String? = null
)
