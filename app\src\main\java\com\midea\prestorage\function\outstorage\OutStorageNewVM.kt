package com.midea.prestorage.function.outstorage

import android.app.Application
import android.text.TextUtils
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.ContainerPickSecondList
import com.midea.prestorage.beans.net.OutStorageQuery
import com.midea.prestorage.beans.net.OutStorageScan
import com.midea.prestorage.beans.net.RuleDataList
import com.midea.prestorage.beans.setting.HandingInfoDb
import com.midea.prestorage.function.instorage.response.InOrderDataSort
import com.midea.prestorage.function.inv.response.InReceiptOrder
import com.midea.prestorage.function.outstorage.response.RespShipmentDetail
import com.midea.prestorage.function.outstorage.response.ShipmentDetailSort
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.*
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody
import org.xutils.db.sqlite.WhereBuilder

class OutStorageNewVM(application: Application) : BaseViewModel(application) {
    var finishActivity = MutableLiveData(false)
    var curOrderNo = MutableLiveData<String>("") // 单号 (入库单号或波次单号)
    var dayInfo = MutableLiveData<String>("7天")
    var goodsNo = MutableLiveData<String>("")
    val processInfo = MutableLiveData<String>("")
    var goodsRequest = MutableLiveData(false)
    var isShowDeleteDialog = MutableLiveData(false)
    var isShowHandingDialog = MutableLiveData(false)
    var isShowTipDialog = MutableLiveData<String>()
    var isPalletEnter = MutableLiveData(false)
    var containerChangeTip = MutableLiveData(false)
    var showDatas = MutableLiveData<MutableList<ShipmentDetailSort>>()
    var ruleList: MutableList<RuleDataList>? = null
    var queryType = 1 //默认出库单
    var bean: OutStorageQuery? = null
    val isNoData = MutableLiveData(true)
    var handingStartTime = MutableLiveData<String>("")
    var handingEndTime = MutableLiveData<String>("")
    val db = DbUtils.db
    var isShowValidateSomeDialog = MutableLiveData<String>()
    var handingGroupCode = MutableLiveData<String>("")
    var handingGroupName = MutableLiveData<String>("")
    var isNeedParse = MutableLiveData(false)
    var dismissDialog = MutableLiveData(false)

    override fun init() {

    }

    fun onEnterOrderNo() {
        if (CheckUtil.isFastDoubleClick()) {
            if (TextUtils.isEmpty(curOrderNo.value)) {
                showNotification("单号不能为空", false)
                return
            }
            //波次号校验位数
            if (curOrderNo.value?.trim()!!.length < 4) {
                showNotification("波次号必须大于3位", false)
                return
            }
//            isPalletEnter.value = true
//            goodsRequest.value = true
            queryShipmentDetail()
        }
    }

    fun goodsEnterKeyPress() {
        if (CheckUtil.isFastDoubleClick()) {
            if (TextUtils.isEmpty(goodsNo.value)) {
                showNotification("SN码不能为空", false)
                return
            }
//            if(handingStartTime.value.toString().isNullOrBlank()) {
//                showNotification("请先设置装卸开始时间", false)
//            }else {
            scan(goodsNo.value.toString().trim())
//            }
        }
    }

    fun queryShipmentDetail() {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf(
                "whCode" to Constants.whInfo?.whCode,
                "waveNo" to curOrderNo.value.toString().trim(),
                "days" to dayInfo.value.toString().substring(0, 1).toInt()
            )
            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp =
                    async { RetrofitHelper.getOutStorageAPI().queryShipmentDetailNew(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                //result.msg?.let { showNotification(it, true) }

                if (result.data != null && result.data!!.size > 0) {
                    isPalletEnter.value = true
                    goodsRequest.value = true

                    //合并操作
                    val combines = mutableListOf<RespShipmentDetail>()
                    result.data!!.forEach {
                        if (combines.contains(it)) {
                            val result = combines.find { item -> item == it }

                            result?.planQty = result?.planQty?.add(it.planQty)
                            result?.allocatedQty = result?.allocatedQty?.add(it.allocatedQty)
                            result?.scannedQty = result?.scannedQty?.add(it.scannedQty)
                            result?.pickedQty = result?.pickedQty?.add(it.pickedQty)
                        } else {
                            combines.add(it)
                        }
                    }

                    curOrderNo.value = combines[0].waveNo
                    isNeedParse.value = combines[0].isNeedParse

                    if (combines[0].handingStartTime != null) {
                        handingStartTime.value = combines[0].handingStartTime
                    } else {
                        handingStartTime.value = ""
                    }
                    if (combines[0].handingEndTime != null) {
                        handingEndTime.value = combines[0].handingEndTime
                    } else {
                        handingEndTime.value = ""
                    }

                    val temp = mutableListOf<ShipmentDetailSort>()
                    combines.forEach {
                        temp.add(ShipmentDetailSort(it))
                    }
                    showDatas.value = temp
                    barcodeRule(combines)
                    var scanQty = 0
                    var planQty = 0
                    combines.forEach {
                        scanQty += AppUtils.getBigDecimalValueStr(it.scannedQty).toInt()
                        planQty += AppUtils.getBigDecimalValueStr(it.allocatedQty).toInt()
                    }
                    processInfo.value = "$scanQty/$planQty"
                }

            } else {
                curOrderNo.value = ""
                result.msg?.let { showNotification(it, false) }
                //showNotification("单号不存在或无待扫码商品", false)
            }
        }
    }

    private fun barcodeRule(shipmentDetailSumQtyDtoList: MutableList<RespShipmentDetail>) {
        ruleList?.clear()
        launch(showDialog = false,
            error = {
            }, finish = {
            }) {

            val param = mutableSetOf<String>()
            shipmentDetailSumQtyDtoList.forEach {
                param.add(it.itemCode)
            }
            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getOutStorageAPI().barcodeRuleNew(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                if (result.data != null) {
                    //ruleList = result.data
                    val beans = mutableListOf<RuleDataList>()
                    result.data?.forEach { data ->
                        data.details?.forEach {
                            beans.add(RuleDataList(data.cdcmMaterialNo, data.cdcmCustMaterialNo, it.codeNum, it.paraNum, it.paraNumTo, it.paragraphDefine, data.cdcmBarcode,
                                data.userdefined1, data.userdefined2,data.userdefined3,data.userdefined4,data.userdefined5,data.userdefined6,data.userdefined7,data.userdefined8,data.userdefined9,data.userdefined10))
                        }
                    }
                    ruleList = beans
                }
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    private fun scan(serialNo: String) {
        //匹配本地规则
        if (canParseWithRule()) {
            //  从大物流取
            for (rule in ruleList!!) {
                //  优先匹配字符串长度
                if (rule.codeNum == null || rule.codeNum == "" || serialNo.length != Integer.parseInt(
                        rule.codeNum
                    )
                ) continue

                val subStr = serialNo.substring(
                    Integer.parseInt(rule.paraNum) - 1,
                    Integer.parseInt(rule.paraNumTo)
                )
                val targetStr: String? = when (rule.paragraphDefine) {
                    "CDCM_BARCODE" -> rule.cdcmBarcode
                    "CDCM_BARCODE1" -> rule.cdcmBarcode1
                    "CDCM_BARCODE2" -> rule.cdcmBarcode2
                    "CDCM_BARCODE3" -> rule.cdcmBarcode3
                    "CDCM_BARCODE4" -> rule.cdcmBarcode4
                    "CDCM_BARCODE5" -> rule.cdcmBarcode5
                    "CDCM_BARCODE6" -> rule.cdcmBarcode6
                    "CDCM_BARCODE7" -> rule.cdcmBarcode7
                    "CDCM_BARCODE8" -> rule.cdcmBarcode8
                    "CDCM_BARCODE9" -> rule.cdcmBarcode9
                    "CDCM_BARCODE10" -> rule.cdcmBarcode10
                    else -> rule.cdcmBarcode
                }

                if (targetStr != null && subStr == targetStr) {
                    startScan(serialNo, rule.cdcmCustMaterialNo, rule.cdcmMaterialNo)
                    return
                }
            }
        }
        startScan(serialNo)
    }

    fun handingTime(isShowMsg: Boolean) {
        launch(showDialog = isShowMsg,
            error = {
            }, finish = {
            }) {

            val handingInfo = withContext(Dispatchers.IO) {
                val resp = async {
                    db.selector(HandingInfoDb::class.java)
                        .where("userId", "==", Constants.userInfo?.id)
                        .and(WhereBuilder.b("mode", "==", 5))
                        .findFirst()
                }
                resp.await()
            }
            var handlingGroupName = ""
            var handlingGroupCode = ""
            var supplierNo = ""
            var supplierName = ""
            if (handingInfo != null) {
                handlingGroupName = handingInfo.handlingName
                handlingGroupCode = handingInfo.handlingCode
                if (!handingInfo.supplierCode.isNullOrBlank()) {
                    supplierNo = handingInfo.supplierCode
                }
                if (!handingInfo.supplierName.isNullOrBlank()) {
                    supplierName = handingInfo.supplierName
                }
            }

            val param = mutableMapOf(
                "whCode" to Constants.whInfo?.whCode,
                "waveNo" to curOrderNo.value.toString().trim(),
                "handingGroupCode" to handlingGroupCode,
                "handingGroupName" to handlingGroupName,
                "supplierNo" to supplierNo,
                "supplierName" to supplierName,
                "handingStartTime" to handingStartTime.value.toString(),
                "handingEndTime" to handingEndTime.value.toString()
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getOutStorageAPI().handingTime(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                if (isShowMsg) {
                    result.msg?.let { showNotification(it, true) }
                }
                if (!handingStartTime.value.toString()
                        .isNullOrBlank() && !handingEndTime.value.toString().isNullOrBlank()
                ) {
                    dismissDialog.value = true
                }
            } else {
                if (isShowMsg) {
                    result.msg?.let { showNotification(it, false) }
                }
            }
        }
    }

    private fun startScan(serialNo: String, custItemCode: String = "", itemCode: String = "") {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf(
                "whCode" to Constants.whInfo?.whCode,
                "waveNo" to curOrderNo.value.toString().trim(),
                "barcode" to serialNo,
                "bearingSystem" to Constants.whInfo?.bearingSystemStr,
                "custItemCode" to custItemCode
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getOutStorageAPI().scanNew(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                showNotification("扫码成功!", true)
                goodsNo.value = ""
                queryShipmentDetail()
            } else {
                goodsNo.value = ""
                result.msg?.let { showNotification(it, false) }
                //queryShipmentDetail()
            }
        }
    }

    /**
     * 大物流-删除条码
     */
    fun deleteBarcode(serialNo: String) {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf(
                "whCode" to Constants.whInfo?.whCode,
                "waveNo" to curOrderNo.value.toString().trim(),
                "barcode" to serialNo
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getOutStorageAPI().deleteBarcode(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                var data = result.data as String?
                if (!data.isNullOrBlank()) {
                    showNotification(data, true)
                } else {
                    result.msg?.let { showNotification(it, true) }
                }
                queryShipmentDetail()
            } else {
                result.msg?.let { showNotification(it, false) }
                //queryShipmentDetail() //删除条码不成功，则不刷新明细
            }
        }
    }

    /**
     * 大物流-校验是否已拣货完成
     */
    fun validatePick() {
        launch(showDialog = false,
            error = {
            }, finish = {
            }) {

            isDialogShow.value = 2

            val param = mutableMapOf(
                "whCode" to Constants.whInfo?.whCode,
                "waveNo" to curOrderNo.value.toString().trim()
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getOutStorageAPI().validatePick(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                var data = result.data as String?
                if (!data.isNullOrBlank()) { //拣货未完成,则跳到拣货界面
                    isDialogShow.value = 0
                    isShowTipDialog.value = data
                } else { //已拣货完成的，则继续校验是否存在部分发货
                    validateSome()
                }
            } else {
                isDialogShow.value = 0
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    /**
     * 大物流-校验是否存在部分发货
     */
    fun validateSome() {
        launch(showDialog = false,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf(
                "whCode" to Constants.whInfo?.whCode,
                "waveNo" to curOrderNo.value.toString().trim()
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getOutStorageAPI().validateSome(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                if (!result.data?.handingGroupCode.isNullOrBlank()) {
                    handingGroupCode.value = result.data?.handingGroupCode
                }
                if (!result.data?.handingGroupName.isNullOrBlank()) {
                    handingGroupName.value = result.data?.handingGroupName
                }
                if (!result.data?.remark.isNullOrBlank()) { //存在部分发货
                    isDialogShow.value = 0
                    isShowValidateSomeDialog.value = result.data?.remark
                } else { //不存在部分发货，则直接调用发货确认接口
                    sendConfirm()
                }
            } else {
                isDialogShow.value = 0
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    /**
     * 大物流-发货确认
     */
    fun sendConfirm() {
        if (handingStartTime.value.toString().isNullOrBlank()) {
            handingTime(false)
        }

        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val handingInfo = withContext(Dispatchers.IO) {
                val resp = async {
                    db.selector(HandingInfoDb::class.java)
                        .where("userId", "==", Constants.userInfo?.id)
                        .and(WhereBuilder.b("mode", "==", 5))
                        .findFirst()
                }
                resp.await()
            }
            var supplierNo = ""
            var supplierName = ""
            if (handingInfo != null) {
                if (!handingInfo.supplierCode.isNullOrBlank()) {
                    supplierNo = handingInfo.supplierCode
                }
                if (!handingInfo.supplierName.isNullOrBlank()) {
                    supplierName = handingInfo.supplierName
                }
            }

            val param = mutableMapOf(
                "whCode" to Constants.whInfo?.whCode,
                "waveNo" to curOrderNo.value.toString().trim()
            )

            param["handingGroupCode"] = handingGroupCode.value.toString()
            param["handingGroupName"] = handingGroupName.value.toString()

            param["handingStartTime"] = handingStartTime.value.toString()
            param["handingEndTime"] = handingEndTime.value.toString()

            param["supplierNo"] = supplierNo
            param["supplierName"] = supplierName

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getOutStorageAPI().sendConfirm(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                showNotification("发货成功", true)
                //queryShipmentDetail() //发货成功，刷新商品明细列表
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    // 是否根据条码规则解析
    private fun canParseWithRule(): Boolean {
        return (!goodsNo.value.toString().trim().endsWith("W")
                && ruleList != null
                && ruleList!!.size > 0 && isNeedParse.value!!)
    }

    fun showErrorNotification(msg: String, isSuccess: Boolean) {
        showNotification(msg, isSuccess)
    }

    fun containerChangeTip() {
        if (CheckUtil.isFastDoubleClick()) {
            containerChangeTip.value = true
        }
    }

    fun deleteBarcode() {
        isShowDeleteDialog.value = true
    }

    fun startHanding() {
        isShowHandingDialog.value = true
    }

    fun startSend() {
//        if (handingStartTime.value.toString().isNullOrBlank()) {
//            showNotification("请先设置装卸开始时间", false)
//            return
//        }
        validatePick()
    }
}