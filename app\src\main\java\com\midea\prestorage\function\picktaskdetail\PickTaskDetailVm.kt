package com.midea.prestorage.function.picktaskdetail

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.DictDetail
import com.midea.prestorage.function.instorage.response.InOrderData
import com.midea.prestorage.function.instorage.response.InOrderDataSort
import com.midea.prestorage.function.inv.InventorySearchVM
import com.midea.prestorage.function.inv.response.InvStockTakeTaskDetail
import com.midea.prestorage.function.inv.response.InvStockTakeTaskHeader
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.launch
import com.midea.prestorage.widgets.ViewBindingAdapter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody

class PickTaskDetailVm(application: Application) : BaseViewModel(application) {

    var waveNo = MutableLiveData<String>("")
    var returnData: MutableList<InvStockTakeTaskDetail>? = null
    var oldReturnData: MutableList<InvStockTakeTaskDetail>? = null
    var orderByGood = MutableLiveData(0)
    var notificationDataListChange = MutableLiveData<Boolean>(false)
    var isPickFinish = MutableLiveData<Boolean>(false)
    var isHasTaskPick = MutableLiveData<Boolean>(false)
    var isOutStorageNew = MutableLiveData<Boolean>(false)
    var totalString = MutableLiveData<String>("")
    var filterValue = MutableLiveData<String>("")
    var isFilter = MutableLiveData<Boolean>(false)
    var searCode = MutableLiveData<String>("")
    var isRecyleView = MutableLiveData<Boolean>(false)
    var isShowSearchTypeMenu = MutableLiveData<Boolean>(false)
    var taskDetailIdList = mutableListOf<String>()

    // 需求是 要记录上次的查询模式，所以这里用 静态变量
    companion object {
        // 查询模式
        val checkLotAtt01 = "CHECKLOTATT01"
        val checkLotAtt02 = "CHECKLOTATT02"
        val checkLotAtt03 = "CHECKLOTATT03"
        val checkLotAtt05 = "CHECKLOTATT05"
    }

    override fun init() {

    }

    //拣货完成数据
    fun onPickFinish() {
        isPickFinish.value = true
    }

    //出库扫码
    fun onOutStorageNew() {
        isOutStorageNew.value = true
    }

    //已拣任务
    fun onHasTaskPick() {
        isHasTaskPick.value = true
    }

    //网络请求
    fun webGetData() {
        launch(showDialog = true, error = {
        }, finish = {}) {
            val param = mutableMapOf(
                "waveNo" to waveNo.value as String,
                "whCode" to Constants.whInfo?.whCode,
                "taskDetailIdList" to taskDetailIdList
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val status = withContext(Dispatchers.Default) { getDict() }
                val details = withContext(Dispatchers.Default) {
                    RetrofitHelper.getInventoryAPI().getPickTaskDetail(requestBody)
                }
                status?.removeAll { it.enableFlag == 0 }
                details.data?.bwmsRfPickTaskDetailResponses?.forEach {
                    it.lotAtt04Str = status.find { item ->
                        item.code == it.lotAtt04
                    }?.name
                }
                details
            }

            if (result.code == 0L) {
                dealData(result.data)
            } else {
                showNotification(result.msg as String, false)
                isRecyleView.value = false
            }
        }
    }

    fun dealData(result: InvStockTakeTaskHeader?) {
        // 后端返回的列表前端进行汇总
        val groupMap = mutableMapOf<String, InvStockTakeTaskDetail>()
        result?.bwmsRfPickTaskDetailResponses?.forEach {
            var key = it.fromLoc + it.custItemCode + it.lotAtt04
            if (SPUtils[checkLotAtt01, false] as Boolean) {
                key += it.lotAtt01
            }
            if (SPUtils[checkLotAtt02, false] as Boolean) {
                key += it.lotAtt02
            }
            if (SPUtils[checkLotAtt03, false] as Boolean) {
                key += it.lotAtt03
            }
            if (SPUtils[checkLotAtt05, false] as Boolean) {
                key += it.lotAtt05
            }
            if (groupMap.get(key) == null) {
                groupMap.put(key, InvStockTakeTaskDetail())
            }

            val inOrderData = groupMap.get(key) as InvStockTakeTaskDetail
            inOrderData.custItemCode = it.custItemCode
            inOrderData.fromLoc = it.fromLoc
            inOrderData.itemName = it.itemName
            if (inOrderData.fromQty != null) {
                inOrderData.fromQty = inOrderData.fromQty + it.fromQty
            } else {
                inOrderData.fromQty = it.fromQty
            }
            if (inOrderData.toQty != null) {
                inOrderData.toQty = inOrderData.toQty + it.toQty
            } else {
                inOrderData.toQty = it.toQty
            }
            inOrderData.lotAtt04Str = it.lotAtt04Str
            inOrderData.lotAtt01 = it.lotAtt01
            inOrderData.lotAtt02 = it.lotAtt02
            inOrderData.lotAtt03 = it.lotAtt03
            inOrderData.lotAtt04 = it.lotAtt04
            inOrderData.lotAtt05 = it.lotAtt05
            inOrderData.barcode = it.barcode
            if (inOrderData.ids.isNullOrEmpty()) {
                inOrderData.ids = it.ids
            } else {
                inOrderData.ids = inOrderData.ids + "," + it.ids
            }

        }

        // map 转 list
        val temp = mutableListOf<InvStockTakeTaskDetail>()
        groupMap.forEach {
            temp.add(it.value)
        }

        returnData = temp
        oldReturnData = temp
        totalString.value =
            "${AppUtils.getBigDecimalValueStr(result?.totalToQty)}/${AppUtils.getBigDecimalValueStr(
                result?.totalFromQty
            )}"

        isRecyleView.value = result?.totalToQty != result?.totalFromQty

        //如果搜索到数据就显示否则就不要显示
        if (!result?.bwmsRfPickTaskDetailResponses.isNullOrEmpty()) {
            isRecyleView.value = true
            notificationDataListChange.value = true//通知改变列表
        } else {
            isRecyleView.value = false
        }
    }

    private var dictList: MutableList<DictDetail>? = null
    private suspend fun getDict(): MutableList<DictDetail> = if (dictList != null) {
        dictList!!
    } else {
        var response = RetrofitHelper.getDirectionAPI().searchDictNew("CL_INVENTORY_STS")
        dictList = if (response.code == 0L) {
            response.data
        } else {
            mutableListOf()
        }
        dictList!!
    }

    fun onClearText() {
        searCode.value = ""//清空
        filterValue.value = ""
        isFilter.value = true
    }

    fun onEnterSearch() {

        isFilter.value = true
    }

    fun showSearchTypeMenu() {
        isShowSearchTypeMenu.value = true
    }

    val inputTextChange = object : ViewBindingAdapter.TextChangedListener {

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            filterValue.value = s.toString()
        }
    }
}

