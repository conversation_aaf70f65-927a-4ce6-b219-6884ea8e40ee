package com.midea.prestorage.function.mainyg

import android.widget.Switch
import android.widget.TextView
import com.midea.prestorage.function.main.ProfileUnionBinding
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.widgets.MyGridView
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.databinding.ActivityMainYgBinding
import com.midea.prestoragesaas.databinding.ActivityMainYgCareBinding
import com.midea.prestoragesaas.databinding.ActivityProfileBinding
import com.midea.prestoragesaas.databinding.ActivityProfileGhBinding

sealed class MainYgUnionBinding {
    abstract var vm: MainYgVM?
    abstract val gridIn: MyGridView
    abstract val gridOut: MyGridView
    abstract val gridStorage: MyGridView
    abstract val tvNotification: TextView

    class V2(val binding: ActivityMainYgCareBinding) : MainYgUnionBinding() {
        override var vm: MainYgVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val gridIn = binding.gridIn
        override val gridOut = binding.gridOut
        override val gridStorage = binding.gridStorage
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityMainYgBinding) : MainYgUnionBinding() {
        override var vm: MainYgVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val gridIn = binding.gridIn
        override val gridOut = binding.gridOut
        override val gridStorage = binding.gridStorage
        override val tvNotification = binding.tvNotification
    }

}
