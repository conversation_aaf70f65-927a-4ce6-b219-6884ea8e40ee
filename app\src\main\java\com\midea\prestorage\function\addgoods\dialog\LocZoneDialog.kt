package com.midea.prestorage.function.addgoods.dialog

import android.app.AlertDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.main.dialog.DialogTenantChooseUnionBinding
import com.midea.prestorage.function.receivecpkx.dialog.SimpleLoadMoreView
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.CareLoadMoreView
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogLocZoneBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

class LocZoneDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {

    private var binding: DialogLocZoneUnionBinding
    val adapter = LocZoneAdapter()

    init {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window?.setGravity(Gravity.CENTER)
            window?.attributes?.run {
                gravity = Gravity.CENTER
            }
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_loc_zone_care, null)
            setView(contentView)
            DialogLocZoneUnionBinding.V2(DataBindingUtil.bind(contentView)!!)
        } else {
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_loc_zone, null)
            setView(contentView)
            DialogLocZoneUnionBinding.V1(DataBindingUtil.bind(contentView)!!)
        }
        binding.vm = LocZoneDialogVM(this)

        setCanceledOnTouchOutside(true)
        // 加上这个 确保按回退键也不关闭选择框
        setCancelable(true)

        initRecycleView()
        initLoadMore()
    }

    private fun initRecycleView() {
        binding.recycle.layoutManager = LinearLayoutManager(mContext)
        binding.recycle.adapter = adapter
    }

    fun addData(info: MutableList<BaseItemShowInfo>) {
        adapter.addData(info)
        adapter.notifyDataSetChanged()
    }

    fun setOnItemClick(palletBack: LocBack) {
        adapter.setOnItemClickListener { adapter, _, position ->
            val bean = adapter.data[position] as BaseItemShowInfo
            if (bean.showInfo.isNullOrEmpty()) {
                return@setOnItemClickListener
            }
            palletBack.locBack(bean)
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.initPalletInfo(false)
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            adapter.loadMoreModule.loadMoreView = CareLoadMoreView()
        } else {
            adapter.loadMoreModule.loadMoreView = SimpleLoadMoreView()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    fun stopLoad() {
        adapter.loadMoreModule.loadMoreComplete()
    }

    fun endLoad() {
        adapter.loadMoreModule.loadMoreEnd()
    }

    fun setTitle(title: String) {
        if (!TextUtils.isEmpty(title)) {
            binding.vm!!.title.set(title)
        }
    }

    fun setType(type: String) {
        if (!TextUtils.isEmpty(type)) {
            binding.vm!!.type.set(type)
        }
    }

    override fun show() {
        super.show()

        binding.vm?.pageNo = 1
        binding.vm?.totalPage = 10
        adapter.setNewInstance(mutableListOf())
        adapter.notifyDataSetChanged()

        binding.vm?.filterInfo?.set("")
        binding.vm?.initPalletInfo(false)
    }

    interface LocBack {
        fun locBack(info: BaseItemShowInfo)
    }

    class LocZoneAdapter :
        CommonAdapter<BaseItemShowInfo>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.dialog_list_item_tenant_care else R.layout.dialog_list_item_click),
        LoadMoreModule {
    }
}