package com.midea.prestorage.beans.net;

import java.io.Serializable;
import java.util.List;

public class PickWaveBean implements Serializable {
    private int totalCount;
    private int totalPage;
    private int pageNo;
    private int pageSize;
    private int offset;
    private List<PickWaveListBean> list;

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public List<PickWaveListBean> getList() {
        return list;
    }

    public void setList(List<PickWaveListBean> list) {
        this.list = list;
    }

    public static class PickWaveListBean implements Serializable {
        private String taskId;
        private String waveId;
        private String orderQty;
        private String itemClassQty;
        private String itemQty;
        private String waveType;
        private String createTime;
        private String createDate;
        private String pickBy;
        private String pickStatus;
        private String pickStatusStr;
        private String fromLoc;
        private String itemCode;
        private String itemName;
        private String detailId;
        private String itemLocFromQty;
        private String itemLocTotalQty;
        private String companyCode;
        private String barCodes;
        private String itemSpecs;
        private List<GridDtoList> gridDtoList;
        private String waveFromQty;
        private String waveTotalQty;
        private String wavePickProgress;
        private String isFinish;

        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public String getWaveId() {
            return waveId;
        }

        public void setWaveId(String waveId) {
            this.waveId = waveId;
        }

        public String getOrderQty() {
            return orderQty;
        }

        public void setOrderQty(String orderQty) {
            this.orderQty = orderQty;
        }

        public String getItemClassQty() {
            return itemClassQty;
        }

        public void setItemClassQty(String itemClassQty) {
            this.itemClassQty = itemClassQty;
        }

        public String getItemQty() {
            return itemQty;
        }

        public void setItemQty(String itemQty) {
            this.itemQty = itemQty;
        }

        public String getWaveType() {
            return waveType;
        }

        public void setWaveType(String waveType) {
            this.waveType = waveType;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getCreateDate() {
            return createDate;
        }

        public void setCreateDate(String createDate) {
            this.createDate = createDate;
        }

        public String getPickBy() {
            return pickBy;
        }

        public void setPickBy(String pickBy) {
            this.pickBy = pickBy;
        }

        public String getPickStatus() {
            return pickStatus;
        }

        public void setPickStatus(String pickStatus) {
            this.pickStatus = pickStatus;
        }

        public String getPickStatusStr() {
            return pickStatusStr;
        }

        public void setPickStatusStr(String pickStatusStr) {
            this.pickStatusStr = pickStatusStr;
        }

        public String getFromLoc() {
            return fromLoc;
        }

        public void setFromLoc(String fromLoc) {
            this.fromLoc = fromLoc;
        }

        public String getItemCode() {
            return itemCode;
        }

        public void setItemCode(String itemCode) {
            this.itemCode = itemCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getDetailId() {
            return detailId;
        }

        public void setDetailId(String detailId) {
            this.detailId = detailId;
        }

        public String getItemLocFromQty() {
            return itemLocFromQty;
        }

        public void setItemLocFromQty(String itemLocFromQty) {
            this.itemLocFromQty = itemLocFromQty;
        }

        public String getItemLocTotalQty() {
            return itemLocTotalQty;
        }

        public void setItemLocTotalQty(String itemLocTotalQty) {
            this.itemLocTotalQty = itemLocTotalQty;
        }

        public String getCompanyCode() {
            return companyCode;
        }

        public void setCompanyCode(String companyCode) {
            this.companyCode = companyCode;
        }

        public String getBarCodes() {
            return barCodes;
        }

        public void setBarCodes(String barCodes) {
            this.barCodes = barCodes;
        }

        public String getItemSpecs() {
            return itemSpecs;
        }

        public void setItemSpecs(String itemSpecs) {
            this.itemSpecs = itemSpecs;
        }

        public String getWaveFromQty() {
            return waveFromQty;
        }

        public void setWaveFromQty(String waveFromQty) {
            this.waveFromQty = waveFromQty;
        }

        public String getWaveTotalQty() {
            return waveTotalQty;
        }

        public void setWaveTotalQty(String waveTotalQty) {
            this.waveTotalQty = waveTotalQty;
        }

        public String getWavePickProgress() {
            return wavePickProgress;
        }

        public void setWavePickProgress(String wavePickProgress) {
            this.wavePickProgress = wavePickProgress;
        }

        public List<GridDtoList> getGridDtoList() {
            return gridDtoList;
        }

        public void setGridDtoList(List<GridDtoList> gridDtoList) {
            this.gridDtoList = gridDtoList;
        }

        public String getIsFinish() {
            return isFinish;
        }

        public void setIsFinish(String isFinish) {
            this.isFinish = isFinish;
        }

        public static class GridDtoList {
            private int groupIndex;
            private int itemLocOrderFromQty;
            private int itemLocOrderTotalQty;
            private boolean isShowData;

            public int getGroupIndex() {
                return groupIndex;
            }

            public void setGroupIndex(int groupIndex) {
                this.groupIndex = groupIndex;
            }

            public int getItemLocOrderFromQty() {
                return itemLocOrderFromQty;
            }

            public void setItemLocOrderFromQty(int itemLocOrderFromQty) {
                this.itemLocOrderFromQty = itemLocOrderFromQty;
            }

            public int getItemLocOrderTotalQty() {
                return itemLocOrderTotalQty;
            }

            public void setItemLocOrderTotalQty(int itemLocOrderTotalQty) {
                this.itemLocOrderTotalQty = itemLocOrderTotalQty;
            }

            public boolean isShowData() {
                return isShowData;
            }

            public void setShowData(boolean showData) {
                isShowData = showData;
            }
        }
    }
}
