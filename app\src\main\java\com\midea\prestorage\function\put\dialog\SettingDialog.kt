package com.midea.prestorage.function.put.dialog

import android.app.AlertDialog
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestoragesaas.databinding.DialogSettingBinding
import com.midea.prestoragesaas.databinding.DialogSettingPutBinding

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/6/26$
 */
class SettingDialog(
    var mContext: BaseActivity
) : AlertDialog(mContext) {

    private var binding: DialogSettingPutBinding

    init {
        val contentView =
            LayoutInflater.from(mContext).inflate(R.layout.dialog_setting_put, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = SettingDialogVM(this, binding)

        setCanceledOnTouchOutside(true)
    }

    override fun show() {
        super.show()

        binding.vm!!.init()
    }

    fun setOnSettingClick(listener: SettingBack) {
        binding.vm!!.listener = listener
    }

    interface SettingBack {
        fun onConfirmClick(mode: Int)
    }
}
