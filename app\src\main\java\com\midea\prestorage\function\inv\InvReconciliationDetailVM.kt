package com.midea.prestorage.function.inv

import CheckUtil
import android.app.Application
import android.content.Intent
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.*
import com.midea.prestorage.beans.req.LotAtt04DictReq
import com.midea.prestorage.event.LiveEvent
import com.midea.prestorage.function.inv.response.LotDetail
import com.midea.prestorage.function.inv.response.PackageDetail
import com.midea.prestorage.function.inv.response.RespAttributeList
import com.midea.prestorage.function.inv.response.RespMaterialList
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal
import java.util.*

class InvReconciliationDetailVM(application: Application) : BaseViewModel(application) {

    val title = MutableLiveData("")
    val locCode = ObservableField<String>("")
    val serialNo = ObservableField<String>("")
    val anntoItemCode = ObservableField<String>("")
    val statusName = ObservableField<String>("")
    val unitName = ObservableField<String>("")
    val statusCode = ObservableField<String>("")
    val qty = ObservableField<String>("")
    val isRefreshing = ObservableBoolean(false)
    val isNoData = ObservableBoolean(false)
    var respAdjustList: RespAdjustList? = null
    var inputType = MutableLiveData<Int>()
    val changeLocNotification = MutableLiveData(false)
    var isShowStatueDialog = MutableLiveData(false)
    var isShowUnitDialog = MutableLiveData(false)
    var isShowSettingDialog = MutableLiveData(false)
    var showGoods = MutableLiveData<MutableList<RespMaterialList>>()
    var setUnit = MutableLiveData<MutableList<PackageDetail>>()
    var lotDetail = MutableLiveData<MutableList<LotDetail>>()
    var cdprQuantity = BigDecimal.ONE //这个是包装系数
    val isShowGoods = ObservableField(false)
    val custItemCode = ObservableField<String>("")
    val itemName = ObservableField<String>("")
    val cdpaFormat = ObservableField<String>("")
    val lotDetailsStr = ObservableField<String>("")
    val templates = mutableMapOf<String, MutableList<LotDetail>?>()
    var template: MutableList<LotDetail>? = null
    val setQtyNotification = MutableLiveData(false)
    var resetGoodsUnit = MutableLiveData(false)
    var attributeBean: RespAttributeList? = null

    var curUnit = ""

    // 当前页码
    var pageNo = 1

    val loadMoreComplete = MutableLiveData(0)
    var showDatas = MutableLiveData<MutableList<RespAdjustDetailPage>>()
    var loadMoreDatas = MutableLiveData<MutableList<RespAdjustDetailPage>>()

    var tempMaterialList: RespMaterialList? = null

    val isPoint = MutableLiveData("-1")

    val showDialogLiveEvent = LiveEvent<Unit>()

    companion object {
        const val INPUT_LOC = 1
        const val INPUT_SERIAL = 2
        const val INPUT_QTY = 3

        const val NO_PIECE_SCAN = 0
        const val PIECE_SCAN = 1

        const val NO_RESERVED_LOTATT = 0
        const val RESERVED_LOTATT = 1

        const val NO_MULTIPLE_UNIT_INPUT = 0
        const val MULTIPLE_UNIT_INPUT = 1

        var scanType = NO_PIECE_SCAN //逐件扫描
        var lotType = NO_RESERVED_LOTATT //保留属性
        var unitInputType = NO_MULTIPLE_UNIT_INPUT //多单位数量输入
    }

    override fun init() {

    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        pageNo = 1
        isRefreshing.set(true)
        initOrderList()
    }

    fun showTypeMenu() {
        if (CheckUtil.isFastDoubleClick()) {
            isShowSettingDialog.value = true
        }
    }

    fun onEnterLocCode() {
        if (CheckUtil.isFastDoubleClick()) {
            if (locCode.get().toString().trim().isEmpty()) {
                showNotification("库位不能为空", false)
                return
            }
            if (checkSystemLoc(locCode.get().toString().trim().toUpperCase(Locale.ROOT))) {
                locCode.set("")
                showNotification("系统库位不能调账", false)
                return
            }
            checkLocCode()
        }
    }

    fun serialKeyPress() {
        if (CheckUtil.isFastDoubleClick()) {
            if (serialNo.get().toString().trim().isEmpty()) {
                showNotification("货品条码不能为空", false)
                return
            }
            getMaterialList()
        }
    }

    fun qtyKeyPress() {
        if (CheckUtil.isFastDoubleClick()) {
            if (qty.get().toString().trim().isEmpty()) {
                showNotification("调账数量不能为空", false)
                return
            }
            addDetail()
        }
    }

    fun changeLoc() {
        if (CheckUtil.isFastDoubleClick()) {
            changeLocNotification.value = true
        }
    }

    fun onSelectStatus() {
        if (CheckUtil.isFastDoubleClick()) {
            isShowStatueDialog.value = true
        }
    }

    fun onSelectUnit() {
        if (CheckUtil.isFastDoubleClick()) {
            isShowUnitDialog.value = true
        }
    }

    //自动填充货品条码栏位的商品编码
    fun autoFillSerialNo() {
        if (CheckUtil.isFastDoubleClick()) {
            serialNo.set(custItemCode.get())
            qty.set("")
            inputType.value = INPUT_SERIAL
            if (serialNo.get().toString().trim().isNotEmpty()) {
                //填充商品后自动查货品
                getMaterialList()
            }
        }
    }

    fun toAdjustDetail() {
        if (CheckUtil.isFastDoubleClick()) {
            val it = Intent()
            it.putExtra("RespAdjustList", respAdjustList)
            toActivity(it, InvReconciliationDetailListActivity::class.java)
        }
    }

    //切换重置
    fun reset() {
        locCode.set("")
        serialNo.set("")
        qty.set("")
        isShowGoods.set(false)
    }

    fun getStatusCode() {
        DCUtils.goodsStatueN2C[statusName.get()]?.let {
            statusCode.set(it.toString())
        }
    }

    fun setCdprQuantity(unitName: String) {
        var packageDetail = setUnit.value?.find { unitName == it.unitName }
        curUnit = packageDetail?.unitCode ?: ""
        isPoint.value = isPoint.value
        cdprQuantity = if (packageDetail != null) {
            packageDetail.qty ?: BigDecimal.ONE
        } else {
            BigDecimal.ONE
        }
    }

    /**
     * 校验库位
     */
    private fun checkLocCode() {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getWareManageAPI()
                    .checkLocCode(ReqCheckLocCode(locCode = locCode.get().toString().trim()))
            }

            if (result.code == 0L) {
                inputType.value = INPUT_SERIAL
                onRefreshCommand.onRefresh() //库位校验通过后，获取库位明细列表
            } else {
                locCode.set("")
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    /**
     * 货品条码查货品
     */
    private fun getMaterialList() {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getWareManageAPI()
                    .getMaterialList(
                        ReqGetMaterialList(
                            customerCode = respAdjustList?.customerCode ?: "",
                            keyWord = serialNo.get().toString().trim()
                        )
                    )
            }

            if (result.code == 0L) {
                result.data?.let {
                    when (it.size) {
                        0 -> {
                            serialNo.set("")
                            showNotification("暂无该货品", false)
                        }
                        1 -> {
                            serialNo.set(it.getOrNull(0)?.custItemCode ?: "")
                            anntoItemCode.set(it.getOrNull(0)?.itemCode ?: "")
                            custItemCode.set(it.getOrNull(0)?.custItemCode ?: "")
                            itemName.set(it.getOrNull(0)?.itemName ?: "")
                            tempMaterialList = it.getOrNull(0)
                            isPoint.value = it.getOrNull(0)?.isPoint ?: ""
                            getAttributeList(it.getOrNull(0))
                        }
                        else -> {
                            showGoods.value = it
                        }
                    }
                }
            } else {
                serialNo.set("")
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    /**
     * 获取包装和批次属性明细
     */
    fun getAttributeList(data: RespMaterialList?) {
        isShowGoods.set(true)
        cdpaFormat.set("")
        lotDetailsStr.set("")
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getWareManageAPI()
                    .getAttributeList(
                        ReqGetAttributeList(
                            cdpaCode = data?.cdcmPackageCode ?: "",
                            lotCode = data?.cdcmLot ?: "",
                            itemCode = data?.itemCode ?: ""
                        )
                    )
            }

            if (result.code == 0L) {
                result.data?.let { data ->
                    if (data.packageDetails.isNullOrEmpty()) {
                        showNotification("请先维护商品包装信息", false)
                        return@launch
                    }

                    attributeBean = data

                    data.packageDetails?.let {
                        setUnit.value = it.toMutableList()
                        cdpaFormat.set(it.getOrNull(0)?.cdpaFormat ?: "")
                        lotDetailsStr.set("")
                    }

                    template = templates["${locCode.get()}${serialNo.get()}"]
                    if (template == null || lotType == NO_RESERVED_LOTATT) {
                        data.lotDetails?.let {
                            lotDetail.value = it.toMutableList()
                        }
                    } else {
                        //同SKU商品属性信息只采集一次，后续同一个单同库位扫同SKU自动默认
                        lotDetailsStr.set(handleLotAttStr(template))

                        //若勾选逐件扫描，则扫描商品成功后数量栏位默认设置为1,直接走 新增调账明细 接口
                        if (scanType == PIECE_SCAN) {
                            setQtyNotification.value = true
                        } else if (unitInputType == MULTIPLE_UNIT_INPUT) {
                            inputType.value = INPUT_QTY
                            showDialogLiveEvent.value  =Unit
                        } else {
                            inputType.value = INPUT_QTY
                        }
                    }
                }
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    /**
     * 库位明细列表
     */
    fun initOrderList() {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
                isRefreshing.set(false)
            }) {

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getWareManageAPI()
                    .adDetailPage(
                        Constants.whInfo?.whCode.toString(),
                        pageNo,
                        10,
                        locCode.get().toString().trim(),
                        title.value,
                        null
                    )
            }

            if (result.code == 0L) {
                loadMoreComplete.value = 1

                result.data?.let { data ->

                    data.list?.forEach {
                        val arr = arrayListOf<String>()
                        with(it) {
                            if (!lotAtt01.isNullOrEmpty()) {
                                lotAtt01 = lotAtt01?.split(" ")?.getOrNull(0) ?: ""
                                arr.add("生产日期: $lotAtt01")
                            }
                            if (!lotAtt02.isNullOrEmpty()) {
                                lotAtt02 = lotAtt02?.split(" ")?.getOrNull(0) ?: ""
                                arr.add("失效日期: $lotAtt02")
                            }
                            if (!lotAtt03.isNullOrEmpty()) {
                                lotAtt03 = lotAtt03?.split(" ")?.getOrNull(0) ?: ""
                                arr.add("入库日期: $lotAtt03")
                            }
                            if (!lotAtt04Cn.isNullOrEmpty()) arr.add(lotAtt04Cn)
                            if (!lotAtt05.isNullOrEmpty()) arr.add("批次: $lotAtt05")
                            if (!lotAtt06.isNullOrEmpty()) arr.add("批次属性06: $lotAtt06")
                            if (!lotAtt07.isNullOrEmpty()) arr.add("批次属性07: $lotAtt07")
                            if (!lotAtt08.isNullOrEmpty()) arr.add("批次属性08: $lotAtt08")
                            if (!lotAtt09.isNullOrEmpty()) arr.add("批次属性09: $lotAtt09")
                            if (!lotAtt10.isNullOrEmpty()) arr.add("批次属性10: $lotAtt10")
                            if (!lotAtt11.isNullOrEmpty()) arr.add("批次属性11: $lotAtt11")
                            if (!lotAtt12.isNullOrEmpty()) arr.add("批次属性12: $lotAtt12")
                            lotAttStr = arr.joinToString(separator = " | ")
                        }
                    }

                    if (pageNo == 1) {
                        showDatas.value = data.list
                    } else {
                        loadMoreDatas.value = data.list
                    }
                    if (pageNo < data.totalPage) {
                        pageNo++
                    } else {
                        loadMoreComplete.value = 2
                    }
                }
            } else {
                result.msg?.let { showNotification(it, false) }
                if (pageNo == 1) {
                    val emptyList = mutableListOf<RespAdjustDetailPage>()
                    showDatas.value = emptyList
                }
            }
        }
    }

    /**
     * 新增明细
     */
    fun addDetail(num: BigDecimal? = null) {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf<String, Any>()
            param["whCode"] = Constants.whInfo?.whCode.toString()
            param["adNo"] = title.value.toString()
            param["itemCode"] = anntoItemCode.get().toString()
            param["locCode"] = locCode.get().toString()
            param["actQty"] = num ?: AppUtils.getBigDecimalValue(qty.get().toString())
                .multiply(cdprQuantity)

            template?.forEach {
                param[when (it.lotAtt) {
                    "LOT_ATT01" -> "lotAtt01"
                    "LOT_ATT02" -> "lotAtt02"
                    "LOT_ATT03" -> "lotAtt03"
                    "LOT_ATT04" -> "lotAtt04"
                    "LOT_ATT05" -> "lotAtt05"
                    "LOT_ATT06" -> "lotAtt06"
                    "LOT_ATT07" -> "lotAtt07"
                    "LOT_ATT08" -> "lotAtt08"
                    "LOT_ATT09" -> "lotAtt09"
                    "LOT_ATT10" -> "lotAtt10"
                    "LOT_ATT11" -> "lotAtt11"
                    "LOT_ATT12" -> "lotAtt12"
                    else -> ""
                }] = if ("LOT_ATT04" == it.lotAtt) statusCode.get() ?: "" else it.value ?: ""
            }

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getWareManageAPI()
                    .addDetail(requestBody)
            }

            if (result.code == 0L) {
                showNotification("调账明细提交成功", true)
                serialNo.set("")
                qty.set("")
                inputType.value = INPUT_SERIAL
                resetGoodsUnit.value = true
                onRefreshCommand.onRefresh() //新增明细成功后，刷新库位明细列表
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    /**
     * 作废明细
     */
    fun reduceDetail(id: Long?, actQty: BigDecimal?, onSuccess: () -> Unit) {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getWareManageAPI()
                    .reduceDetail(
                        ReqReduceDetail(
                            id = id.toString(),
                            actQty = AppUtils.getBigDecimalValueStr(actQty)
                        )
                    )
            }

            if (result.code == 0L) {
                withContext(Dispatchers.Main) {
                    onSuccess()
                }
                onRefreshCommand.onRefresh() //作废明细成功后，刷新库位明细列表
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    fun handleLotAttStr(lotDetail: MutableList<LotDetail>?): String {
        val arr = arrayListOf<String>()

        lotDetail?.forEach {
            if ("LOT_ATT04" == it.lotAtt) {
                arr.add(statusName.get() ?: "")
            } else if (!it.value.isNullOrEmpty()) {
                arr.add("${it.title}: ${it.value}")
            }
        }

        return arr.joinToString(separator = " | ")
    }

    private fun checkSystemLoc(loc: String): Boolean =
        loc in listOf("RECV01", "PICK01", "SHIP01", "MOV01", "IRA")
}