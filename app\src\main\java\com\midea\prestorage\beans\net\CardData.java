package com.midea.prestorage.beans.net;

public class CardData {

    // 待收货
    String forReceiving ;

    // 待备货
    String forStockUp ;

    // 待交接
    String forEngineer ;

    // 异常订单
    String exception ;


    public String getForReceiving() {
        return forReceiving;
    }

    public void setForReceiving(String forReceiving) {
        this.forReceiving = forReceiving;
    }

    public String getForStockUp() {
        return forStockUp;
    }

    public void setForStockUp(String forStockUp) {
        this.forStockUp = forStockUp;
    }

    public String getForEngineer() {
        return forEngineer;
    }

    public void setForEngineer(String forEngineer) {
        this.forEngineer = forEngineer;
    }

    public String getException() {
        return exception;
    }

    public void setException(String exception) {
        this.exception = exception;
    }
}
