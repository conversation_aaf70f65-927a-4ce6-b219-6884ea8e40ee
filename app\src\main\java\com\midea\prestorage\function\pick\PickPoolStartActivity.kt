package com.midea.prestorage.function.pick

import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.View.MeasureSpec
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.OutPickPoolStorageDetail
import com.midea.prestoragesaas.databinding.ActivityPickPoolStartBinding
import com.midea.prestorage.function.pick.fragment.PickPoolStartFragment


class PickPoolStartActivity : BaseActivity() {

    private lateinit var binding: ActivityPickPoolStartBinding
    private var vm = PickPoolStartVM(this)
    var adapter = MyAdapter(this)
    private var mPosition = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_pick_pool_start)
        binding.vm = vm

        initVp()
        vm.init()
    }

    fun initSw() {
        binding.sw.setOnCheckedListener {
            vm.changeMode(it)
        }
    }

    private fun initVp() {
        binding.vp.adapter = adapter
        binding.vp.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                mPosition = position
            }
        })
    }

    class MyAdapter(activity: FragmentActivity) : FragmentStateAdapter(activity) {
        private var dates: MutableList<ArrayList<OutPickPoolStorageDetail.DetailResponses>>? = null
        val fragments = mutableListOf<PickPoolStartFragment>()

        override fun getItemCount(): Int {
            return if (dates == null) 0 else dates!!.size
        }

        override fun createFragment(position: Int): Fragment {
            return fragments[position]
        }

        fun setPageData(
            detailResponses: MutableList<ArrayList<OutPickPoolStorageDetail.DetailResponses>>,
            data: Boolean
        ) {
            dates = detailResponses
            fragments.clear()
            detailResponses.forEachIndexed { index, it ->
                fragments.add(
                    PickPoolStartFragment.newInstance(
                        it,
                        if (data) 0 else 1,
                        index,
                        dates!!.size
                    )
                )
            }
        }

        fun setStart() {
            fragments.forEach {
                it.startUp()
            }
        }

        fun setStarted() {
            fragments.forEach {
                it.startedUp()
            }
        }
    }

    override fun onResume() {
        super.onResume()

        vm.initData()
    }

    /**
     * 显示提示框
     */
    fun showPop() {
        val popView = LayoutInflater.from(this).inflate(R.layout.pop_view_tip, null)

        val popupWindow = PopupWindow(
            popView,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        popupWindow.setBackgroundDrawable(
            ColorDrawable(
                ContextCompat.getColor(
                    this,
                    R.color.transparent
                )
            )
        )
        popupWindow.isOutsideTouchable = true
        popView.setOnClickListener {
            popupWindow.dismiss()
        }

        binding.vp.post {
            popView.measure(MeasureSpec.UNSPECIFIED, MeasureSpec.UNSPECIFIED)
            val popupWidth: Int = popView.measuredWidth
            val popupHeight: Int = popView.measuredHeight
            val location = IntArray(2)
            binding.vp.getLocationOnScreen(location)
            popupWindow.showAtLocation(
                binding.vp,
                Gravity.NO_GRAVITY,
                location[0] + binding.vp.width / 2 - popupWidth / 2,
                location[1] - popupHeight
            )
        }

        popView.findViewById<View>(R.id.tv_do_not_notice).setOnClickListener {
            vm.noticeOk()
            popupWindow.dismiss()
        }
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun setListEnable() {
        adapter.setStart()
        adapter.notifyDataSetChanged()
    }

    fun setStartUnable() {
        binding.tvStartPick.isEnabled = false
        binding.tvStartPick.setBackgroundResource(R.drawable.bg_bt_gray)
    }

    fun setStartToEnd() {
        binding.tvStartPick.tag = "end"
        binding.tvStartPick.text = "结束"
    }

    fun getBtnTag(): Any? {
        return binding.tvStartPick.tag
    }

    fun showWaveMode(
        detailResponses: MutableList<ArrayList<OutPickPoolStorageDetail.DetailResponses>>,
        data: Boolean
    ) {
        //不换适配器搞不定，没办法
        adapter = MyAdapter(this)
        binding.vp.adapter = adapter
        adapter.setPageData(detailResponses, data)
        adapter.notifyDataSetChanged()
    }

    fun getShowMode(): Boolean {
        return binding.sw.isChecked
    }

    fun addPickNum(requestQty: Int) {
        val toInt = vm.checked.get()!!.toInt()
        vm.checked.set((toInt + requestQty).toString())
    }

    fun next() {
        val currentItem = binding.vp.currentItem
        if (adapter.itemCount > currentItem + 1) {
            binding.vp.currentItem = currentItem + 1
        }
    }

    fun cancelDisable() {
        binding.tvCancelPick.setBackgroundResource(R.drawable.bg_bt_gray)
        binding.tvCancelPick.isEnabled = false
    }

    fun cancelEnable() {
        binding.tvCancelPick.setBackgroundResource(R.drawable.bg_bt_orange)
        binding.tvCancelPick.isEnabled = true
    }

    fun checkStatue() {
        if (!adapter.fragments.none { it.isAllPick() }) {
            vm.changeStatue()
        }
    }
}