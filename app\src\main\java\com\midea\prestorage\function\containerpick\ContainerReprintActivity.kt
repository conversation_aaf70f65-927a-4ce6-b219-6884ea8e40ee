package com.midea.prestorage.function.containerpick

import android.os.Bundle
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.beans.net.ContainerPickSecondPrintList
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.sendcheck.ActivitySerialCheckReprintUnionBinding
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityContainerReprintBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class ContainerReprintActivity : BaseActivity() {

    lateinit var binding: ActivityContainerReprintUnionBinding
    private var vm: ContainerReprintVM? = null
    val adapter = OutPoolStorageAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityContainerReprintUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_container_reprint_care
                )
            )
        } else {
            ActivityContainerReprintUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_container_reprint
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        binding.vm = ContainerReprintVM(this)
        vm = binding.vm

        lifecycleScope.launch {
            while (Constants.isShowContainerPick) {
                delay(60 * 1000)
                vm?.bluetoothOpen(true)
            }
        }

        initRecycle()
    }

    private fun initRecycle() {
        binding.rv.layoutManager = LinearLayoutManager(this)
        binding.rv.adapter = adapter
    }

    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }

    override fun onResume() {
        super.onResume()
        if (Constants.isShowContainerPick) {
            vm?.bluetoothOpen(true)
        }
    }

    class OutPoolStorageAdapter :
        ListCheckBoxAdapter<ContainerPickSecondPrintList>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_container_reprint_care else R.layout.item_container_reprint) {
        override fun convert(helper: BaseViewHolder, item: BaseItemForPopup) {
            super.convert(helper, item as ContainerPickSecondPrintList)

            val check = helper.getView<ImageView>(R.id.img_goods)
            if (item.isSelected()) {
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    check.setImageResource(R.drawable.select_selected_care)
                } else {
                    check.setImageResource(R.drawable.ic_check_selected)
                }
            } else {
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    check.setImageResource(R.drawable.select_normal_care)
                } else {
                    check.setImageResource(R.drawable.ic_check_unselect)
                }
            }

            if (AppUtils.isZero(item.printCount)) {
                helper.setText(R.id.tv_print_count, "0")
            } else {
                if (item.lastPrintDate.isNullOrEmpty()) {
                    helper.setText(
                        R.id.tv_print_count,
                        AppUtils.getBigDecimalValueStr(item.printCount)
                    )
                } else {
                    helper.setText(
                        R.id.tv_print_count,
                        AppUtils.getBigDecimalValueStr(item.printCount) + " / " + item.lastPrintDate
                    )
                }
            }

        }
    }
}