package com.midea.prestorage.function.pointjoin

import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.PointDivideDetailBean
import com.midea.prestoragesaas.databinding.ActivityPointJoinDetailBinding
import com.midea.prestorage.function.pointjoin.fragment.PointJoinDialogFragment

/**
 * 网点交接
 */
class PointJoinDetailActivity : BaseActivity() {

    private lateinit var binding: ActivityPointJoinDetailBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_point_join_detail)
        binding.vm = PointJoinDetailVM(this)

        initViewPage()
        binding.vm!!.init()
    }

    private fun initViewPage() {
        val beans = intent.getSerializableExtra("beans") as MutableList<PointDivideDetailBean>
        val adapter = MyAdapter(this, beans)
        adapter.setFragments()
        binding.viewPager.adapter = adapter
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                binding.vm!!.isWaiting.set(position == 0)
                adapter.fragments[position].notified()
            }
        })
        binding.viewPager.post {
            adapter.fragments[0].notified()
        }
    }

    class MyAdapter(
        activity: FragmentActivity,
        private val itemsCount: MutableList<PointDivideDetailBean>
    ) : FragmentStateAdapter(activity) {
        var fragments = mutableListOf<PointJoinDialogFragment>()

        override fun getItemCount(): Int {
            return itemsCount.size
        }

        override fun createFragment(position: Int): Fragment {
            return fragments[position]
        }

        fun setFragments() {
            itemsCount.forEachIndexed { index, pointDivideDetailBean ->
                val fragment = PointJoinDialogFragment.newInstance(
                    pointDivideDetailBean,
                    index,
                    itemsCount.size
                )
                fragments.add(fragment)
            }
        }
    }

    fun next() {
        val position = binding.viewPager.currentItem + 1
        if (binding.viewPager.adapter?.itemCount!! > position) {
            binding.viewPager.currentItem = position
        } else {
            binding.viewPager.currentItem = 0
        }
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }
}