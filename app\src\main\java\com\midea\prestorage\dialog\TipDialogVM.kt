package com.midea.prestorage.dialog

import android.view.View
import androidx.databinding.ObservableField

class TipDialogVM(var dialog: TipDialog) {
    val titleName = ObservableField<String>("")
    val confirmTitle = ObservableField<String>("确认")
    val cancelTitle = ObservableField<String>("取消")
    val msg = ObservableField<String>("")

    var listener: TipDialog.OnTipBack? = null

    /**
     * 确认按钮
     */
    val confirmClick = View.OnClickListener {
        dialog?.dismiss()
        listener?.onConfirmClick()
    }

    /**
     * 取消按钮
     */
    var cancelClick = View.OnClickListener {
        dialog?.dismiss()
        listener?.onDismissClick()
    }

    var cancelClickNoBack = View.OnClickListener {
        dialog?.dismiss()
    }
}