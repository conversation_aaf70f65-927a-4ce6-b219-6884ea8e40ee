package com.midea.prestorage.function.barcode.dialog

import android.app.Application
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Transformations
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestoragesaas.BuildConfig

class DeleteCollectionBarcodeDialogVM(application: Application) : BaseViewModel(application) {
    val title = ObservableField<String>("提示")
    val etInfo = MutableLiveData("")
    var startScan = MutableLiveData<Boolean>(false)
    var isDissmiss = MutableLiveData<Boolean>(false)
    var content = MutableLiveData<String>("")
    val displayClearBarcode = Transformations.map(etInfo) {
        it.isNotEmpty()
    }

    fun onEnterAnyCode() {
        if (!BuildConfig.DEBUG || CheckUtil.isFastDoubleClick()) {
            if (etInfo.value.isNullOrBlank()) {
                return
            }
            etInfo.value?.let { content.value = it.trim() }
        }
    }

    fun close() {
        isDissmiss.value = true
    }

    fun confirm() {
        if (!BuildConfig.DEBUG || CheckUtil.isFastDoubleClick()) {
            if (etInfo.value.isNullOrBlank()) {
                return
            }
            etInfo.value?.let { content.value = it.trim() }
        }
    }

    fun startScan() {
        if (CheckUtil.isFastDoubleClick()) {
            startScan.value = true
        }
    }

    fun clearBarcode() {
        etInfo.value = ""
    }

    override fun init() {

    }

}