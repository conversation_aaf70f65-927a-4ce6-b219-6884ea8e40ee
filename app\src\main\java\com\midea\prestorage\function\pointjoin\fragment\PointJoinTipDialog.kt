package com.midea.prestorage.function.pointjoin.fragment

import android.app.AlertDialog
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestoragesaas.databinding.DialogTipPointJoinBinding

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/6/26$
 */
class PointJoinTipDialog(
    var mContext: BaseActivity
) : AlertDialog(mContext) {

    private var binding: DialogTipPointJoinBinding

    init {
        val contentView =
            LayoutInflater.from(mContext).inflate(R.layout.dialog_tip_point_join, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = PointJoinTipDialogVM(this)

        setCanceledOnTouchOutside(true)
    }

    fun setTitle(title: String) {
        binding.vm!!.titleName.set(title)
    }

    fun setMsg(msg: String) {
        binding.vm!!.msg.set(msg)
    }

    fun setOnTipBackListener(listener: OnTipBack) {
        binding.vm!!.listener = listener
    }

    interface OnTipBack {
        fun onConfirmClick()
        fun onDismissClick()
    }
}
