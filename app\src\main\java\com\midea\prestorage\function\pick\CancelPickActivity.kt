package com.midea.prestorage.function.pick

import android.os.Bundle
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestoragesaas.databinding.ActivityCancelPickBinding


class CancelPickActivity : BaseActivity() {

    private lateinit var binding: ActivityCancelPickBinding
    private var vm = CancelPickVM(this)
    var adapter = OutCancelDetailAdapter(this, ArrayList())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_cancel_pick)
        binding.vm = vm

        vm.init()
        initRecycle()
    }

    private fun initRecycle() {
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter

        adapter.setOnItemChildClickListener { adapter, _, position ->
            val bean = adapter.data[position] as OutCancelDetailHelp
            if (bean.title != null) {
                vm.cancelPick(bean.title)
            }
        }
    }

    fun setTvStatue(color: Int) {
        binding.tvStatus.setTextColor(ContextCompat.getColor(this, color))
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun setNewData(beans: MutableList<OutCancelDetailHelp>) {
        adapter.setOriginDate(beans)
        adapter.notifyDataSetChanged()
    }
}