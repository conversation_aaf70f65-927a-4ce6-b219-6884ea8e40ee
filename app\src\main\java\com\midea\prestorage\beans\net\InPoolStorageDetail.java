package com.midea.prestorage.beans.net;

import java.io.Serializable;
import java.util.List;

public class InPoolStorageDetail implements Serializable {

    private String waveNo;
    private String totalQty;
    private String totalVolume;
    private String totalWeight;
    private List<OrderInfoList> headerVOList;

    public class OrderInfoList {
        private String waveNo;
        private String custOrderNo;
        private String totalQty;
        private String receiptType;
        private String receiptCode;
        private String carNo;
        private String dispatchNo;
        private String status;
        private String pickupTime;
        private String unscanMark;
        private String totalVolume;
        private String totalWeight;
        private String whCode;
        private String ownerCode;
        private String siteCode;
        private String siteName;

        private boolean isChecked;

        public String getWhCode() {
            return whCode;
        }

        public void setWhCode(String whCode) {
            this.whCode = whCode;
        }

        public String getOwnerCode() {
            return ownerCode;
        }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
        }

        public String getSiteCode() {
            return siteCode;
        }

        public void setSiteCode(String siteCode) {
            this.siteCode = siteCode;
        }

        public String getSiteName() {
            return siteName;
        }

        public void setSiteName(String siteName) {
            this.siteName = siteName;
        }

        private List<OrderInfoDetailsList> receiptDetailVOList;

        public boolean isChecked() {
            return isChecked;
        }

        public void setChecked(boolean checked) {
            isChecked = checked;
        }

        public class OrderInfoDetailsList {
            private String totalQty;
            private String receiptQty;
            private String unit;
            private String custItemCode;
            private String lotAtt04;
            private String itemName;
            private String receiptCode;

            public OrderInfoDetailsList copy() {
                OrderInfoDetailsList item = new OrderInfoDetailsList();
                item.totalQty = totalQty;
                item.unit = unit;
                item.custItemCode = custItemCode;
                item.lotAtt04 = lotAtt04;
                item.itemName = itemName;
                item.receiptCode = receiptCode;
                item.receiptQty = receiptQty;
                return item;
            }

            public String getTotalQty() {
                return totalQty;
            }

            public void setTotalQty(String totalQty) {
                this.totalQty = totalQty;
            }

            public String getUnit() {
                return unit;
            }

            public void setUnit(String unit) {
                this.unit = unit;
            }

            public String getCustItemCode() {
                return custItemCode;
            }

            public void setCustItemCode(String custItemCode) {
                this.custItemCode = custItemCode;
            }

            public String getLotAtt04() {
                return lotAtt04;
            }

            public void setLotAtt04(String lotAtt04) {
                this.lotAtt04 = lotAtt04;
            }

            public String getItemName() {
                return itemName;
            }

            public void setItemName(String itemName) {
                this.itemName = itemName;
            }

            public String getReceiptCode() {
                return receiptCode;
            }

            public void setReceiptCode(String receiptCode) {
                this.receiptCode = receiptCode;
            }

            public String getReceiptQty() {
                return receiptQty;
            }

            public void setReceiptQty(String receiptQty) {
                this.receiptQty = receiptQty;
            }
        }

        public List<OrderInfoDetailsList> getReceiptDetailVOList() {
            return receiptDetailVOList;
        }

        public void setReceiptDetailVOList(List<OrderInfoDetailsList> receiptDetailVOList) {
            this.receiptDetailVOList = receiptDetailVOList;
        }

        public String getWaveNo() {
            return waveNo;
        }

        public void setWaveNo(String waveNo) {
            this.waveNo = waveNo;
        }

        public String getCustOrderNo() {
            return custOrderNo;
        }

        public void setCustOrderNo(String custOrderNo) {
            this.custOrderNo = custOrderNo;
        }

        public String getTotalQty() {
            return totalQty;
        }

        public void setTotalQty(String totalQty) {
            this.totalQty = totalQty;
        }

        public String getReceiptType() {
            return receiptType;
        }

        public void setReceiptType(String receiptType) {
            this.receiptType = receiptType;
        }

        public String getReceiptCode() {
            return receiptCode;
        }

        public void setReceiptCode(String receiptCode) {
            this.receiptCode = receiptCode;
        }

        public String getCarNo() {
            return carNo;
        }

        public void setCarNo(String carNo) {
            this.carNo = carNo;
        }

        public String getDispatchNo() {
            return dispatchNo;
        }

        public void setDispatchNo(String dispatchNo) {
            this.dispatchNo = dispatchNo;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getPickupTime() {
            return pickupTime;
        }

        public void setPickupTime(String pickupTime) {
            this.pickupTime = pickupTime;
        }

        public String getUnscanMark() {
            return unscanMark;
        }

        public void setUnscanMark(String unscanMark) {
            this.unscanMark = unscanMark;
        }

        public String getTotalVolume() {
            return totalVolume;
        }

        public void setTotalVolume(String totalVolume) {
            this.totalVolume = totalVolume;
        }

        public String getTotalWeight() {
            return totalWeight;
        }

        public void setTotalWeight(String totalWeight) {
            this.totalWeight = totalWeight;
        }
    }

    public List<OrderInfoList> getHeaderVOList() {
        return headerVOList;
    }

    public void setHeaderVOList(List<OrderInfoList> headerVOList) {
        this.headerVOList = headerVOList;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(String totalQty) {
        this.totalQty = totalQty;
    }

    public String getTotalVolume() {
        return totalVolume;
    }

    public void setTotalVolume(String totalVolume) {
        this.totalVolume = totalVolume;
    }

    public String getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(String totalWeight) {
        this.totalWeight = totalWeight;
    }
}
