package com.midea.prestorage.function.main

import android.annotation.SuppressLint
import android.content.Intent
import android.text.TextUtils
import android.util.Log
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestoragesaas.R
import com.midea.prestorage.beans.help.HeaderInfo
import com.midea.prestorage.beans.net.AuthBean
import com.midea.prestorage.beans.net.CardData
import com.midea.prestorage.beans.net.FuShipmentStatue
import com.midea.prestorage.beans.net.ProfileSetting
import com.midea.prestorage.beans.setting.ImplWarehouse
import com.midea.prestorage.beans.setting.ProfileSettingDbV2
import com.midea.prestorage.dialog.TipNoCancelDialog
import com.midea.prestorage.function.arriving.ArrivingActivity
import com.midea.prestorage.function.inpool.InPoolStorageActivity
import com.midea.prestorage.function.instorage.InStorageActivity
import com.midea.prestorage.function.inv.*
import com.midea.prestorage.function.main.dialog.MsgDialog
import com.midea.prestorage.function.mainyg.MainYgActivity
import com.midea.prestorage.function.outpool.OutPoolStorageActivity
import com.midea.prestorage.function.outstorage.OutStorageActivity
import com.midea.prestorage.function.pick.PickPoolStorageActivity
import com.midea.prestorage.function.planstock.PlanStockActivity
import com.midea.prestorage.function.pointjoin.PointJoinActivity
import com.midea.prestorage.function.unscan.CheckUnScanActivity
import com.midea.prestorage.function.waybill.WaybillSearchActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DbUtils
import com.midea.prestorage.utils.MySoundUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.worker.ApkDownloadWorker
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import okhttp3.MediaType
import okhttp3.RequestBody
import java.text.SimpleDateFormat
import java.util.*

@SuppressLint("SimpleDateFormat")
class MainVM(val activity: MainActivity) {
    val storage = ObservableField<String>("请选择仓库")

    val textDaiShouHuo = ObservableField<String>("...")
    val textDaiBeiHuo = ObservableField<String>("...")
    val textDaiJiaoJie = ObservableField<String>("...")
    val textYiChangDingDan = ObservableField<String>("0")

    private val msgDialog: MsgDialog = MsgDialog(activity)

    var curProfileSetting: ProfileSetting? = null

    val db = DbUtils.db

    private var tipNoCancelDialog: TipNoCancelDialog? = null

    val codes = mutableListOf<String>()

    init {
        initData()

        tipNoCancelDialog = TipNoCancelDialog(activity)
    }

    // 四个小卡片 数据
    fun loadCardData(isClick: Boolean) {


        /*
           旧需求 如果当前时间 <18:00 取当前时间  >18:00 则取 18:00
           if (calendarEnd.get(Calendar.HOUR_OF_DAY) >= 18) {
               calendarEnd.set(Calendar.HOUR_OF_DAY, 18)
           }
        */

        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

        val calendarStart = getStartTime()
        val startTime = dateFormat.format(calendarStart.time)

        val calendarEnd = getEndTime()
        val endTime = dateFormat.format(calendarEnd.time)

        if (isClick) {
            AppUtils.showToast(activity, "取数时间:\n$startTime 到 $endTime")
        }

        if (Constants.whInfo == null || Constants.whInfo?.whCode.isNullOrEmpty()) {
            return
        }
        RetrofitHelper.getAppAPI()
            .queryCardData(activity.getWhCode(), startTime, endTime)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<CardData>(activity) {
                override fun success(data: CardData?) {
                    // activity.waitingDialogHelp.hidenDialog()
                    if (data != null) {
                        //待收货
                        if (!data.forReceiving.isNullOrBlank()) {
                            textDaiShouHuo.set(data.forReceiving)
                        }

                        // 待备货
                        if (!data.forStockUp.isNullOrBlank()) {
                            textDaiBeiHuo.set(data.forStockUp)
                        }

                        // 待交接
                        if (!data.forEngineer.isNullOrBlank()) {
                            textDaiJiaoJie.set(data.forEngineer)
                        }

                        // 异常
                        if (!data.exception.isNullOrBlank()) {
                            textYiChangDingDan.set(data.exception)
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    // activity.waitingDialogHelp.hidenDialog()
                    //ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                    textDaiShouHuo.set("...")
                    textDaiBeiHuo.set("...")
                    textDaiJiaoJie.set("...")
                    textYiChangDingDan.set("...")
                }
            })
    }

    fun getStartTime(): Calendar {
        var curProfileSetting = ProfileSetting()
        val listDbRecords = db.findAll(ProfileSettingDbV2::class.java)
        if (null == listDbRecords || listDbRecords.size == 0) {
            curProfileSetting.dashboardStartDay = ProfileActivity.defaultStartDay
            curProfileSetting.dashboardEndDay = ProfileActivity.defaultEndDay
            curProfileSetting.dashboardStartHour = ProfileActivity.defaultStartHour
            curProfileSetting.dashboardEndHour = ProfileActivity.defaultEndHour
        } else {
            val jsonStr = listDbRecords.get(0)!!.setting
            curProfileSetting = Gson().fromJson(jsonStr, ProfileSetting::class.java)
        }


        // 开始时间  昨日18:00
        val calendarStart = Calendar.getInstance()
        // 开始日 =  当前日 - 设定中的startDay
        calendarStart.set(
            Calendar.DAY_OF_YEAR,
            calendarStart.get(Calendar.DAY_OF_YEAR) - curProfileSetting.dashboardStartDay
        )
        // 开始小时
        calendarStart.set(
            Calendar.HOUR_OF_DAY,
            curProfileSetting.dashboardStartHour.split(":")[0].toInt()
        )
        // 开始分钟
        calendarStart.set(
            Calendar.MINUTE,
            curProfileSetting.dashboardStartHour.split(":")[1].toInt()
        )
        // 秒
        calendarStart.set(Calendar.SECOND, 0)
        return calendarStart
    }

    fun getEndTime(): Calendar {
        var curProfileSetting = ProfileSetting()
        val listDbRecords = db.findAll(ProfileSettingDbV2::class.java)
        if (null == listDbRecords || listDbRecords.size == 0) {
            curProfileSetting.dashboardStartDay = ProfileActivity.defaultStartDay
            curProfileSetting.dashboardEndDay = ProfileActivity.defaultEndDay
            curProfileSetting.dashboardStartHour = ProfileActivity.defaultStartHour
            curProfileSetting.dashboardEndHour = ProfileActivity.defaultEndHour
        } else {
            val jsonStr = listDbRecords.get(0)!!.setting
            curProfileSetting = Gson().fromJson(jsonStr, ProfileSetting::class.java)
        }

        // 结束时间
        val calendarEnd = Calendar.getInstance()
        // 结束日 =  当前日 - 设定中的endDay
        calendarEnd.set(
            Calendar.DAY_OF_YEAR,
            calendarEnd.get(Calendar.DAY_OF_YEAR) - curProfileSetting.dashboardEndDay
        )
        // 结束小时
        calendarEnd.set(
            Calendar.HOUR_OF_DAY,
            curProfileSetting.dashboardEndHour.split(":")[0].toInt()
        )
        // 结束分钟
        calendarEnd.set(Calendar.MINUTE, curProfileSetting.dashboardEndHour.split(":")[1].toInt())
        // 秒
        calendarEnd.set(Calendar.SECOND, 0)

        return calendarEnd
    }

    // 带查询条件 跳入 入库订单池
    fun jumpToInReceiptPool() {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        val calendarStart = getStartTime()
        val beginTime = dateFormat.format(calendarStart.time)
        val calendarEnd = getEndTime()
        val endTime = dateFormat.format(calendarEnd.time)

        val intent = Intent(activity, InPoolStorageActivity::class.java)
        intent.putExtra("beginTime", beginTime)
        intent.putExtra("endTime", endTime)
        intent.putExtra("fromMainActivity", true)

        activity.startActivity(intent)
    }


    // 带查询条件 跳入 出库订单池
    fun jumpToOutShipmentPool() {

        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        val calendarStart = getStartTime()
        val beginTime = dateFormat.format(calendarStart.time)
        val calendarEnd = getEndTime()
        val endTime = dateFormat.format(calendarEnd.time)

        val intent = Intent(activity, OutPoolStorageActivity::class.java)
        intent.putExtra("beginTime", beginTime)
        intent.putExtra("endTime", endTime)
        intent.putExtra("fromMainActivity", true)

        activity.startActivity(intent)
    }

    private fun initData() {
        Observable.create<ImplWarehouse> {
            if (Constants.userInfo?.whCode.isNullOrEmpty()) {
                it.onNext(ImplWarehouse())
            } else {
                val findFirst = db.selector(ImplWarehouse::class.java)
                    .where("whCode", "==", Constants.userInfo?.whCode).findFirst()
                if (findFirst != null) {
                    //清除掉所有的已经选中的数据
                    val allCheckBeans = db.selector(ImplWarehouse::class.java)
                        .where("isChecked", "==", true).findAll()
                    allCheckBeans?.forEach { item ->
                        item.isChecked = false
                    }
                    findFirst.isChecked = true
                    if (!allCheckBeans.isNullOrEmpty()) {
                        db.saveOrUpdate(allCheckBeans)
                    }

                    db.saveOrUpdate(findFirst)
                    it.onNext(findFirst)
                } else {
                    it.onNext(
                        ImplWarehouse(
                            Constants.userInfo?.whName,
                            Constants.userInfo?.whCode
                        )
                    )
                }
            }
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<ImplWarehouse?> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: ImplWarehouse) {
                    if (!TextUtils.isEmpty(t.whCode)) {
                        storage.set(t.cdwhName)
                        activity.whCancelAble(true)
                        t.isChecked = true
                        Constants.whInfo = t

                        initWhDc(t)
                        uploadDevicesInfo()
                    } else {
                        // 弹框选仓库
                        activity.whCancelAble(false)
                        initWhDc(null)
                    }
                }

                override fun onError(e: Throwable) {
                }
            })

        initAuth()
    }

    fun showWhCode() {
        val allWh = db.findAll(ImplWarehouse::class.java)
        if (allWh.isNullOrEmpty()) {
            resetWhCodeInfo(true)
        } else {
            showStorage(allWh)
        }
    }

    private fun initWhDc(t: ImplWarehouse?) {
        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getDirectionAPI()
            .fuShipmentStatus("FU_WH_TYPE")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<FuShipmentStatue>>(activity) {
                override fun success(data: MutableList<FuShipmentStatue>?) {
                    data?.removeAll { it.enableFlag == 0 }
                    if (data.isNullOrEmpty()) {
                        tipNoCancelDialog?.setTitle("提示")
                        tipNoCancelDialog?.setMsg("暂无仓库权限，请先开通!")
                        tipNoCancelDialog?.setOnTipBackListener(object :
                            TipNoCancelDialog.OnTipNoCancelBack {
                            override fun onConfirmClick() {
                                MySoundUtils.getInstance().dingSound()
                                activity.finish()
                            }
                        })
                        tipNoCancelDialog?.show()
                    } else {
                        codes.addAll(data.map { it.code })
                        if (t != null) {
                            compareWhCodeInfo(t, codes)
                        } else {
                            resetWhCodeInfo(true)
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    AppUtils.showToast(activity, apiErrorModel.message)
                }
            })
    }

    /**
     * 重新拉取网络数据
     */
    fun resetWhCodeInfo(isShowDialog: Boolean) {
        if (isShowDialog) {
            activity.waitingDialogHelp.showDialog()
        }
        val map = mutableMapOf(
            "userCode" to Constants.userInfo?.name,
            "whCategories" to codes
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        RetrofitHelper.getBasicDataAPI()
            .getCodeAndName(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<ImplWarehouse>>(activity) {
                override fun success(result: MutableList<ImplWarehouse>?) {
                    var data = result?.filter { it.cdwhIsStop == 0 }?.toMutableList()
                    if (data.isNullOrEmpty()) {
                        tipNoCancelDialog?.setTitle("提示")
                        tipNoCancelDialog?.setMsg("暂无仓库权限，请先开通!")
                        tipNoCancelDialog?.setOnTipBackListener(object :
                            TipNoCancelDialog.OnTipNoCancelBack {
                            override fun onConfirmClick() {
                                MySoundUtils.getInstance().dingSound()
                                activity.finish()
                            }
                        })
                        tipNoCancelDialog?.show()
                    }
                    data?.forEach {
                        if (it.bearingSystem == "2" || it.bearingSystem == "3") {
                            it.whSystem = "2"
                        } else {
                            it.whSystem = ""
                        }

//                        it.whName = it.cdwhName
                    }
                    saveWhInfo(data)
                    //不要ALL的仓库
                    val result =
                        data?.find { it.whCode.toLowerCase(Locale.getDefault()) == "all" }
                    data?.remove(result)
                    if (isShowDialog) {
                        showStorage(data)
                    }
                    activity.waitingDialogHelp.hidenDialog()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    /**
     * 重新拉取网络数据
     */
    fun compareWhCodeInfo(
        whInfo: ImplWarehouse,
        codes: List<String>
    ) {
        activity.waitingDialogHelp.showDialog()
        val map = mutableMapOf(
            "userCode" to Constants.userInfo?.name,
            "whCategories" to codes
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )
        RetrofitHelper.getBasicDataAPI()
            .getCodeAndName(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<ImplWarehouse>>(activity) {
                override fun success(result: MutableList<ImplWarehouse>?) {
                    var data = result?.filter { it.cdwhIsStop == 0 }?.toMutableList()
                    if (data.isNullOrEmpty()) {
                        tipNoCancelDialog?.setTitle("提示")
                        tipNoCancelDialog?.setMsg("暂无仓库权限，请先开通!")
                        tipNoCancelDialog?.setOnTipBackListener(object :
                            TipNoCancelDialog.OnTipNoCancelBack {
                            override fun onConfirmClick() {
                                MySoundUtils.getInstance().dingSound()
                                activity.finish()
                            }
                        })
                        tipNoCancelDialog?.show()
                    }
                    data?.forEach {
                        if (it.bearingSystem == "2" || it.bearingSystem == "3") {
                            it.whSystem = "2"
                        } else {
                            it.whSystem = ""
                        }

//                        it.whName = it.cdwhName
                    }

                    //不要ALL的仓库
                    val allWh =
                        data?.find { it.whCode.toLowerCase(Locale.getDefault()) == "all" }
                    data?.remove(allWh)

                    data?.let {
                        val result = it.find { item -> item.whCode == whInfo.whCode }
                        if (result?.whSystem != whInfo.whSystem) {
                            Constants.whInfo = result
                            result?.isChecked = true

                            saveWhInfo(data)
                            saveUserInfo()
                        }
                    }

                    activity.waitingDialogHelp.hidenDialog()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    private fun saveWhInfo(list: MutableList<ImplWarehouse>?) {
        Observable.create<String> {
            try {
                db.delete(ImplWarehouse::class.java)
                db.saveOrUpdate(list)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    private fun initAuth() {
        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getBarcodeAPI()
            .getAllResources(Constants.userInfo?.name, "APP202109180029")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<AuthBean>>(activity) {
                override fun success(data: MutableList<AuthBean>?) {
                    if (!data.isNullOrEmpty()) {
                        val auth = data[0].subResources.find { it.resourceUrl == "RF" }
                        if (auth != null) {
                            if (!auth.subResources.isNullOrEmpty()) {
                                val authInStorage =  //入库
                                    auth.subResources.find { it.resourceUrl == "receipt" }
                                val authOutStorage =  //出库
                                    auth.subResources.find { it.resourceUrl == "shipment" }
                                val authStorage =  //库内
                                    auth.subResources.find { it.resourceUrl == "manage" }

                                if (authInStorage?.subResources.isNullOrEmpty()
                                    && authOutStorage?.subResources.isNullOrEmpty()
                                    && authStorage?.subResources.isNullOrEmpty()
                                ) {
                                    tipNoCancelDialog?.setTitle("提示")
                                    tipNoCancelDialog?.setMsg("暂无仓库权限，请先开通!")
                                    tipNoCancelDialog?.setOnTipBackListener(object :
                                        TipNoCancelDialog.OnTipNoCancelBack {
                                        override fun onConfirmClick() {
                                            MySoundUtils.getInstance().dingSound()
                                            activity.finish()
                                        }
                                    })
                                    tipNoCancelDialog?.show()
                                    return
                                }

                                initInStorage(authInStorage)
                                initOutStorage(authOutStorage)
                                initStorage(authStorage)
                            }
                        }
                    }
                    activity.waitingDialogHelp.hidenDialog()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    private fun initInStorage(auths: AuthBean?) {
        auths?.subResources?.forEach {
            val result = authInStorage.find { item -> it.frontUrl == item.tag }
            if (result != null) {
                activity.adapterIn.addData(result)
            }
        }
        activity.adapterIn.notifyDataSetChanged()
    }

    private fun initOutStorage(auths: AuthBean?) {
        auths?.subResources?.forEach {
            val result = authOutStorage.find { item -> it.frontUrl == item.tag }
            if (result != null) {
                activity.adapterOut.addData(result)
            }
        }
        activity.adapterOut.notifyDataSetChanged()
    }

    private fun initStorage(auths: AuthBean?) {
        auths?.subResources?.forEach {
            val result = authStorage.find { item -> it.frontUrl == item.tag }
            if (result != null) {
                activity.adapterStorage.addData(result)
            }
        }
        activity.adapterStorage.notifyDataSetChanged()
    }

    fun checkUpGrade(isClick: Boolean) {
        ApkDownloadWorker.getInstance().checkApkVersion(activity, isClick)
    }


    fun toProfileActivity() {
        val intent = Intent(activity, ProfileActivity::class.java)
        activity.startActivity(intent)
    }

    fun showStorage(list: MutableList<ImplWarehouse>?) {
        list?.forEach {
            it.showInfo = it.cdwhName
        }
        activity.showStorageInfo(list)
    }

    fun back() {
        activity.finish()
    }

    fun whInfoCheck(it: ImplWarehouse) {
        storage.set(it.cdwhName)
        Constants.whInfo = it
        Constants.userInfo?.whName = it.cdwhName
        Constants.userInfo?.whCode = it.whCode
        Constants.userInfo?.whType =
            if (TextUtils.isEmpty(it.getWhSystem())) 4 else it.getWhSystem().toInt()

        activity.whCancelAble(true)
        saveUserInfo()
    }

    private fun saveUserInfo() {
        Observable.create<String> {
            try {
                if (Constants.whInfo != null) {
                    db.saveOrUpdate(Constants.whInfo)
                    db.saveOrUpdate(Constants.userInfo)
                }
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                    jumpToFunction()
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    // 上报登录成功记录
    fun uploadDevicesInfo() {
        if (!Constants.userInfo?.whCode.isNullOrEmpty()) {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

            val map = mutableMapOf(
                "deviceId" to AppUtils.getDevicesId(activity),
                "deviceName" to AppUtils.getDeviceName(),
                "deviceModel" to AppUtils.getDeviceModel(),
                "deviceType" to AppUtils.getDeviceType(activity),
                "systemVersion" to AppUtils.getDeviceAndroidVersion(),
                "appVersion" to AppUtils.getVersionCode(activity),
                "deviceStatus" to "00",
                "sourceSystem" to "AWMS",
                "appLoginTime" to dateFormat.format(Date()),
                "whCode" to Constants.userInfo?.whCode,
                "whName" to Constants.userInfo?.whName,
                "appLoginUser" to Constants.userInfo?.name,
                "appLoginUserName" to Constants.userInfo?.userName
            )
            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(map)
            )
            RetrofitHelper.getBasicDataAPI()
                .saveLoginInfo(requestBody)
                .compose(NetworkScheduler.compose())
                .bindUntilEvent(activity, ActivityEvent.DESTROY)
                .subscribe(object : RequestCallback<Any>(activity) {
                    override fun success(data: Any?) {
                        Log.i("wms", "记录登录记录----成功")
                    }

                    override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                        Log.e("wms", "上报登录记录失败")
                        Log.e("wms", statusCode.toString() + ":" + apiErrorModel.message)
                    }
                })
        }
    }

    fun jumpToFunction() {
        if (Constants.userInfo?.whType == 2) {
            activity.startActivity(Intent(activity, MainYgActivity::class.java))
            activity.finish()
        }
    }

    @SuppressLint("SimpleDateFormat")
    val dateFormat = SimpleDateFormat("yyyy-MM-dd")

    // 带查询条件 跳入 入库订单池
    fun waitingReceive() {
        if (curProfileSetting == null) {
            return
        }
        val calStart = Calendar.getInstance()
        calStart.add(Calendar.DAY_OF_MONTH, -curProfileSetting?.dashboardStartDay!!)
        val calEnd = Calendar.getInstance()
        calEnd.add(Calendar.DAY_OF_MONTH, -curProfileSetting?.dashboardEndDay!!)

        val msg = "待收货说明：\n" +
                "1、定义：本仓需要收货的单据\n" +
                "2、取数时间：${dateFormat.format(calStart.time)} ${curProfileSetting?.dashboardStartHour}" +
                "至${dateFormat.format(calEnd.time)} ${curProfileSetting?.dashboardEndHour}\n" +
                "3、取数范围：订单状态为“创建”“收货中”\n" +
                "4、最小统计单元为订单"

        msgDialog.setTitle("提示")
        msgDialog.setMsg(msg)
        msgDialog.show()
    }

    // 带查询条件 跳入 入库订单池
    fun waitingReady() {
        if (curProfileSetting == null) {
            return
        }
        val calStart = Calendar.getInstance()
        calStart.add(Calendar.DAY_OF_MONTH, -curProfileSetting?.dashboardStartDay!!)
        val calEnd = Calendar.getInstance()
        calEnd.add(Calendar.DAY_OF_MONTH, -curProfileSetting?.dashboardEndDay!!)

        val msg = "待备货说明：\n" +
                "1、定义：本仓需要备货的单据\n" +
                "2、取数时间：${dateFormat.format(calStart.time)} ${curProfileSetting?.dashboardStartHour}" +
                "至${dateFormat.format(calEnd.time)} ${curProfileSetting?.dashboardEndHour}\n" +
                "3、取数范围：订单状态为“创建”“已分配”“拣货中”“拣货完成”\n" +
                "4、最小统计单元为订单"

        msgDialog.setTitle("提示")
        msgDialog.setMsg(msg)
        msgDialog.show()
    }

    // 带查询条件 跳入 入库订单池
    fun waitingForChange() {
        if (curProfileSetting == null) {
            return
        }
        val calStart = Calendar.getInstance()
        calStart.add(Calendar.DAY_OF_MONTH, -curProfileSetting?.dashboardStartDay!!)
        val calEnd = Calendar.getInstance()
        calEnd.add(Calendar.DAY_OF_MONTH, -curProfileSetting?.dashboardEndDay!!)

        val msg = "待发货说明：\n" +
                "1、定义：需要网点/自提人员来完成发货交接的单据\n" +
                "2、取数时间：${dateFormat.format(calStart.time)} ${curProfileSetting?.dashboardStartHour}" +
                "至${dateFormat.format(calEnd.time)} ${curProfileSetting?.dashboardEndHour}\n" +
                "3、取数范围：\n" +
                "     1）配送方式为自提且出库单状态为“已复核”“发货中”的单据；\n" +
                "     2）配送方式为网点直配且CSP已提交提货申请，仓库未确认的单据；\n" +
                "4、最小统计单元为订单"

        msgDialog.setTitle("提示")
        msgDialog.setMsg(msg)
        msgDialog.show()
    }

    // 从本地缓存加载之前的设置  没有的话写入一份默认设置
    fun loadSettingFromDB() {
        Observable.create<Any> {
            try {
                val listDbRecords = db.findAll(ProfileSettingDbV2::class.java)
                if (null == listDbRecords || listDbRecords.size == 0) {
                    curProfileSetting = ProfileSetting()
                    curProfileSetting?.dashboardStartDay = ProfileActivity.defaultStartDay
                    curProfileSetting?.dashboardEndDay = ProfileActivity.defaultEndDay
                    curProfileSetting?.dashboardStartHour = ProfileActivity.defaultStartHour
                    curProfileSetting?.dashboardEndHour = ProfileActivity.defaultEndHour
                } else {
                    val jsonStr = listDbRecords.get(0)!!.setting
                    curProfileSetting = Gson().fromJson(jsonStr, ProfileSetting::class.java)
                }
            } catch (e: Exception) {
                e.message?.let { item -> Log.e("wq", item) }
            }
            //初始化设置的时间
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<Any?> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: Any) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    fun onItemClick(item: HeaderInfo) {
        val intent = Intent(activity, item.cls)
        activity.startActivity(intent)
    }

    //入库
    private val authInStorage = mutableListOf(
        HeaderInfo(
            "/receipt/pool",
            R.drawable.rukudindanci,
            "入库订单池",
            InPoolStorageActivity::class.java
        ),
        HeaderInfo(
            "/receipt/scan",
            R.drawable.qianzhichangshouhuo,
            "收货扫码",
            InStorageActivity::class.java
        ),
        HeaderInfo(
            "/arrival",
            R.drawable.qianzhichangshouhuo,
            "到车登记",
            ArrivingActivity::class.java
        )
    )

    //出库
    private val authOutStorage = mutableListOf(
        HeaderInfo(
            "/shipment/pool",
            R.drawable.rukudindanci,
            "出库订单池",
            OutPoolStorageActivity::class.java
        ),
        HeaderInfo(
            "/shipment/pick/task",
            R.drawable.jianhuo,
            "拣货任务",
            PickPoolStorageActivity::class.java
        ),
        HeaderInfo(
            "/shipment/scan",
            R.drawable.jihuapandian,
            "出库扫码",
            OutStorageActivity::class.java
        ),
        HeaderInfo(
            "/shipment/engineer",
            R.drawable.fahuojiaojie,
            "网点交接",
            PointJoinActivity::class.java
        )
    )

    //库内
    private val authStorage = mutableListOf(
        HeaderInfo(
            "/inventory/query",
            R.drawable.kucunchaxun,
            "库存查询",
            InventorySearchActivity::class.java
        ),
        HeaderInfo(
            "/inventory/transfer",
            R.drawable.jianhuo,
            "移库",
            TransferActivity::class.java
        ),
        HeaderInfo(
            "/inventory/inventoryCheck",
            R.drawable.shishipandian,
            "实时盘点",
            CountInTimeActivity::class.java
        ),
        HeaderInfo(
            "/inventory/attributeAdjustment",
            R.drawable.fahuojiaojie,
            "批次调整",
            LotSearchActivity::class.java
        ),
        HeaderInfo(
            "/barcode/unScanAudit",
            R.drawable.bushaomashenghe,
            "不扫码审核",
            CheckUnScanActivity::class.java
        ),
        HeaderInfo(
            "/barcode/serialTrace",
            R.drawable.ic_search_sn,
            "条码流向",
            SerialNoCheckActivity::class.java
        ),
        HeaderInfo(
            "/shipment/print",
            R.drawable.print_bill,
            "面单打印",
            WaybillSearchActivity::class.java
        ),
        HeaderInfo(
            "/barcode/collect",
            R.drawable.ic_collect_sn,
            "条码采集",
            SerialNoAdjustActivity::class.java
        ),
        HeaderInfo(
            "/inventory/stocktake/new",
            R.drawable.ic_collect_sn,
            "计划盘点新",
            PlanStockActivity::class.java
        ),
        HeaderInfo(
            "/inventory/statusAdjustment",
            R.drawable.fahuojiaojie,
            "状态调整",
            StatusAdjustmentActivity::class.java
        )
    )
}