package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.math.BigDecimal;
import java.util.List;

public class RespExportContainer implements Comparable<RespExportContainer>{


    private String whCode;
    private String waveNo;
    @ShowAnnotation
    private String custItemCode;
    private String whBarcode69;
    private String itemCode;
    @ShowAnnotation
    private String itemName;
    private String lotAtt04;
    @ShowAnnotation
    private BigDecimal planQty;
    @ShowAnnotation
    private BigDecimal pickedQty;
    @ShowAnnotation
    private BigDecimal requestQty;
    private List<Integer> signDetailIds;
    private String shipmentCode;
    private String ownerCode;
    private List<ShipmentSerialsDTO> shipmentSerials;
    private List<String> serialNos;
    private String isCheckContainerNo;
    private String isCheckLockNo;
    private String headerId;

    public String getHeaderId() {
        return headerId;
    }

    public void setHeaderId(String headerId) {
        this.headerId = headerId;
    }

    public String getIsCheckContainerNo() {
        return isCheckContainerNo;
    }

    public void setIsCheckContainerNo(String isCheckContainerNo) {
        this.isCheckContainerNo = isCheckContainerNo;
    }

    public String getIsCheckLockNo() {
        return isCheckLockNo;
    }

    public void setIsCheckLockNo(String isCheckLockNo) {
        this.isCheckLockNo = isCheckLockNo;
    }

    public List<String> getSerialNos() {
        return serialNos;
    }

    public void setSerialNos(List<String> serialNos) {
        this.serialNos = serialNos;
    }

    public List<ShipmentSerialsDTO> getShipmentSerials() {
        return shipmentSerials;
    }

    public void setShipmentSerials(List<ShipmentSerialsDTO> shipmentSerials) {
        this.shipmentSerials = shipmentSerials;
    }

    public String getShipmentCode() {
        return shipmentCode;
    }

    public void setShipmentCode(String shipmentCode) {
        this.shipmentCode = shipmentCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public List<Integer> getSignDetailIds() {
        return signDetailIds;
    }

    public void setSignDetailIds(List<Integer> signDetailIds) {
        this.signDetailIds = signDetailIds;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getWhBarcode69() {
        return whBarcode69;
    }

    public void setWhBarcode69(String whBarcode69) {
        this.whBarcode69 = whBarcode69;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public BigDecimal getPlanQty() {
        return planQty;
    }

    public void setPlanQty(BigDecimal planQty) {
        this.planQty = planQty;
    }

    public BigDecimal getPickedQty() {
        return pickedQty;
    }

    public void setPickedQty(BigDecimal pickedQty) {
        this.pickedQty = pickedQty;
    }

    public BigDecimal getRequestQty() {
        return requestQty;
    }

    public void setRequestQty(BigDecimal requestQty) {
        this.requestQty = requestQty;
    }

    public static class ShipmentSerialsDTO {

        private int id;
        private String createTime;
        private String updateTime;
        private String tenantCode;
        private String createUserCode;
        private String createUserName;
        private String updateUserCode;
        private String updateUserName;
        private Object remark;
        private int version;
        private int deleteFlag;
        private Object pageNo;
        private int pageSize;
        private Object offset;
        private Object orderBy;
        private Object orderByType;
        private Object ids;
        private Object tenantCodes;
        private Object count;
        private Object startTime;
        private Object endTime;
        private String serialNo;
        private Object serialCs;
        private Object shipmentContainerCode;
        private Object status;
        private String ownerCode;
        private String custOrderNo;
        private String shipmentCode;
        private String referenceNo;
        private int shipmentDetailId;
        private Object containerDetailId;
        private String custItemCode;
        private String itemCode;
        private String itemName;
        private String serialType;
        private BigDecimal qty;
        private Object unit;
        private String toLoc;
        private Object lotNum;
        private String whCode;
        private String barcode;
        private String scanType;
        private String serialPl;
        private String sourceFrom;
        private Object huaweiPo;
        private Object huaweiCode;
        private Object salesCode;
        private Object lotAtt04;
        private Object lotAtt05;
        private String sourceSys;
        private String waveNo;
        private int taskDetailId;
        private String orgSerialPl;
        private int referenceId;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getTenantCode() {
            return tenantCode;
        }

        public void setTenantCode(String tenantCode) {
            this.tenantCode = tenantCode;
        }

        public String getCreateUserCode() {
            return createUserCode;
        }

        public void setCreateUserCode(String createUserCode) {
            this.createUserCode = createUserCode;
        }

        public String getCreateUserName() {
            return createUserName;
        }

        public void setCreateUserName(String createUserName) {
            this.createUserName = createUserName;
        }

        public String getUpdateUserCode() {
            return updateUserCode;
        }

        public void setUpdateUserCode(String updateUserCode) {
            this.updateUserCode = updateUserCode;
        }

        public String getUpdateUserName() {
            return updateUserName;
        }

        public void setUpdateUserName(String updateUserName) {
            this.updateUserName = updateUserName;
        }

        public Object getRemark() {
            return remark;
        }

        public void setRemark(Object remark) {
            this.remark = remark;
        }

        public int getVersion() {
            return version;
        }

        public void setVersion(int version) {
            this.version = version;
        }

        public int getDeleteFlag() {
            return deleteFlag;
        }

        public void setDeleteFlag(int deleteFlag) {
            this.deleteFlag = deleteFlag;
        }

        public Object getPageNo() {
            return pageNo;
        }

        public void setPageNo(Object pageNo) {
            this.pageNo = pageNo;
        }

        public int getPageSize() {
            return pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }

        public Object getOffset() {
            return offset;
        }

        public void setOffset(Object offset) {
            this.offset = offset;
        }

        public Object getOrderBy() {
            return orderBy;
        }

        public void setOrderBy(Object orderBy) {
            this.orderBy = orderBy;
        }

        public Object getOrderByType() {
            return orderByType;
        }

        public void setOrderByType(Object orderByType) {
            this.orderByType = orderByType;
        }

        public Object getIds() {
            return ids;
        }

        public void setIds(Object ids) {
            this.ids = ids;
        }

        public Object getTenantCodes() {
            return tenantCodes;
        }

        public void setTenantCodes(Object tenantCodes) {
            this.tenantCodes = tenantCodes;
        }

        public Object getCount() {
            return count;
        }

        public void setCount(Object count) {
            this.count = count;
        }

        public Object getStartTime() {
            return startTime;
        }

        public void setStartTime(Object startTime) {
            this.startTime = startTime;
        }

        public Object getEndTime() {
            return endTime;
        }

        public void setEndTime(Object endTime) {
            this.endTime = endTime;
        }

        public String getSerialNo() {
            return serialNo;
        }

        public void setSerialNo(String serialNo) {
            this.serialNo = serialNo;
        }

        public Object getSerialCs() {
            return serialCs;
        }

        public void setSerialCs(Object serialCs) {
            this.serialCs = serialCs;
        }

        public Object getShipmentContainerCode() {
            return shipmentContainerCode;
        }

        public void setShipmentContainerCode(Object shipmentContainerCode) {
            this.shipmentContainerCode = shipmentContainerCode;
        }

        public Object getStatus() {
            return status;
        }

        public void setStatus(Object status) {
            this.status = status;
        }

        public String getOwnerCode() {
            return ownerCode;
        }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
        }

        public String getCustOrderNo() {
            return custOrderNo;
        }

        public void setCustOrderNo(String custOrderNo) {
            this.custOrderNo = custOrderNo;
        }

        public String getShipmentCode() {
            return shipmentCode;
        }

        public void setShipmentCode(String shipmentCode) {
            this.shipmentCode = shipmentCode;
        }

        public String getReferenceNo() {
            return referenceNo;
        }

        public void setReferenceNo(String referenceNo) {
            this.referenceNo = referenceNo;
        }

        public int getShipmentDetailId() {
            return shipmentDetailId;
        }

        public void setShipmentDetailId(int shipmentDetailId) {
            this.shipmentDetailId = shipmentDetailId;
        }

        public Object getContainerDetailId() {
            return containerDetailId;
        }

        public void setContainerDetailId(Object containerDetailId) {
            this.containerDetailId = containerDetailId;
        }

        public String getCustItemCode() {
            return custItemCode;
        }

        public void setCustItemCode(String custItemCode) {
            this.custItemCode = custItemCode;
        }

        public String getItemCode() {
            return itemCode;
        }

        public void setItemCode(String itemCode) {
            this.itemCode = itemCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getSerialType() {
            return serialType;
        }

        public void setSerialType(String serialType) {
            this.serialType = serialType;
        }

        public BigDecimal getQty() {
            return qty;
        }

        public void setQty(BigDecimal qty) {
            this.qty = qty;
        }

        public Object getUnit() {
            return unit;
        }

        public void setUnit(Object unit) {
            this.unit = unit;
        }

        public String getToLoc() {
            return toLoc;
        }

        public void setToLoc(String toLoc) {
            this.toLoc = toLoc;
        }

        public Object getLotNum() {
            return lotNum;
        }

        public void setLotNum(Object lotNum) {
            this.lotNum = lotNum;
        }

        public String getWhCode() {
            return whCode;
        }

        public void setWhCode(String whCode) {
            this.whCode = whCode;
        }

        public String getBarcode() {
            return barcode;
        }

        public void setBarcode(String barcode) {
            this.barcode = barcode;
        }

        public String getScanType() {
            return scanType;
        }

        public void setScanType(String scanType) {
            this.scanType = scanType;
        }

        public String getSerialPl() {
            return serialPl;
        }

        public void setSerialPl(String serialPl) {
            this.serialPl = serialPl;
        }

        public String getSourceFrom() {
            return sourceFrom;
        }

        public void setSourceFrom(String sourceFrom) {
            this.sourceFrom = sourceFrom;
        }

        public Object getHuaweiPo() {
            return huaweiPo;
        }

        public void setHuaweiPo(Object huaweiPo) {
            this.huaweiPo = huaweiPo;
        }

        public Object getHuaweiCode() {
            return huaweiCode;
        }

        public void setHuaweiCode(Object huaweiCode) {
            this.huaweiCode = huaweiCode;
        }

        public Object getSalesCode() {
            return salesCode;
        }

        public void setSalesCode(Object salesCode) {
            this.salesCode = salesCode;
        }

        public Object getLotAtt04() {
            return lotAtt04;
        }

        public void setLotAtt04(Object lotAtt04) {
            this.lotAtt04 = lotAtt04;
        }

        public Object getLotAtt05() {
            return lotAtt05;
        }

        public void setLotAtt05(Object lotAtt05) {
            this.lotAtt05 = lotAtt05;
        }

        public String getSourceSys() {
            return sourceSys;
        }

        public void setSourceSys(String sourceSys) {
            this.sourceSys = sourceSys;
        }

        public String getWaveNo() {
            return waveNo;
        }

        public void setWaveNo(String waveNo) {
            this.waveNo = waveNo;
        }

        public int getTaskDetailId() {
            return taskDetailId;
        }

        public void setTaskDetailId(int taskDetailId) {
            this.taskDetailId = taskDetailId;
        }

        public String getOrgSerialPl() {
            return orgSerialPl;
        }

        public void setOrgSerialPl(String orgSerialPl) {
            this.orgSerialPl = orgSerialPl;
        }

        public int getReferenceId() {
            return referenceId;
        }

        public void setReferenceId(int referenceId) {
            this.referenceId = referenceId;
        }
    }

    @Override
    public int compareTo(RespExportContainer o) {
        int sortFlag1 = 0;
        int sortFlag2 = 0;

        //订单数大于0且订单数等于交接数的就绿色沉底
        if(this.planQty.compareTo(new BigDecimal(0)) == 1 && this.planQty.compareTo(this.requestQty) == 0) {
            sortFlag1 = 1;
        }

        if(o.planQty.compareTo(new BigDecimal(0)) == 1 && o.planQty.compareTo(o.requestQty) == 0) {
            sortFlag2 = 1;
        }

        if(sortFlag1 > sortFlag2){
            return 1;
        }else if(sortFlag1 < sortFlag2){
            return -1;
        }
        return 0;
    }
}
