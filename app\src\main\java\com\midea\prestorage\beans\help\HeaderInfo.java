package com.midea.prestorage.beans.help;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;

//首页icon的信息类
public class HeaderInfo implements Serializable {

    private String funcName;
    private String tag;
    private int iconResource;
    private Class<?> cls;

    public HeaderInfo(String tag, int iconResource, String funcName, Class<?> cls) {
        this.tag = tag;
        this.iconResource = iconResource;
        this.funcName = funcName;
        this.cls = cls;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public int getIconResource() {
        return iconResource;
    }

    public void setIconResource(int iconResource) {
        this.iconResource = iconResource;
    }

    public String getFuncName() {
        return funcName;
    }

    public void setFuncName(String funcName) {
        this.funcName = funcName;
    }

    public Class<?> getCls() {
        return cls;
    }

    public void setCls(Class<?> cls) {
        this.cls = cls;
    }
}
