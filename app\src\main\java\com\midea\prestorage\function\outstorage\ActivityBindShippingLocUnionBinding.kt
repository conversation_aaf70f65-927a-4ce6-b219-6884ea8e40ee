package com.midea.prestorage.function.outstorage

import android.widget.EditText
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestorage.function.sendcheck.ActivitySerialCheckFirstUnionBinding
import com.midea.prestorage.function.sendcheck.SerialCheckFirstVM
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.databinding.ActivityBindShippingLocBinding
import com.midea.prestoragesaas.databinding.ActivityBindShippingLocCareBinding
import com.midea.prestoragesaas.databinding.ActivitySerialCheckFirstBinding
import com.midea.prestoragesaas.databinding.ActivitySerialCheckFirstCareBinding

sealed class ActivityBindShippingLocUnionBinding{
    abstract var vm: BindShippingLocVM?
    abstract val llTitleBar: RelativeLayout
    abstract val etCode: EditText
    abstract val spinnerStatus: MaterialSpinner
    abstract val recyclerView: RecyclerView
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: ActivityBindShippingLocCareBinding) : ActivityBindShippingLocUnionBinding() {
        override var vm: BindShippingLocVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etCode = binding.etCode
        override val spinnerStatus = binding.spinnerStatus
        override val recyclerView = binding.recyclerView
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: ActivityBindShippingLocBinding) : ActivityBindShippingLocUnionBinding() {
        override var vm: BindShippingLocVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etCode = binding.etCode
        override val spinnerStatus = binding.spinnerStatus
        override val recyclerView = binding.recyclerView
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
