package com.midea.prestorage.function.addgoods.fragment

import android.text.TextUtils
import androidx.databinding.ObservableBoolean
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.mideadspda.module.electro.fragment.AddFinishFragment
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.AreaList
import com.midea.prestorage.beans.net.ReplenishmentBean
import com.midea.prestorage.function.addgoods.AddGoodsActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.MySoundUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody

class AddFinishVM(val fragment: AddFinishFragment) {

    var pageNo = 1

    val isRefreshing = ObservableBoolean(false)
    var dayType: DCBean = DCBean("3天", "3")
    var areaArgs: AreaList? = null

    var isEnterGoods = false//用于控制是否输入搜索的参数

    init {
        refreshData()
    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        refreshData()
    }

    fun refreshData() {
        pageNo = 1
        isRefreshing.set(true)
        initData(true)
    }

    fun unitOk() {
        (fragment.activity as AddGoodsActivity).vm.areaArgs?.let { fragment.initArea(it) }
    }

    fun showErrorInfo(info: String) {
        ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, info)
    }

    fun initData(isRefresh: Boolean) {
        val map = mutableMapOf(
            "whCode" to Constants.whInfo?.whCode,
            "day" to dayType.value,
            "queryType" to 4,
            "pageNo" to pageNo,
            "pageSize" to 10,
            "queryCode" to fragment.goodsInfo,
            "subTaskType" to "PUTAWAY",
            "orderBy" to "update_time",
            "orderByType" to "desc"
        )
        if (!TextUtils.isEmpty(areaArgs?.zoneCode)) {
            map["zoneCodes"] = mutableListOf(areaArgs?.zoneCode)
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        val response = if (Constants.whInfo?.bearingSystem == "3") {
            RetrofitHelper.getAddGoodsService()
                .rfQueryNew(requestBody)
        } else {
            RetrofitHelper.getAddGoodsService()
                .rfQuery(requestBody)
        }

        response
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ReplenishmentBean>(fragment.requireActivity()) {
                override fun success(data: ReplenishmentBean?) {
                    if (isRefresh) {
                        isRefreshing.set(false)
                    } else {
                        fragment.adapter.loadMoreModule.loadMoreEnd()
                    }
                    if (data != null && data.list != null && data.list.isNotEmpty()) {
                        data.list.forEach {
                            if ((fragment.activity as AddGoodsActivity).packageResp != null) {
                                val results = (fragment.activity as AddGoodsActivity)
                                    .packageResp?.list?.filter { item -> item.code == it.unit }
                                val resultBatch = (fragment.activity as AddGoodsActivity)
                                    .batchResp?.list?.filter { item -> item.code == it.orderByAttribute }
                                if (results != null && results.isNotEmpty()) {
                                    it.unit = results[0].name
                                }
                                if (resultBatch != null && resultBatch.isNotEmpty()) {
                                    it.orderByAttribute = resultBatch[0].name
                                }
                            }
                        }
                        if (isRefresh) {
                            fragment.adapter.setNewInstance(data.list)
                        } else {
                            fragment.adapter.addData(data.list)
                        }
                        fragment.showDataInfo()
                    } else {
                        if (isRefresh) {
                            fragment.showNoDataInfo()
                        }
                    }

                    if (pageNo >= data?.totalPage!!) {
                        //加载到了最后一页
                        fragment.adapter.loadMoreModule.isEnableLoadMore = false
                    } else {
                        pageNo += 1
                    }
                    (fragment.activity as AddGoodsActivity).setCompleteDataNum(data.totalCount)

                    if (isEnterGoods) {
                        MySoundUtils.getInstance().dingSound()
                        isEnterGoods = false
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isRefreshing.set(false)
                    showErrorInfo(apiErrorModel.message)
                    if (statusCode == 605025L) {
                        fragment.cleanOrderNo()
                    } else {
                        fragment.showNoDataInfo()
                    }
                }
            })
    }

    //天数
    var days = mutableListOf(
        DCBean("1天", 1, DCBean.SHOW_KEY),
        DCBean("3天", 3, DCBean.SHOW_KEY),
        DCBean("7天", 7, DCBean.SHOW_KEY)
    )
}