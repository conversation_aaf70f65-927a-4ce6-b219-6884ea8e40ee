package com.midea.prestorage.function.inv

import android.os.Bundle
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.ListChoiceClickPositionAdapter
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestoragesaas.databinding.ActivityStatusAdjustmentScanBinding
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.function.inv.response.AdjustDetailSearchSort
import com.midea.prestorage.function.outstorage.dialog.DeleteBarcodeDialog
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.ToastUtilsCare

class StatusAdjustmentScanActivity : BaseViewModelActivity<StatusAdjustmentScanVM>() {
    private lateinit var binding: ActivityStatusAdjustmentScanBinding
    private lateinit var deleteDialog: DeleteBarcodeDialog
    var adapter = InStorageScanGoodsAdapter()

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_status_adjustment_scan)
        vm = ViewModelProvider.AndroidViewModelFactory(application)
            .create(StatusAdjustmentScanVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        //返回
        vm.finishActivity.observe(this, Observer<Boolean> {
            if (it) {
                finish()
            }
        })

        //弹出删除条码
        vm.isShowDeleteDialog.observe(this, Observer<Boolean> {
            if (it) {
                deleteDialog.show()
            }
        })

        //确认执行成功
        vm.isConfirmSuc.observe(this, Observer<Boolean> {
            if (it) {
                ToastUtilsCare.toastBig(this, "执行成功", Toast.LENGTH_SHORT)
                finish()
            }
        })

        initData()
        initDialog()
        initSpinner()
        initRecycleView()
    }

    fun initData() {
        val orderNo = intent.getStringExtra("adjustCode")

        if (orderNo.isNullOrEmpty()) {
            AlertDialogUtil.showOnlyOkDialog(this, "单号为空", AlertDialogUtil.OnOkCallback { })
        } else {
            vm.curOrderNo.value = orderNo
        }

        DCUtils.goodsStatue(this, object : DCUtils.DCBack {

            override fun dcBack(statusDC: MutableList<DCBean>) {

            }
        })
    }

    private fun initDialog() {
        //删除条码dialog
        deleteDialog = DeleteBarcodeDialog(this)
        deleteDialog.setTitle("删除条码")
        deleteDialog.setDeleteBack(object : DeleteBarcodeDialog.DeleteBarcodeBack {
            override fun deleteBarcodeBack(barcode: String) {
                //deleteDialog.dismiss()  //前端优化：RF-状态调整-删除条码后弹框不要关闭
                vm.deleteSerial(getWhCode(), barcode)
            }
        })

        deleteDialog.setOnDismissListener {
            vm.loadInOrderDatas(getWhCode())
        }
    }

    // 商品库存状态
    private fun initSpinner() {

        DCUtils.goodsStatue(this, object : DCUtils.DCBack {

            override fun dcBack(statusDC: MutableList<DCBean>) {

            }
        })
    }

    override fun onResume() {
        super.onResume()
        vm.loadInOrderDatas(getWhCode())
    }

    fun initRecycleView() {
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter

        vm.inOrderDatas.observe(this, Observer<MutableList<AdjustDetailSearchSort>> { result ->
            adapter.data.clear()
            result.sort()
            adapter.addData(result)
            adapter.notifyDataSetChanged()
        })
    }

    class InStorageScanGoodsAdapter :
        ListChoiceClickPositionAdapter<AdjustDetailSearchSort>(R.layout.item_out_storage_scan_goods) {

        override fun convert(helper: BaseViewHolder, item: AdjustDetailSearchSort) {
            super.convert(helper, item)

            item.vs.sortFlag.get().let {
                when (it) {
                    0 -> {
                        helper.setBackgroundResource(R.id.rl_root, R.drawable.bg_round_rectangle_light_blue)
                        helper.setVisible(R.id.img_finish, false)
                    }   //进行中
                    1 -> {
                        helper.setBackgroundResource(R.id.rl_root, R.drawable.bg_round_rectangle_white)
                        helper.setVisible(R.id.img_finish, false)
                    }    //未完成
                    2 -> {
                        helper.setBackgroundResource(R.id.rl_root, R.drawable.bg_round_rectangle_green)
                        helper.setVisible(R.id.img_finish, true)
                    }    //已完成
                    else -> {
                        helper.setBackgroundResource(R.id.rl_root, R.drawable.bg_round_rectangle_white)
                        helper.setVisible(R.id.img_finish, false)
                    }
                }
            }

            if(!item.item.custItemCode.isNullOrBlank()) {
                helper.setText(R.id.tv_goodsCode, item.item.custItemCode)
            }else {
                helper.setText(R.id.tv_goodsCode, "")
            }

            if(!item.item.itemName.isNullOrBlank()) {
                helper.setText(R.id.tv_goodsName, item.item.itemName)
            }else {
                helper.setText(R.id.tv_goodsName, "")
            }

            if(!AppUtils.getBigDecimalValueStr(item.item.planQty).isNullOrBlank()) {
                helper.setText(R.id.tv_planNum, AppUtils.getBigDecimalValueStr(item.item.planQty))
            }else {
                helper.setText(R.id.tv_planNum, "")
            }

            if(!AppUtils.getBigDecimalValueStr(item.item.allocatedQty).isNullOrBlank()) {
                helper.setText(R.id.tv_spceStr, AppUtils.getBigDecimalValueStr(item.item.allocatedQty))
            }else {
                helper.setText(R.id.tv_spceStr, "")
            }

            if(!AppUtils.getBigDecimalValueStr(item.item.scanNum).isNullOrBlank()) {
                helper.setText(R.id.tv_scanNum, AppUtils.getBigDecimalValueStr(item.item.scanNum))
            }else {
                helper.setText(R.id.tv_scanNum, "")
            }

            if(item.item.scanFlag != "Y") {
                helper.setVisible(R.id.tv_no_scan, false)
            }else {
                helper.setVisible(R.id.tv_no_scan, true)
            }

            helper.setGone(R.id.ll_pick, true)

            DCUtils.goodsStatue?.forEach {
                if(it.value == item.item.fmInventorySts) {
                    helper.setText(R.id.tv_inventoryStsStr, it.key.toString())
                }
            }

        }
    }
}