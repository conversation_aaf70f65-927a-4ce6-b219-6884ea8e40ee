package com.midea.prestorage.function.inv

import android.content.Intent
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.net.InvSetDetailList
import com.midea.prestorage.beans.net.InvSetList
import com.midea.prestorage.beans.net.SetPackageConfirmReq
import com.midea.prestorage.beans.net.SetPackagePrintBean
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.printer.BluetoothConnectBack
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody

class SetPackageDetailVM(val activity: SetPackageDetailActivity) {

    var flag = MutableLiveData<String>("") // 标识是从哪个页面进入的，main：主列表页面，wait：待集托页
    var setCode = ObservableField("")
    val isNoData = ObservableBoolean(true)
    var wholeNum = ObservableField("0")
    var partNum = ObservableField("0")
    var setStatus = ObservableBoolean(false) //集托的状态,false:集托中， true：集托完成
    val isPrintOk = ObservableField(false)
    var bean: InvSetList? = null
    var setArea = ObservableField("")
    var setStartTime = ObservableField("")
    var setEndTime = ObservableField("")
    var setUserName = ObservableField("")

    fun init() {
        if (setStatus.get()) {
            bluetoothOpen() //如果是集托完成状态的，则进入页面就去连接蓝牙
        }
        activity.waitingDialogHelp.showDialog()
        initList(setCode.get().toString())
    }

    fun back() {
        activity.finish()
    }

    private val blueBack = object : BluetoothConnectBack {
        override fun success() {
            activity.waitingDialogHelp.hidenDialog()
            isPrintOk.set(true)
        }

        override fun fail() {
            isPrintOk.set(false)
            AppUtils.showToast(activity, "打印机连接失败，请重启打印机!")
        }

        override fun connect() {
            bluetoothOpen()
        }
    }

    fun bluetoothOpen() {
        if (CheckUtil.isFastDoubleClick()) {
            if(!Printer.isPrintOk()) {
                Printer.openBluetooth(activity, blueBack)
            }else {
                isPrintOk.set(true)
            }
        }
    }

    fun initList(setCode: String) {
        val param = mutableMapOf(
            "setCode" to setCode,
            "whCode" to Constants.whInfo?.whCode
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getInventoryAPI()
            .loadSetDetailList(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<InvSetDetailList>>(activity) {
                override fun success(data: MutableList<InvSetDetailList>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (data != null) {
                        if (data.size > 0) {
                            isNoData.set(false)
                        } else {
                            isNoData.set(true)
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(activity, "暂无集托信息！")
                        }
                        data.forEachIndexed { index, invSetDetailList ->
                            invSetDetailList.index = index + 1
                            invSetDetailList.opNum =
                                AppUtils.getBigDecimalValueStr(invSetDetailList.num)
                        }
                        activity.showData(data)

                        var wNum = 0
                        var pNum = 0
                        data.forEach {
                            if (!it.opNum.isNullOrEmpty()) {
                                if (it.unit == "CS") {
                                    wNum += it.opNum.toInt()
                                } else {
                                    pNum += it.opNum.toInt()
                                }
                            }
                        }
                        wholeNum.set(wNum.toString())
                        partNum.set(pNum.toString())
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun deleteNum(num: String, ids: String) {
        val beans = mutableListOf<SetPackageConfirmReq>()
        val bean = SetPackageConfirmReq()
        bean.ids = ids
        bean.opNum = num.toInt()
        beans.add(bean)

        val param = mutableMapOf(
            "setCode" to setCode.get().toString(),
            "whCode" to Constants.whInfo?.whCode,
            "setUserCode" to Constants.userInfo?.name,
            "opNumRequestList" to beans
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getInventoryAPI()
            .setDelDetail(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showSuccessToastWithSound(activity, "删除成功！")
                    initList(setCode.get().toString()) //接口处理之后，刷新当前页面
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    /**
     * 继续集托/打印
     */
    fun setPackageDone() {
        if (CheckUtil.isFastDoubleClick()) {
            if (setStatus.get()) { //打印
                if (activity.adapter.data.isEmpty()) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, "暂无可打印的集托信息！")
                    return
                }
                val printBeans = mutableListOf<SetPackagePrintBean>()
                val printBean = SetPackagePrintBean()
                printBean.setCode = setCode.get().toString()
                printBean.setUserName = setUserName.get().toString()
                printBean.wholeNum = bean?.wholeNum
                printBean.partNum = bean?.partNum
                printBean.setEndTime = setEndTime.get().toString()
                printBean.setArea = setArea.get().toString()
                printBean.totalNum = (bean?.wholeNum?.toInt()!! + bean?.partNum?.toInt()!!).toString()
                val beans = mutableListOf<String>()
                var hashSetWaveNo = HashSet<String>() //波次号排重
                activity.adapter.data.forEach {
                    if (!it.waveNo.isNullOrBlank() && !hashSetWaveNo.contains(it.waveNo)) {
                        beans.add(it.waveNo)
                        hashSetWaveNo.add(it.waveNo)
                    }
                }
                printBean.waveNum = beans.size.toString()
                printBean.waveNo = beans?.joinToString(separator = ",") { it}
                printBeans.add(printBean)
                Printer.printSetPackage(printBeans)


            } else { //继续集托
                if (flag.value.toString() == "main") {
                    val it = Intent(activity, WaitSetPackageActivity::class.java)
                    it.putExtra("flag", "detail") //标识是从集托详情页面进去的
                    it.putExtra("setCode", setCode.get().toString()) //集托号
                    it.putExtra("bean", bean)
                    activity.startActivity(it)
                } else {
                    activity.finish()
                }
            }
        }
    }
}