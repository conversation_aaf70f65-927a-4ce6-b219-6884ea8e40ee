package com.midea.prestorage.beans.setting;

import org.xutils.db.annotation.Column;
import org.xutils.db.annotation.Table;

@Table(name = "ProfileSettingDbV2") //换表，清理数据
public class ProfileSettingDbV2 {

    @Column(name = "id", isId = true, autoGen = true, property = "NOT NULL")
    private int id;

    // 这个setting 是一个json  会被解析为 ProfileSetting 对象
    @Column(name = "setting")
    private String setting;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getSetting() {
        return setting;
    }

    public void setSetting(String setting) {
        this.setting = setting;
    }
}
