package com.midea.prestorage.function.inv.fragment

import android.view.View
import android.widget.Button
import android.widget.EditText
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestoragesaas.databinding.FragmentTransByGoodBinding
import com.midea.prestoragesaas.databinding.FragmentTransByGoodCareBinding

sealed class FragmentTransByGoodUnionBinding {
    abstract var vm: TransByGoodVM?
    abstract val root: View
    abstract val etFromLocCode: EditText
    abstract val rv: RecyclerView
    abstract val etTargetLocCode: EditText
    abstract val etCustItemCode: EditText
    abstract val btnSubmitTransfer: Button

    class V2(val binding: FragmentTransByGoodCareBinding) : FragmentTransByGoodUnionBinding() {
        override var vm: TransByGoodVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val etFromLocCode = binding.etFromLocCode
        override val rv = binding.rv
        override val etTargetLocCode = binding.etTargetLocCode
        override val etCustItemCode = binding.etCustItemCode
        override val btnSubmitTransfer = binding.btnSubmitTransfer
    }

    class V1(val binding: FragmentTransByGoodBinding) : FragmentTransByGoodUnionBinding() {
        override var vm: TransByGoodVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val etFromLocCode = binding.etFromLocCode
        override val rv = binding.rv
        override val etTargetLocCode = binding.etTargetLocCode
        override val etCustItemCode = binding.etCustItemCode
        override val btnSubmitTransfer = binding.btnSubmitTransfer
    }
}
