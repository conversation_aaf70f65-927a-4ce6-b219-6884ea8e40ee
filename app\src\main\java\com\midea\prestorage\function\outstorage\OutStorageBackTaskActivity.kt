package com.midea.prestorage.function.outstorage

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.planstock.ActivityPlanStockListUnionBinding
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityOutStorageBackTaskBinding
import com.xuexiang.xqrcode.XQRCode

class OutStorageBackTaskActivity : BaseViewModelActivity<OutStorageBackTaskViewModel>() {

    private lateinit var binding: ActivityOutStorageBackTaskUnionBinding
    private var adapter: OutStorageBackTaskAdapter? = null
    private var fromScan = false

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityOutStorageBackTaskUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_out_storage_back_task_care
                )
            )
        } else {
            ActivityOutStorageBackTaskUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_out_storage_back_task
                )
            )
        }
        immersionBar {
            titleBarMarginTop(binding.clTitleLayout)
        }
        vm = ViewModelProvider(this).get(OutStorageBackTaskViewModel::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this
        initView()
        AppUtils.requestFocus(binding.etCustItemCode)
    }

    override fun onResume() {
        super.onResume()
        if (!fromScan) {
            vm.onRefreshCommand.onRefresh()
        }
        fromScan = false
    }

    private fun initView() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        binding.rv.apply {
            <EMAIL> = OutStorageBackTaskAdapter()
            adapter = <EMAIL>
        }
        adapter?.setOnItemClickListener { _, _, position ->
            startActivity(
                OutStorageBackTaskDetailActivity.newIntent(
                    this,
                    adapter?.data?.get(position)!!
                )
            )
        }
        vm.scanCodeResult.observe(this) {
            startActivity(OutStorageBackTaskDetailActivity.newIntent(this, it))
        }
        vm.taskListLiveData.observe(this) {
            adapter?.setList(it)
        }
        vm.scanEvent.observe(this) {
            XQRCode.startScan(this, QR_CODE_BACK)
        }

        vm.deleteEvent.observe(this) {
            vm.barcode.value = ""
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            fromScan = true
            vm.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

}