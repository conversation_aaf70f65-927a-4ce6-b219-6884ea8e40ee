package com.midea.prestorage.function.instorage.response;

public class InReceiptHeaderCheckRFVO {

    // 入库单号 或 波次单号
    private String orderNo;

    //是否是波次单
    private boolean wave;


    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }


    public boolean isWave() {
        return wave;
    }

    public void setWave(boolean wave) {
        this.wave = wave;
    }
}
