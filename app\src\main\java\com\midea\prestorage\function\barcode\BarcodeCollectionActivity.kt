package com.midea.prestorage.function.barcode

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.beans.net.BarcodeCollectionInfoResp
import com.midea.prestorage.function.barcode.dialog.DeleteCollectionBarcodeDialog
import com.midea.prestorage.function.outstorage.dialog.DeleteBarcodeDialog
import com.midea.prestorage.utils.AppUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityBarcodeCollectionBinding
import com.xuexiang.xqrcode.XQRCode
import java.math.BigDecimal

class BarcodeCollectionActivity : BaseViewModelActivity<BarcodeCollectionViewModel>() {

    private lateinit var binding: ActivityBarcodeCollectionBinding
    private var adapter: BarcodeCollectionAdapter? = null

    companion object {
        fun newIntent(context: Context, list: MutableList<BarcodeCollectionInfoResp>, sumTotal: String) =
            Intent(context, BarcodeCollectionActivity::class.java).also {
                it.putParcelableArrayListExtra("collectionList", ArrayList(list))
                it.putExtra("sumTotal", sumTotal)
            }
    }

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_barcode_collection)
        immersionBar {
            titleBarMarginTop(binding.clTitleLayout)
        }
        vm = ViewModelProvider(this, ViewModelProvider.AndroidViewModelFactory(application)).get(
            BarcodeCollectionViewModel::class.java
        )
        binding.vm = vm
        binding.lifecycleOwner = this
        init()
    }

    private fun init() {
        vm.deleteBarcodeEvent.observe(this) {
            deleteBarcodeDialog()
        }
        binding.rv.apply {
            <EMAIL> = BarcodeCollectionAdapter()
            adapter = <EMAIL>
        }
        vm.barcodeCollectionList.observe(this) {
            adapter?.setList(it)
        }
        vm.focusBarcodeEvent.observe(this) {
            AppUtils.requestFocus(binding.etBarcode)
        }
        vm.scanBarcodeLiveEvent.observe(this) {
            XQRCode.startScan(this, QR_CODE_BACK)
        }
        val list = intent.getParcelableArrayListExtra<BarcodeCollectionInfoResp>("collectionList")
        vm.cjNo.value = list?.getOrNull(0)?.cjNo ?: ""
        vm.sumTotal.value = intent.getStringExtra("sumTotal")
        adapter?.setList(list)
        AppUtils.requestFocus(binding.etBarcode)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            binding.vm?.scanBarcodeResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    private fun deleteBarcodeDialog() {
        val deleteDialog = DeleteCollectionBarcodeDialog()
        deleteDialog.setTitle("删除条码")
        deleteDialog.setDeleteBack(object : DeleteCollectionBarcodeDialog.DeleteBarcodeBack {
            override fun deleteBarcodeBack(barcode: String) {
                vm.deleteBarcode(barcode)
            }
        })
        deleteDialog.showNow(supportFragmentManager, "deleteBarcodeDialog")
    }

}