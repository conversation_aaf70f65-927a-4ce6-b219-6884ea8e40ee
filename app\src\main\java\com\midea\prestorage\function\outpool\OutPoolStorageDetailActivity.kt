package com.midea.prestorage.function.outpool

import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestoragesaas.databinding.ActivityOutStoragePoolDetailBinding


class OutPoolStorageDetailActivity : BaseActivity() {

    private lateinit var binding: ActivityOutStoragePoolDetailBinding
    lateinit var adapter: OutPoolDetailAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_out_storage_pool_detail)
        binding.vm = OutPoolStorageDetailVM(this)

        adapter = OutPoolDetailAdapter(ArrayList(), binding.vm as OutPoolStorageDetailVM)
        binding.vm!!.init()

        initRecycle()
    }

    private fun initRecycle() {
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter
    }

    fun resetAdapterData(beans: MutableList<OutPoolStorageDetailHelp>) {
        adapter.setNewInstance(beans)
        adapter.notifyDataSetChanged()
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }
}