package com.midea.prestorage.function.outstorage

import CheckUtil
import android.content.Intent
import android.text.TextUtils
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.*
import com.midea.prestorage.beans.setting.BluetoothInfo
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.dialog.ConfirmFilterDialog
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.instorage.response.HandlingGroup
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.function.pointjoin.PointJoinActivity
import com.midea.prestorage.function.unscan.UnScanActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.printer.BluetoothConnectBack
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.*
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal
import java.util.*
import java.util.concurrent.TimeUnit


@Suppress("CAST_NEVER_SUCCEEDS", "UNCHECKED_CAST")
class OutStorageVM(val activity: OutStorageActivity) {

    val title = ObservableField("出库扫码")
    val hint = ObservableField("")
    val isOrderOk = ObservableField(false)
    val orderNo = ObservableField("")
    val goodsNo = ObservableField("")
    val processInfo = ObservableField("")
    val btnInfo = ObservableField("发货交接")
    val isPrintOk = ObservableField(false)
    val db = DbUtils.db
    private lateinit var waveNoChooseDialog: FilterDialog

    var bean: OutStorageQuery? = null
    var ruleList: MutableList<RuleDataList>? = null
    var goodsStatus: MutableList<DCBean>? = null
    private var bluetoothInfo: BluetoothInfo? = null

    var queryType = 1 //默认出库单

    var currentGoods: String? = null

    lateinit var handlingGroupDialog: ConfirmFilterDialog

    private var configValue = 0 //1是发运模式，0是交接模式
    private val shipmentsSend = mutableListOf<String?>()

    private val blueBack = object : BluetoothConnectBack {
        var connectNum = 0
        override fun success() {
            activity.waitingDialogHelp.hidenDialog()
            isPrintOk.set(true)
        }

        override fun fail() {
            isPrintOk.set(false)
            subscribe = Observable.timer(1, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe {
                    connectNum++
                    bluetoothOpen()
                    if (connectNum > 2) {
                        subscribe?.dispose()
                        AppUtils.showToast(activity, "打印机连接失败，请重启打印机!")
                        activity.waitingDialogHelp.hidenDialog()
                    }
                }
        }

        override fun connect() {
            bluetoothOpen()
        }
    }

    private fun bluetoothOpen() {
        Printer.openBluetooth(activity, blueBack)
    }

    var subscribe: Disposable? = null
    fun init() {
        val orderNoStr = activity.intent.getStringExtra("orderNo")
        if (!TextUtils.isEmpty(orderNoStr)) {
            orderNo.set(orderNoStr)
        }
        readProperty()

        DCUtils.getGoodsStatusDC(activity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                goodsStatus = statusDC
                combineDCInfo()
            }
        })

        waveNoChooseDialog = FilterDialog(activity)
        waveNoChooseDialog.setTitle("请选择波次单号")
        waveNoChooseDialog.dismissEdit()
        waveNoChooseDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            orderNo.set(it.showInfo)
            orderEnterKeyPress.onEnterKey()
            waveNoChooseDialog.dismiss()
        })

        loadMaxNum()

        initChangeType()
        initHandingDialog()
    }

    private fun initHandingDialog() {
        handlingGroupDialog = ConfirmFilterDialog(activity as RxAppCompatActivity)
        handlingGroupDialog.setTitle("选择装卸组")
        handlingGroupDialog.dismissEdit()
        handlingGroupDialog.setBack(object : ConfirmFilterDialog.ConfirmFilterChooseBack {
            override fun confirmFilterChooseBack(baseInfo: BaseItemShowInfo) {
                baseInfo as HandlingGroup
                sendGoods(baseInfo)
                handlingGroupDialog.dismiss()
            }
        })
    }

    private fun sendGoods(it: HandlingGroup) {
        val param = mutableMapOf<String, Any?>(
            "whCode" to activity.getWhCode(),
            "handingGroupCode" to it.handlingCode,
            "handingGroupName" to it.handlingName
        )

        if (queryType == 1) {
            param["shipmentCodes"] = shipmentsSend
        } else {
            param["waveNo"] = title.get()
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .shippingConfirm(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    ToastUtils.getInstance()
                        .showSuccessToastWithSound(activity, "发运成功!")
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun initChangeType() {
        RetrofitHelper.getDirectionAPI()
            .controlParam()
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ContainerConfigBean>(activity) {
                override fun success(data: ContainerConfigBean?) {
                    data?.let {
                        configValue = it.value

                        if (configValue == 1) {
                            initHandlingGroupList()
                            btnInfo.set("发运")
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
//                    AppUtils.showToast(activity, apiErrorModel.message)
                }
            })
    }

    private fun initHandlingGroupList() {
        RetrofitHelper.getBasicDataAPI()
            .queryPageHandlingGroup(Constants.tenantCode, (activity as BaseActivity).getWhCode(), "02")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<PageResult<HandlingGroup>>(activity as BaseActivity) {
                override fun success(data: PageResult<HandlingGroup>?) {
                    val beans = mutableListOf<BaseItemShowInfo>()
                    if (activity.getWhCode() == "W00514") {
                        val handlingGroup = HandlingGroup()
                        handlingGroup.showInfo = "虚拟装卸组"
                        handlingGroup.handlingCode = "zx0001"
                        handlingGroup.handlingName = "虚拟装卸组"
                        beans.add(0, handlingGroup)
                    }

                    if (data != null && data.list != null && data.list.size > 0) {
                        data.list.forEach {
                            it.showInfo = it.handlingName
                            if (!it.showInfo.isNullOrBlank()) {
                                beans.add(it)
                            }
                        }
                    }

                    if (beans.isEmpty()) {
                        ToastUtils.getInstance().showErrorToastWithSound(activity, "必须配置装卸组!")
                        return
                    }

                    handlingGroupDialog.addAllData(beans)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    // 四个小卡片 数据
    private fun loadMaxNum() {
        RetrofitHelper.getBarcodeAPI()
            .configEntity()
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ConfigEntity>(activity) {
                override fun success(data: ConfigEntity?) {
                    Constants.maxInputNum = data?.configValue
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                }
            })
    }

    private fun readProperty() {
        Observable.create<Any> {
            bluetoothInfo = db.findFirst(BluetoothInfo::class.java)
            if (bluetoothInfo == null || bluetoothInfo?.printMode == 0) {
                it.onNext(BluetoothInfo())
            }
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .subscribe(object : Observer<Any> {
                override fun onComplete() {
                    activity.waitingDialogHelp.hidenDialog()
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: Any) {
                    activity.waitingDialogHelp.showDialog()
                    bluetoothOpen()
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    val orderEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                queryShipmentDetail(true)
            }
        }
    }

    val goodsEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                if (!TextUtils.isEmpty(goodsNo.get())) {
                    scan(goodsNo.get()!!)
                } else {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "请输入或扫码货品条码!")
                }
            }
        }
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        if (!isOrderOk.get()!!) {
            orderNo.set(result)
            orderEnterKeyPress.onEnterKey()
        } else {
            goodsNo.set(result)
            goodsEnterKeyPress.onEnterKey()
        }
    }

    /**
     * 融合数据字典
     */
    fun combineDCInfo() {
        activity.adapter.data.forEach {
            if (goodsStatus != null) {
                val results = goodsStatus?.filter { item ->
                    item.value.toString() == it.lotAtt04
                }
                if (!results.isNullOrEmpty()) {
                    it.lotAtt04Str = results[0].key.toString()
                }
            }
        }
        activity.adapter.notifyDataSetChanged()
    }


    fun changePrint() {
        Printer.showDialog(activity, blueBack)
    }

    fun queryShipmentDetail(isEnter: Boolean) {
        if (TextUtils.isEmpty(orderNo.get())) {
            if (isEnter) {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "请扫描货品条码!")
            }
            return
        }
        activity.waitingDialogHelp.showDialog()
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "queryCode" to orderNo.get()
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .queryShipmentDetail(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<OutStorageQuery>(activity) {
                override fun success(data: OutStorageQuery?) {
                    //判断是否存在多个波次单
                    activity.waitingDialogHelp.hidenDialog()
                    val waveNos = data?.waveNoList
                    if (waveNos != null && waveNos.size > 1) {
                        val sets = mutableListOf<BaseItemShowInfo>()
                        waveNos.forEach {
                            sets.add(BaseItemShowInfo(it))
                        }
                        waveNoChooseDialog.addAllData(sets)
                        waveNoChooseDialog.show()
                        return
                    } else {
                        if (waveNos != null && waveNos.isNotEmpty()) {
                            orderNo.set(waveNos[0])
                            title.set(waveNos[0])
                            queryShipmentDetail(true)
                        }
                    }

                    if (data?.outShipmentDetailInfoVOS == null) {
                        return
                    }

                    isOrderOk.set(true)
                    activity.orderNoRequest()
                    bean = data
                    activity.showData(combineData(data.outShipmentDetailInfoVOS))
                    if (data.outShipmentDetailInfoVOS.isNotEmpty()) {
                        barcodeRule(data.outShipmentDetailInfoVOS)
                    }
                    queryType = if (data.queryType != null) data.queryType else 1

                    if (!data.outShipmentDetailInfoVOS.isNullOrEmpty()) {
                        if (!TextUtils.isEmpty(data.outShipmentDetailInfoVOS[0].waveNo)) {
                            orderNo.set(data.outShipmentDetailInfoVOS[0].waveNo)
                            title.set(data.outShipmentDetailInfoVOS[0].waveNo)
                        }
                    }

                    processInfo.set(data.planQty.toString() + "/" + data.scanQty.toString())
                    if (data.planQty == data.scanQty) {
                        TTSUtils.startAuto("扫描完毕!")
                    }
                    combineDCInfo()
                    title.set(orderNo.get())
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    orderNo.set("")
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    private fun combineData(outShipmentDetailInfoVOS: MutableList<OutStorageQuery.ShipmentDetailSumQtyDtoList>): MutableList<OutStorageQuery.ShipmentDetailSumQtyDtoList> {
        val set = mutableSetOf<String>()
        outShipmentDetailInfoVOS.forEach {
            set.add(it.itemCode + it.lotAtt04)
        }
        val shipments = mutableListOf<OutStorageQuery.ShipmentDetailSumQtyDtoList>()
        set.forEach { item ->
            val results = outShipmentDetailInfoVOS.filter { it.itemCode + it.lotAtt04 == item }
            val cloneObj = AppUtils.cloneObj(results[0])
            cloneObj.setPlanQty(BigDecimal(results.sumBy { it.planQty }))
            cloneObj.serialQty = results.sumBy { it.serialQty }
            shipments.add(cloneObj)
        }
        return shipments
    }

    private fun barcodeRule(shipmentDetailSumQtyDtoList: MutableList<OutStorageQuery.ShipmentDetailSumQtyDtoList>) {
        if (ruleList != null) {
            return
        }
        val param = mutableSetOf<String>()
        shipmentDetailSumQtyDtoList.forEach {
            param.add(it.itemCode)
        }
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .barcodeRule(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<RuleDataList>>(activity) {
                override fun success(data: MutableList<RuleDataList>?) {
                    if (data != null) {
                        ruleList = data
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun scan(serialNo: String) {
        //匹配本地规则
        if (canParseWithRule()) {
            //  从大物流取
            for (rule in ruleList!!) {
                //  优先匹配字符串长度
                if (rule.codeNum == null || rule.codeNum == "" || serialNo.length != Integer.parseInt(
                        rule.codeNum
                    )
                ) continue

                val subStr = serialNo.substring(
                    Integer.parseInt(rule.paraNum) - 1,
                    Integer.parseInt(rule.paraNumTo)
                )
                val targetStr: String? = when (rule.paragraphDefine) {
                    "CDCM_BARCODE" -> rule.cdcmBarcode
                    "CDCM_BARCODE1" -> rule.cdcmBarcode1
                    "CDCM_BARCODE2" -> rule.cdcmBarcode2
                    "CDCM_BARCODE3" -> rule.cdcmBarcode3
                    "CDCM_BARCODE4" -> rule.cdcmBarcode4
                    "CDCM_BARCODE5" -> rule.cdcmBarcode5
                    "CDCM_BARCODE6" -> rule.cdcmBarcode6
                    "CDCM_BARCODE7" -> rule.cdcmBarcode7
                    "CDCM_BARCODE8" -> rule.cdcmBarcode8
                    "CDCM_BARCODE9" -> rule.cdcmBarcode9
                    "CDCM_BARCODE10" -> rule.cdcmBarcode10
                    else -> rule.cdcmBarcode
                }

                if (targetStr != null && subStr == targetStr) {
                    startScan(serialNo, rule.cdcmCustMaterialNo, rule.cdcmMaterialNo)
                    return
                }
            }
        }
        startScan(serialNo)
    }

    private fun startScan(serialNo: String, custItemCode: String = "", itemCode: String = "") {
        activity.waitingDialogHelp.showDialog()
        val param = mutableMapOf(
            "serialNo" to serialNo,
            "barcode" to serialNo,
            "custItemCode" to custItemCode,
            "whCode" to activity.getWhCode(),
            "outShipmentDetailInfoVOS" to bean?.outShipmentDetailInfoVOS?.filter { it.itemCode == itemCode }
        )
        if (queryType == 1) {
            param["shipmentCode"] = activity.adapter.data[0].shipmentCode
        } else {
            param["waveNo"] = title.get().toString()
        }
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .scan(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<OutStorageScan>(activity) {
                override fun success(data: OutStorageScan?) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showSuccessToastWithSound(activity, "扫码成功!")
                    if (data != null) {
                        if (data.barcodeType == 3 || data.barcodeType == 4) {
                            val results =
                                bean?.outShipmentDetailInfoVOS?.filter {
                                    it.cdcmUnScanMark == "Y" || it.unScanMarkBoolean
                                }
                            if (results.isNullOrEmpty()) {
                                goodsNo.set("")
                                ToastUtils.getInstance()
                                    .showErrorToastWithSound(activity, "扫码商品必须扫SN码!")
                                queryShipmentDetail(false)
                            } else {
                                if (data.totalQty.compareTo(data.scanQty) == 1) {
                                    activity.showDeleteTipDialog(data, bean)
                                    return
                                } else {
                                    goodsNo.set("")
                                    ToastUtils.getInstance()
                                        .showErrorToastWithSound(activity, "数量超出!")
                                }
                            }
                            return
                        }
                        currentGoods = if (!TextUtils.isEmpty(currentGoods)) {
                            if (currentGoods != data.custItemCode) {
                                AppUtils.showToast(activity, "编码切换!")
                                MySoundUtils.getInstance().codeChangeSound()
                            }
                            data.custItemCode
                        } else {
                            data.custItemCode
                        }
                        startPrinter(data)
                    }
                    goodsNo.set("")
                    queryShipmentDetail(false)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    goodsNo.set("")
                    activity.waitingDialogHelp.hidenDialog()
                    if (statusCode == 600013L || statusCode == 600012L) {
//                        TTSUtils.startAuto(apiErrorModel.message)
//                        ToastUtils.getInstance().showErrorToast(activity, apiErrorModel.message)
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, apiErrorModel.message)
                    } else {
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, apiErrorModel.message)
                    }
                    queryShipmentDetail(false)
                }
            })
    }

    fun startPrinter(data: OutStorageScan) {
        if (data.containerCodes.isNullOrEmpty()) {
            activity.reviewEnable()
            return
        } else {
            activity.reviewUnable()
        }
        if (!isPrintOk.get()!!) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "未连接打印机!")
            return
        }

        val param = mutableMapOf(
            "cartonCodes" to data.containerCodes,
            "opCode" to "REVIEW",
            "whCode" to activity.getWhCode(),
            "userCode" to Constants.userInfo?.name
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .printInfo(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<PrintInfo>>(activity) {
                override fun success(data: MutableList<PrintInfo>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    data?.let {
                        Printer.print(data)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    // 是否根据条码规则解析
    private fun canParseWithRule(): Boolean {
        return (!goodsNo.get()!!.endsWith("W")
                && ruleList != null
                && ruleList!!.size > 0)
    }

    fun back() {
        activity.finish()
    }

    fun deleteSerialNo() {
        if (CheckUtil.isFastDoubleClick()) {
            if (isOrderOk.get()!!) {
                val it = Intent(activity, OutStorageDeleteActivity::class.java)
                it.putExtra("orderNo", title.get())
                it.putExtra("queryType", queryType)
                activity.startActivity(it)
            }
        }
    }

    fun receiveGoods() {
        activity.waitingDialogHelp.showDialog()
        val param = mutableMapOf(
            "whCode" to activity.getWhCode()
        )

        if (queryType == 1) {
            param["shipmentCode"] = title.get().toString()
        } else {
            param["waveNo"] = title.get().toString()
        }
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .completeReview(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    ToastUtils.getInstance().showSuccessToastWithSound(activity, "复核成功")
                    activity.waitingDialogHelp.hidenDialog()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun send() {
        if (CheckUtil.isFastDoubleClick()) {
            if (configValue == 1) {
                val set = mutableSetOf<String?>()
                activity.adapter.data.forEach {
                    set.add(it.shipmentCode)
                }
                shipmentsSend.clear()
                if (queryType == 1) {
                    shipmentsSend.addAll(set)
                }

                handlingGroupDialog.show()
            } else {
                if (bean?.planQty != bean?.scanQty) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "必须复核完成!")
                    return
                }
                val set = mutableSetOf<String?>()
                activity.adapter.data.forEach {
                    if (it.planQty != it.serialQty) {
                        ToastUtils.getInstance().showErrorToastWithSound(activity, "存在未复核完成的单据!")
                        return
                    }
                    set.add(it.shipmentCode)
                }

                checkDetail(set.toMutableList())
            }
        }
    }

    private fun checkDetail(ids: MutableList<String?>) {
        val param = mutableMapOf<String, Any?>(
            "whCode" to activity.getWhCode()
        )
        if (queryType == 1) {
            param["shipmentCodeList"] = ids
        } else {
            param["shipmentCodeList"] = mutableListOf<String>()
            param["waveNo"] = title.get()
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .engineerDetail(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<MutableList<PointDivideDetailBean>>(activity as RxAppCompatActivity) {
                override fun success(data: MutableList<PointDivideDetailBean>?) {
                    if (!data.isNullOrEmpty()) {
                        val results = data.filter { it.engineerSignResponse.shippingWay != "ZT" }
                        if (results.isEmpty()) {
                            val it = Intent(activity, PointJoinActivity::class.java)
                            it.putExtra("jump", true)
                            it.putExtra("beans", ArrayList(data))
                            activity.startActivity(it)
                            return
                        }
                    }
                    val it = Intent(activity, PointJoinActivity::class.java)
                    it.putExtra("orderNo", title.get())
                    activity.startActivity(it)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun unScanApply() {
        activity.toolMenu.show(activity, activity.binding.titleBtnMore)
    }

    fun requestUnscan() {
        if (bean?.planQty == bean?.scanQty) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "复核完成的订单不能再申请不扫码!")
            return
        }
        AlertDialogUtil.showOkAndCancelDialog(activity, "确定申请不扫码?",
            { _, _ ->  //点了确定

                val intent = Intent(activity, UnScanActivity::class.java)
                intent.putExtra("type", "out")
                if (queryType == 1) {
                    //  出库单
                    intent.putExtra("orderType", "order")
                } else {
                    // 波次单
                    intent.putExtra("orderType", "wave")
                }
                intent.putExtra("orderNo", orderNo.get()) //
                activity.startActivity(intent)
            },
            { _, _ ->  //点了取消

            })
    }
}