package com.midea.prestorage.function.outstorage.dialog

import android.app.AlertDialog
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.OutStorageDeleteQueryBean
import com.midea.prestoragesaas.databinding.DialogInStorageDeleteSerialBinding
import com.midea.prestoragesaas.databinding.DialogOutStorageDeleteTipBinding
import com.midea.prestorage.function.instorage.InStorageDeleteActivity
import com.midea.prestorage.function.instorage.response.InReceiptSerial

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/6/26$
 */
class DeleteSerialDialog(var activity: InStorageDeleteActivity) : AlertDialog(activity) {

    var binding: DialogInStorageDeleteSerialBinding


    init {
        val contentView = LayoutInflater.from(activity).inflate(R.layout.dialog_in_storage_delete_serial, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding!!.vm = DeleteSerialDialogVM(this)

        setCanceledOnTouchOutside(true)
    }



    fun showDlg(inReceiptSerial: InReceiptSerial) {
        super.show()
        binding!!.vm!!.show(inReceiptSerial)
    }


}
