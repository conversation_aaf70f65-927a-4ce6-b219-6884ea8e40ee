package com.midea.prestorage.function.containerpick.provider

import com.chad.library.adapter.base.entity.node.BaseExpandNode
import com.chad.library.adapter.base.entity.node.BaseNode
import com.midea.prestorage.beans.net.RespBulkPacking

class BulkPickToBeWrap(
    val respBulkPacking: RespBulkPacking,
    val childNodes : MutableList<BaseNode>?
) : BaseExpandNode() {
    override val childNode: MutableList<BaseNode>?
        get() = childNodes

    var selected = false
}