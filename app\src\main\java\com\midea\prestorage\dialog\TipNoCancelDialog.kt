package com.midea.prestorage.dialog

import android.app.AlertDialog
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestoragesaas.databinding.DialogTipBinding
import com.midea.prestoragesaas.databinding.DialogTipNoCancelBinding

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/6/26$
 */
class TipNoCancelDialog(
    var mContext: BaseActivity
) : AlertDialog(mContext) {

    private var binding: DialogTipNoCancelBinding
    var tag: Any? = null

    init {
        val contentView =
            LayoutInflater.from(mContext).inflate(R.layout.dialog_tip_no_cancel, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = TipNoCancelDialogVM(this)

        setCanceledOnTouchOutside(true)
    }

    fun setTitle(title: String) {
        binding.vm!!.titleName.set(title)
    }

    fun setMsg(msg: String) {
        binding.vm!!.msg.set(msg)
    }

    fun setOnTipBackListener(listener: OnTipNoCancelBack) {
        binding.vm!!.listener = listener
    }

    interface OnTipNoCancelBack {
        fun onConfirmClick()
    }
}
