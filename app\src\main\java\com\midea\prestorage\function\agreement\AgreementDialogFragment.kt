package com.midea.prestorage.function.agreement

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.midea.prestorage.utils.AgreementUtil.attachAgreement
import com.midea.prestoragesaas.databinding.DialogAgreementBinding

class AgreementDialogFragment : DialogFragment() {

    interface AgreementListener {
        fun onAgree()
    }

    companion object {
        fun newInstance(): AgreementDialogFragment {
            return AgreementDialogFragment()
        }
    }

    private lateinit var binding: DialogAgreementBinding
    private var listener: AgreementListener? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        listener = context as? AgreementListener
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        binding = DialogAgreementBinding.inflate(LayoutInflater.from(context), null, false)
        initView()
        dialog.setContentView(binding.root)
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DialogAgreementBinding.inflate(LayoutInflater.from(context), null, false)
        dialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog?.window?.setGravity(Gravity.CENTER)
        dialog?.window?.attributes?.run {
            gravity = Gravity.CENTER
        }
        initView()
        return binding.root
    }

    private fun initView() {
        binding.tvCancel.setOnClickListener {
            dismiss()
        }
        binding.tvConfirm.setOnClickListener {
            listener?.onAgree()
            dismiss()
        }
        binding.tvContent.attachAgreement()
    }

    override fun onDetach() {
        super.onDetach()
        listener = null
    }

}