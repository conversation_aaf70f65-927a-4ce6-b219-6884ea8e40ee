package com.midea.prestorage.function.inv.dialog

import android.app.AlertDialog
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.net.InvSetDetailList
import com.midea.prestorage.beans.net.RespAdjustDetailPage
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.inv.response.LotDetail
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogInvRecDeleteBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import java.math.BigDecimal

class InvRecDeleteDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {
    var binding: DialogInvRecDeleteBinding
    private lateinit var statueDialog: FilterDialog
    var tag: String? = null

    init {
        val contentView =
            LayoutInflater.from(mContext).inflate(R.layout.dialog_inv_rec_delete, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = InvRecDeleteDialogVM(this)

        setCanceledOnTouchOutside(false)
        // 加上这个 确保按回退键也不关闭选择框
        setCancelable(false)
    }

    fun setData(data: RespAdjustDetailPage) {
        binding!!.vm!!.bean = data
    }

    override fun show() {
        super.show()

        binding!!.vm!!.qty.set("")
        binding!!.vm!!.cdpaFormat.set(binding!!.vm!!.bean?.cdpaFormat ?: "")

        val unitNames =
            binding!!.vm!!.bean?.packageDetails?.mapNotNull { it.cdprDesc } ?: emptyList()
        val mainPosition = binding!!.vm!!.bean?.packageDetails?.indexOfFirst { it.cdprUnit == "EA" }

        binding.spinnerStatus.setItems(unitNames)
        if (!unitNames.isNullOrEmpty()) {
            binding.spinnerStatus.selectedIndex = mainPosition ?: 0
            binding!!.vm!!.cdprQuantity = mainPosition?.let {
                binding!!.vm!!.bean?.packageDetails?.get(
                    it
                )?.cdprQuantity
            }
                ?: BigDecimal.ONE
        }

        binding.spinnerStatus.setOnItemSelectedListener { _, position, _, _ ->
            binding!!.vm!!.cdprQuantity =
                binding!!.vm!!.bean?.packageDetails?.get(position)?.cdprQuantity ?: BigDecimal.ONE
        }
    }

    fun setBackData(backData: BackData) {
        binding.vm!!.backDataListener = backData
    }

    interface BackData {
        fun onConfirmClick(id: Long?, actQty: BigDecimal?)
        fun onCancelClick()
    }
}