package com.midea.prestorage.dialog

import android.app.AlertDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.receive.dialog.DialogContainerReceiveUnionBinding
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogContainerCodeBinding

class ContainerCodeDialog(
    var mContext: BaseActivity
) : AlertDialog(mContext) {

    private var binding: DialogContainerCodeUnionBinding
    var tag:Any? = null

    init {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window?.setGravity(Gravity.CENTER)
            window?.attributes?.run {
                gravity = Gravity.CENTER
            }
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_container_code_care, null)
            setView(contentView)
            DialogContainerCodeUnionBinding.V2(DataBindingUtil.bind(contentView)!!)
        } else {
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_container_code, null)
            setView(contentView)
            DialogContainerCodeUnionBinding.V1(DataBindingUtil.bind(contentView)!!)
        }
        binding.vm = ContainerCodeDialogVM(this)

        setCanceledOnTouchOutside(false)
        // 加上这个 确保按回退键也不关闭选择框
        setCancelable(false)
    }

    fun setTitle(title: String) {
        binding.vm!!.titleName.set(title)
    }

    fun setMsg(msg: String) {
        binding.vm!!.msg.set(msg)
    }

    fun setContainerCode(containerCode: String?) {
        binding.vm!!.containerCode.set("【容器号】$containerCode")
    }

    fun setTaskCode(taskCode: String?) {
        binding.vm!!.taskCode.set("【任务号】$taskCode")
    }

    fun setOnTipBackListener(listener: OnTipBack) {
        binding.vm!!.listener = listener
    }

    interface OnTipBack {
        fun onConfirmClick()
    }
}