package com.midea.prestorage.beans.net;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class SerialPickContainerDetailList implements Serializable {

    private String id;
    private String createTime;
    private String updateTime;
    private String tenantCode;
    private String createUserCode;
    private String createUserName;
    private String updateUserCode;
    private String updateUserName;
    private String remark;
    private String version;
    private String deleteFlag;
    private String pageNo;
    private String pageSize;
    private String offset;
    private String orderBy;
    private String orderByType;
    private String tenantCodes;
    private String count;
    private String startTime;
    private String endTime;
    private String pickContainerHeaderId;
    private String referenceCode;
    private String referenceLineId;
    private String taskCode;
    private String taskLineId;
    private String ownerCode;
    private String custItemCode;
    private String itemCode;
    private String itemName;
    private String pickedUnit;
    private String sortingUnit;
    private BigDecimal pickedQty;
    private BigDecimal sortingQty;
    private BigDecimal sortingEpQty;
    private BigDecimal sortingUnitQty;
    private BigDecimal oqcQty;
    private BigDecimal oqcUnitQty;
    private String oqcUnit;
    private String waveNo;
    private String lotNum;
    private String fromLoc;
    private String traceId;
    private String whCode;

    private List<String> ids;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getPageNo() {
        return pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getOffset() {
        return offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderByType() {
        return orderByType;
    }

    public void setOrderByType(String orderByType) {
        this.orderByType = orderByType;
    }

    public String getTenantCodes() {
        return tenantCodes;
    }

    public void setTenantCodes(String tenantCodes) {
        this.tenantCodes = tenantCodes;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getPickContainerHeaderId() {
        return pickContainerHeaderId;
    }

    public void setPickContainerHeaderId(String pickContainerHeaderId) {
        this.pickContainerHeaderId = pickContainerHeaderId;
    }

    public String getReferenceCode() {
        return referenceCode;
    }

    public void setReferenceCode(String referenceCode) {
        this.referenceCode = referenceCode;
    }

    public String getReferenceLineId() {
        return referenceLineId;
    }

    public void setReferenceLineId(String referenceLineId) {
        this.referenceLineId = referenceLineId;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getTaskLineId() {
        return taskLineId;
    }

    public void setTaskLineId(String taskLineId) {
        this.taskLineId = taskLineId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public BigDecimal getPickedQty() {
        return pickedQty;
    }

    public void setPickedQty(BigDecimal pickedQty) {
        this.pickedQty = pickedQty;
    }

    public String getPickedUnit() {
        return pickedUnit;
    }

    public void setPickedUnit(String pickedUnit) {
        this.pickedUnit = pickedUnit;
    }

    public BigDecimal getSortingQty() {
        return sortingQty;
    }

    public void setSortingQty(BigDecimal sortingQty) {
        this.sortingQty = sortingQty;
    }

    public BigDecimal getSortingEpQty() {
        return sortingEpQty;
    }

    public void setSortingEpQty(BigDecimal sortingEpQty) {
        this.sortingEpQty = sortingEpQty;
    }

    public BigDecimal getSortingUnitQty() {
        return sortingUnitQty;
    }

    public void setSortingUnitQty(BigDecimal sortingUnitQty) {
        this.sortingUnitQty = sortingUnitQty;
    }

    public String getSortingUnit() {
        return sortingUnit;
    }

    public void setSortingUnit(String sortingUnit) {
        this.sortingUnit = sortingUnit;
    }

    public BigDecimal getOqcQty() {
        return oqcQty;
    }

    public void setOqcQty(BigDecimal oqcQty) {
        this.oqcQty = oqcQty;
    }

    public BigDecimal getOqcUnitQty() {
        return oqcUnitQty;
    }

    public void setOqcUnitQty(BigDecimal oqcUnitQty) {
        this.oqcUnitQty = oqcUnitQty;
    }

    public String getOqcUnit() {
        return oqcUnit;
    }

    public void setOqcUnit(String oqcUnit) {
        this.oqcUnit = oqcUnit;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getLotNum() {
        return lotNum;
    }

    public void setLotNum(String lotNum) {
        this.lotNum = lotNum;
    }

    public String getFromLoc() {
        return fromLoc;
    }

    public void setFromLoc(String fromLoc) {
        this.fromLoc = fromLoc;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SerialPickContainerDetailList that = (SerialPickContainerDetailList) o;
        return Objects.equals(taskCode, that.taskCode) &&
                Objects.equals(ownerCode, that.ownerCode) &&
                Objects.equals(itemCode, that.itemCode) &&
                Objects.equals(fromLoc, that.fromLoc);
    }

    public List<String> getIds() {
        if (ids == null) {
            ids = new ArrayList<>();
        }
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }
}