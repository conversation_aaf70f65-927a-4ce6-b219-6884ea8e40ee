package com.midea.prestorage.function.pointjoin

import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayoutMediator
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestoragesaas.databinding.ActivityPointJoinBinding
import com.midea.prestorage.function.main.MainActivity
import com.midea.prestorage.function.pointjoin.fragment.PointJoinFragment

/**
 * 网点交接
 */
class PointJoinActivity : BaseActivity() {

    private lateinit var binding: ActivityPointJoinBinding
    private val titles = arrayOf("待处理", "已完成")
    val adapter = MyAdapter(this, 2)

    private var tabNumText1: TextView? = null
    private var tabText1: TextView? = null

    private var tabNumText2: TextView? = null
    private var tabText2: TextView? = null

    private var vm = PointJoinVM(this)

    var isLoadParamFromMainActivity = false
    var beginTime = ""
    var endTime = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_point_join)
        binding.vm = vm

        initViewPage()

        val isJump = intent.getBooleanExtra("jump", false)
        if (isJump) {
            val it = Intent(this, PointJoinDetailActivity::class.java)
            it.putExtra("beans", intent.getSerializableExtra("beans"))
            startActivity(it)
        }


        // 如果是从首页带查询条件跳进来的 ， 则直接按条件查询
        if (this.intent != null && this.intent.getBooleanExtra("fromMainActivity", false) != null
            && this.intent.getBooleanExtra("fromMainActivity", false).equals(true)
        ) {
            isLoadParamFromMainActivity = true
            val beginTime = this.intent.getStringExtra("beginTime")
            val endTime = this.intent.getStringExtra("endTime")
            if (beginTime != null && endTime != null) {
                this.beginTime = beginTime
                this.endTime = endTime
            }
        }
    }

    private fun initViewPage() {
        binding.viewPager.adapter = adapter
        adapter.setFragments()
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                binding.vm!!.isWaiting.set(position == 0)
                if (position == 0) {
                    tabText1?.setTextColor(
                        ContextCompat.getColor(
                            this@PointJoinActivity,
                            R.color.black
                        )
                    )
                    tabNumText1?.background = ContextCompat.getDrawable(
                        this@PointJoinActivity,
                        R.drawable.bg_circle_bt_red
                    )

                    tabText2?.setTextColor(
                        ContextCompat.getColor(
                            this@PointJoinActivity,
                            R.color.button_gray
                        )
                    )
                    tabNumText2?.background = ContextCompat.getDrawable(
                        this@PointJoinActivity,
                        R.drawable.bg_circle_bt_gray
                    )
                } else if (position == 1) {
                    tabText2?.setTextColor(
                        ContextCompat.getColor(
                            this@PointJoinActivity,
                            R.color.black
                        )
                    )
                    tabNumText2?.background = ContextCompat.getDrawable(
                        this@PointJoinActivity,
                        R.drawable.bg_circle_bt_red
                    )

                    tabText1?.setTextColor(
                        ContextCompat.getColor(
                            this@PointJoinActivity,
                            R.color.button_gray
                        )
                    )
                    tabNumText1?.background = ContextCompat.getDrawable(
                        this@PointJoinActivity,
                        R.drawable.bg_circle_bt_gray
                    )
                }
                adapter.fragments[position].notified(false)
            }
        })
        val tabLayoutMediator =
            TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
                tab.text = titles[position]
            }
        tabLayoutMediator.attach()
        binding.viewPager.offscreenPageLimit = 1

        //设置tab
        val tab1 = binding.tabLayout.getTabAt(0)
        tab1?.setCustomView(R.layout.item_custorm_title)
        tabText1 = tab1?.customView?.findViewById(R.id.tab_text)
        tabNumText1 = tab1?.customView?.findViewById(R.id.tab_num)
        tabText1?.text = "待处理"

        val tab2 = binding.tabLayout.getTabAt(1)
        tab2?.setCustomView(R.layout.item_custorm_title)
        tabText2 = tab2?.customView?.findViewById(R.id.tab_text)
        tabNumText2 = tab2?.customView?.findViewById(R.id.tab_num)
        tabText2?.text = "已完成"
    }

    override fun onResume() {
        super.onResume()
        if (adapter.fragments.size > 0) {
            adapter.fragments[binding.viewPager.currentItem].notified(true)
        }
    }

    fun setTabNum1(num: Int) {
        tabNumText1?.text = num.toString()
    }

    fun setTabNum2(num: Int) {
        tabNumText2?.text = num.toString()
    }

    class MyAdapter(activity: FragmentActivity, private val itemsCount: Int) :
        FragmentStateAdapter(activity) {
        val fragments = mutableListOf<PointJoinFragment>()

        override fun getItemCount(): Int {
            return itemsCount
        }

        override fun createFragment(position: Int): Fragment {
            return fragments[position]
        }

        fun setFragments() {
            fragments.add(PointJoinFragment.newInstance(0))
            fragments.add(PointJoinFragment.newInstance(1))
        }
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    override fun toastDismiss() {
        adapter.fragments[binding.viewPager.currentItem].toastDismiss()
    }

    fun changeMode(mode: Boolean) {
        val adapter = binding.viewPager.adapter as MyAdapter
        adapter.fragments[0].batchOrSingle(mode)
    }

    override fun onDestroy() {
        super.onDestroy()
        startActivity(Intent(this, MainActivity::class.java))
    }
}