package com.midea.prestorage.beans.net;

import android.text.TextUtils;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.beans.base.BaseItemForPopup;
import com.midea.prestorage.function.inv.response.PackageRelation;
import com.midea.prestorage.utils.AppUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.BitSet;
import java.util.List;

public class ContainerDetailInfo implements Serializable {

    private BigDecimal totalReceiptQty;
    private String receiptBy;
    private String receiptByName;
    private String receiptTime;
    private List<ContainerDetailList> listDetail;

    public String getReceiptByName() {
        return receiptByName;
    }

    public void setReceiptByName(String receiptByName) {
        this.receiptByName = receiptByName;
    }

    public String getReceiptBy() {
        return receiptBy;
    }

    public void setReceiptBy(String receiptBy) {
        this.receiptBy = receiptBy;
    }

    public String getReceiptTime() {
        return receiptTime;
    }

    public void setReceiptTime(String receiptTime) {
        this.receiptTime = receiptTime;
    }

    public List<ContainerDetailList> getListDetail() {
        return listDetail;
    }

    public void setListDetail(List<ContainerDetailList> listDetail) {
        this.listDetail = listDetail;
    }

    public BigDecimal getTotalReceiptQty() {
        return AppUtils.getBigDecimalValue(totalReceiptQty);
    }

    public void setTotalReceiptQty(BigDecimal totalReceiptQty) {
        this.totalReceiptQty = totalReceiptQty;
    }

    public class ContainerDetailList extends BaseItemForPopup {

        @ShowAnnotation
        private String custItemCode;
        @ShowAnnotation
        private String lotAtt04Str;
        @ShowAnnotation
        private BigDecimal qty;
        @ShowAnnotation
        private String itemName;
        @ShowAnnotation
        private String lotAtt05;
        @ShowAnnotation
        private String lotAtt03;
        @ShowAnnotation
        private String lotAtt01;
        @ShowAnnotation
        private String statusStr;

        private String id;
        private String createUserCode;
        private String createUserName;
        private String updateUserCode;
        private String updateUserName;
        private String remark;
        private String version;
        private String deleteFlag;
        private String pageNo;
        private String pageSize;
        private String offset;
        private String orderBy;
        private String orderByType;
        private String ids;
        private String tenantCodes;
        private String count;
        private String startTime;
        private String endTime;
        private String whCode;
        private String ownerCode;
        private String receiptCode;
        private String receiptHeaderId;
        private String receiptDetailId;
        private String custOrderLineNum;
        private String receiptType;
        private String containerCode;
        private String status;
        private String palletCode;
        private BigDecimal unitQty;
        private String lotNum;
        private String traceId;
        private String itemCode;
        private String unit;
        private String receiptBy;
        private String containerClosedTime;
        private String checkQty;
        private String checkBy;
        private String checkTime;
        private String referenceNo;
        private String receiptChannel;
        private String customerCode;
        private String inventoryId;
        private String uploadBatch;
        private String uploadNum;
        private String lotAtt02;
        private String lotAtt04;
        private String receiptTime;
        private String whBarcode69;
        private String whCsBarcode69;
        private List<PackageRelation> packageRelationList;
        private int isDecimal;

        public String getWhCsBarcode69() {
            return whCsBarcode69;
        }

        public void setWhCsBarcode69(String whCsBarcode69) {
            this.whCsBarcode69 = whCsBarcode69;
        }

        public int getIsDecimal() {
            return isDecimal;
        }

        public void setIsDecimal(int isDecimal) {
            this.isDecimal = isDecimal;
        }

        public List<PackageRelation> getPackageRelationList() {
            return packageRelationList;
        }

        public void setPackageRelationList(List<PackageRelation> packageRelationList) {
            this.packageRelationList = packageRelationList;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCreateUserCode() {
            return createUserCode;
        }

        public void setCreateUserCode(String createUserCode) {
            this.createUserCode = createUserCode;
        }

        public String getCreateUserName() {
            return createUserName;
        }

        public void setCreateUserName(String createUserName) {
            this.createUserName = createUserName;
        }

        public String getUpdateUserCode() {
            return updateUserCode;
        }

        public void setUpdateUserCode(String updateUserCode) {
            this.updateUserCode = updateUserCode;
        }

        public String getUpdateUserName() {
            return updateUserName;
        }

        public void setUpdateUserName(String updateUserName) {
            this.updateUserName = updateUserName;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getDeleteFlag() {
            return deleteFlag;
        }

        public void setDeleteFlag(String deleteFlag) {
            this.deleteFlag = deleteFlag;
        }

        public String getPageNo() {
            return pageNo;
        }

        public void setPageNo(String pageNo) {
            this.pageNo = pageNo;
        }

        public String getPageSize() {
            return pageSize;
        }

        public void setPageSize(String pageSize) {
            this.pageSize = pageSize;
        }

        public String getOffset() {
            return offset;
        }

        public void setOffset(String offset) {
            this.offset = offset;
        }

        public String getOrderBy() {
            return orderBy;
        }

        public void setOrderBy(String orderBy) {
            this.orderBy = orderBy;
        }

        public String getOrderByType() {
            return orderByType;
        }

        public void setOrderByType(String orderByType) {
            this.orderByType = orderByType;
        }

        public String getIds() {
            return ids;
        }

        public void setIds(String ids) {
            this.ids = ids;
        }

        public String getTenantCodes() {
            return tenantCodes;
        }

        public void setTenantCodes(String tenantCodes) {
            this.tenantCodes = tenantCodes;
        }

        public String getCount() {
            return count;
        }

        public void setCount(String count) {
            this.count = count;
        }

        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public String getWhCode() {
            return whCode;
        }

        public void setWhCode(String whCode) {
            this.whCode = whCode;
        }

        public String getOwnerCode() {
            return ownerCode;
        }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
        }

        public String getReceiptCode() {
            return receiptCode;
        }

        public void setReceiptCode(String receiptCode) {
            this.receiptCode = receiptCode;
        }

        public String getReceiptHeaderId() {
            return receiptHeaderId;
        }

        public void setReceiptHeaderId(String receiptHeaderId) {
            this.receiptHeaderId = receiptHeaderId;
        }

        public String getReceiptDetailId() {
            return receiptDetailId;
        }

        public void setReceiptDetailId(String receiptDetailId) {
            this.receiptDetailId = receiptDetailId;
        }

        public String getCustOrderLineNum() {
            return custOrderLineNum;
        }

        public void setCustOrderLineNum(String custOrderLineNum) {
            this.custOrderLineNum = custOrderLineNum;
        }

        public String getReceiptType() {
            return receiptType;
        }

        public void setReceiptType(String receiptType) {
            this.receiptType = receiptType;
        }

        public String getContainerCode() {
            return containerCode;
        }

        public void setContainerCode(String containerCode) {
            this.containerCode = containerCode;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getCustItemCode() {
            return custItemCode;
        }

        public void setCustItemCode(String custItemCode) {
            this.custItemCode = custItemCode;
        }

        public String getItemCode() {
            return itemCode;
        }

        public void setItemCode(String itemCode) {
            this.itemCode = itemCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getPalletCode() {
            return palletCode;
        }

        public void setPalletCode(String palletCode) {
            this.palletCode = palletCode;
        }

        public String getLotNum() {
            return lotNum;
        }

        public void setLotNum(String lotNum) {
            this.lotNum = lotNum;
        }

        public String getTraceId() {
            return traceId;
        }

        public void setTraceId(String traceId) {
            this.traceId = traceId;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public String getReceiptBy() {
            return receiptBy;
        }

        public void setReceiptBy(String receiptBy) {
            this.receiptBy = receiptBy;
        }

        public String getReceiptTime() {
            return receiptTime;
        }

        public void setReceiptTime(String receiptTime) {
            this.receiptTime = receiptTime;
        }

        public String getContainerClosedTime() {
            return containerClosedTime;
        }

        public void setContainerClosedTime(String containerClosedTime) {
            this.containerClosedTime = containerClosedTime;
        }

        public String getCheckQty() {
            return checkQty;
        }

        public void setCheckQty(String checkQty) {
            this.checkQty = checkQty;
        }

        public String getCheckBy() {
            return checkBy;
        }

        public void setCheckBy(String checkBy) {
            this.checkBy = checkBy;
        }

        public String getCheckTime() {
            return checkTime;
        }

        public void setCheckTime(String checkTime) {
            this.checkTime = checkTime;
        }

        public String getReferenceNo() {
            return referenceNo;
        }

        public void setReferenceNo(String referenceNo) {
            this.referenceNo = referenceNo;
        }

        public String getReceiptChannel() {
            return receiptChannel;
        }

        public void setReceiptChannel(String receiptChannel) {
            this.receiptChannel = receiptChannel;
        }

        public String getCustomerCode() {
            return customerCode;
        }

        public void setCustomerCode(String customerCode) {
            this.customerCode = customerCode;
        }

        public String getInventoryId() {
            return inventoryId;
        }

        public void setInventoryId(String inventoryId) {
            this.inventoryId = inventoryId;
        }

        public String getUploadBatch() {
            return uploadBatch;
        }

        public void setUploadBatch(String uploadBatch) {
            this.uploadBatch = uploadBatch;
        }

        public String getUploadNum() {
            return uploadNum;
        }

        public void setUploadNum(String uploadNum) {
            this.uploadNum = uploadNum;
        }

        public String getLotAtt01() {
            return lotAtt01;
        }

        public void setLotAtt01(String lotAtt01) {
            this.lotAtt01 = lotAtt01;
        }

        public String getLotAtt02() {
            return lotAtt02;
        }

        public void setLotAtt02(String lotAtt02) {
            this.lotAtt02 = lotAtt02;
        }

        public String getLotAtt03() {
            if (TextUtils.isEmpty(lotAtt03)) {
                return "";
            } else {
                return lotAtt03.split(" ")[0];
            }
        }

        public void setLotAtt03(String lotAtt03) {
            this.lotAtt03 = lotAtt03;
        }

        public String getLotAtt04() {
            return lotAtt04;
        }

        public void setLotAtt04(String lotAtt04) {
            this.lotAtt04 = lotAtt04;
        }

        public String getLotAtt05() {
            return lotAtt05;
        }

        public void setLotAtt05(String lotAtt05) {
            this.lotAtt05 = lotAtt05;
        }

        public String getQty() {
            return AppUtils.getBigDecimalValue(qty).toPlainString();
        }

        public void setQty(BigDecimal qty) {
            this.qty = qty;
        }

        public String getLotAtt04Str() {
            return lotAtt04Str;
        }

        public void setUnitQty(BigDecimal unitQty) {
            this.unitQty = unitQty;
        }

        public BigDecimal getUnitQty() {
            return unitQty;
        }

        public void setLotAtt04Str(String lotAtt04Str) {
            this.lotAtt04Str = lotAtt04Str;
        }

        public String getStatusStr() {
            return statusStr;
        }

        public void setStatusStr(String statusStr) {
            this.statusStr = statusStr;
        }

        public String getWhBarcode69() {
            return whBarcode69;
        }

        public void setWhBarcode69(String whBarcode69) {
            this.whBarcode69 = whBarcode69;
        }
    }
}
