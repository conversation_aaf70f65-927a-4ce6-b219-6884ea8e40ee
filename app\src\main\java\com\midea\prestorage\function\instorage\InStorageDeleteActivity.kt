package com.midea.prestorage.function.instorage

import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestoragesaas.databinding.ActivityInStorageDeleteBinding
import com.midea.prestorage.function.instorage.response.InReceiptSerial
import com.midea.prestorage.function.outstorage.dialog.DeleteSerialDialog
import com.midea.prestorage.utils.DCUtils
import com.xuexiang.xqrcode.XQRCode

// 004-前置仓收货删除界面
// 用户在‘002-前置仓收货扫码界面’点击‘删除’按钮后界面跳转到这里的 ‘004-前置仓收货删除界面’，扫描‘货品条码’后回车，系统根据条码类型删除收货记录。
// 删除界面显示的列表明细为当前收货容器号下的入库扫码记录明细行。
// 条码类型为‘1’的通过单击明细行弹出数量录入框删除；条码类型为‘0’的通过扫描条码删除。
class InStorageDeleteActivity : BaseActivity() {

    lateinit var binding: ActivityInStorageDeleteBinding
    var adapter = ListSerialDeleteAdapter()
    lateinit var deleteDialog: DeleteSerialDialog

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_in_storage_delete)
        binding.vm = InStorageDeleteVM(this)

        initRecycleView()

        deleteDialog = DeleteSerialDialog(this)
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }


    fun initRecycleView() {

        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter

        //  点击监听
        adapter.setOnCheckListener {
            // 类型1 的条码可以点击然后弹框删除 类型0的只能扫码后删除
            if (!it.serialType.isNullOrEmpty() && it.serialType.equals("1")) {
                /*if (it.serialNo.isNullOrEmpty()) {
                    it.serialNo = it.custItemCode
                }
                binding.vm!!.anyCode.set(it.serialNo)*/
                if (it.qty != null) {
                    binding.vm!!.onDeleteSerial(it)
                }

            }

        }

    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == BaseActivity.QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果
            binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }


    class ListSerialDeleteAdapter : ListChoiceClickAdapter<InReceiptSerial>(R.layout.item_for_receipt_serial_delete) {

        override fun convert(helper: BaseViewHolder, item: InReceiptSerial) {
            super.convert(helper, item as InReceiptSerial)

            //货品描述
            if (!item.itemName.isNullOrEmpty()) {
                helper.setText(R.id.tvItemName, item.itemName)
            } else {
                helper.setText(R.id.tvItemName, "")
            }

            // 客户商品编码
            if (item.custItemCode != null) {
                helper.setText(R.id.tvCustItemCode, item.custItemCode.toString())
            } else {
                helper.setText(R.id.tvCustItemCode, "")
            }

            // 数量
            if (item.qty != null) {
                helper.setText(R.id.tvScanNum, item.qty.toInt().toString())
            } else {
                helper.setText(R.id.tvScanNum, "")
            }

            // 属性4  状态
            if (!item.lotAtt04.isNullOrEmpty()) {
                if (DCUtils.lot4TypeC2N.get(item.lotAtt04) != null) {
                    helper.setText(R.id.tvLot4, DCUtils.lot4TypeC2N.get(item.lotAtt04).toString())
                } else {
                    helper.setText(R.id.tvLot4, item.lotAtt04)
                }

                if (item.lotAtt04.toUpperCase().equals("Y")) {  //正品
                    helper.setTextColorRes(R.id.tvLot4, R.color.ui_font_color_blue)
                } else if (item.lotAtt04.toUpperCase().equals("N")) {  //不良品
                    helper.setTextColorRes(R.id.tvLot4, R.color.ui_font_color_orange)
                } else if (item.lotAtt04.toUpperCase().equals("B")) { //包装破损
                    helper.setTextColorRes(R.id.tvLot4, R.color.ui_font_color_red)
                } else {
                    helper.setTextColorRes(R.id.tvLot4, R.color.ui_font_color_default)
                }
            } else {
                helper.setText(R.id.tvLot4, "")
            }

            //条码类型
            if (item.serialType != null) {
                helper.setText(R.id.tvSerialType, item.serialType.toString())
            } else {
                helper.setText(R.id.tvSerialType, "")
            }

            // SerialNo
            if (item.serialNo != null) {
                helper.setText(R.id.tvSerialNo, item.serialNo.toString())
            } else {
                helper.setText(R.id.tvSerialNo, "")
            }

            // barcode
            if (item.barcode != null) {
                helper.setText(R.id.tvBarcode, item.barcode.toString())
            } else {
                helper.setText(R.id.tvBarcode, "")
            }


            // 除了 状态 lot4 以外的其他属性，如果有就显示
            var lotInfos = "";
            var arr = arrayListOf<String>()
            if (!item.lotAtt05.isNullOrEmpty()) {
                arr.add("批次: " + item.lotAtt05)
            }
            if (!item.lotAtt01.isNullOrEmpty()) {
                var info = item.lotAtt01 // 把日期格式转成 yyyymmdd
                if (info.split(" ").size > 0) {
                    info = item.lotAtt01.split(" ")[0]
                }
                arr.add("生产日期: " + info.replace("-", ""))
            }
            if (!item.lotAtt02.isNullOrEmpty()) {
                var info = item.lotAtt02 // 把日期格式转成 yyyymmdd
                if (info.split(" ").size > 0) {
                    info = item.lotAtt02.split(" ")[0]
                }
                arr.add("失效日期: " + info.replace("-", ""))
            }
            if (!item.lotAtt03.isNullOrEmpty()) {
                var info = item.lotAtt03 // 把日期格式转成 yyyymmdd
                if (info.split(" ").size > 0) {
                    info = item.lotAtt03.split(" ")[0]
                }
                arr.add("入库日期: " + info.replace("-", ""))
            }
            if (!item.lotAtt06.isNullOrEmpty()) {
                arr.add("属性06: " + item.lotAtt06)
            }
            if (!item.lotAtt07.isNullOrEmpty()) {
                arr.add("属性07: " + item.lotAtt07)
            }
            if (!item.lotAtt08.isNullOrEmpty()) {
                arr.add("属性08: " + item.lotAtt08)
            }
            if (!item.lotAtt09.isNullOrEmpty()) {
                arr.add("属性09: " + item.lotAtt09)
            }
            if (!item.lotAtt10.isNullOrEmpty()) {
                arr.add("属性10: " + item.lotAtt10)
            }
            if (!item.lotAtt11.isNullOrEmpty()) {
                arr.add("属性11: " + item.lotAtt11)
            }
            if (!item.lotAtt12.isNullOrEmpty()) {
                arr.add("属性12: " + item.lotAtt12)
            }
            lotInfos = arr.joinToString(separator = " / ")
            helper.setText(R.id.tvLotInfos, lotInfos)

        }
    }


}