package com.midea.prestorage.function.addgoods

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.mideadspda.module.electro.fragment.AddMyTaskFragment
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.beans.net.PackageResp
import com.midea.prestorage.beans.net.ReplenishmentBean
import com.midea.prestorage.function.inv.response.FuInvLocationInventory
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestoragesaas.databinding.ActivityPutInGoodsBinding
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.CareLoadMoreView
import com.midea.prestorage.widgets.FilterDigitTextWatcher
import com.midea.prestorage.widgets.ScrollingLinearLayoutManager
import com.xuexiang.xqrcode.XQRCode
import java.math.BigDecimal

class PutInActivity : BaseActivity() {

    companion object {
        fun newIntent(
            context: Context,
            toTitle: String,
            packageResp: PackageResp?,
            batchResp: PackageResp?
        ): Intent {
            val intent = Intent(context, PutInActivity::class.java)
            intent.putExtra("toTitle", toTitle)
            intent.putExtra("packageResp", packageResp)
            intent.putExtra("batchResp", batchResp)
            return intent
        }
    }

    private lateinit var binding: ActivityPutInGoodsUnionBinding
    lateinit var inStorageVM: PutInVM
    val adapter = PutInAdapter()
    private val selectedPositions = mutableSetOf<Int>()
    var textWatcher: FilterDigitTextWatcher? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityPutInGoodsUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_put_in_goods_care
                )
            )
        } else {
            ActivityPutInGoodsUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_put_in_goods
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        inStorageVM = PutInVM(this)
        initView()
        initRecycle()
        initLoadMore()
        binding.vm = inStorageVM

        initWatcher()
        initListen()
    }

    private fun initListen() {
        inStorageVM.isPoint.observe(this, {
            if (it != 9999) {
                setWatcher(it)
            }
        })
    }

    private fun initWatcher() {
        if (textWatcher == null) {
            textWatcher =
                FilterDigitTextWatcher(binding.edQty, 0, true) {
                    if ("只能输入正整数" == it) {
                        binding.edQty.setText(
                            binding.edQty.text.toString().replace(Regex("[^0-9]"), "")
                        )
                        if (!binding.edQty.text.isNullOrEmpty()) {
                            binding.edQty.setSelection(binding.edQty.text!!.length)
                        }
                    }
                    AppUtils.showToast(this, it)
                }
        }
    }

    private fun setWatcher(isPoint: Int) {
        binding.edQty.removeTextChangedListener(textWatcher)
        if (isPoint != 0) {
            textWatcher?.limitDecimalPlaces = 4
        }else {
            textWatcher?.limitDecimalPlaces = 0
        }
        binding.edQty.addTextChangedListener(textWatcher)
    }

    private fun initView() {
        localRequest()

        binding.cbSelectAll.setOnCheckedChangeListener { _, isChecked ->
            inStorageVM.checkChange(isChecked)
            if (!isChecked) {
                selectedPositions.clear()
                inStorageVM.firstSelectedPosition = -1
            }
        }
    }

    private fun initRecycle() {
        //binding.srl.setOnRefreshListener(inStorageVM.onRefreshCommand)
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            binding.rv.layoutManager = ScrollingLinearLayoutManager(this)
        } else {
            binding.rv.layoutManager = LinearLayoutManager(this)
        }
        binding.rv.adapter = adapter
        adapter.setClickId(R.id.ll_checked)

        adapter.setOnDataChangeListenerPosition<ReplenishmentBean.ReplenishmentListBean> { bean: ReplenishmentBean.ReplenishmentListBean, isSelected: Boolean ->
            val results = adapter.data.filter { !it.isSelected }
            binding.cbSelectAll.isChecked = results.isEmpty()
            if (isSelected) {
                selectedPositions.add(adapter.data.indexOf(bean))
            } else {
                selectedPositions.remove(adapter.data.indexOf(bean))
            }

            if (inStorageVM.firstSelectedPosition != (selectedPositions.firstOrNull()
                    ?: -1) && (selectedPositions.firstOrNull() ?: -1) != -1
            ) {
                inStorageVM.firstSelectedPosition = selectedPositions.firstOrNull() ?: -1
                if (adapter.data[inStorageVM.firstSelectedPosition].isEditable != 0) {
                    inStorageVM.getRecommendLoc(adapter.data[inStorageVM.firstSelectedPosition])
                }
            }
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.initData(false)
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            adapter.loadMoreModule.loadMoreView = CareLoadMoreView()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    fun showNoDataInfo() {
        binding.rv.visibility = View.GONE
        binding.ivNoOrder.visibility = View.VISIBLE
    }

    fun showDataInfo() {
        binding.rv.visibility = View.VISIBLE
        binding.ivNoOrder.visibility = View.GONE
    }

    override fun onResume() {
        super.onResume()
        readStorageInfo()
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    private fun readStorageInfo() {
        inStorageVM.defaultStorage = Constants.userInfo?.putMode

        inStorageVM.setMode()
    }

    override fun onPause() {
        super.onPause()
        if (inStorageVM.defaultStorage == 1 && !TextUtils.isEmpty(inStorageVM.location.get())) {
            inStorageVM.location.get()?.let {
                inStorageVM.saveUserInfo(it)
            }
        }
    }

    fun stopRefresh() {
        //binding.srl.isRefreshing = false
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            inStorageVM.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    fun dismissAllSelect() {
        binding.cbSelectAll.visibility = View.INVISIBLE
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            binding.llSelectAll.visibility = View.GONE
        }
    }

    fun showAllSelect() {
        binding.cbSelectAll.visibility = View.VISIBLE
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            binding.llSelectAll.visibility = View.VISIBLE
        }
    }

    fun localRequest() {
        binding.edLocation.requestFocus()
    }

    fun goodsRequest() {
        binding.edGoods.requestFocus()
    }

    fun qtyRequest() {
        binding.edQty.requestFocus()
    }

    fun localUnable() {
        binding.edLocation.post {
            binding.edLocation.isEnabled = false
        }
    }

    fun localEnable() {
        binding.edLocation.isEnabled = true
    }

    fun setUnSelectAll() {
        binding.cbSelectAll.isChecked = false
    }

    class PutInAdapter :
        ListCheckBoxAdapter<ReplenishmentBean.ReplenishmentListBean>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_put_in_care else R.layout.item_put_in),
        LoadMoreModule {

        var isShowSelect = false
        var isSearch = false
        override fun convert(helper: BaseViewHolder?, item: BaseItemForPopup?) {
            super.convert(helper, item as BaseItemForPopup)

            if (item.isSelected) {
                helper?.setBackgroundResource(
                    R.id.img_goods,
                    if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.drawable.select_selected_care else R.mipmap.select_selected
                )
                helper?.setBackgroundResource(R.id.ll_bg, R.drawable.bg_round_rectangle_light_blue)
            } else {
                helper?.setBackgroundResource(
                    R.id.img_goods,
                    if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.drawable.select_normal_care else R.mipmap.select_normal
                )
                helper?.setBackgroundResource(R.id.ll_bg, R.drawable.bg_round_rectangle_white)
            }

            if (isSearch) {
                helper?.setBackgroundResource(R.id.ll_bg, R.drawable.bg_round_rectangle_white)
            }

            if (isShowSelect) {
                helper?.setBackgroundResource(
                    R.id.img_goods,
                    if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.drawable.select_selected_care else R.mipmap.select_selected
                )
                helper?.setBackgroundResource(R.id.ll_bg, R.drawable.bg_round_rectangle_light_blue)
            }

            val bean = item as ReplenishmentBean.ReplenishmentListBean
            helper?.setText(R.id.tv_unit_info, bean.unitQty + "/" + bean.unit)
            helper?.setText(R.id.tv_batch_info, bean.orderByAttribute + ":")
            helper?.setText(R.id.tv_task_type, "补货上架")
            helper?.setText(R.id.tv_storage, bean.toLoc)
            helper?.setText(R.id.tv_area, bean.toZoneName)

            when (bean.taskLevel) {
                1 -> helper?.setBackgroundResource(
                    R.id.img_status,
                    if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.drawable.ic_urgent else R.mipmap.icon_second
                )
                2 -> helper?.setBackgroundResource(
                    R.id.img_status,
                    if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.drawable.ic_jiaji else R.mipmap.icon_first
                )
            }

            helper?.setVisible(R.id.img_status, bean.taskLevel != 3)
            helper?.setGone(R.id.img_goods, isShowSelect)
        }

        fun setIsShowSelected(isShowSelect: Boolean) {
            <EMAIL> = isShowSelect
        }

        fun setIsSearchResult(isSearch: Boolean) {
            <EMAIL> = isSearch
        }
    }
}