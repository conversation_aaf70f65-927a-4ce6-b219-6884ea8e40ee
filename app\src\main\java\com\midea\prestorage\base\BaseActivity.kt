package com.midea.prestorage.base

import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.WindowManager
import android.widget.TextView
import com.midea.prestorage.beans.setting.ImplWarehouse
import com.midea.prestorage.beans.setting.LoginInfo
import com.midea.prestorage.function.login.LoginActivity
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.DbUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.ToastQueueUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.utils.WaitingDialogHelp
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity


/**
 * Created by LIXK5 on 2019/3/27.
 */
abstract class BaseActivity : RxAppCompatActivity() {

    lateinit var waitingDialogHelp: WaitingDialogHelp

    companion object {
        const val QR_CODE_BACK = 0X01
        const val QR_CODE_BACK1 = 0X02
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN)
        waitingDialogHelp = WaitingDialogHelp(this)
    }

    override fun onResume() {
        super.onResume()
        ToastUtils.getInstance().setTvInfo(this, getTvInfo())
        ToastQueueUtils.getInstance().setTvInfo(getTvInfo())
    }

    open fun toastDismiss() {
    }

    /**
     * 防空指向
     */
    override fun onDestroy() {
        super.onDestroy()
        ToastUtils.getInstance().setTvInfoNull(this)
        ToastQueueUtils.getInstance().setTvInfoNull()

        if (this::waitingDialogHelp.isInitialized) {
            waitingDialogHelp.hidenDialog()
        }
        waitingDialogHelp.removeCallback()
    }


    abstract fun getTvInfo(): TextView?

    val db = DbUtils.db

    // 其他界面 调这个方法获取 whCode
    fun getWhCode(): String {
        if (Constants.whInfo != null && !(Constants.whInfo!!.whCode.isNullOrBlank())) {
            return Constants.whInfo!!.whCode
        } else {
            Constants.userInfo =
                db.selector(LoginInfo::class.java).orderBy("isFirst", true).findFirst()
            Constants.whInfo = db.selector(ImplWarehouse::class.java)
                .where("whCode", "==", Constants.userInfo?.whCode).findFirst()
            // whCode 为空的话  从本地db缓存 重新读取一次
            return if (Constants.whInfo != null && !(Constants.whInfo!!.whCode.isNullOrBlank())) {
                Constants.whInfo!!.whCode
            } else {
                SPUtils.remove(application, "accessToken")
                SPUtils.remove(application, "host")
                SPUtils.remove(application, "serverName")
                //还是获取不到的话 应该是长时间没操作 干脆直接跳到登录界面
                val intent = Intent(this, LoginActivity::class.java)
                //跳Activity时清除掉当前Activity
                intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
                startActivity(intent)
                ""
            }
        }
    }

    // 其他界面 调这个方法获取 whName
    fun getWhName(): String {
        if (Constants.whInfo != null && !(Constants.whInfo!!.cdwhName.isNullOrBlank())) {
            return Constants.whInfo!!.cdwhName
        } else {
            Constants.userInfo =
                db.selector(LoginInfo::class.java).orderBy("isFirst", true).findFirst()
            Constants.whInfo = db.selector(ImplWarehouse::class.java)
                .where("whCode", "==", Constants.userInfo?.whCode).findFirst()
            // whCode 为空的话  从本地db缓存 重新读取一次
            return if (Constants.whInfo != null && !(Constants.whInfo!!.cdwhName.isNullOrBlank())) {
                Constants.whInfo!!.cdwhName
            } else {
                //还是获取不到的话 应该是长时间没操作 干脆直接跳到登录界面
                SPUtils.remove(application, "accessToken")
                SPUtils.remove(application, "host")
                SPUtils.remove(application, "serverName")
                val intent = Intent(this, LoginActivity::class.java)
                //跳Activity时清除掉当前Activity
                intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
                startActivity(intent)
                ""
            }
        }
        return ""
    }
}