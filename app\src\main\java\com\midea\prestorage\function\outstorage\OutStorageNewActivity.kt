package com.midea.prestorage.function.outstorage

import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestorage.base.App
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.ListChoiceClickPositionAdapter
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestoragesaas.databinding.ActivityOutStorageNewBinding
import com.midea.prestorage.dialog.TipDialog
import com.midea.prestorage.function.outstorage.dialog.DeleteBarcodeDialog
import com.midea.prestorage.function.outstorage.dialog.HandlingDialog
import com.midea.prestorage.function.outstorage.dialog.SendTipDialog
import com.midea.prestorage.function.outstorage.response.OutOrderData
import com.midea.prestorage.function.outstorage.response.ShipmentDetailSort
import com.midea.prestorage.function.picktaskdetail.PickTaskDetailActivity
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils

class OutStorageNewActivity : BaseViewModelActivity<OutStorageNewVM>() {
    private lateinit var binding: ActivityOutStorageNewBinding
    private lateinit var deleteDialog: DeleteBarcodeDialog
    private lateinit var handlingDialog: HandlingDialog
    private lateinit var sendTipDialog: SendTipDialog
    private lateinit var validateSomeDialog: SendTipDialog //存在部分发货时的友情提示是否部分发货
    private lateinit var tipDialog: TipDialog
    var adapter = OutStorageScanGoodsAdapter()
    private val datas = mutableListOf<OutOrderData>()

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_out_storage_new)
        vm = ViewModelProvider.AndroidViewModelFactory(application).create(OutStorageNewVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        //返回
        vm.finishActivity.observe(this, Observer<Boolean> {
            if (it) {
                finish()
            }
        })

        //波次号/订单号查询成功后光标调到sn码栏位
        vm.goodsRequest.observe(this, Observer<Boolean> {
            if (it) {
                goodsRequest()
            }
        })

        //弹出删除条码
        vm.isShowDeleteDialog.observe(this, Observer<Boolean> {
            if (it) {
                deleteDialog.show()
            }
        })

        //弹出装卸时间
        vm.isShowHandingDialog.observe(this, Observer<Boolean> {
            if (it) {
                handlingDialog.setStartTime(vm.handingStartTime.value.toString())
                handlingDialog.setEndTime(vm.handingEndTime.value.toString())
                handlingDialog.show()
            }
        })

        //收货时存在拣货未完成的弹窗
        vm.isShowTipDialog.observe(this, Observer<String> {
            sendTipDialog.setContent(it)
            sendTipDialog.show()
        })

        //收货时存在部分拣货的弹窗
        vm.isShowValidateSomeDialog.observe(this, Observer<String> {
            validateSomeDialog.setContent(it)
            validateSomeDialog.show()
        })

        //提示重置
        vm.containerChangeTip.observe(this, Observer<Boolean> {
            if (it) {
                containerChangeTip()
            }
        })

        vm.showDatas.observe(this, Observer<MutableList<ShipmentDetailSort>> {
            it.sort()
            showData(it)
        })

        //设置装卸时间成功后，弹出自动消失
        vm.dismissDialog.observe(this, Observer<Boolean> {
            if (it) {
                handlingDialog.dismiss()
            }
        })

        if (App.tenantCode != null && App.tenantCode == "annto") {
            binding.tvTime.visibility = View.VISIBLE
        }else {
            binding.tvTime.visibility = View.GONE
        }

        initData()
        initSpinner()
        initDialog()
        initRecycleView()
    }

    fun initData() {
        val orderNo = intent.getStringExtra("waveNo")
        if (!orderNo.isNullOrEmpty()) {
            vm.curOrderNo.value = orderNo
            vm.onEnterOrderNo()
        }
    }

    private fun initSpinner() {
        val beans = mutableListOf<String>(
            "1天",
            "3天",
            "7天"
        )
        binding.spinnerStatus.setItems(beans)
        binding.spinnerStatus.selectedIndex = 1

        binding.spinnerStatus.setOnItemSelectedListener { _, position, _, _ ->
            vm.dayInfo.value = beans[position]
            //vm.queryShipmentDetail()
        }

        DCUtils.goodsStatue(this, object : DCUtils.DCBack {

            override fun dcBack(statusDC: MutableList<DCBean>) {

            }
        })
    }

    private fun initDialog() {
        //删除条码dialog
        deleteDialog = DeleteBarcodeDialog(this)
        deleteDialog.setTitle("删除条码")
        deleteDialog.setDeleteBack(object : DeleteBarcodeDialog.DeleteBarcodeBack {
            override fun deleteBarcodeBack(barcode : String) {
                //deleteDialog.dismiss()
                vm.deleteBarcode(barcode)
            }
        })

        //装卸时间dialog
        handlingDialog = HandlingDialog(this)
        handlingDialog.setConfirmBack(object: HandlingDialog.ConfirmBack {
            override fun confirmBack(type: Int, time: String) {
                if(type == 1) {
                    vm.handingStartTime.value = time
                }else {
                    vm.handingEndTime.value = time
                }
                vm.handingTime(true)
            }

        })

        //发货时存在拣货未完成的弹窗dialog
        sendTipDialog = SendTipDialog(this)
        sendTipDialog.setTitle("提示")
        sendTipDialog.setConfirmBack(object: SendTipDialog.ConfirmBack {
            override fun confirmBack() {
                sendTipDialog.dismiss()

                //跳转到拣货执行界面
                val intent = PickTaskDetailActivity.newIntent(
                    this@OutStorageNewActivity,
                    vm.curOrderNo.value.toString().trim()
                )
                startActivity(intent)
            }

        })

        //存在部分发货时的友情提示是否部分发货
        validateSomeDialog = SendTipDialog(this)
        validateSomeDialog.setTitle("提示")
        validateSomeDialog.setConfirmBack(object: SendTipDialog.ConfirmBack {
            override fun confirmBack() {
                validateSomeDialog.dismiss()
                vm.sendConfirm() //发货确认
            }

        })

    }

    private fun containerChangeTip() {
//        tipDialog = TipDialog(this)
//        tipDialog.setTitle("提示")
//        tipDialog.setMsg("确定要重置吗?")
//        tipDialog.setOnTipBackListener(object : TipDialog.OnTipBack {
//            override fun onConfirmClick() {
//                if (CheckUtil.isFastDoubleClick()) {
//                    containerRequest()
//                }
//            }
//
//            override fun onDismissClick() {
//            }
//        })
//        tipDialog.show()
        containerRequest()
    }

    /**
     * 重置操作
     */
    fun containerRequest() {
        binding.etOrderNo.setText("")
        binding.edGoodsNo.setText("")
        binding.etOrderNo.isEnabled = true
        binding.etOrderNo.requestFocus()
        vm.isPalletEnter.value = false
        binding.spinnerStatus.selectedIndex = 2
        vm.dayInfo.value = "3天"

        adapter.data.clear()
        adapter.notifyDataSetChanged()

        vm.processInfo.value = ""
        vm.isNoData.value = adapter.data.isNullOrEmpty()
    }

    private fun goodsRequest() {
        binding.etOrderNo.isEnabled = false
        binding.edGoodsNo.requestFocus()
    }

    override fun onResume() {
        super.onResume()
        binding.etOrderNo.onFocusChangeListener = onFocusChangeListener
        binding.etOrderNo.requestFocus()
    }

    private val onFocusChangeListener =
        View.OnFocusChangeListener { view: View, hasFocus: Boolean ->
            if (!hasFocus) {
                if (!vm.isPalletEnter.value!!) {
                    vm.showErrorNotification("请先回车查询波次号", false)
                    view.post {
                        binding.etOrderNo.requestFocus()
                    }
                }
            }
        }

    fun initRecycleView() {
        binding.recycle.layoutManager = LinearLayoutManager(this)
        //binding.recycle.addItemDecoration(DividerItemDecoration(this, DividerItemDecoration.VERTICAL))
        binding.recycle.adapter = adapter
    }

    fun showData(data: MutableList<ShipmentDetailSort>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()

        vm.isNoData.value = adapter.data.isNullOrEmpty()
    }

    class OutStorageScanGoodsAdapter : ListChoiceClickPositionAdapter<ShipmentDetailSort>(R.layout.item_out_storage_scan_goods) {

        override fun convert(helper: BaseViewHolder, item: ShipmentDetailSort) {
            super.convert(helper, item)

            item.vs.sortFlag.get().let {
                when (it) {
                    0 -> {
                        helper.setBackgroundResource(R.id.rl_root, R.drawable.bg_round_rectangle_light_blue)
                        helper.setVisible(R.id.img_finish, false)
                    }   //进行中
                    1 -> {
                        helper.setBackgroundResource(R.id.rl_root, R.drawable.bg_round_rectangle_white)
                        helper.setVisible(R.id.img_finish, false)
                    }    //未完成
                    2 -> {
                        helper.setBackgroundResource(R.id.rl_root, R.drawable.bg_round_rectangle_green)
                        helper.setVisible(R.id.img_finish, true)
                    }    //已完成
                    else -> {
                        helper.setBackgroundResource(R.id.rl_root, R.drawable.bg_round_rectangle_white)
                        helper.setVisible(R.id.img_finish, false)
                    }
                }
            }

            if(!item.item.custItemCode.isNullOrBlank()) {
                helper.setText(R.id.tv_goodsCode, item.item.custItemCode)
            }else {
                helper.setText(R.id.tv_goodsCode, "")
            }

            if(!item.item.itemName.isNullOrBlank()) {
                helper.setText(R.id.tv_goodsName, item.item.itemName)
            }else {
                helper.setText(R.id.tv_goodsName, "")
            }

            if(!AppUtils.getBigDecimalValueStr(item.item.planQty).isNullOrBlank()) {
                helper.setText(R.id.tv_planNum, AppUtils.getBigDecimalValueStr(item.item.planQty))
            }else {
                helper.setText(R.id.tv_planNum, "")
            }

            if(!AppUtils.getBigDecimalValueStr(item.item.allocatedQty).isNullOrBlank()) {
                helper.setText(R.id.tv_spceStr, AppUtils.getBigDecimalValueStr(item.item.allocatedQty))
            }else {
                helper.setText(R.id.tv_spceStr, "")
            }

            if(!AppUtils.getBigDecimalValueStr(item.item.pickedQty).isNullOrBlank()) {
                helper.setText(R.id.tv_pickStr, AppUtils.getBigDecimalValueStr(item.item.pickedQty))
            }else {
                helper.setText(R.id.tv_pickStr, "")
            }

            if(!AppUtils.getBigDecimalValueStr(item.item.scannedQty).isNullOrBlank()) {
                helper.setText(R.id.tv_scanNum, AppUtils.getBigDecimalValueStr(item.item.scannedQty))
            }else {
                helper.setText(R.id.tv_scanNum, "")
            }

            if(item.item.isNotScan) {
                helper.setVisible(R.id.tv_no_scan, true)
            }else {
                helper.setVisible(R.id.tv_no_scan, false)
            }

            DCUtils.goodsStatue?.forEach {
                if(it.value == item.item.lotAtt04) {
                    helper.setText(R.id.tv_inventoryStsStr, it.key.toString())
                }
            }

        }
    }

}