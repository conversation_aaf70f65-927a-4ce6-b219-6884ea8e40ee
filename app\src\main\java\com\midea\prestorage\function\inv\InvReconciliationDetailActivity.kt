package com.midea.prestorage.function.inv

import android.app.AlertDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.RespAdjustDetailPage
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.dialog.InvReconciliationDialog
import com.midea.prestorage.function.inv.InvReconciliationDetailVM.Companion.INPUT_LOC
import com.midea.prestorage.function.inv.InvReconciliationDetailVM.Companion.INPUT_QTY
import com.midea.prestorage.function.inv.InvReconciliationDetailVM.Companion.INPUT_SERIAL
import com.midea.prestorage.function.inv.InvReconciliationDetailVM.Companion.MULTIPLE_UNIT_INPUT
import com.midea.prestorage.function.inv.InvReconciliationDetailVM.Companion.PIECE_SCAN
import com.midea.prestorage.function.inv.InvReconciliationDetailVM.Companion.RESERVED_LOTATT
import com.midea.prestorage.function.inv.InvReconciliationDetailVM.Companion.unitInputType
import com.midea.prestorage.function.inv.InvReconciliationDetailVM.Companion.lotType
import com.midea.prestorage.function.inv.InvReconciliationDetailVM.Companion.scanType
import com.midea.prestorage.function.inv.dialog.InputNumActualDialog
import com.midea.prestorage.function.inv.dialog.InvRecDeleteDialog
import com.midea.prestorage.function.inv.dialog.InvRecLotAttDialog
import com.midea.prestorage.function.inv.dialog.InvReconciliationInputDialog
import com.midea.prestorage.function.inv.response.*
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.widgets.FilterDigitTextWatcher
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityInvReconciliationDetailBinding
import com.midea.prestoragesaas.databinding.PopViewForSelectCustemItemCodeBinding
import java.math.BigDecimal

class InvReconciliationDetailActivity : BaseViewModelActivity<InvReconciliationDetailVM>() {
    private lateinit var binding: ActivityInvReconciliationDetailBinding
    private lateinit var statueDialog: FilterDialog
    private lateinit var unitDialog: FilterDialog
    val lot4Options = mutableListOf<String>()
    private lateinit var settingDialog: InvReconciliationDialog
    private lateinit var invRecLotAttDialog: InvRecLotAttDialog
    private lateinit var invRecDeleteDialog: InvRecDeleteDialog

    //69码或sn码 查询出多个custItemCodes的时候 弹框让用户选择custItemCode
    lateinit var dlgSelectCustItemCode: AlertDialog
    private lateinit var popBindingSelectCustItemCode: PopViewForSelectCustemItemCodeBinding
    lateinit var popAdapterSelectCustItemCode: PopListAdapter

    private lateinit var adapter: InStorageOrderAdapter
    var textWatcher: FilterDigitTextWatcher? = null

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_inv_reconciliation_detail)
        vm = ViewModelProvider.AndroidViewModelFactory(application)
            .create(InvReconciliationDetailVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        vm.inputType.observe(this, { result ->
            setStep(result)
        })

        vm.changeLocNotification.observe(this, {
            if (it) {
                vm.reset()
                setGoodsUnit(mutableListOf())
                vm.inputType.value = INPUT_LOC
                adapter.data.clear()
                adapter.notifyDataSetChanged()
                vm.isNoData.set(false)
            }
        })

        //弹出商品状态
        vm.isShowStatueDialog.observe(this, {
            if (it) {
                statueDialog.show()
            }
        })

        //弹出选中单位
        vm.isShowUnitDialog.observe(this, {
            if (it) {
                unitDialog.show()
            }
        })

        //设置dialog
        vm.isShowSettingDialog.observe(this, {
            if (it) {
                settingDialog.show()
            }
        })

        //选择商品
        vm.showGoods.observe(this, { data ->
            //扫69码 后端没有返回 custItemCode  如果有custItemCode数组，就弹框选择
            dlgSelectCustItemCode.show()
            popAdapterSelectCustItemCode.data.clear()
            data.forEach {
                popAdapterSelectCustItemCode.addData(it)
            }
            popAdapterSelectCustItemCode.notifyDataSetChanged()
        })

        //初始化商品下拉单位
        vm.setUnit.observe(this, { data ->
            if (data.isNotEmpty()) {
                setGoodsUnit(data)
            }
        })

        vm.lotDetail.observe(this, { data ->
            if (data.any { "LOT_ATT04" != it.lotAtt }) {
                invRecLotAttDialog.setData(data)
                invRecLotAttDialog.materialList = vm.tempMaterialList
                invRecLotAttDialog.tag = "${vm.locCode.get()}${vm.serialNo.get()}"
                invRecLotAttDialog.show()
            } else {
                vm.template = data
                vm.inputType.value = INPUT_QTY
                vm.lotDetailsStr.set(vm.handleLotAttStr(vm.template))

                //若勾选逐件扫描，则扫描商品成功后数量栏位默认设置为1,直接走 新增调账明细 接口
                if (scanType == PIECE_SCAN) {
                    setQtyStatus()
                } else if (unitInputType == MULTIPLE_UNIT_INPUT) {
                    showInputNumActualDialog()
                }
            }
        })

        vm.setQtyNotification.observe(this, {
            if (it) {
                setQtyStatus()
            }
        })

        vm.loadMoreComplete.observe(this, {
            if (it == 1) {
                adapter.loadMoreModule.loadMoreComplete()
            } else if (it == 2) {
                adapter.loadMoreModule.loadMoreEnd()
            }
        })

        vm.showDatas.observe(this, {
            showData(it)
        })

        vm.loadMoreDatas.observe(this, {
            loadMoreData(it)
        })

        vm.resetGoodsUnit.observe(this, {
            if (it) {
                setGoodsUnit(mutableListOf())
            }
        })

        vm.isPoint.observe(this, {
            if ("-1" != it) {
                setWatcher(it)
            }
        })

        vm.showDialogLiveEvent.observe(this, {
            showInputNumActualDialog()
        })

        initWatcher()
        initData()
        initDialog()
        initSpinner()
        initPopWinSelectCustItemCode()
        initRecycleView()
        initLoadMore()
    }

    private fun initWatcher() {
        if (textWatcher == null) {
            textWatcher =
                FilterDigitTextWatcher(binding.edQty, 0, true) {
                    if ("只能输入正整数" == it) {
                        binding.edQty.setText(
                            binding.edQty.text.toString().replace(Regex("[^0-9]"), "")
                        )
                        if (!binding.edQty.text.isNullOrEmpty()) {
                            binding.edQty.setSelection(binding.edQty.text!!.length)
                        }
                    }
                    AppUtils.showToast(this, it)
                }
        }
    }

    private fun setWatcher(isPoint: String?) {
        binding.edQty.removeTextChangedListener(textWatcher)
        when (isPoint) {
            "Y" -> {
                if ("EA" == vm.curUnit) {
                    textWatcher?.limitDecimalPlaces = 4
                }else {
                    textWatcher?.limitDecimalPlaces = 0
                }
            }
            else -> {
                textWatcher?.limitDecimalPlaces = 0
            }
        }
        binding.edQty.addTextChangedListener(textWatcher)
    }

    fun initData() {
        vm.respAdjustList =
            intent.getParcelableExtra("RespAdjustList")

        vm.respAdjustList?.let {
            vm.title.value = it.adNo
        }

        vm.inputType.value = INPUT_LOC
    }

    private fun initDialog() {
        //商品状态dialog
        statueDialog = FilterDialog(this)
        statueDialog.setTitle("请选择商品状态")
        statueDialog.dismissEdit()
        statueDialog.setOnCheckListener {
            vm.statusName.set(it.showInfo)
            vm.getStatusCode()
            vm.lotDetailsStr.set(vm.handleLotAttStr(vm.template))
            statueDialog.dismiss()
        }

        //商品单位dialog
        unitDialog = FilterDialog(this)
        unitDialog.setTitle("请选择单位")
        unitDialog.dismissEdit()
        unitDialog.setOnCheckListener {
            vm.unitName.set(it.showInfo)
            vm.qty.set("") //切换单位，把数量清空，因为加入了小数点，防止在EA时输入了小数点，切换到CS后还保留了小数点的情况
            vm.setCdprQuantity(vm.unitName.get().toString())
            unitDialog.dismiss()
        }

        //设置dialog
        settingDialog = InvReconciliationDialog(this)
        settingDialog.setOnSettingClick(object : InvReconciliationDialog.SettingBack {
            override fun onConfirmClick(scan: Int, lot: Int, input: Int) {
                scanType = scan
                lotType = lot
                unitInputType = input
            }
        })

        //商品属性采集dialog
        invRecLotAttDialog = InvRecLotAttDialog(this)
        invRecLotAttDialog.setBackData(object : InvRecLotAttDialog.BackData {
            override fun onConfirmClick(templates: MutableList<LotDetail>?) {
                invRecLotAttDialog.tag?.let {
                    //保存属性模版
                    if (lotType == RESERVED_LOTATT) {
                        vm.templates[it] = templates
                    }
                    vm.template = templates
                    vm.inputType.value = INPUT_QTY
                    vm.lotDetailsStr.set(vm.handleLotAttStr(vm.template))

                    //若勾选逐件扫描，则扫描商品成功后数量栏位默认设置为1,直接走 新增调账明细 接口
                    if (scanType == PIECE_SCAN) {
                        setQtyStatus()
                    } else if (unitInputType == MULTIPLE_UNIT_INPUT) {
                        showInputNumActualDialog()
                    }
                }
            }

            override fun onCancelClick() {

            }
        })

        //作废调账数量dialog
        invRecDeleteDialog = InvRecDeleteDialog(this)
        invRecDeleteDialog.setBackData(object : InvRecDeleteDialog.BackData {
            override fun onConfirmClick(id: Long?, actQty: BigDecimal?) {
                vm.reduceDetail(id, actQty) {
                    invRecDeleteDialog.dismiss()
                }
            }

            override fun onCancelClick() {
            }
        })
    }

    fun showInputNumActualDialog() {
        //新增盘点库位dialog
        val inputDialog = InvReconciliationInputDialog(this)
        inputDialog.inputBack = object : InvReconciliationInputDialog.InputNumActualBack {
            override fun inputOk(data: RespAttributeList, num: BigDecimal) {
                vm.addDetail(num)
            }

            override fun inputFail() {
            }
        }
        inputDialog.deleteInfo = vm.attributeBean
        inputDialog.show()
    }

    private fun initSpinner() {
        DCUtils.goodsStatue(this, object : DCUtils.DCBack {

            override fun dcBack(statusDC: MutableList<DCBean>) {
                statusDC.forEach {
                    lot4Options.add(it.key.toString())
                }

                val result = statusDC.find { it.value == "Y" }
                if (result != null) {
                    binding.vm!!.statusName.set(result.key)
                } else {
                    //默认选第一个
                    if (lot4Options.size > 0) {
                        binding.vm!!.statusName.set(lot4Options[0])
                    }
                }

                vm.getStatusCode()

                addStatueData(lot4Options)
            }
        })
    }

    // 初始化选择客户商品编码的弹窗
    private fun initPopWinSelectCustItemCode() {

        val popViewSelectCustItemCode =
            LayoutInflater.from(this).inflate(R.layout.pop_view_for_select_custem_item_code, null)
        popBindingSelectCustItemCode = DataBindingUtil.bind(popViewSelectCustItemCode)!!

        val alertDialogBuilder = AlertDialog.Builder(this)
        alertDialogBuilder.setView(popViewSelectCustItemCode)
        dlgSelectCustItemCode = alertDialogBuilder.create()

        popAdapterSelectCustItemCode = PopListAdapter()
        popBindingSelectCustItemCode.recyclerViewInPop.layoutManager = LinearLayoutManager(this)
        popBindingSelectCustItemCode.recyclerViewInPop.adapter = popAdapterSelectCustItemCode

        popAdapterSelectCustItemCode.setOnItemClickListener { adapter, _, position ->
            val data = adapter.getItem(position) as RespMaterialList
            vm.serialNo.set(data.custItemCode ?: "")
            vm.anntoItemCode.set(data.itemCode ?: "")
            vm.custItemCode.set(data.custItemCode ?: "")
            vm.itemName.set(data.itemName ?: "")
            vm.tempMaterialList = data
            vm.isPoint.value = data.isPoint
            vm.getAttributeList(data)
            dlgSelectCustItemCode.dismiss()
        }

        popBindingSelectCustItemCode.closePop.setOnClickListener {
            dlgSelectCustItemCode.dismiss()
        }

    }

    private fun setGoodsUnit(data: MutableList<PackageDetail>) {
        val unitNames = data.mapNotNull { it.unitName }
        val mainResult = data.find { it.unitCode == "EA" }

        if (mainResult != null) {
            binding.vm!!.unitName.set(mainResult.unitName)
        } else {
            //默认选第一个
            if (unitNames.isNotEmpty()) {
                binding.vm!!.unitName.set(unitNames[0])
            }
        }

        vm.setCdprQuantity(vm.unitName.get().toString())

        unitDialog.addAllData(unitNames.map { BaseItemShowInfo(it) }.toMutableList())
    }

    fun addStatueData(data: MutableList<String>) {
        statueDialog.addAllData(data.map { BaseItemShowInfo(it) }.toMutableList())
    }

    fun setStep(step: Int) {
        when (step) {
            INPUT_LOC -> {
                binding.etLocCode.isEnabled = true
                binding.edGoods.isEnabled = false
                binding.edQty.isEnabled = false
                AppUtils.requestFocus(binding.etLocCode)
                binding.srl.visibility = View.INVISIBLE
            }
            INPUT_SERIAL -> {
                binding.etLocCode.isEnabled = false
                binding.edGoods.isEnabled = true
                binding.edQty.isEnabled = false
                AppUtils.requestFocus(binding.edGoods)
                binding.srl.visibility = View.VISIBLE
            }
            INPUT_QTY -> {
                binding.etLocCode.isEnabled = false
                binding.edGoods.isEnabled = true
                binding.edQty.isEnabled = true
                AppUtils.requestFocus(binding.edQty)
                binding.srl.visibility = View.VISIBLE
            }
        }
    }

    fun setQtyStatus() {
        vm.qty.set("1")
        binding.etLocCode.isEnabled = false
        binding.edGoods.isEnabled = false
        binding.edQty.isEnabled = false

        vm.addDetail()
    }

    fun initRecycleView() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        adapter = InStorageOrderAdapter()
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.itemAnimator = null
        binding.recycle.adapter = adapter

        adapter.setOnItemClickListener { adapter, _, position ->
            val bean = adapter.data[position] as RespAdjustDetailPage
            invRecDeleteDialog.setData(bean)
            invRecDeleteDialog.show()
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.initOrderList()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    fun showData(data: MutableList<RespAdjustDetailPage>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()

        vm.isNoData.set(adapter.data.isNullOrEmpty())
        vm.isRefreshing.set(false)
    }

    fun loadMoreData(data: MutableList<RespAdjustDetailPage>?) {
        data?.let { adapter.addData(it) }
        adapter.notifyDataSetChanged()
    }

    class PopListAdapter :
        CommonAdapter<RespMaterialList>(R.layout.item_pop_view_for_select_cust_item_code_pop) {
        override fun convert(holder: BaseViewHolder?, item: RespMaterialList) {
            super.convert(holder, item)

            holder?.setGone(R.id.tv_item_name, item.itemName.isNullOrEmpty())
        }
    }

    class InStorageOrderAdapter :
        CommonAdapter<RespAdjustDetailPage>(
            R.layout.item_inv_reconciliation_detail
        ), LoadMoreModule {

        override fun convert(holder: BaseViewHolder?, item: RespAdjustDetailPage?) {
            super.convert(holder, item)

            holder?.setText(
                R.id.tv_actQty,
                AppUtils.getBigDecimalValueStr(item?.actQty) + " " + (item?.packageDetails?.find { "EA" == it.cdprUnit }?.cdprDesc
                    ?: "")
            )

        }
    }
}