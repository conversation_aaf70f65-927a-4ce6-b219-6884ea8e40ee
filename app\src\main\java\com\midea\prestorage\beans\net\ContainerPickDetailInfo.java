package com.midea.prestorage.beans.net;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class ContainerPickDetailInfo implements Serializable {

    private String id;
    private String containerCode;
    private String whCode;
    private String createUserCode;
    private String createTime;
    private String pickUserCode;
    private String pickStartTime;
    private String csUnit;
    private String eaUnit;
    private BigDecimal sumQty;
    private BigDecimal sumCsQty;
    private BigDecimal sumEaQty;
    private String pickUserName;

    private List<ContainerPickDetailListV2> outPickContainerList;

    public String getPickUserName() {
        return pickUserName;
    }

    public void setPickUserName(String pickUserName) {
        this.pickUserName = pickUserName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public BigDecimal getSumQty() {
        return sumQty;
    }

    public void setSumQty(BigDecimal sumQty) {
        this.sumQty = sumQty;
    }

    public BigDecimal getSumCsQty() {
        return sumCsQty;
    }

    public void setSumCsQty(BigDecimal sumCsQty) {
        this.sumCsQty = sumCsQty;
    }

    public BigDecimal getSumEaQty() {
        return sumEaQty;
    }

    public void setSumEaQty(BigDecimal sumEaQty) {
        this.sumEaQty = sumEaQty;
    }

    public String getPickUserCode() {
        return pickUserCode;
    }

    public void setPickUserCode(String pickUserCode) {
        this.pickUserCode = pickUserCode;
    }

    public String getPickStartTime() {
        return pickStartTime;
    }

    public void setPickStartTime(String pickStartTime) {
        this.pickStartTime = pickStartTime;
    }

    public List<ContainerPickDetailListV2> getOutPickContainerList() {
        return outPickContainerList;
    }

    public void setOutPickContainerList(List<ContainerPickDetailListV2> outPickContainerList) {
        this.outPickContainerList = outPickContainerList;
    }

    public String getCsUnit() {
        return csUnit;
    }

    public void setCsUnit(String csUnit) {
        this.csUnit = csUnit;
    }

    public String getEaUnit() {
        return eaUnit;
    }

    public void setEaUnit(String eaUnit) {
        this.eaUnit = eaUnit;
    }
}