package com.midea.prestorage.function.containerpick.adapter

import com.chad.library.adapter.base.BaseNodeAdapter
import com.chad.library.adapter.base.entity.node.BaseNode
import com.chad.library.adapter.base.module.LoadMoreModule
import com.midea.prestorage.function.containerpick.fragment.BulkAlreadyPackedVM
import com.midea.prestorage.function.containerpick.provider.BulkPickAlreadyDetailProvider
import com.midea.prestorage.function.containerpick.provider.BulkPickAlreadyHeaderProvider
import com.midea.prestorage.function.containerpick.provider.BulkPickToBeWrap

class BulkAlreadyPackedAdapter(vm: BulkAlreadyPackedVM) :
    BaseNodeAdapter() {

    companion object {
        const val TYPE_HEADER = 1
        const val TYPE_DETAIL = 2
    }

    init {
        addNodeProvider(BulkPickAlreadyHeaderProvider(vm))
        addNodeProvider(BulkPickAlreadyDetailProvider(vm))
    }

    override fun getItemType(data: List<BaseNode>, position: Int): Int {
        return if (data[position] is BulkPickToBeWrap) TYPE_HEADER else TYPE_DETAIL
    }

}