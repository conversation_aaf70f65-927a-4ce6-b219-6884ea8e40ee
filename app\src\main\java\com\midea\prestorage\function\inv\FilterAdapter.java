package com.midea.prestorage.function.inv;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Filter;
import android.widget.Filterable;
import android.widget.TextView;

import com.midea.prestorage.beans.base.BaseItemShowInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/10/22$
 */
public class FilterAdapter<T> extends BaseAdapter implements Filterable {

    private List<T> mOriginalValues;
    private List<T> mObjects;
    private final Object mLock = new Object();
    private int mResource;
    private int mDropDownResource;
    private Context mContext = null;
    private MyFilter mFilter = null;
    private LayoutInflater mInflater = null;

    public FilterAdapter(Context context, int textViewResourceId) {
        init(context, textViewResourceId);
    }

    private void init(Context context, int resource) {
        mContext = context;
        mInflater = (LayoutInflater) context
                .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        mResource = mDropDownResource = resource;
        mObjects = new ArrayList<T>();
        mFilter = new MyFilter();
    }

    public void add(T object) {
        if (mOriginalValues != null) {
            synchronized (mLock) {
                mOriginalValues.add(object);
            }
        } else {
            mObjects.add(object);
        }
    }

    public void addAll(List<T> object) {
        if (mOriginalValues != null) {
            synchronized (mLock) {
                mOriginalValues.addAll(object);
            }
        } else {
            mObjects.addAll(object);
        }
    }

    public void insert(T object, int index) {
        if (mOriginalValues != null) {
            synchronized (mLock) {
                mOriginalValues.add(index, object);
            }
        } else {
            mObjects.add(index, object);
        }
    }

    public void remove(T object) {
        if (mOriginalValues != null) {
            synchronized (mLock) {
                mOriginalValues.remove(object);
            }
        } else {
            mObjects.remove(object);
        }
    }

    public void clear() {
        if (mOriginalValues != null) {
            synchronized (mLock) {
                mOriginalValues.clear();
            }
        } else {
            mObjects.clear();
        }
    }

    public Context getContext() {
        return mContext;
    }

    @Override
    public int getCount() {
        return mObjects.size();
    }

    @Override
    public T getItem(int position) {
        return mObjects.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        return createViewFromResource(position, convertView, parent, mResource);
    }

    private View createViewFromResource(int position, View convertView,
                                        ViewGroup parent, int resource) {
        View view;
        TextView text;

        if (convertView == null) {
            view = mInflater.inflate(resource, parent, false);
        } else {
            view = convertView;
        }

        try {
            text = (TextView) view;
        } catch (ClassCastException e) {
            throw new IllegalStateException(
                    "ArrayAdapter requires the resource ID to be a TextView", e);
        }

        T item = getItem(position);
        if (item instanceof CharSequence) {
            text.setText((CharSequence) item);
        } else {
            text.setText(item.toString());
        }

        return view;
    }

    @Override
    public Filter getFilter() {
        return mFilter;
    }

    public void setDropDownViewResource(int resource) {
        this.mDropDownResource = resource;
    }

    @Override
    public View getDropDownView(int position, View convertView, ViewGroup parent) {
        return createViewFromResource(position, convertView, parent,
                mDropDownResource);
    }

    private class MyFilter extends Filter {

        @Override
        protected FilterResults performFiltering(CharSequence constraint) {
            FilterResults results = new FilterResults();
            if (mOriginalValues == null) {
                synchronized (mLock) {
                    mOriginalValues = new ArrayList<T>(mObjects);
                }
            }
            int count = mOriginalValues.size();
            ArrayList<T> values = new ArrayList<T>();

            for (int i = 0; i < count; i++) {
                T value = mOriginalValues.get(i);
                String valueText = value.toString();
                if (null != valueText && null != constraint
                        && valueText.contains(constraint)) {
                    values.add(value);
                }
            }
            results.values = values;
            results.count = values.size();
            return results;
        }

        @Override
        protected void publishResults(CharSequence constraint,
                                      FilterResults results) {
            mObjects = (List<T>) results.values;
            if (results.count > 0) {
                notifyDataSetChanged();
            } else {
                notifyDataSetInvalidated();
            }
        }
    }
}
