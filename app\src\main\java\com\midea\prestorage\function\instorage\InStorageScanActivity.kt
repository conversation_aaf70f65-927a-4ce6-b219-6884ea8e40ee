package com.midea.prestorage.function.instorage


import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.github.zawadz88.materialpopupmenu.popupMenu
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestoragesaas.databinding.ActivityInStorageReceiveBinding
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.instorage.response.HandlingGroup
import com.midea.prestorage.function.instorage.response.InOrderData
import com.midea.prestorage.function.inv.response.BsLocation
import com.midea.prestorage.function.outstorage.dialog.EditLotInfoDialog
import com.midea.prestorage.function.outstorage.dialog.EditReceiptNumDialog
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.TTSUtils
import com.xuexiang.xqrcode.XQRCode


class InStorageScanActivity : BaseActivity() {

    lateinit var binding: ActivityInStorageReceiveBinding
    var adapter = ListScanStorageAdapter()

    lateinit var handlingGroupDialog: FilterDialog
    lateinit var locationsDialog: FilterDialog
    lateinit var editLotInfoDialog: EditLotInfoDialog
    lateinit var editReceiptNumDialog: EditReceiptNumDialog
    val lot4Options = mutableListOf<String>()

    var isNeedRefresh = false


    companion object {
        val CODE_LOAD_CONTAINER = 6698
    }

    var toolMenu = popupMenu {
        section {
            title = "更多操作:"
            item {
                label = "申请不扫码"
                //icon = R.drawable.abc_ic_menu_copy_mtrl_am_alpha //optional
                callback = { //optional
                    binding.vm!!.requestUnscan()
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_in_storage_receive)
        binding.vm = InStorageScanVM(this)

        initSpinner()
        initRecycleView()
        initDialog()

        DCUtils.initLot4dicts(this, null)

        //加载装卸队数据
        binding.vm!!.initHandlingGroupList()
        TTSUtils.initTts(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        TTSUtils.stop()
        TTSUtils.shutdown()
    }

    override fun onResume() {
        super.onResume()
        isNeedRefresh = false
        binding.vm!!.loadInOrderDatas()
    }


    // 商品库存状态 下拉框
    private fun initSpinner() {

        DCUtils.initLot4dicts(this, object : DCUtils.OnInitFinish {
            override fun finish() {

                DCUtils.lot4TypeC2N.values.forEach {
                    lot4Options.add(it.toString())
                }

                //默认选第一个
                if (lot4Options.size > 0) {
                    binding.vm!!.curSelectLot4Name = lot4Options[0]
                    binding.vm!!.statueStr.set(lot4Options[0])
                }

                binding.vm!!.addStatueData(lot4Options)
            }
        })
    }


    fun initRecycleView() {
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter
    }


    private fun initDialog() {
        handlingGroupDialog = FilterDialog(this)
        handlingGroupDialog.setTitle("选择装卸组")
        handlingGroupDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            it as HandlingGroup
            binding.vm!!.handlingGroupCode.set(it.handlingCode)
            binding.vm!!.handlingGroupName.set(it.handlingName)
            handlingGroupDialog.dismiss()
        })

        locationsDialog = FilterDialog(this)
        locationsDialog.setTitle("选择上架库位")
        locationsDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            locationsDialog.dismiss()
            binding.vm!!.afterSelectLocation(it as BsLocation)
        })

        editLotInfoDialog = EditLotInfoDialog(this)
        editReceiptNumDialog = EditReceiptNumDialog(this)
    }

    override fun finish() {
        val it = Intent()
        it.putExtra("containerCode", binding.vm!!.containerCode)
        setResult(RESULT_OK, it)

        super.finish()
    }


    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == CODE_LOAD_CONTAINER) {
            if (data != null && data.getStringExtra("confirmSuccess") != null) {
                if (data.getStringExtra("confirmSuccess").equals("1")) {
                    Log.e("tao","扫码界面收到《上架成功》指令")
                    //如果是上架成功回来这个界面 就重新获取容器号
                    binding.vm!!.reloadContainerCode()
                }
            }
        } else if (requestCode == BaseActivity.QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果
            binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }


    }


    // 某个入库单或波次单 里的商品数据列表
    class ListScanStorageAdapter : ListChoiceClickAdapter<InOrderData>(R.layout.item_for_scan_instorage) {

        override fun convert(helper: BaseViewHolder, item: InOrderData) {
            super.convert(helper, item)

            //货品描述
            if (!item.itemName.isNullOrEmpty()) {
                helper.setText(R.id.tvItemName, item.itemName)
            } else {
                helper.setText(R.id.tvItemName, "")
            }

            // 客户商品编码
            if (item.custItemCode != null) {
                helper.setText(R.id.tvCustItemCode, item.custItemCode.toString())
            } else  {
                helper.setText(R.id.tvCustItemCode, "")
            }

            // 是否 有申请不扫码并且审核通过
            /*if (!item.unscanMark.isNullOrBlank() && item.unscanMark.equals("01")) {
                // 01表示审核通过
                // 前置仓不扫码标识
                helper.setText(R.id.tvUnScanMark, "不扫码")
            } else */
            if (!item.cdcmUnscanMark.isNullOrBlank() && item.cdcmUnscanMark.equals("Y")) {
                // 大物流不扫码标识
                //helper.setText(R.id.tvUnScanMark, Html.fromHtml("<u>" + "不扫码" + "</u>"))
                helper.setText(R.id.tvUnScanMark, "不扫码" )
            } else {
                helper.setText(R.id.tvUnScanMark, "")
            }


            // 数量
            if (item.totalQty != null) {
                helper.setText(R.id.tvTotalQty, item.totalQty.toDouble().toInt().toString())
            } else {
                helper.setText(R.id.tvTotalQty, "")
            }

            // 已扫
            if (item.scanNum != null) {
                helper.setText(R.id.tvScanNum, item.scanNum.toDouble().toInt().toString())
            } else {
                helper.setText(R.id.tvScanNum, "")
            }


            // 已完成的 标记为绿色背景色  未完成的白色背景色
            if (item.totalQty != null && item.scanNum != null) {
                if (item.totalQty.toInt().equals(item.scanNum.toInt())) {
                    helper.setBackgroundColor(R.id.llScanItem, Color.parseColor("#8de0cc"))
                } else {
                    helper.setBackgroundColor(R.id.llScanItem, Color.parseColor("#ffffff"))
                }
            } else {
                helper.setBackgroundColor(R.id.llScanItem, Color.parseColor("#ffffff"))
            }


            //入库单号
            /*  2021年11月11日 星期四 需求 ： 不需要显示入库单号
            if (item.receiptCode != null) {
                helper.setText(R.id.tvInOrderNo, item.receiptCode.toString())
            }
            */


            // 属性4  状态
            if (!item.lotAtt04.isNullOrEmpty()) {
                if (DCUtils.lot4TypeC2N.get(item.lotAtt04) != null) {
                    helper.setText(R.id.tvLot4, DCUtils.lot4TypeC2N.get(item.lotAtt04).toString())
                } else {
                    helper.setText(R.id.tvLot4, item.lotAtt04)
                }

                if (item.lotAtt04.toUpperCase().equals("Y")) {  //正品
                    helper.setTextColorRes(R.id.tvLot4, R.color.ui_font_color_blue)
                } else if (item.lotAtt04.toUpperCase().equals("N")) {  //不良品
                    helper.setTextColorRes(R.id.tvLot4, R.color.ui_font_color_orange)
                } else if (item.lotAtt04.toUpperCase().equals("B")) { //包装破损
                    helper.setTextColorRes(R.id.tvLot4, R.color.ui_font_color_red)
                } else {
                    helper.setTextColorRes(R.id.tvLot4, R.color.ui_font_color_default)
                }
            } else {
                helper.setText(R.id.tvLot4, "")
            }


        }
    }
}