package com.midea.prestorage.dialog

import android.app.AlertDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter.OnCheckListener
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.receive.dialog.DialogContainerDeleteUnionBinding
import com.midea.prestoragesaas.databinding.DialogListClickBinding
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/6/26$
 */
class FilterDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {

    private var binding: DialogListClickUnionBinding
    private val adapter =
        ListChoiceClickAdapter<BaseItemShowInfo>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.dialog_list_item_click_care else R.layout.dialog_list_item_click)

    init {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window?.setGravity(Gravity.CENTER)
            window?.attributes?.run {
                gravity = Gravity.CENTER
            }
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_list_click_care, null)
            setView(contentView)
            DialogListClickUnionBinding.V2(DataBindingUtil.bind(contentView)!!)
        } else {
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_list_click, null)
            setView(contentView)
            DialogListClickUnionBinding.V1(DataBindingUtil.bind(contentView)!!)
        }
        binding.vm = FilterDialogVM(this)

        setCanceledOnTouchOutside(true)

        initRecycleView()
    }

    private fun initRecycleView() {
        binding.recycle.layoutManager = LinearLayoutManager(mContext)
        binding.recycle.adapter = adapter
    }

    fun addAllData(beans: MutableList<BaseItemShowInfo>) {
        binding!!.vm!!.allData = beans
        changeDataNotify(beans)
    }

    fun addAllDataV2(beans: MutableList<BaseItemShowInfo>) {
        adapter.data.addAll(beans)
        adapter.notifyDataSetChanged()
    }

    fun addFirst(bean: BaseItemShowInfo) {
        adapter.data.add(0, bean)
        adapter.notifyDataSetChanged()
    }

    fun changeDataNotify(beans: List<BaseItemShowInfo>) {
        adapter.setNewInstance(beans.toMutableList())
        adapter.notifyDataSetChanged()
    }

    fun setTitle(title: String) {
        if (!TextUtils.isEmpty(title)) {
            binding!!.vm!!.title.set(title)
        }
    }

    fun getData(): MutableList<BaseItemShowInfo> {
        return adapter.data
    }

    fun setOnCheckListener(onCheckListener: OnCheckListener<BaseItemShowInfo>) {
        adapter.setOnCheckListener(onCheckListener)
    }

    fun dismissEdit() {
        binding!!.vm!!.editUnable()
    }

    override fun dismiss() {
        binding!!.vm!!.cleanFilter()
        super.dismiss()
    }

    fun setUnCancel() {
        binding.imgClose.visibility = View.INVISIBLE
        setCanceledOnTouchOutside(false)
    }

    fun setCancel() {
        binding.imgClose.visibility = View.VISIBLE
        setCanceledOnTouchOutside(true)
    }

    fun lockSize() {
        binding.recycle.layoutParams.height = AppUtils.dp2px(mContext, 200)
    }
}
