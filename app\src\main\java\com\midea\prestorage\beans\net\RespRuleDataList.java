package com.midea.prestorage.beans.net;

import java.util.List;

public class RespRuleDataList {

    private String cdcmBarcode;
    private String cdcmMaterialNo;
    private String cdcmCustMaterialNo;
    private Object cdcmOwnerGroupCode;
    private String cdcmNameCn;
    private String cdcmBarcode69;
    private String cdcmCustomerCode;
    private String cdcmRuleCode;
    private List<DetailsDTO> details;
    private String userdefined1;
    private String userdefined2;
    private String userdefined3;
    private String userdefined4;
    private String userdefined5;
    private String userdefined6;
    private String userdefined7;
    private String userdefined8;
    private String userdefined9;
    private String userdefined10;

    public String getCdcmBarcode() {
        return cdcmBarcode;
    }

    public void setCdcmBarcode(String cdcmBarcode) {
        this.cdcmBarcode = cdcmBarcode;
    }

    public String getCdcmMaterialNo() {
        return cdcmMaterialNo;
    }

    public void setCdcmMaterialNo(String cdcmMaterialNo) {
        this.cdcmMaterialNo = cdcmMaterialNo;
    }

    public String getCdcmCustMaterialNo() {
        return cdcmCustMaterialNo;
    }

    public void setCdcmCustMaterialNo(String cdcmCustMaterialNo) {
        this.cdcmCustMaterialNo = cdcmCustMaterialNo;
    }

    public Object getCdcmOwnerGroupCode() {
        return cdcmOwnerGroupCode;
    }

    public void setCdcmOwnerGroupCode(Object cdcmOwnerGroupCode) {
        this.cdcmOwnerGroupCode = cdcmOwnerGroupCode;
    }

    public String getCdcmNameCn() {
        return cdcmNameCn;
    }

    public void setCdcmNameCn(String cdcmNameCn) {
        this.cdcmNameCn = cdcmNameCn;
    }

    public String getCdcmBarcode69() {
        return cdcmBarcode69;
    }

    public void setCdcmBarcode69(String cdcmBarcode69) {
        this.cdcmBarcode69 = cdcmBarcode69;
    }

    public String getCdcmCustomerCode() {
        return cdcmCustomerCode;
    }

    public void setCdcmCustomerCode(String cdcmCustomerCode) {
        this.cdcmCustomerCode = cdcmCustomerCode;
    }

    public String getCdcmRuleCode() {
        return cdcmRuleCode;
    }

    public void setCdcmRuleCode(String cdcmRuleCode) {
        this.cdcmRuleCode = cdcmRuleCode;
    }

    public List<DetailsDTO> getDetails() {
        return details;
    }

    public void setDetails(List<DetailsDTO> details) {
        this.details = details;
    }

    public String getUserdefined1() {
        return userdefined1;
    }

    public void setUserdefined1(String userdefined1) {
        this.userdefined1 = userdefined1;
    }

    public String getUserdefined2() {
        return userdefined2;
    }

    public void setUserdefined2(String userdefined2) {
        this.userdefined2 = userdefined2;
    }

    public String getUserdefined3() {
        return userdefined3;
    }

    public void setUserdefined3(String userdefined3) {
        this.userdefined3 = userdefined3;
    }

    public String getUserdefined4() {
        return userdefined4;
    }

    public void setUserdefined4(String userdefined4) {
        this.userdefined4 = userdefined4;
    }

    public String getUserdefined5() {
        return userdefined5;
    }

    public void setUserdefined5(String userdefined5) {
        this.userdefined5 = userdefined5;
    }

    public String getUserdefined6() {
        return userdefined6;
    }

    public void setUserdefined6(String userdefined6) {
        this.userdefined6 = userdefined6;
    }

    public String getUserdefined7() {
        return userdefined7;
    }

    public void setUserdefined7(String userdefined7) {
        this.userdefined7 = userdefined7;
    }

    public String getUserdefined8() {
        return userdefined8;
    }

    public void setUserdefined8(String userdefined8) {
        this.userdefined8 = userdefined8;
    }

    public String getUserdefined9() {
        return userdefined9;
    }

    public void setUserdefined9(String userdefined9) {
        this.userdefined9 = userdefined9;
    }

    public String getUserdefined10() {
        return userdefined10;
    }

    public void setUserdefined10(String userdefined10) {
        this.userdefined10 = userdefined10;
    }

    public static class DetailsDTO {
        private String ruleGroupCode;
        private String ruleCode;
        private String codeNum;
        private String paraNum;
        private String paraNumTo;
        private String paragraphDefine;

        public String getRuleGroupCode() {
            return ruleGroupCode;
        }

        public void setRuleGroupCode(String ruleGroupCode) {
            this.ruleGroupCode = ruleGroupCode;
        }

        public String getRuleCode() {
            return ruleCode;
        }

        public void setRuleCode(String ruleCode) {
            this.ruleCode = ruleCode;
        }

        public String getCodeNum() {
            return codeNum;
        }

        public void setCodeNum(String codeNum) {
            this.codeNum = codeNum;
        }

        public String getParaNum() {
            return paraNum;
        }

        public void setParaNum(String paraNum) {
            this.paraNum = paraNum;
        }

        public String getParaNumTo() {
            return paraNumTo;
        }

        public void setParaNumTo(String paraNumTo) {
            this.paraNumTo = paraNumTo;
        }

        public String getParagraphDefine() {
            return paragraphDefine;
        }

        public void setParagraphDefine(String paragraphDefine) {
            this.paragraphDefine = paragraphDefine;
        }
    }
}
