package com.midea.prestorage.function.instorage

import android.app.Application
import android.text.TextUtils
import android.util.Log
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.net.CdWhLotDetailDto
import com.midea.prestorage.beans.net.InReceiptSerialScanDto
import com.midea.prestorage.beans.net.RuleDataList
import com.midea.prestorage.beans.setting.CcsSettingDb
import com.midea.prestorage.beans.setting.HandingInfoDb
import com.midea.prestorage.beans.setting.WorkingInfoDb
import com.midea.prestorage.function.instorage.response.InOrderData
import com.midea.prestorage.function.instorage.response.InOrderDataSort
import com.midea.prestorage.function.instorage.response.InOrderDetail
import com.midea.prestorage.function.instorage.response.RespReceiptDetail
import com.midea.prestorage.function.inv.response.BarcodeLotDto
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody
import org.xutils.db.sqlite.WhereBuilder
import java.text.SimpleDateFormat
import java.util.*

class InStorageScanNewVM(application: Application) : BaseViewModel(application) {
    var finishActivity = MutableLiveData(false)

    //单号类型
    var curOrderReceiveType = ObservableField<String>("receipt")   // receipt 入库单  wave波次单
    var curOrderNo = MutableLiveData<String>("") // 单号 (入库单号或波次单号)
    var anyCode = MutableLiveData<String>("")
    var startScan = MutableLiveData(false)
    var isShowStatueDialog = MutableLiveData(false)
    var statueStr = MutableLiveData<String>("") // 商品状态
    var curSelectLot4Name = ""
    val processInfo = MutableLiveData<String>("0/0/0")
    var isShowCcsDialog = MutableLiveData(false)
    var isShowDeleteDialog = MutableLiveData(false)
    var isShowHandingDialog = MutableLiveData(false)
    var toReceivingActivity = MutableLiveData(false)
    var input = MutableLiveData<String>("")
    var goodsRequest = MutableLiveData(false)
    var containerChangeTip = MutableLiveData(false)
    var isShowCcs = MutableLiveData(false)
    var isPalletEnter = MutableLiveData(false)
    var isInitStatus = MutableLiveData(true)
    val db = DbUtils.db
    var containerCode: String? = null
    var curListOrderDetail = mutableListOf<InOrderDetail>()
    var curListReceiptDetail = mutableListOf<RespReceiptDetail>()

    // 条码规则
    var ruleList: MutableList<RuleDataList>? = null

    // 批次属性规则  根据custItemCode 获取其对应的批次属性列表信息
    var mapCustItemCodeToLotList = mutableMapOf<String, MutableList<CdWhLotDetailDto>>()
    var inOrderDatas = MutableLiveData<MutableList<InOrderDataSort>>()
    var whCode: String? = null
    var handingStartTime = MutableLiveData<String>("")
    var handingEndTime = MutableLiveData<String>("")
    var dismissDialog = MutableLiveData(false)

    override fun init() {

    }

    fun onEnterAnyCode() {
        if (CheckUtil.isFastDoubleClick()) {
            // 这里要找后端执行一次  货品条码校验
            // 并根据返回的数据中 是否含有 是否需要采集货品属性 和 收货数量控制 属性 来决定

            if (anyCode.value.isNullOrEmpty()) {
                showNotification("SN码不能为空", false)
                return
            }

//            if(handingStartTime.value.toString().isNullOrBlank()) {
//                anyCode.value = ""
//                showNotification("请先设置装卸开始时间", false)
//                return
//            }

            launch(showDialog = true,
                error = {
                }, finish = {
                }) {

                //本地解析  sn -> custItemCode
                val snNo = CheckUtil.localParseCode(anyCode.value.toString(), ruleList)
                var isLocalParseSnSuccess = false
                // 输入输出不同 表示 本地解析成功 (sn进去  custItemCode 出来)，
                if (!snNo.equals(anyCode.value.toString())) {
                    isLocalParseSnSuccess = true
                }

                if (!isLocalParseSnSuccess) {
                    showNotification("条码错误", false)
                    anyCode.value = ""
                    return@launch
                }

                val param = mutableMapOf(
                    "whCode" to whCode,
                    "orderNo" to curOrderNo.value.toString().trim(),
                    "barcode" to anyCode.value.toString(),
                    "bearingSystem" to Constants.whInfo?.bearingSystemStr,
                    "custItemCode" to snNo
                )

                if (isShowCcs.value!!) {
                    param["serialPl"] = input.value.toString()
                }

                // 请求参数： 批次属性4 库存状态
                var lot4Code = "Y"
                DCUtils.goodsStatueN2C.get(curSelectLot4Name)?.let {
                    lot4Code = DCUtils.goodsStatueN2C.get(curSelectLot4Name).toString()
                }
                param["lotAtt04"] = lot4Code

                val requestBody = RequestBody.create(
                    MediaType.parse("Content-Type, application/json"),
                    Gson().toJson(param)
                )

                val result = withContext(Dispatchers.IO) {
                    val resp = async { RetrofitHelper.getAppAPI().scanBarcode(requestBody) }
                    resp.await()
                }

                if (result.code == 0.toLong()) {
                    //inReceiptSerialScanDto 为null 表示后端已经识别出了该sn码 ，并且自动填写数量做了收货操作，
                    // 不需要前端再处理，直接刷新界面即可
                    showNotification("扫码成功", true)
                    //重置条码编辑框
                    anyCode.value = ""
                    // 扫码成功 后 刷新
                    whCode?.let { loadInOrderDatas(it) }

                } else {
                    result.msg?.let { showNotification(it, false) }
                    anyCode.value = ""
                }
            }
        }
    }

    fun onEnterOrderNo() {
        if (CheckUtil.isFastDoubleClick()) {
            if (TextUtils.isEmpty(input.value)) {
                return
            }
            isPalletEnter.value = true
            goodsRequest.value = true

        }
    }

    // 加载 入库单或波次单 对应的所有商品信息
    fun loadInOrderDatas(whCode: String) {
        this.whCode = whCode
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf(
                "orderNo" to curOrderNo.value.toString(),
                "whCode" to whCode
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().queryReceiptDetail(requestBody) }
                resp.await()
            }

            if (result.code == 0.toLong()) {

                if (result.data != null && result.data!!.size > 0) {
                    curListReceiptDetail = result.data!!

                    if (result.data!![0].startReceiveDate != null) {
                        handingStartTime.value = result.data!![0].startReceiveDate
                    } else {
                        handingStartTime.value = ""
                    }
                    if (result.data!![0].endReceiveDate != null) {
                        handingEndTime.value = result.data!![0].endReceiveDate
                    } else {
                        handingEndTime.value = ""
                    }

                    //加载到商品信息后 把商品的itemCode都放到数据里 去mdm查一次条码规则 缓存到ruleList
                    loadServerRuleList(result.data!!)
                    // 查询批次属性模板
                    //loadServerLotList(result.data!!)

                    // 后端返回的明细列表  按 custItemCode + lot4 汇总
                    val groupMap = mutableMapOf<String, InOrderData>()

                    result.data!!.forEach {

                        val key = it.custItemCode + it.lotAtt04
                        if (groupMap.get(key) == null) {
                            groupMap.put(key, InOrderData())
                        }

                        val inOrderData = groupMap.get(key) as InOrderData
                        inOrderData.custItemCode = it.custItemCode
                        inOrderData.receiptCode = it.receiptCode
                        inOrderData.itemCode = it.itemCode
                        inOrderData.custOrderNo = ""
                        inOrderData.lotAtt04 = it.lotAtt04
                        inOrderData.itemName = it.itemName
                        if (it.isNotScan != null && it.isNotScan) {
                            inOrderData.cdcmUnscanMark = "1"
                        } else {
                            inOrderData.cdcmUnscanMark = "0"
                        }

                        if (inOrderData.scanNum == null) {
                            inOrderData.scanNum = 0
                        }

                        if (inOrderData.totalQty == null) {
                            inOrderData.totalQty = 0.0
                        }

                        if (inOrderData.receiptQty == null) {
                            inOrderData.receiptQty = 0.0
                        }

                        // 已扫数量
                        inOrderData.scanNum = inOrderData.scanNum + AppUtils.getBigDecimalValueStr(it.serialQty).toInt()
                        // 总数量
                        inOrderData.totalQty = inOrderData.totalQty + it.totalQty.toDouble()
                        // 已收数
                        inOrderData.receiptQty = inOrderData.receiptQty + it.receiptQty.toDouble()
                    }

                    // map 转 list
                    val temp = mutableListOf<InOrderDataSort>()
                    groupMap.forEach {
                        temp.add(InOrderDataSort(it.value))
                    }
                    inOrderDatas.value = temp

                    var scanQty = 0
                    var planQty = 0
                    var recvQty = 0
                    result.data!!.forEach {
                        scanQty += AppUtils.getBigDecimalValueStr(it.serialQty).toInt()
                        planQty += AppUtils.getBigDecimalValueStr(it.totalQty).toInt()
                        recvQty += AppUtils.getBigDecimalValueStr(it.receiptQty).toInt()
                    }
                    processInfo.value = "$scanQty/$planQty/$recvQty"
                }
            } else {
                showNotification(result.msg as String, false)
            }
        }
    }

    //根据 itemCode 列表  去查询条码规则  (留着用来后面做条码解析)
    private fun loadServerRuleList(list: List<RespReceiptDetail>) {

        // 如果已经查过了，就不用查了
        if (ruleList != null && ruleList!!.size > 0) {
            return
        }

        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableListOf<String>()
            list.forEach {
                param.add(it.itemCode)
            }


            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getOutStorageAPI().barcodeRuleNew(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                if (result.data != null) {
                    //ruleList = result.data
                    val beans = mutableListOf<RuleDataList>()
                    result.data?.forEach { data ->
                        data.details?.forEach {
                            beans.add(RuleDataList(data.cdcmMaterialNo, data.cdcmCustMaterialNo, it.codeNum, it.paraNum, it.paraNumTo, it.paragraphDefine, data.cdcmBarcode,
                                data.userdefined1, data.userdefined2,data.userdefined3,data.userdefined4,data.userdefined5,data.userdefined6,data.userdefined7,data.userdefined8,data.userdefined9,data.userdefined10))
                        }
                    }
                    ruleList = beans
                }
            }
        }
    }

    //根据 itemCode 列表  查询商品批次属性信息
    private fun loadServerLotList(list: List<RespReceiptDetail>) {

        // 如果已经查过了，就不用查了
        if (mapCustItemCodeToLotList.size > 0) {
            return
        }

        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            // 拼接 当前订单 的所有商品 itemCode ，然后去查他们对应的批属性配置
            val param = mutableListOf<String>()
            list.forEach {
                param.add(it.itemCode)
            }


            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().getCdcmLotDetailsNew(requestBody) }
                resp.await()
            }

            if (result.code == 0.toLong()) {
                if (result.data != null) {
                    result.data?.get(0)?.details?.forEach {
                        if (!it.cdcmCustMaterialNo.isNullOrEmpty()) { //
                            if (mapCustItemCodeToLotList.get(it.cdcmCustMaterialNo)
                                    .isNullOrEmpty()
                            ) {
                                mapCustItemCodeToLotList.put(it.cdcmCustMaterialNo, LinkedList())
                            }
                            if (it.inputControl.toUpperCase().equals("R")) {  //必填的批属性才需要缓存
                                mapCustItemCodeToLotList.get(it.cdcmCustMaterialNo)!!.add(it)
                            }

                        }
                    }
                }
            }
        }
    }

    fun handingTime() {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val handingInfo = withContext(Dispatchers.IO) {
                val resp = async {
                    db.selector(HandingInfoDb::class.java)
                        .where("userId", "==", Constants.userInfo?.id)
                        .and(WhereBuilder.b("mode", "==", 5))
                        .findFirst()
                }
                resp.await()
            }
            var handlingGroupName = ""
            var handlingGroupCode = ""
            if (handingInfo != null) {
                handlingGroupName = handingInfo.handlingName
                handlingGroupCode = handingInfo.handlingCode
            }

            val workInfo = withContext(Dispatchers.IO) {
                val resp = async {
                    db.selector(WorkingInfoDb::class.java)
                        .where("userId", "==", Constants.userInfo?.id)
                        .findFirst()
                }
                resp.await()
            }
            var workGroupName = ""
            var workGroupCode = ""
            if (workInfo != null) {
                workGroupName = workInfo.workGroupName
                workGroupCode = workInfo.workGroupCode
            }

            val param = mutableMapOf(
                "whCode" to Constants.whInfo?.whCode,
                "orderNo" to curOrderNo.value.toString().trim(),
                "handingGroupCode" to handlingGroupCode,
                "handingGroupName" to handlingGroupName,
                "workGroupCode" to workGroupCode,
                "workGroupName" to workGroupName,
                "startReceiveDate" to handingStartTime.value.toString(),
                "endReceiveDate" to handingEndTime.value.toString()
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().handingTime(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                result.msg?.let { showNotification(it, true) }
                if (!handingStartTime.value.toString()
                        .isNullOrBlank() && !handingEndTime.value.toString().isNullOrBlank()
                ) {
                    dismissDialog.value = true
                }
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    fun deleteSerial(whCode: String, code: String) {

        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf(
                "whCode" to whCode,
                "orderNo" to curOrderNo.value.toString().trim(),
                "barcode" to code
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().deleteBarcode(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                var data = result.data as String?
                if (!data.isNullOrBlank()) {
                    showNotification(data, true)
                } else {
                    result.msg?.let { showNotification(it, true) }
                }
                loadInOrderDatas(whCode)
            } else {
                result.msg?.let { showNotification(it, false) }
                loadInOrderDatas(whCode)
            }
        }
    }

    fun regenerateReceive() {

        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf<String, Any>()
            if (curOrderReceiveType.get().toString() == "receipt") {
                param["receiptCode"] = curOrderNo.value.toString()
            } else {
                param["waveNo"] = curOrderNo.value.toString()
            }

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().regenerateReceive(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                if (handingStartTime.value.toString().isNullOrBlank()) {
                    handingTime()
                }
                toReceivingActivity.value = true
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    fun showErrorNotification(msg: String, isSuccess: Boolean) {
        showNotification(msg, isSuccess)
    }

    fun startScan() {
        startScan.value = true
    }

    fun scanResult(result: String?) {
        result?.let {
            anyCode.value = result
            onEnterAnyCode()
        }

    }

    fun containerChangeTip() {
        containerChangeTip.value = true
    }

    fun showStatueDialog() {
        isShowStatueDialog.value = true
    }

    fun showCcsDialog() {
        isShowCcsDialog.value = true
    }

    fun saveCssInfo(isChecked: Boolean) {
        launch(showDialog = false,
            error = {
                //错误用这个方法
            }, finish = {
                //完成用这个方法
            }) {
            withContext(Dispatchers.IO) {
                var findFirst = db.selector(CcsSettingDb::class.java).findFirst()

                if (findFirst == null) {
                    findFirst = CcsSettingDb()
                }
                findFirst.isChecked = isChecked
                db.saveOrUpdate(findFirst)
            }
        }
    }

    suspend fun getCssInfo(): Boolean {
        var result = false
        result = withContext(Dispatchers.IO) {
            var findFirst = db.selector(CcsSettingDb::class.java).findFirst()
            findFirst?.isChecked ?: false
        }
        return result
    }

    fun deleteBarcode() {
        isShowDeleteDialog.value = true
    }

    fun startHanding() {
        isShowHandingDialog.value = true
    }

    fun startReceive() {
        regenerateReceive()
    }

    fun getLot5FromSerialNo(serialNo: String?): String {
        var prdDate = ""
        var lotAtt05 = ""
        if (serialNo!!.length == 15) {
            prdDate = "20" + serialNo.substring(6, 8) + "-" + serialNo.substring(8, 10) + "-" + "01"
            lotAtt05 = serialNo.substring(6, 10)
        } else if (serialNo.length == 16) {
            val year = serialNo.substring(4, 6)
            var month = ""
            if ("A" == serialNo.substring(6, 7))
                month = "10"
            else if ("B" == serialNo.substring(6, 7))
                month = "11"
            else if ("C" == serialNo.substring(6, 7))
                month = "12"
            else
                month = "0" + serialNo.substring(6, 7)
            lotAtt05 = year + month
            prdDate = "20" + year + "-" + month + "-" + serialNo.substring(7, 9)
        } else if (serialNo.length == 22) {
            if ("D" == serialNo.substring(0, 1)) {
                val year = serialNo.substring(11, 13)
                var month = ""
                if ("A" == serialNo.substring(13, 14))
                    month = "10"
                else if ("B" == serialNo.substring(13, 14))
                    month = "11"
                else if ("C" == serialNo.substring(13, 14))
                    month = "12"
                else
                    month = "0" + serialNo.substring(13, 14)
                lotAtt05 = year + month
                prdDate = "20" + year + "-" + month + "-" + serialNo.substring(14, 16)
            } else {
                var year = serialNo.substring(11, 12).toUpperCase()
                if ("01234".contains(year)) {
                    lotAtt05 = "2" + year
                    year = "202" + year
                } else if ("56789".contains(year)) {
                    lotAtt05 = "1" + year
                    year = "201" + year
                } else {
                    val a = year[0]
                    var b = 0
                    if ("ABCD".contains(year))
                        b = 2024 + a.toByte().toInt() - 64
                    else if ("FGH".contains(year))
                        b = 2024 + a.toByte().toInt() - 65
                    else if ("JKLMN".contains(year))
                        b = 2024 + a.toByte().toInt() - 66
                    else if ("PQRSTUVWXYZ".contains(year)) b = 2024 + a.toByte().toInt() - 67
                    year = b.toString()
                    lotAtt05 = year.substring(2, 4)
                }
                var month = ""
                if ("A" == serialNo.substring(12, 13))
                    month = "10"
                else if ("B" == serialNo.substring(12, 13))
                    month = "11"
                else if ("C" == serialNo.substring(12, 13))
                    month = "12"
                else
                    month = "0" + serialNo.substring(12, 13)
                prdDate = year + "-" + month + "-" + serialNo.substring(13, 15)
                lotAtt05 += month
            }
        }
        val sdf = SimpleDateFormat("yyyy-MM-dd")
        try {
            sdf.isLenient = false
            sdf.parse(prdDate)
        } catch (e: Exception) {
            prdDate = ""
        }

        return lotAtt05
    }
}