package com.midea.prestorage.function.arriving.dialog

import android.app.AlertDialog
import android.text.TextUtils
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter.OnCheckListener
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.net.ArrivingBean
import com.midea.prestoragesaas.databinding.DialogWhChooseBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.midea.prestorage.beans.setting.ImplWarehouse
import com.midea.prestoragesaas.databinding.DialogArrivingBinding

class ArrivingDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {

    private var binding: DialogArrivingBinding
    var tag: MutableList<ArrivingBean>? = null

    init {
        val contentView = LayoutInflater.from(mContext).inflate(R.layout.dialog_arriving, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = ArrivingDialogVM(this)

        setCanceledOnTouchOutside(false)
        // 加上这个 确保按回退键也不关闭选择框
        setCancelable(false)

        binding.rgGroup.setOnCheckedChangeListener { _, checkedId ->
            if (checkedId == R.id.rb_manual) {
                binding.vm!!.noteMode.set(1)
            } else {
                binding.vm!!.noteMode.set(2)
            }
        }
    }

    override fun dismiss() {
        super.dismiss()

        binding.vm!!.dismiss()
    }

    fun addPhoneData(phoneBeans: MutableList<BaseItemShowInfo>) {
        binding.vm!!.addAllData(phoneBeans)
    }
}
