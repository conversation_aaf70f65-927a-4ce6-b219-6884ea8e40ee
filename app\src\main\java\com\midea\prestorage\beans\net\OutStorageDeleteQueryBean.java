package com.midea.prestorage.beans.net;

import java.io.Serializable;
import java.math.BigDecimal;

public class OutStorageDeleteQueryBean implements Serializable {
    private String id;
    private String serialNo;
    private String itemCode;
    private String itemName;
    private String serialType; //1为不扫码，要加输入框
    private String custItemCode;
    private BigDecimal qty;
    private String shipmentCode;

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getSerialType() {
        return serialType;
    }

    public void setSerialType(String serialType) {
        this.serialType = serialType;
    }

    public int getQty() {
        if (qty == null) {
            return 0;
        }
        return qty.intValue();
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getShipmentCode() {
        return shipmentCode;
    }

    public void setShipmentCode(String shipmentCode) {
        this.shipmentCode = shipmentCode;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}