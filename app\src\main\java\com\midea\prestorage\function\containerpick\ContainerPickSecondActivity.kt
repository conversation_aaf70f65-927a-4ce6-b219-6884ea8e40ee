package com.midea.prestorage.function.containerpick

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.daimajia.swipe.SimpleSwipeListener
import com.daimajia.swipe.SwipeLayout
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.ContainerPickSecondList
import com.midea.prestorage.beans.net.PrintBean
import com.midea.prestorage.function.addgoods.ReplenishmentActivity
import com.midea.prestorage.function.inv.InfoCollectionActivity
import com.midea.prestorage.function.inv.response.RespMaterial
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.http.constants.Constants
import com.midea.prestoragesaas.databinding.ActivityOutContainerPickSecondBinding
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.LotAttUnit
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.ToastUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class ContainerPickSecondActivity : BaseActivity() {

    lateinit var binding: ActivityOutContainerPickSecondUnionBinding
    private var vm = ContainerPickSecondVM(this)
    val adapter = ContainerPickSecondAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityOutContainerPickSecondUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_out_container_pick_second_care
                )
            )
        } else {
            ActivityOutContainerPickSecondUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_out_container_pick_second
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        binding.vm = vm

        lifecycleScope.launch {
            while (Constants.isShowContainerPick) {
                delay(60 * 1000)
                vm.bluetoothOpen(true)
            }
        }
        initRecycle()
        vm.init()
    }

    override fun onDestroy() {
        super.onDestroy()

        //Printer.closeBluetooth()
    }

    override fun onResume() {
        super.onResume()
        vm.resume()
        if (Constants.isShowContainerPick) {
            vm.bluetoothOpen(true)
        }
    }

    fun containerRequestFocus() {
        binding.edContainer.requestFocus()
    }

    fun locationRequestFocus() {
        binding.edLocation.post {
            binding.edLocation.requestFocus()
            binding.edLocation.selectAll()
        }
    }

    fun goodsRequestFocus() {
        binding.edGoodsCode.post {
            binding.edGoodsCode.requestFocus()
            binding.edGoodsCode.selectAll()
        }
    }

    private fun initRecycle() {
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter

        adapter.setOnItemChildClickListener { adapter, view, position ->
            when (view.id) {
                R.id.ll_bg -> {
                    if (!vm.isStart.get()) {
                        return@setOnItemChildClickListener
                    }
                    val bean = adapter.data[position] as ContainerPickSecondList
                    if (bean.isNeedScan69 == "1") {
                        ToastUtils.getInstance().showErrorToastWithSound(
                            this@ContainerPickSecondActivity,
                            "请扫码货品69码或货品条码！"
                        )
                        return@setOnItemChildClickListener
                    }
                    if (!AppUtils.isZero(bean.repFlag)) {
                        ToastUtils.getInstance().showErrorToastWithSound(
                            this@ContainerPickSecondActivity,
                            "请先补货！"
                        )
                        return@setOnItemChildClickListener
                    }

                    vm.onItemClick(bean)
                }

                //快速补货
                R.id.bottom_wrapper -> {
                    val bean = adapter.data[position] as ContainerPickSecondList
                    adapter.notifyItemChanged(position)
                    val intent = Intent(this, ReplenishmentActivity::class.java)
                    intent.putExtra("ContainerPickSecondList", bean)
                    startActivity(intent)
                }
            }
        }
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun showData(data: MutableList<ContainerPickSecondList>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
    }

    class ContainerPickSecondAdapter :
        CommonAdapter<ContainerPickSecondList>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_container_out_second_care else R.layout.item_container_out_second) {

        private var currentSwipeLayout: SwipeLayout? = null

        init {
            addChildClickViewIds(R.id.ll_bg, R.id.bottom_wrapper)
        }

        override fun convert(holder: BaseViewHolder?, item: ContainerPickSecondList) {
            super.convert(holder, item)

            val swipeLayout = holder?.itemView?.findViewById<SwipeLayout>(R.id.sample)
            if (swipeLayout?.openStatus == SwipeLayout.Status.Open) {
                swipeLayout?.close(true)
            }

            swipeLayout?.isSwipeEnabled = !AppUtils.isZero(item.repFlag)

            holder?.setText(
                R.id.tv_cust_item_code, item?.custItemCode
            )

            if (item?.csBarcode69.isNullOrEmpty() && item?.whBarcode69.isNullOrEmpty()) {
                holder?.setGone(R.id.tv_carcode69, true)
            } else {
                holder?.setGone(R.id.tv_carcode69, false)
                holder?.setText(
                    R.id.tv_carcode69,
                    LotAttUnit.formatWhBarcode69(item?.csBarcode69, item?.whBarcode69)
                )
            }

            holder?.setGone(R.id.ll_sort_pick, !item.isSortPick)
            if (item.isSortPick) {
                holder?.setGone(R.id.ll_pending_replenishment, true)
            } else {
                holder?.setGone(R.id.ll_pending_replenishment, AppUtils.isZero(item.repFlag))
            }
        }

        override fun createBaseViewHolder(view: View): BaseViewHolder {
            val swipeLayout = view.findViewById<SwipeLayout>(R.id.sample)

            swipeLayout?.addSwipeListener(object : SimpleSwipeListener() {
                override fun onStartOpen(layout: SwipeLayout?) {
                    // 如果当前已经有 item 处于打开状态，则关闭该 item
                    if (currentSwipeLayout != null && currentSwipeLayout != layout) {
                        currentSwipeLayout?.close()
                    }
                    // 记录当前打开的 item
                    currentSwipeLayout = layout
                }

                override fun onOpen(layout: SwipeLayout?) {
                    // 如果当前已经有 item 处于打开状态，则关闭该 item
                    if (currentSwipeLayout != null && currentSwipeLayout != layout) {
                        currentSwipeLayout?.close()
                    }
                    // 记录当前打开的 item
                    currentSwipeLayout = layout
                }

                override fun onClose(layout: SwipeLayout?) {
                    // 如果当前关闭的 item 与当前打开的 item 相同，则清空记录
                    if (currentSwipeLayout == layout) {
                        currentSwipeLayout = null
                    }
                }
            })
            return BaseViewHolder(view)
        }
    }
}