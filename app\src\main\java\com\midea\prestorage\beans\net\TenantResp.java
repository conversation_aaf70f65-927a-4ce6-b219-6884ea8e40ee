package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;
import java.util.List;

public class TenantResp implements Serializable {

    private CurrentTenantDTO currentTenant;
    private List<TenantsDTO> tenants;

    public CurrentTenantDTO getCurrentTenant() {
        return currentTenant;
    }

    public void setCurrentTenant(CurrentTenantDTO currentTenant) {
        this.currentTenant = currentTenant;
    }

    public List<TenantsDTO> getTenants() {
        return tenants;
    }

    public void setTenants(List<TenantsDTO> tenants) {
        this.tenants = tenants;
    }

    public static class CurrentTenantDTO {
        private String asc;
        private int busCenter;
        private String createTime;
        private String createUserCode;
        private String customCode;
        private int deleteFlag;
        private int enableFlag;
        private String expireDate;
        private String id;
        private String orderBy;
        private String orderByType;
        private int pageSize;
        private int reportFlag;
        private int sortNo;
        private int source;
        private String sourceTenantCode;
        private int start;
        private String tenantCode;
        private String tenantCreditCode;
        private String tenantName;
        private int type;
        private String updateTime;
        private String updateUserCode;
        private int userFlag;
        private int version;

        public String getAsc() {
            return asc;
        }

        public void setAsc(String asc) {
            this.asc = asc;
        }

        public int getBusCenter() {
            return busCenter;
        }

        public void setBusCenter(int busCenter) {
            this.busCenter = busCenter;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getCreateUserCode() {
            return createUserCode;
        }

        public void setCreateUserCode(String createUserCode) {
            this.createUserCode = createUserCode;
        }

        public String getCustomCode() {
            return customCode;
        }

        public void setCustomCode(String customCode) {
            this.customCode = customCode;
        }

        public int getDeleteFlag() {
            return deleteFlag;
        }

        public void setDeleteFlag(int deleteFlag) {
            this.deleteFlag = deleteFlag;
        }

        public int getEnableFlag() {
            return enableFlag;
        }

        public void setEnableFlag(int enableFlag) {
            this.enableFlag = enableFlag;
        }

        public String getExpireDate() {
            return expireDate;
        }

        public void setExpireDate(String expireDate) {
            this.expireDate = expireDate;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getOrderBy() {
            return orderBy;
        }

        public void setOrderBy(String orderBy) {
            this.orderBy = orderBy;
        }

        public String getOrderByType() {
            return orderByType;
        }

        public void setOrderByType(String orderByType) {
            this.orderByType = orderByType;
        }

        public int getPageSize() {
            return pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }

        public int getReportFlag() {
            return reportFlag;
        }

        public void setReportFlag(int reportFlag) {
            this.reportFlag = reportFlag;
        }

        public int getSortNo() {
            return sortNo;
        }

        public void setSortNo(int sortNo) {
            this.sortNo = sortNo;
        }

        public int getSource() {
            return source;
        }

        public void setSource(int source) {
            this.source = source;
        }

        public String getSourceTenantCode() {
            return sourceTenantCode;
        }

        public void setSourceTenantCode(String sourceTenantCode) {
            this.sourceTenantCode = sourceTenantCode;
        }

        public int getStart() {
            return start;
        }

        public void setStart(int start) {
            this.start = start;
        }

        public String getTenantCode() {
            return tenantCode;
        }

        public void setTenantCode(String tenantCode) {
            this.tenantCode = tenantCode;
        }

        public String getTenantCreditCode() {
            return tenantCreditCode;
        }

        public void setTenantCreditCode(String tenantCreditCode) {
            this.tenantCreditCode = tenantCreditCode;
        }

        public String getTenantName() {
            return tenantName;
        }

        public void setTenantName(String tenantName) {
            this.tenantName = tenantName;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getUpdateUserCode() {
            return updateUserCode;
        }

        public void setUpdateUserCode(String updateUserCode) {
            this.updateUserCode = updateUserCode;
        }

        public int getUserFlag() {
            return userFlag;
        }

        public void setUserFlag(int userFlag) {
            this.userFlag = userFlag;
        }

        public int getVersion() {
            return version;
        }

        public void setVersion(int version) {
            this.version = version;
        }
    }

    public static class TenantsDTO {
        @ShowAnnotation
        private String showInfo;
        private String asc;
        private int busCenter;
        private String createTime;
        private String createUserCode;
        private String customCode;
        private int deleteFlag;
        private int enableFlag;
        private String expireDate;
        private String id;
        private String orderBy;
        private String orderByType;
        private int pageSize;
        private int reportFlag;
        private int sortNo;
        private int source;
        private String sourceTenantCode;
        private int start;
        private String tenantCode;
        private String tenantCreditCode;
        private String tenantName;
        private int type;
        private String updateTime;
        private String updateUserCode;
        private int userFlag;
        private int version;

        public String getShowInfo() {
            return showInfo;
        }

        public void setShowInfo(String showInfo) {
            this.showInfo = showInfo;
        }

        public String getAsc() {
            return asc;
        }

        public void setAsc(String asc) {
            this.asc = asc;
        }

        public int getBusCenter() {
            return busCenter;
        }

        public void setBusCenter(int busCenter) {
            this.busCenter = busCenter;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getCreateUserCode() {
            return createUserCode;
        }

        public void setCreateUserCode(String createUserCode) {
            this.createUserCode = createUserCode;
        }

        public String getCustomCode() {
            return customCode;
        }

        public void setCustomCode(String customCode) {
            this.customCode = customCode;
        }

        public int getDeleteFlag() {
            return deleteFlag;
        }

        public void setDeleteFlag(int deleteFlag) {
            this.deleteFlag = deleteFlag;
        }

        public int getEnableFlag() {
            return enableFlag;
        }

        public void setEnableFlag(int enableFlag) {
            this.enableFlag = enableFlag;
        }

        public String getExpireDate() {
            return expireDate;
        }

        public void setExpireDate(String expireDate) {
            this.expireDate = expireDate;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getOrderBy() {
            return orderBy;
        }

        public void setOrderBy(String orderBy) {
            this.orderBy = orderBy;
        }

        public String getOrderByType() {
            return orderByType;
        }

        public void setOrderByType(String orderByType) {
            this.orderByType = orderByType;
        }

        public int getPageSize() {
            return pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }

        public int getReportFlag() {
            return reportFlag;
        }

        public void setReportFlag(int reportFlag) {
            this.reportFlag = reportFlag;
        }

        public int getSortNo() {
            return sortNo;
        }

        public void setSortNo(int sortNo) {
            this.sortNo = sortNo;
        }

        public int getSource() {
            return source;
        }

        public void setSource(int source) {
            this.source = source;
        }

        public String getSourceTenantCode() {
            return sourceTenantCode;
        }

        public void setSourceTenantCode(String sourceTenantCode) {
            this.sourceTenantCode = sourceTenantCode;
        }

        public int getStart() {
            return start;
        }

        public void setStart(int start) {
            this.start = start;
        }

        public String getTenantCode() {
            return tenantCode;
        }

        public void setTenantCode(String tenantCode) {
            this.tenantCode = tenantCode;
        }

        public String getTenantCreditCode() {
            return tenantCreditCode;
        }

        public void setTenantCreditCode(String tenantCreditCode) {
            this.tenantCreditCode = tenantCreditCode;
        }

        public String getTenantName() {
            return tenantName;
        }

        public void setTenantName(String tenantName) {
            this.tenantName = tenantName;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getUpdateUserCode() {
            return updateUserCode;
        }

        public void setUpdateUserCode(String updateUserCode) {
            this.updateUserCode = updateUserCode;
        }

        public int getUserFlag() {
            return userFlag;
        }

        public void setUserFlag(int userFlag) {
            this.userFlag = userFlag;
        }

        public int getVersion() {
            return version;
        }

        public void setVersion(int version) {
            this.version = version;
        }
    }
}
