package com.midea.prestorage.function.outpool

import CheckUtil
import android.content.Intent
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.OutPoolStorageList
import com.midea.prestoragesaas.databinding.ActivityOutStoragePoolBinding
import com.midea.prestoragesaas.databinding.PopViewOutPoolSendBinding
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.dialog.MultiChooseDialog
import com.midea.prestorage.function.outpool.dialog.CopyDialog
import com.midea.prestorage.utils.AppUtils
import com.xuexiang.xqrcode.XQRCode

class OutPoolStorageActivity : BaseActivity() {

    private lateinit var binding: ActivityOutStoragePoolBinding
    private lateinit var popBinding: PopViewOutPoolSendBinding
    private lateinit var popupWindow: PopupWindow
    private lateinit var daysDialog: FilterDialog
    private lateinit var sendDialog: FilterDialog
    private lateinit var engineerDialog: FilterDialog
    private lateinit var statueDialog: MultiChooseDialog
    private lateinit var typeDialog: MultiChooseDialog
    private lateinit var copyDialog: CopyDialog
    private var vm = OutPoolStorageVM(this)
    val adapter = OutPoolStorageAdapter()
    var isLoadParamFromMainActivity = false
    var paramFromMainActivity = mutableMapOf<String, Any>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_out_storage_pool)
        binding.vm = vm

        // 如果是从首页带查询条件跳进来的 ， 则直接按条件查询
        if (this.intent != null && this.intent.getBooleanExtra("fromMainActivity", false) != null
            && this.intent.getBooleanExtra("fromMainActivity", false).equals(true)
        ) {
            isLoadParamFromMainActivity = true
            val beginTime = this.intent.getStringExtra("beginTime")
            val endTime = this.intent.getStringExtra("endTime")

            if (beginTime != null && endTime != null) {

                paramFromMainActivity.put("whCode", getWhCode())
                paramFromMainActivity.put("beginTime", beginTime)
                paramFromMainActivity.put("endTime", endTime)

                // 创建、已分配、拣货中、拣货完成
                val status = "100,250,300,400,401"
                paramFromMainActivity.put("statusStr", status)
            }
        }


        initPop()
        initRecycle()
        initDialog()
    }

    override fun onResume() {
        super.onResume()
        vm.init()
    }

    private fun initPop() {
        val popView = LayoutInflater.from(this).inflate(R.layout.pop_view_out_pool_send, null)
        popBinding = DataBindingUtil.bind(popView)!!
        popBinding.vm = vm

        popupWindow = PopupWindow(
            popView,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        popupWindow.setBackgroundDrawable(
            ColorDrawable(
                ContextCompat.getColor(
                    this,
                    R.color.colorOutlineGrey
                )
            )
        )

        popupWindow.setOnDismissListener {
            vm.initFilterInfo()
            vm.initOrderList()
        }

        popupWindow.isOutsideTouchable = true

        binding.llContainer.setOnClickListener {
            vm.popShow()
            popupWindow.showAsDropDown(it)
        }

        binding.llAllChoose.setOnClickListener {
            binding.cbSelectAll.performClick()
        }

        popBinding.llFilterClose.setOnClickListener {
            popupWindow.dismiss()
        }

        binding.cbSelectAll.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                adapter.allSelect(true)
            }
        }

        binding.cbSelectAll.setOnClickListener {
            if (!binding.cbSelectAll.isChecked) {
                adapter.allSelect(false)
            }
        }
    }

    private fun initRecycle() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter
        adapter.setClickId(R.id.ll_checked)

        adapter.setOnItemClickListener { adapter, _, position ->
            val bean = adapter.data[position] as OutPoolStorageList
            vm.onItemClick(bean)
        }

        adapter.setChangeSelectStatus {
            val results = adapter.data.filter { !it.isSelected }
            binding.cbSelectAll.isChecked = results.isEmpty()
        }

        adapter.setOnItemLongClickListener { adapter, _, position ->
            val bean = adapter.data[position] as OutPoolStorageList
            if (TextUtils.isEmpty(bean.waveNo)) {
                CheckUtil.copyToClipboard(bean.custOrderCode)
                AppUtils.showToast(this@OutPoolStorageActivity, "客户单号已复制到剪切板")
            } else {
                copyDialog.setCopyText(bean.waveNo, bean.custOrderCode)
                copyDialog.show()
            }
            true
        }
    }

    fun setCbSelectChecked(isChecked: Boolean) {
        binding.cbSelectAll.isChecked = isChecked
    }

    private fun initDialog() {
        daysDialog = FilterDialog(this)
        daysDialog.setTitle("请选择天数")
        daysDialog.dismissEdit()
        daysDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            popBinding.tvDays.text = it.showInfo
            vm.dayInfo = it as DCBean
            vm.initEngineerList()
            daysDialog.dismiss()
        })

        statueDialog = MultiChooseDialog(this)
        statueDialog.setTitle("请选择商品状态")
        statueDialog.setBack(object : MultiChooseDialog.MultiChooseBack {
            override fun multiChooseBack(baseInfo: MutableList<BaseItemShowInfo>) {
                vm.orderStatue = baseInfo as MutableList<DCBean>
                vm.showStatus()
                vm.initEngineerList()
                statueDialog.dismiss()
            }
        })

        typeDialog = MultiChooseDialog(this)
        typeDialog.setTitle("请选择商品类型")
        typeDialog.setBack(object : MultiChooseDialog.MultiChooseBack {
            override fun multiChooseBack(baseInfo: MutableList<BaseItemShowInfo>) {
                vm.typeStatue = baseInfo as MutableList<DCBean>
                vm.showType()
                vm.initEngineerList()
                typeDialog.dismiss()
            }
        })

        copyDialog = CopyDialog(this)

        sendDialog = FilterDialog(this)
        sendDialog.setTitle("请选择")
        sendDialog.dismissEdit()
        sendDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            vm.sendInfoDC = it as DCBean
            vm.sendInfo.set(it.key)
            vm.sendInfoCheck()
            sendDialog.dismiss()
        })

        engineerDialog = FilterDialog(this)
        engineerDialog.setTitle("请选择")
        engineerDialog.dismissEdit()
        engineerDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            it as DCBean
            vm.engineerInfo.set(it.key)
            engineerDialog.dismiss()
        })
    }

    fun resetFilterInfo(filters: MutableList<String>) {
        binding.warpLinear.removeAllViews()
        filters.forEach {
            binding.warpLinear.addView(getShowView(it))
        }
    }

    private fun getShowView(text: String): View {
        var view: View = LayoutInflater.from(this).inflate(R.layout.item_filter, null)
        view.findViewById<TextView>(R.id.tv_item).text = text
        return view
    }

    fun showDaysDialog(days: MutableList<BaseItemShowInfo>) {
        daysDialog.addAllData(days)
        daysDialog.show()
    }

    fun showStatueDialog(days: MutableList<BaseItemShowInfo>) {
        statueDialog.addAllData(days)
        statueDialog.show()
    }

    fun showTypeDialog(days: MutableList<BaseItemShowInfo>) {
        typeDialog.addAllData(days)
        typeDialog.show()
    }

    fun showSendDialog(days: MutableList<BaseItemShowInfo>) {
        sendDialog.addAllData(days)
        sendDialog.show()
    }

    fun showEngineerDialog(days: MutableList<BaseItemShowInfo>) {
        engineerDialog.addAllData(days)
        engineerDialog.show()
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun showData(data: MutableList<OutPoolStorageList>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            vm.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    fun popDismiss() {
        popupWindow.dismiss()
    }

    class OutPoolStorageAdapter :
        ListCheckBoxAdapter<OutPoolStorageList>(R.layout.item_order_out_pool) {
        override fun convert(helper: BaseViewHolder, item: BaseItemForPopup) {
            super.convert(helper, item as OutPoolStorageList)

            val check = helper.getView<ImageView>(R.id.img_select)
            if (item.isSelected()) {
                check.setImageResource(R.drawable.ic_check_selected)
            } else {
                check.setImageResource(R.drawable.ic_check_unselect)
            }

            helper.setVisible(R.id.ll_engineer, !item.netEngineerName.isNullOrEmpty())

            // 前置仓不扫码印章
            if (!item.unscanMark.isNullOrBlank()) {
                helper.itemView.findViewById<LinearLayout>(R.id.redMarkUnScan).visibility =
                    View.VISIBLE
                if (item.unscanMark == "00") {
                    helper.setText(R.id.tvUnscanText, "不扫码申请中")
                } else if (item.unscanMark == "01") {
                    helper.setText(R.id.tvUnscanText, "不扫码已审核")
                }
            } else {
                helper.itemView.findViewById<LinearLayout>(R.id.redMarkUnScan).visibility =
                    View.INVISIBLE
            }

            if (item.status == 100) {
                helper.setTextColor(
                    R.id.tv_status,
                    ContextCompat.getColor(context, R.color.colorWhite)
                )
                helper.setBackgroundColor(
                    R.id.tv_status,
                    ContextCompat.getColor(context, R.color.colorRed2)
                )
            } else {
                helper.setTextColor(
                    R.id.tv_status,
                    ContextCompat.getColor(context, R.color.colorGreen)
                )
                helper.setBackgroundColor(
                    R.id.tv_status,
                    ContextCompat.getColor(context, R.color.colorWhite)
                )
            }
        }
    }
}