package com.midea.prestorage.function.planstock

import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.*
import android.widget.BaseAdapter
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.PlanStockDetailList
import com.midea.prestorage.function.inv.InventorySearchActivity
import com.midea.prestorage.function.inv.PopViewForUnionBinding
import com.midea.prestorage.function.inv.response.ItemRfVO
import com.midea.prestorage.function.inv.response.PackageRelation
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestoragesaas.databinding.ActivityPlanStockDetailBinding
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.LotAttUnit
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.CareLoadMoreView
import com.midea.prestorage.widgets.ZeroEditText
import com.midea.prestoragesaas.databinding.PopViewForSelectCustemItemCodeBinding
import java.math.BigDecimal

class PlanStockDetailActivity : BaseActivity() {

    lateinit var binding: ActivityPlanStockDetailUnionBinding
    private var vm: PlanStockDetailVM? = null
    val adapter = OutPoolStorageAdapter()

    //69码或sn码 查询出多个custItemCodes的时候 弹框让用户选择custItemCode
    lateinit var dlgSelectCustItemCode: AlertDialog
    private lateinit var popBindingSelectCustItemCode: PopViewForUnionBinding
    lateinit var popAdapterSelectCustItemCode: PopListAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityPlanStockDetailUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_plan_stock_detail_care
                )
            )
        } else {
            ActivityPlanStockDetailUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_plan_stock_detail
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        binding.vm = PlanStockDetailVM(this)
        vm = binding.vm

        initRecycle()
        initPopWinSelectCustItemCode()
        initLoadMore()

        AppUtils.requestFocus(binding.edGoods)
    }

    // 初始化选择客户商品编码的弹窗
    private fun initPopWinSelectCustItemCode() {

        val alertDialogBuilder = AlertDialog.Builder(this)
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            val popViewSelectCustItemCode =
                LayoutInflater.from(this)
                    .inflate(R.layout.pop_view_for_select_custem_item_code_care, null)
            popBindingSelectCustItemCode =
                PopViewForUnionBinding.V2(DataBindingUtil.bind(popViewSelectCustItemCode)!!)
            alertDialogBuilder.setView(popViewSelectCustItemCode)
        } else {
            val popViewSelectCustItemCode =
                LayoutInflater.from(this)
                    .inflate(R.layout.pop_view_for_select_custem_item_code, null)
            popBindingSelectCustItemCode =
                PopViewForUnionBinding.V1(DataBindingUtil.bind(popViewSelectCustItemCode)!!)
            alertDialogBuilder.setView(popViewSelectCustItemCode)
        }

        dlgSelectCustItemCode = alertDialogBuilder.create()

        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            dlgSelectCustItemCode.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            dlgSelectCustItemCode.window?.setGravity(Gravity.CENTER)
            dlgSelectCustItemCode.window?.attributes?.run {
                gravity = Gravity.CENTER
            }
        }

        popAdapterSelectCustItemCode = PopListAdapter()
        popBindingSelectCustItemCode.recyclerViewInPop.layoutManager = LinearLayoutManager(this)
        popBindingSelectCustItemCode.recyclerViewInPop.adapter = popAdapterSelectCustItemCode

        popAdapterSelectCustItemCode.setOnItemClickListener { adapter, view, position ->
            val item = adapter.getItem(position) as ItemRfVO
            dlgSelectCustItemCode.dismiss()

            binding.vm?.itemName?.set(item.itemName)
            binding.vm?.serialScanDto = item

            binding.vm?.qty?.set("")
            binding.vm?.qtyEnable?.set(true)
            binding.vm?.existTaskDetail(item.custItemCode, item.ownerCode)
        }

        popBindingSelectCustItemCode.closePop.setOnClickListener {
            dlgSelectCustItemCode.dismiss()
        }

    }

    private fun initRecycle() {

        binding.rv.layoutManager = LinearLayoutManager(this)
        binding.rv.adapter = adapter

        adapter.setOnItemClickListener { adapter, view, position ->
            val item = adapter.data[position] as PlanStockDetailList
            binding.vm!!.setOnItemClick(item)
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.startSearch()
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            adapter.loadMoreModule.loadMoreView = CareLoadMoreView()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    fun initSpinner(
        list: MutableList<DCBean>,
        indexOf: Int
    ) {
        val beans = mutableListOf<String>()
        list.forEach {
            beans.add(it.key)
        }
        binding.spinnerStatus.setItems(beans)
        binding.spinnerStatus.selectedIndex = indexOf

        binding.spinnerStatus.setOnItemSelectedListener { _, position, _, _ ->
            binding.vm!!.onChangeStatue(list[position])
        }
    }

    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果
            //binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    fun goodsFocus() {
        binding.edGoods.post {
            binding.edGoods.requestFocus()
        }
    }

    fun qtyFocus(data: Boolean) {
        var item = PlanStockDetailList()
        item.custItemCode = vm?.serialScanDto?.custItemCode
        item.itemCode = vm?.serialScanDto?.itemCode
        item.itemName = vm?.serialScanDto?.itemName
        item.packageRelationList = vm?.serialScanDto?.packageRelationList
        item.isDecimal = vm?.serialScanDto?.isDecimal!!
        item.ownerCode = vm?.serialScanDto?.ownerCode
        item.ownerName = vm?.serialScanDto?.ownerName
        item.isValidity = vm?.serialScanDto?.isValidity
        item.periodOfValidity = vm?.serialScanDto?.periodOfValidity
        item.validityUnit = vm?.serialScanDto?.validityUnit
        item.cdpaFormat = vm?.serialScanDto?.cdpaFormat
        item.whBarcode69 = vm?.serialScanDto?.whBarcode69
        item.whCsBarcode69 = vm?.serialScanDto?.whCsBarcode69
        vm?.inputDialog?.deleteInfo = item
        vm?.inputDialog?.isEnterKeyPress = true
        vm?.inputDialog?.existTaskDetail = data
        vm?.inputDialog?.show()
    }

    class PopListAdapter :
        CommonAdapter<ItemRfVO>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_pop_view_for_select_cust_item_code_pop_care else R.layout.item_pop_view_for_select_cust_item_code_pop) {
        override fun convert(holder: BaseViewHolder?, item: ItemRfVO) {
            super.convert(holder, item)

            holder?.setGone(R.id.tv_item_name, item.itemName.isNullOrEmpty())
        }
    }

    class OutPoolStorageAdapter :
        CommonAdapter<PlanStockDetailList>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_plan_stock_detail_care else R.layout.item_plan_stock_detail),
        LoadMoreModule {
        override fun convert(holder: BaseViewHolder?, item: PlanStockDetailList) {
            super.convert(holder, item)

            holder?.setText(
                R.id.tvBarcode69,
                LotAttUnit.formatBarcode69(item.whCsBarcode69, item.whBarcode69, item.whIpBarcode69)
            )


            //货品编码+69码
            holder?.setText(R.id.custItemCode, item.custItemCode)

            if (!item.sysQty.contains("*")) {
                holder?.setText(
                    R.id.sysQty,
                    AppUtils.getBigDecimalValueStr(BigDecimal(item.sysQty)) + " " + ((item.packageRelationList.find { it.cdprUnit == "EA" }?.cdprDesc)
                        ?: "")
                )
            } else {
                holder?.setText(R.id.sysQty, item.sysQty)
            }

            if (!item.firstQty.isNullOrEmpty()) {
                holder?.setText(
                    R.id.stockQty,
                    item.firstQty + " " + ((item.packageRelationList.find { it.cdprUnit == "EA" }?.cdprDesc)
                        ?: "")
                )
            }

            if (item.isLot05Check == 1) {
                holder?.setGone(R.id.ll_lotAtt05, false)
            } else {
                holder?.setGone(R.id.ll_lotAtt05, true)
            }

            if (item.isLot01Check == 1) {
                holder?.setGone(R.id.ll_lotAtt01, false)
            } else {
                holder?.setGone(R.id.ll_lotAtt01, true)
            }

            if (item.isLot02Check == 1) {
                holder?.setGone(R.id.ll_lotAtt02, false)
            } else {
                holder?.setGone(R.id.ll_lotAtt02, true)
            }

        }
    }
}