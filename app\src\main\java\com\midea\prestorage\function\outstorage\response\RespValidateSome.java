package com.midea.prestorage.function.outstorage.response;

import java.io.Serializable;

public class RespValidateSome implements Serializable {


    private String id;
    private String createTime;
    private String updateTime;
    private String tenantCode;
    private String createUserCode;
    private String createUserName;
    private String updateUserCode;
    private String updateUserName;
    private String remark;
    private int version;
    private int deleteFlag;
    private int pageSize;
    private String shipmentCode;
    private String parentOrderNo;
    private String orderNo;
    private String rpFlag;
    private String otpUploadBatch;
    private int otpUploadNumber;
    private String handingGroupCode;
    private String handingGroupName;
    private String supplierCode;
    private String supplierName;
    private String upperWhCode;
    private String handlingFeeSts;
    private String freightBasis;
    private String handingStartTime;
    private String handingEndTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public int getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(int deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getShipmentCode() {
        return shipmentCode;
    }

    public void setShipmentCode(String shipmentCode) {
        this.shipmentCode = shipmentCode;
    }

    public String getParentOrderNo() {
        return parentOrderNo;
    }

    public void setParentOrderNo(String parentOrderNo) {
        this.parentOrderNo = parentOrderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getRpFlag() {
        return rpFlag;
    }

    public void setRpFlag(String rpFlag) {
        this.rpFlag = rpFlag;
    }

    public String getOtpUploadBatch() {
        return otpUploadBatch;
    }

    public void setOtpUploadBatch(String otpUploadBatch) {
        this.otpUploadBatch = otpUploadBatch;
    }

    public int getOtpUploadNumber() {
        return otpUploadNumber;
    }

    public void setOtpUploadNumber(int otpUploadNumber) {
        this.otpUploadNumber = otpUploadNumber;
    }

    public String getHandingGroupCode() {
        return handingGroupCode;
    }

    public void setHandingGroupCode(String handingGroupCode) {
        this.handingGroupCode = handingGroupCode;
    }

    public String getHandingGroupName() {
        return handingGroupName;
    }

    public void setHandingGroupName(String handingGroupName) {
        this.handingGroupName = handingGroupName;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getUpperWhCode() {
        return upperWhCode;
    }

    public void setUpperWhCode(String upperWhCode) {
        this.upperWhCode = upperWhCode;
    }

    public String getHandlingFeeSts() {
        return handlingFeeSts;
    }

    public void setHandlingFeeSts(String handlingFeeSts) {
        this.handlingFeeSts = handlingFeeSts;
    }

    public String getFreightBasis() {
        return freightBasis;
    }

    public void setFreightBasis(String freightBasis) {
        this.freightBasis = freightBasis;
    }

    public String getHandingStartTime() {
        return handingStartTime;
    }

    public void setHandingStartTime(String handingStartTime) {
        this.handingStartTime = handingStartTime;
    }

    public String getHandingEndTime() {
        return handingEndTime;
    }

    public void setHandingEndTime(String handingEndTime) {
        this.handingEndTime = handingEndTime;
    }
}
