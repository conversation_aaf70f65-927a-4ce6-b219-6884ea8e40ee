package com.midea.prestorage.function.inv.response;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.math.BigDecimal;
import java.util.List;

public class InvStockTakeTask {

    @ShowAnnotation
    private String waveNo;

    @ShowAnnotation
    private String waveName;

    @ShowAnnotation(isDecimal = true)
    private BigDecimal totalShipments;

    @ShowAnnotation(isDecimal = true)
    private BigDecimal fromQty;

    @ShowAnnotation(isDecimal = true)
    private BigDecimal allocatedQty;

    @ShowAnnotation
    private String status;

    @ShowAnnotation
    private String taskEndTime;

    private List<String> taskDetailIdList;

    public List<String> getTaskDetailIdList() {
        return taskDetailIdList;
    }

    public void setTaskDetailIdList(List<String> taskDetailIdList) {
        this.taskDetailIdList = taskDetailIdList;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getWaveName() {
        return waveName;
    }

    public void setWaveName(String waveName) {
        this.waveName = waveName;
    }

    public BigDecimal getTotalShipments() {
        return totalShipments;
    }

    public void setTotalShipments(BigDecimal totalShipments) {
        this.totalShipments = totalShipments;
    }

    public BigDecimal getFromQty() {
        return fromQty;
    }

    public void setFromQty(BigDecimal fromQty) {
        this.fromQty = fromQty;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTaskEndTime() {
        return taskEndTime;
    }

    public void setTaskEndTime(String taskEndTime) {
        this.taskEndTime = taskEndTime;
    }

    public BigDecimal getAllocatedQty() {
        return allocatedQty;
    }

    public void setAllocatedQty(BigDecimal allocatedQty) {
        this.allocatedQty = allocatedQty;
    }
}
