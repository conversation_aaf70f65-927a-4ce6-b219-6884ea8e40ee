package com.midea.prestorage.beans.net;

import java.io.Serializable;

public class SetPackageConfirmResp implements Serializable {


    private String id;
    private String tenantCode;
    private String createUserCode;
    private String createUserName;
    private String updateUserCode;
    private String updateUserName;
    private int pageSize;
    private String setCode;
    private String status;
    private int wholeNum;
    private int partNum;
    private String setArea;
    private String setUserCode;
    private String setUserName;
    private String setStartTime;
    private String setEndTime;
    private String whCode;
    private String whName;
    private String otpUploadBatch;
    private int otpUploadNumber;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getSetCode() {
        return setCode;
    }

    public void setSetCode(String setCode) {
        this.setCode = setCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getWholeNum() {
        return wholeNum;
    }

    public void setWholeNum(int wholeNum) {
        this.wholeNum = wholeNum;
    }

    public int getPartNum() {
        return partNum;
    }

    public void setPartNum(int partNum) {
        this.partNum = partNum;
    }

    public String getSetArea() {
        return setArea;
    }

    public void setSetArea(String setArea) {
        this.setArea = setArea;
    }

    public String getSetUserCode() {
        return setUserCode;
    }

    public void setSetUserCode(String setUserCode) {
        this.setUserCode = setUserCode;
    }

    public String getSetUserName() {
        return setUserName;
    }

    public void setSetUserName(String setUserName) {
        this.setUserName = setUserName;
    }

    public String getSetStartTime() {
        return setStartTime;
    }

    public void setSetStartTime(String setStartTime) {
        this.setStartTime = setStartTime;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getWhName() {
        return whName;
    }

    public void setWhName(String whName) {
        this.whName = whName;
    }

    public String getOtpUploadBatch() {
        return otpUploadBatch;
    }

    public void setOtpUploadBatch(String otpUploadBatch) {
        this.otpUploadBatch = otpUploadBatch;
    }

    public int getOtpUploadNumber() {
        return otpUploadNumber;
    }

    public void setOtpUploadNumber(int otpUploadNumber) {
        this.otpUploadNumber = otpUploadNumber;
    }

    public String getSetEndTime() {
        return setEndTime;
    }

    public void setSetEndTime(String setEndTime) {
        this.setEndTime = setEndTime;
    }
}
