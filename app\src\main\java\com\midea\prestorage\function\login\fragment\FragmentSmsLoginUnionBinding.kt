package com.midea.prestorage.function.login.fragment

import android.view.View
import android.widget.EditText
import android.widget.TextView
import com.midea.prestoragesaas.databinding.FragmentSmsLoginBinding
import com.midea.prestoragesaas.databinding.FragmentSmsLoginCareBinding

sealed class FragmentSmsLoginUnionBinding {
    abstract var vm: SmsLoginVM?
    abstract val root: View
    abstract val etPassword: EditText
    abstract val tvGetVerificationCode: TextView

    class V2(val binding: FragmentSmsLoginCareBinding) : FragmentSmsLoginUnionBinding() {
        override var vm: SmsLoginVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val etPassword = binding.etPassword
        override val tvGetVerificationCode = binding.tvGetVerificationCode
    }

    class V1(val binding: FragmentSmsLoginBinding) : FragmentSmsLoginUnionBinding() {
        override var vm: SmsLoginVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val etPassword = binding.etPassword
        override val tvGetVerificationCode = binding.tvGetVerificationCode
    }
}
