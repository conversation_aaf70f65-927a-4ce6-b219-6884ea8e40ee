package com.midea.prestorage.function.inv

import android.widget.*
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.databinding.ActivityGoodsSearchBinding
import com.midea.prestoragesaas.databinding.ActivityGoodsSearchCareBinding
import com.midea.prestoragesaas.databinding.ActivityWaitSetPackageBinding
import com.midea.prestoragesaas.databinding.ActivityWaitSetPackageCareBinding

sealed class ActivityGoodsSearchUnionBinding{
    abstract var vm: GoodsSearchVM?
    abstract val llTitleBar: RelativeLayout
    abstract val etCode: AutoCompleteTextView
    abstract val spinnerStatus: MaterialSpinner
    abstract val recyclerView: RecyclerView
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: ActivityGoodsSearchCareBinding) : ActivityGoodsSearchUnionBinding() {
        override var vm: GoodsSearchVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etCode = binding.etCode
        override val spinnerStatus = binding.spinnerStatus
        override val recyclerView = binding.recyclerView
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: ActivityGoodsSearchBinding) : ActivityGoodsSearchUnionBinding() {
        override var vm: GoodsSearchVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etCode = binding.etCode
        override val spinnerStatus = binding.spinnerStatus
        override val recyclerView = binding.recyclerView
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
