package com.midea.prestorage.function.containerpick.fragment

import CheckUtil
import android.app.Application
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.chad.library.adapter.base.entity.node.BaseNode
import com.midea.prestorage.base.ErrorToaster
import com.midea.prestorage.beans.net.*
import com.midea.prestorage.event.LiveEvent
import com.midea.prestorage.function.containerpick.provider.BulkPickToBeDetailWrap
import com.midea.prestorage.function.containerpick.provider.BulkPickToBeWrap
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class BulkAlreadyPackedVM(application: Application) :
    BulkPackVM(application),
    ErrorToaster {

    val isNoData = MutableLiveData(false)
    val showData = MutableLiveData<MutableList<ReceiveDetailsBean.ReceiveDetailsListBean>>()
    val taskListMutableLiveData = MutableLiveData<MutableList<BaseNode>>()
    val loadMoreComplete = MutableLiveData<Unit>()
    val loadMoreEnd = MutableLiveData<Unit>()
    val refreshEvent = LiveEvent<Unit>()
    var taskCode = ObservableField<String>()
    var containerCode = ObservableField<String>()
    var pickingContainerList: MutableList<PickingContainer>? = null

    override fun refresh() {
        //initList()
    }

    fun initList() {
        launch(true, error = {}, finish = {}) {
            val resp = withContext(Dispatchers.IO) {
                if (containerCode.get().isNullOrEmpty()) {
                    RetrofitHelper.getOutStorageAPI().queryComPackHandleList(
                        ReqQueryEaPick(
                            taskCode = taskCode.get().toString(),
                            pickingContainerList = pickingContainerList
                        )
                    )
                }else {
                    RetrofitHelper.getOutStorageAPI().queryPackHandleList(
                        ReqQueryEaPick(
                            taskCode = taskCode.get().toString(),
                            containerCode = containerCode.get().toString()
                        )
                    )
                }
            }
            if (resp.code == 0L) {
                isNoData.value = resp.data?.isNotEmpty() != true
                val nodeList = mapToNodeList(resp.data).toMutableList()
                taskListMutableLiveData.value = nodeList
            } else {
                resp.msg?.let { errorToaster.showError(it) }
            }
        }
    }

    private fun queryPackedPrint(shipContainerIdList: List<String>?) {
        launch(true, error = {}, finish = {}) {
            val resp = withContext(Dispatchers.IO) {
                RetrofitHelper.getOutStorageAPI().queryPackedPrint(
                    ReqPackedPrint(
                        shipContainerIdList = shipContainerIdList ?: mutableListOf()
                    )
                )
            }
            if (resp.code == 0L) {
                resp.data?.let { Printer.printPackedInfo(it) }
            } else {
                resp.msg?.let { errorToaster.showError(it) }
            }
        }
    }

    override fun init() {
    }

    override fun showError(msg: String) {
        showNotification(msg, false)
    }

    fun reprintList() {
        if (CheckUtil.isFastDoubleClick()) {
            if (detailFromSelected().isEmpty()) {
                errorToaster.showError("请选择需要打印的货品！")
            }else if (!Printer.isPrintOk()) {
                errorToaster.showError("请先连接蓝牙打印机！")
            } else {
                queryPackedPrint(detailFromSelected())
            }
        }
    }

    private fun mapToNodeList(list: List<RespBulkPacking>?): MutableList<BaseNode> {
        return list?.map {
            BulkPickToBeWrap(it,
                it.details?.map { detail -> BulkPickToBeDetailWrap(it, detail) }
                    ?.toMutableList()
            )
        }?.toMutableList() ?: mutableListOf()
    }

    fun onItemCheckedChang(item: BulkPickToBeWrap) {
        val find = taskListMutableLiveData.value?.find { it == item }
        (find as? BulkPickToBeWrap)?.selected = item.selected
    }

    private fun detailFromSelected(): List<String> =
        taskListMutableLiveData.value?.filter {
            (it as BulkPickToBeWrap).selected
        }?.flatMap {
            (it as BulkPickToBeWrap).respBulkPacking.shipContainerIdList ?: emptyList()
        }?.distinct() ?: emptyList()
}