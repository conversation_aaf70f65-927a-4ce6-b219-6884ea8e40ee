package com.midea.prestorage.function.about

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.databinding.DataBindingUtil
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.Agreement
import com.midea.prestorage.function.agreement.AgreementActivity
import com.midea.prestorage.worker.ApkDownloadWorker
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityAboutBinding

class AboutActivity : BaseActivity() {

    companion object {
        fun newIntent(context: Context) = Intent(context, AboutActivity::class.java)
    }

    private lateinit var binding: ActivityAboutBinding

    private val vm = AboutVM(this)

    override fun getTvInfo() = binding.layoutNotification.tvNotification

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_about)
        binding.vm = vm
        binding.itemVersion.root.setOnClickListener {
            ApkDownloadWorker.getInstance().checkApkVersion(this, true) {
                vm.updateVersionText()
            }
        }
        binding.itemUserAgreement.root.setOnClickListener {
            startActivity(
                AgreementActivity.newIntent(
                    this,
                    Agreement("用户服务协议", "user_agreement.html")
                )
            )
        }
        binding.itemDataProtection.root.setOnClickListener {
            startActivity(
                AgreementActivity.newIntent(
                    this,
                    Agreement("数据保护承诺书", "data_protection.html")
                )
            )
        }
        binding.itemPrivacy.root.setOnClickListener {
            startActivity(AgreementActivity.newIntent(this, Agreement("隐私协议", "privacy.html")))
        }
    }

}