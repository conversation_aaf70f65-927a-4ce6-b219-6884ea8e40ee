package com.midea.prestorage.function.outstorage.dialog

import android.app.AlertDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.OutStorageQuery
import com.midea.prestorage.beans.net.OutStorageScan
import com.midea.prestorage.beans.net.RespPickContainerInfo
import com.midea.prestorage.function.inv.dialog.DialogPrintNumUnionBinding
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogBindShippingLocBinding

class BindShippingLocDialog(
    var mContext: BaseActivity
) : AlertDialog(mContext) {

    var binding: DialogBindShippingLocUnionBinding
    var deleteInfo: RespPickContainerInfo? = null
    var bean: OutStorageQuery? = null
    var inputBack: InputBack? = null

    init {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window?.setGravity(Gravity.CENTER)
            window?.attributes?.run {
                gravity = Gravity.CENTER
            }
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_bind_shipping_loc_care, null)
            setView(contentView)
            DialogBindShippingLocUnionBinding.V2(DataBindingUtil.bind(contentView)!!)
        } else {
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_bind_shipping_loc, null)
            setView(contentView)
            DialogBindShippingLocUnionBinding.V1(DataBindingUtil.bind(contentView)!!)
        }
        binding.vm = BindShippingLocDialogVM(this)

        setCanceledOnTouchOutside(true)

    }

    override fun show() {
        super.show()
        binding.etLocCode.post {
            binding.etLocCode.requestFocus()
        }
        binding.vm!!.goodsNo.set("")

        binding.cbAppointLoc.isChecked = true
        binding.cbNewLoc.isChecked = false
        binding.tvLocCode.post {
            binding.tvLocCode.isClickable = false
        }

        binding.cbAppointLoc.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.cbNewLoc.isChecked = false
            } else if (!binding.cbNewLoc.isChecked) {
                binding.cbAppointLoc.isChecked = true
            }
            binding.etLocCode.isEnabled = true
            binding.etLocCode.requestFocus()
            binding.tvLocCode.isClickable = false
        }

        binding.cbNewLoc.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.cbAppointLoc.isChecked = false
            } else if (!binding.cbAppointLoc.isChecked) {
                binding.cbNewLoc.isChecked = true
            }
            binding.etLocCode.isEnabled = false
            binding.tvLocCode.isClickable = true
        }

        binding.vm!!.show()
    }

    interface InputBack {
        fun inputOk()
        fun inputFail()
    }
}