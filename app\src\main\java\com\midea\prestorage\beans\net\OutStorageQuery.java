package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.beans.base.BaseItemForPopup;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class OutStorageQuery implements Serializable {

    private List<ShipmentDetailSumQtyDtoList> outShipmentDetailInfoVOS;
    private List<String> waveNoList;
    private BigDecimal planQty;
    private BigDecimal scanQty;
    private Integer queryType;

    public int getPlanQty() {
        if (planQty == null) {
            return 0;
        }
        return planQty.intValue();
    }

    public void setPlanQty(BigDecimal planQty) {
        this.planQty = planQty;
    }

    public int getScanQty() {
        if (scanQty == null) {
            return 0;
        }
        return scanQty.intValue();
    }

    public void setScanQty(BigDecimal scanQty) {
        this.scanQty = scanQty;
    }

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public List<ShipmentDetailSumQtyDtoList> getOutShipmentDetailInfoVOS() {
        return outShipmentDetailInfoVOS;
    }

    public void setOutShipmentDetailInfoVOS(List<ShipmentDetailSumQtyDtoList> outShipmentDetailInfoVOS) {
        this.outShipmentDetailInfoVOS = outShipmentDetailInfoVOS;
    }

    public List<String> getWaveNoList() {
        return waveNoList;
    }

    public void setWaveNoList(List<String> waveNoList) {
        this.waveNoList = waveNoList;
    }

    public class ShipmentDetailSumQtyDtoList extends BaseItemForPopup implements Serializable {
        @ShowAnnotation
        private String shipmentCode;
        @ShowAnnotation
        private String custItemCode;
        @ShowAnnotation
        private String itemCode;
        @ShowAnnotation
        private String itemName;
        @ShowAnnotation
        private BigDecimal planQty;
        @ShowAnnotation
        private Integer serialQty;
        @ShowAnnotation
        private String lotAtt04Str;

        //前置仓不扫码 表示
        private String unscanMark;
        //大物流不扫码 标识
        private String unScanMark;

        private String ownerCode;
        private String siteCode;
        private String siteName;
        private String lotAtt04;
        private String id;
        /**
         * 拣货箱头ID
         */
        private String pickContainerHeaderId;
        /**
         * lotNum
         */
        private String lotNum;
        private String unit;
        private String waveNo;
        private String status;
        private String custOrderNo;
        private BigDecimal oqcQty;
        /**
         * 单位毛重
         */
        private BigDecimal weight;
        /**
         * 单位体积
         */
        private BigDecimal volume;
        private String businessType;
        private Boolean isB2CPrint;
        private boolean isUnScanMark;

        /**
         * 不扫码标识（Y、N）
         */
        private String cdcmUnScanMark;

        public String getUnScanMark() {
            return unScanMark;
        }

        public boolean getUnScanMarkBoolean() {
            try {
                return Boolean.parseBoolean(unScanMark);
            } catch (Exception e) {
                return false;
            }
        }

        public void setUnScanMark(String unScanMark) {
            this.unScanMark = unScanMark;
        }

        public String getUnscanMark() {
            return unscanMark;
        }

        public void setUnscanMark(String unscanMark) {
            this.unscanMark = unscanMark;
        }

        public String getOwnerCode() {
            return ownerCode;
        }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
        }

        public String getSiteCode() {
            return siteCode;
        }

        public void setSiteCode(String siteCode) {
            this.siteCode = siteCode;
        }

        public String getSiteName() {
            return siteName;
        }

        public void setSiteName(String siteName) {
            this.siteName = siteName;
        }

        public String getShipmentCode() {
            return shipmentCode;
        }

        public void setShipmentCode(String shipmentCode) {
            this.shipmentCode = shipmentCode;
        }

        public String getCustItemCode() {
            return custItemCode;
        }

        public void setCustItemCode(String custItemCode) {
            this.custItemCode = custItemCode;
        }

        public String getItemCode() {
            return itemCode;
        }

        public void setItemCode(String itemCode) {
            this.itemCode = itemCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public void setPlanQty(BigDecimal planQty) {
            this.planQty = planQty;
        }

        public int getPlanQty() {
            if (planQty == null) {
                return 0;
            }
            return planQty.intValue();
        }

//        public void setScanQty(BigDecimal scanQty) {
//            this.scanQty = scanQty;
//        }
//
//        public int getScanQty() {
//            if (scanQty == null) {
//                return 0;
//            }
//            return scanQty.intValue();
//        }

        public String getLotAtt04() {
            return lotAtt04;
        }

        public void setLotAtt04(String lotAtt04) {
            this.lotAtt04 = lotAtt04;
        }

        public String getLotAtt04Str() {
            return lotAtt04Str;
        }

        public void setLotAtt04Str(String lotAtt04Str) {
            this.lotAtt04Str = lotAtt04Str;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getPickContainerHeaderId() {
            return pickContainerHeaderId;
        }

        public void setPickContainerHeaderId(String pickContainerHeaderId) {
            this.pickContainerHeaderId = pickContainerHeaderId;
        }

        public String getLotNum() {
            return lotNum;
        }

        public void setLotNum(String lotNum) {
            this.lotNum = lotNum;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public String getWaveNo() {
            return waveNo;
        }

        public void setWaveNo(String waveNo) {
            this.waveNo = waveNo;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getCustOrderNo() {
            return custOrderNo;
        }

        public void setCustOrderNo(String custOrderNo) {
            this.custOrderNo = custOrderNo;
        }

        public Integer getSerialQty() {
            return serialQty;
        }

        public void setSerialQty(Integer serialQty) {
            this.serialQty = serialQty;
        }

        public BigDecimal getOqcQty() {
            return oqcQty;
        }

        public void setOqcQty(BigDecimal oqcQty) {
            this.oqcQty = oqcQty;
        }

        public BigDecimal getWeight() {
            return weight;
        }

        public void setWeight(BigDecimal weight) {
            this.weight = weight;
        }

        public BigDecimal getVolume() {
            return volume;
        }

        public void setVolume(BigDecimal volume) {
            this.volume = volume;
        }

        public String getBusinessType() {
            return businessType;
        }

        public void setBusinessType(String businessType) {
            this.businessType = businessType;
        }

        public Boolean getB2CPrint() {
            return isB2CPrint;
        }

        public void setB2CPrint(Boolean b2CPrint) {
            isB2CPrint = b2CPrint;
        }

        public boolean isUnScanMark() {
            return isUnScanMark;
        }

        public void setUnScanMark(boolean unScanMark) {
            isUnScanMark = unScanMark;
        }

        public String getCdcmUnScanMark() {
            return cdcmUnScanMark;
        }

        public void setCdcmUnScanMark(String cdcmUnScanMark) {
            this.cdcmUnScanMark = cdcmUnScanMark;
        }
    }
}
