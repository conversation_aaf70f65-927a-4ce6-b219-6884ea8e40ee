package com.midea.prestorage.beans.net;

import java.io.Serializable;
import java.math.BigDecimal;

public class ContainerReceivePrintList implements Serializable {

    private String containerCode;
    private String ownerCode;
    private String receiptCode;
    private String custOrderNo;
    private String receiptBy;
    private String containerClosedTime;
    private String itemName;
    private BigDecimal unitQty;
    private String unitName;
    private String lotAtt05;
    private String lotAtt01;
    private String validityUnit;
    private BigDecimal periodOfValidity;
    private String custItemCode;
    private String itemCode;
    private String whBarcode69;
    private String unit;
    private String ownerName;
    private BigDecimal skuCount;
    private BigDecimal totalQty;

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public BigDecimal getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(BigDecimal skuCount) {
        this.skuCount = skuCount;
    }

    public BigDecimal getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getReceiptCode() {
        return receiptCode;
    }

    public void setReceiptCode(String receiptCode) {
        this.receiptCode = receiptCode;
    }

    public String getCustOrderNo() {
        return custOrderNo;
    }

    public void setCustOrderNo(String custOrderNo) {
        this.custOrderNo = custOrderNo;
    }

    public String getReceiptBy() {
        return receiptBy;
    }

    public void setReceiptBy(String receiptBy) {
        this.receiptBy = receiptBy;
    }

    public String getContainerClosedTime() {
        return containerClosedTime;
    }

    public void setContainerClosedTime(String containerClosedTime) {
        this.containerClosedTime = containerClosedTime;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getLotAtt05() {
        return lotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        this.lotAtt05 = lotAtt05;
    }

    public String getLotAtt01() {
        return lotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        this.lotAtt01 = lotAtt01;
    }

    public String getValidityUnit() {
        return validityUnit;
    }

    public void setValidityUnit(String validityUnit) {
        this.validityUnit = validityUnit;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getWhBarcode69() {
        return whBarcode69;
    }

    public void setWhBarcode69(String whBarcode69) {
        this.whBarcode69 = whBarcode69;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getUnitQty() {
        return unitQty;
    }

    public void setUnitQty(BigDecimal unitQty) {
        this.unitQty = unitQty;
    }

    public BigDecimal getPeriodOfValidity() {
        return periodOfValidity;
    }

    public void setPeriodOfValidity(BigDecimal periodOfValidity) {
        this.periodOfValidity = periodOfValidity;
    }
}