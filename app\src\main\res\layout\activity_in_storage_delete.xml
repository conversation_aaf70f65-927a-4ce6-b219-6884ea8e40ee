<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="vm"
            type="com.midea.prestorage.function.instorage.InStorageDeleteVM" />

        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/icon_title_less_bg"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_40"
                android:clickable="true"
                android:gravity="center_vertical|left"
                android:onClick="@{()->vm.back()}"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_black_left" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="返回"
                    android:textColor="@color/title"
                    android:textSize="12sp" />

            </LinearLayout>

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@{vm.title}"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_14" />

            <LinearLayout
                android:layout_width="50dp"
                android:layout_height="wrap_content" />

        </LinearLayout>


        <TextView
            android:id="@+id/tv_notification"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorRed"
            android:drawableLeft="@mipmap/setting_prompt"
            android:drawablePadding="5dp"
            android:textColor="@color/colorWhite"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp_10"
            android:paddingVertical="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="删除条码"
                android:textColor="@color/colorOrange"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_weight="1"
                android:background="@drawable/common_et_bg"
                android:orientation="horizontal">

                <EditText
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"

                    android:layout_weight="1"
                    android:background="@null"
                    android:digits="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-"
                    android:hint="输入或扫描要删除的序列号"
                    android:maxLines="1"
                    android:padding="5dp"
                    android:singleLine="true"
                    android:text="@={vm.anyCode}"
                    android:textColor="@color/title"
                    android:textSize="12sp"
                    app:onEnterKeyPress="@{()->vm.enterSerialNoToDelete()}" />

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="7dp"
                    android:onClick="@{()->vm.onClearSn()}"
                    android:padding="2dp"
                    android:src="@mipmap/close_grey" />


            </LinearLayout>


            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:onClick="@{()->vm.startScan()}"
                android:padding="5dp"
                android:src="@mipmap/scan_code" />
        </LinearLayout>

        <include layout="@layout/line_shadow_down" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/sort_back" />
    </LinearLayout>
</layout>