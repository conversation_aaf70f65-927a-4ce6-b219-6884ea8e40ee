package com.midea.prestorage.function.inv.response;

import com.midea.prestorage.beans.base.BaseItemShowInfo;

public class BsLocation extends BaseItemShowInfo  {

    String locCodeAndZoneName;
    /**
     * 库位编码
     */
    private String locCode;

    /**
     * 库位名称
     */
    private String locName;

    /**
     * 库区编码
     */
    private String zoneCode;

    /**
     * 库区名称
     */
    private String zoneName;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 仓库编码
     */
    private String whCode;

    /**
     * 仓库名称
     */
    private String whName;

    /**
     * 服务平台编码
     */
    private String siteCode;

    /**
     * 服务平台名称
     */
    private String siteName;

    /**
     * 库位属性(正常:0冻结:1)
     */
    private String isEnable;

    /**
     * 状态,0启用,1停用
     */
    private Integer status;

    /**
     * 库位种类
     */
    private String category;

    /**
     * 使用类型
     */
    private String locUseType;

    /**
     * 商品abc
     */
    private String abc;

    /**
     * 长
     */
    private String length;

    /**
     * 宽
     */
    private String width;

    /**
     * 高
     */
    private String height;

    /**
     * 层
     */
    private Integer floor;

    /**
     * 最大数量
     */
    private String maxCount;

    /**
     * 最大重量
     */
    private String maxWeight;

    /**
     * 最大体积
     */
    private String maxCubic;

    /**
     * 最大托盘数
     */
    private String maxPl;

    /**
     * 条码(rfid)
     */
    private String locRfid;

    /**
     * 是否允许混批次
     */
    private String isMixSku;

    /**
     * 最大混商品数量
     */
    private Integer maxMixSku;

    /**
     * 是否允许混批次
     */
    private String isMixLot;

    /**
     * 最大混批次数量
     */
    private Integer maxMixLot;

    /**
     * x坐标
     */
    private String x;

    /**
     * y坐标
     */
    private String y;

    /**
     * z坐标
     */
    private String z;

    /**
     * 拣货顺序号
     */
    private Integer pickSeqNum;

    /**
     * 盘点顺序号
     */
    private Integer stocktakeSeqNum;

    /**
     * 上架顺序号
     */
    private Integer putawaySeqNum;

    /**
     * 整件整箱,0空,1整件,2整箱/托
     */
    private Integer isEach;

    private Integer count;


    public String getLocCodeAndZoneName() {
        return locCodeAndZoneName;
    }

    public void setLocCodeAndZoneName(String locCodeAndZoneName) {
        this.locCodeAndZoneName = locCodeAndZoneName;
    }

    public String getLocCode() {
        return locCode;
    }

    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }

    public String getLocName() {
        return locName;
    }

    public void setLocName(String locName) {
        this.locName = locName;
    }

    public String getZoneCode() {
        return zoneCode;
    }

    public void setZoneCode(String zoneCode) {
        this.zoneCode = zoneCode;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getWhName() {
        return whName;
    }

    public void setWhName(String whName) {
        this.whName = whName;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getLocUseType() {
        return locUseType;
    }

    public void setLocUseType(String locUseType) {
        this.locUseType = locUseType;
    }

    public String getAbc() {
        return abc;
    }

    public void setAbc(String abc) {
        this.abc = abc;
    }

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public Integer getFloor() {
        return floor;
    }

    public void setFloor(Integer floor) {
        this.floor = floor;
    }

    public String getMaxCount() {
        return maxCount;
    }

    public void setMaxCount(String maxCount) {
        this.maxCount = maxCount;
    }

    public String getMaxWeight() {
        return maxWeight;
    }

    public void setMaxWeight(String maxWeight) {
        this.maxWeight = maxWeight;
    }

    public String getMaxCubic() {
        return maxCubic;
    }

    public void setMaxCubic(String maxCubic) {
        this.maxCubic = maxCubic;
    }

    public String getMaxPl() {
        return maxPl;
    }

    public void setMaxPl(String maxPl) {
        this.maxPl = maxPl;
    }

    public String getLocRfid() {
        return locRfid;
    }

    public void setLocRfid(String locRfid) {
        this.locRfid = locRfid;
    }

    public String getIsMixSku() {
        return isMixSku;
    }

    public void setIsMixSku(String isMixSku) {
        this.isMixSku = isMixSku;
    }

    public Integer getMaxMixSku() {
        return maxMixSku;
    }

    public void setMaxMixSku(Integer maxMixSku) {
        this.maxMixSku = maxMixSku;
    }

    public String getIsMixLot() {
        return isMixLot;
    }

    public void setIsMixLot(String isMixLot) {
        this.isMixLot = isMixLot;
    }

    public Integer getMaxMixLot() {
        return maxMixLot;
    }

    public void setMaxMixLot(Integer maxMixLot) {
        this.maxMixLot = maxMixLot;
    }

    public String getX() {
        return x;
    }

    public void setX(String x) {
        this.x = x;
    }

    public String getY() {
        return y;
    }

    public void setY(String y) {
        this.y = y;
    }

    public String getZ() {
        return z;
    }

    public void setZ(String z) {
        this.z = z;
    }

    public Integer getPickSeqNum() {
        return pickSeqNum;
    }

    public void setPickSeqNum(Integer pickSeqNum) {
        this.pickSeqNum = pickSeqNum;
    }

    public Integer getStocktakeSeqNum() {
        return stocktakeSeqNum;
    }

    public void setStocktakeSeqNum(Integer stocktakeSeqNum) {
        this.stocktakeSeqNum = stocktakeSeqNum;
    }

    public Integer getPutawaySeqNum() {
        return putawaySeqNum;
    }

    public void setPutawaySeqNum(Integer putawaySeqNum) {
        this.putawaySeqNum = putawaySeqNum;
    }

    public Integer getIsEach() {
        return isEach;
    }

    public void setIsEach(Integer isEach) {
        this.isEach = isEach;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
	