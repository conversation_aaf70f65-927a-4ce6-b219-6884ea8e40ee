package com.midea.prestorage.function.inv

import android.widget.EditText
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestorage.widgets.DropEditText
import com.midea.prestorage.widgets.TrailingZerosEditText
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.databinding.ActivityLotEditBinding
import com.midea.prestoragesaas.databinding.ActivityLotEditCareBinding
import com.midea.prestoragesaas.databinding.ActivityLotSearchBinding
import com.midea.prestoragesaas.databinding.ActivityLotSearchCareBinding

sealed class ActivityLotEditUnionBinding{
    abstract var vm: LotEditVM?
    abstract val llTitleBar: RelativeLayout
    abstract val etUpdateReason: DropEditText
    abstract val etUpdateNum: TrailingZerosEditText
    abstract val spinnerStatus: MaterialSpinner
    abstract val tvNotification: TextView

    class V2(val binding: ActivityLotEditCareBinding) : ActivityLotEditUnionBinding() {
        override var vm: LotEditVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etUpdateReason = binding.etUpdateReason
        override val etUpdateNum = binding.etUpdateNum
        override val spinnerStatus = binding.spinnerStatus
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityLotEditBinding) : ActivityLotEditUnionBinding() {
        override var vm: LotEditVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etUpdateReason = binding.etUpdateReason
        override val etUpdateNum = binding.etUpdateNum
        override val spinnerStatus = binding.spinnerStatus
        override val tvNotification = binding.tvNotification
    }
}
