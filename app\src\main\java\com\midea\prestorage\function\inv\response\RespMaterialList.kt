package com.midea.prestorage.function.inv.response

import com.midea.prestorage.base.annotation.ShowAnnotation
import java.math.BigDecimal

data class RespMaterialList(
    @ShowAnnotation
    var custItemCode: String? = null,
    var itemCode: String? = null,
    @ShowAnnotation
    var itemName: String? = null,
    var cdcmBarcode69: String? = null,
    var cdcmPackageCode: String? = null,
    var cdcmLot: String? = null,
    var isValidity: String? = null,
    var validityUnit: String? = null,
    var periodOfValidity: BigDecimal? = null,
    var isPoint: String? = null
)
