package com.midea.prestorage.beans.net;

import androidx.annotation.Nullable;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.function.inv.response.PackageRelation;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ContainerPickSecondList implements Serializable {

    @ShowAnnotation
    private int index;
    @ShowAnnotation
    private String qtyInfo;
    @ShowAnnotation
    private String allQtyInfo;
    @ShowAnnotation
    private String custItemCode;
    @ShowAnnotation
    private String whBarcode69;
    @ShowAnnotation
    private String fromLoc;
    @ShowAnnotation
    private String pickUserCode;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal toQty;
    @ShowAnnotation
    private String lotAtt01;
    @ShowAnnotation
    private String lotAtt05;
    @ShowAnnotation
    private String itemName;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal sumEaQty;
    @ShowAnnotation
    private String eaUnit;
    @ShowAnnotation
    private String csBarcode69;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal packagePara;

    private String lotAtt04;
    private String id;
    private String createTime;
    private String waveNo;
    private String updateTime;
    private String tenantCode;
    private String createUserCode;
    private String createUserName;
    private String updateUserCode;
    private String updateUserName;
    private String remark;
    private String version;
    private String deleteFlag;
    private String pageNo;
    private String pageSize;
    private String offset;
    private String orderBy;
    private String orderByType;
    private String tenantCodes;
    private String count;
    private String startTime;
    private String endTime;
    private String taskCode;
    private String taskType;
    private String whCode;
    private String ownerCode;
    private String itemCode;
    private String packageAllocQty;
    private String packageAllocUm;
    private String fromZone;
    private String toLoc;
    private String lotNum;
    private String referenceCode;
    private String referenceLineId;
    private String referenceReqId;
    private String traceId;
    private String taskStartTime;
    private String taskEndTime;
    private String pickSeqNum;
    private String backTaskId;
    private String shipToCustomerCode;
    private String whMaxBarcode69;
    private String shipToCustomerName;
    private String taskDetailId;
    private String unit;
    private String lotAtt02;
    private String lotAtt03;
    private String csUnit;
    private String isNeedScan69;
    private String confirmedBy;
    private String taskHeaderIds;
    private String shortPick;
    private BigDecimal sumQty;
    private BigDecimal sumCsQty;
    private BigDecimal fromQty;
    private int status;
    private String detailStatus;

    private List<String> ids;

    private List<String> taskDetailIdList;

    private int isDecimal;

    private String whIpBarcode69;

    private String cdpaFormat;

    private BigDecimal repFlag;

    @Nullable
    private String comPickNo;

    private List<String> repTaskDetaiIdList;
    private List<String> pickeStoreInfoList;

    @Nullable
    private String commonFlag;

    @Nullable
    private List<String> shippingLocList;

    @Nullable
    public List<String> getShippingLocList() {
        return shippingLocList;
    }

    public void setShippingLocList(@Nullable List<String> shippingLocList) {
        this.shippingLocList = shippingLocList;
    }

    @Nullable
    public String getCommonFlag() {
        return commonFlag;
    }

    public void setCommonFlag(@Nullable String commonFlag) {
        this.commonFlag = commonFlag;
    }

    public List<String> getPickeStoreInfoList() {
        return pickeStoreInfoList;
    }

    public void setPickeStoreInfoList(List<String> pickeStoreInfoList) {
        this.pickeStoreInfoList = pickeStoreInfoList;
    }

    public List<String> getRepTaskDetaiIdList() {
        return repTaskDetaiIdList;
    }

    public void setRepTaskDetaiIdList(List<String> repTaskDetaiIdList) {
        this.repTaskDetaiIdList = repTaskDetaiIdList;
    }

    @Nullable
    public String getComPickNo() {
        return comPickNo;
    }

    public void setComPickNo(@Nullable String comPickNo) {
        this.comPickNo = comPickNo;
    }

    public List<String> getTaskDetailIdList() {
        if (taskDetailIdList == null) {
            taskDetailIdList = new ArrayList<>();
        }
        return taskDetailIdList;
    }

    public void setTaskDetailIdList(List<String> taskDetailIdList) {
        this.taskDetailIdList = taskDetailIdList;
    }

    public BigDecimal getRepFlag() {
        return repFlag;
    }

    public void setRepFlag(BigDecimal repFlag) {
        this.repFlag = repFlag;
    }

    public String getCdpaFormat() {
        return cdpaFormat;
    }

    public void setCdpaFormat(String cdpaFormat) {
        this.cdpaFormat = cdpaFormat;
    }

    public String getWhIpBarcode69() {
        return whIpBarcode69;
    }

    public void setWhIpBarcode69(String whIpBarcode69) {
        this.whIpBarcode69 = whIpBarcode69;
    }

    public int getIsDecimal() {
        return isDecimal;
    }

    public void setIsDecimal(int isDecimal) {
        this.isDecimal = isDecimal;
    }

    private boolean isSortPick;
    private int gridNumber;

    private List<ContainerPickTaskTypeList> taskCombines;

    private List<PackageRelation> packageRelationList;

    private BigDecimal packageParaIp;
    private String ipUnit;

    private BigDecimal sumIpQty;

    public BigDecimal getSumIpQty() {
        return sumIpQty;
    }

    public void setSumIpQty(BigDecimal sumIpQty) {
        this.sumIpQty = sumIpQty;
    }

    public BigDecimal getPackageParaIp() {
        return packageParaIp;
    }

    public void setPackageParaIp(BigDecimal packageParaIp) {
        this.packageParaIp = packageParaIp;
    }

    public String getIpUnit() {
        return ipUnit;
    }

    public void setIpUnit(String ipUnit) {
        this.ipUnit = ipUnit;
    }

    public List<PackageRelation> getPackageRelationList() {
        return packageRelationList;
    }

    public void setPackageRelationList(List<PackageRelation> packageRelationList) {
        this.packageRelationList = packageRelationList;
    }

    public List<ContainerPickTaskTypeList> getTaskCombines() {
        if (taskCombines == null) {
            taskCombines = new ArrayList<>();
        }
        return taskCombines;
    }

    public void setTaskCombines(List<ContainerPickTaskTypeList> taskCombines) {
        this.taskCombines = taskCombines;
    }

    public int getGridNumber() {
        return gridNumber;
    }

    public void setGridNumber(int gridNumber) {
        this.gridNumber = gridNumber;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getPageNo() {
        return pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getOffset() {
        return offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderByType() {
        return orderByType;
    }

    public void setOrderByType(String orderByType) {
        this.orderByType = orderByType;
    }

    public String getTenantCodes() {
        return tenantCodes;
    }

    public void setTenantCodes(String tenantCodes) {
        this.tenantCodes = tenantCodes;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getPackageAllocQty() {
        return packageAllocQty;
    }

    public void setPackageAllocQty(String packageAllocQty) {
        this.packageAllocQty = packageAllocQty;
    }

    public String getPackageAllocUm() {
        return packageAllocUm;
    }

    public void setPackageAllocUm(String packageAllocUm) {
        this.packageAllocUm = packageAllocUm;
    }

    public String getFromZone() {
        return fromZone;
    }

    public void setFromZone(String fromZone) {
        this.fromZone = fromZone;
    }

    public String getFromLoc() {
        return fromLoc;
    }

    public void setFromLoc(String fromLoc) {
        this.fromLoc = fromLoc;
    }

    public String getToLoc() {
        return toLoc;
    }

    public void setToLoc(String toLoc) {
        this.toLoc = toLoc;
    }

    public String getLotNum() {
        return lotNum;
    }

    public void setLotNum(String lotNum) {
        this.lotNum = lotNum;
    }

    public String getReferenceCode() {
        return referenceCode;
    }

    public void setReferenceCode(String referenceCode) {
        this.referenceCode = referenceCode;
    }

    public String getReferenceLineId() {
        return referenceLineId;
    }

    public void setReferenceLineId(String referenceLineId) {
        this.referenceLineId = referenceLineId;
    }

    public String getReferenceReqId() {
        return referenceReqId;
    }

    public void setReferenceReqId(String referenceReqId) {
        this.referenceReqId = referenceReqId;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getTaskStartTime() {
        return taskStartTime;
    }

    public void setTaskStartTime(String taskStartTime) {
        this.taskStartTime = taskStartTime;
    }

    public String getTaskEndTime() {
        return taskEndTime;
    }

    public void setTaskEndTime(String taskEndTime) {
        this.taskEndTime = taskEndTime;
    }

    public String getPickSeqNum() {
        return pickSeqNum;
    }

    public void setPickSeqNum(String pickSeqNum) {
        this.pickSeqNum = pickSeqNum;
    }

    public String getBackTaskId() {
        return backTaskId;
    }

    public void setBackTaskId(String backTaskId) {
        this.backTaskId = backTaskId;
    }

    public String getShipToCustomerCode() {
        return shipToCustomerCode;
    }

    public void setShipToCustomerCode(String shipToCustomerCode) {
        this.shipToCustomerCode = shipToCustomerCode;
    }

    public String getShipToCustomerName() {
        return shipToCustomerName;
    }

    public void setShipToCustomerName(String shipToCustomerName) {
        this.shipToCustomerName = shipToCustomerName;
    }

    public String getTaskDetailId() {
        return taskDetailId;
    }

    public void setTaskDetailId(String taskDetailId) {
        this.taskDetailId = taskDetailId;
    }

    public String getWhBarcode69() {
        return whBarcode69;
    }

    public void setWhBarcode69(String whBarcode69) {
        this.whBarcode69 = whBarcode69;
    }

    public String getCsBarcode69() {
        return csBarcode69;
    }

    public void setCsBarcode69(String csBarcode69) {
        this.csBarcode69 = csBarcode69;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getLotAtt01() {
        return lotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        this.lotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return lotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        this.lotAtt02 = lotAtt02;
    }

    public String getLotAtt03() {
        return lotAtt03;
    }

    public void setLotAtt03(String lotAtt03) {
        this.lotAtt03 = lotAtt03;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getLotAtt05() {
        return lotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        this.lotAtt05 = lotAtt05;
    }

    public String getTaskHeaderIds() {
        return taskHeaderIds;
    }

    public void setTaskHeaderIds(String taskHeaderIds) {
        this.taskHeaderIds = taskHeaderIds;
    }

    public String getQtyInfo() {
        return qtyInfo;
    }

    public void setQtyInfo(String qtyInfo) {
        this.qtyInfo = qtyInfo;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public BigDecimal getPackagePara() {
        return packagePara;
    }

    public void setPackagePara(BigDecimal packagePara) {
        this.packagePara = packagePara;
    }

    public BigDecimal getSumQty() {
        return sumQty;
    }

    public void setSumQty(BigDecimal sumQty) {
        this.sumQty = sumQty;
    }

    public BigDecimal getSumCsQty() {
        return sumCsQty;
    }

    public void setSumCsQty(BigDecimal sumCsQty) {
        this.sumCsQty = sumCsQty;
    }

    public BigDecimal getSumEaQty() {
        return sumEaQty;
    }

    public void setSumEaQty(BigDecimal sumEaQty) {
        this.sumEaQty = sumEaQty;
    }

    public String getPickUserCode() {
        return pickUserCode;
    }

    public void setPickUserCode(String pickUserCode) {
        this.pickUserCode = pickUserCode;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ContainerPickSecondList that = (ContainerPickSecondList) o;
        return Objects.equals(ownerCode, that.ownerCode) &&
                Objects.equals(itemCode, that.itemCode) &&
                Objects.equals(whBarcode69, that.whBarcode69) &&
                Objects.equals(lotAtt01, that.lotAtt01) &&
                Objects.equals(lotAtt04, that.lotAtt04) &&
                Objects.equals(lotAtt05, that.lotAtt05) &&
                Objects.equals(fromLoc, that.fromLoc) &&
                !detailStatus.equals("900") && !that.detailStatus.equals("900");
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    public List<String> getIds() {
        if (ids == null) {
            ids = new ArrayList<>();
        }
        return ids;
    }

    public BigDecimal getFromQty() {
        return fromQty;
    }

    public void setFromQty(BigDecimal fromQty) {
        this.fromQty = fromQty;
    }

    public BigDecimal getToQty() {
        return toQty;
    }

    public void setToQty(BigDecimal toQty) {
        this.toQty = toQty;
    }

    public String getAllQtyInfo() {
        return allQtyInfo;
    }

    public void setAllQtyInfo(String allQtyInfo) {
        this.allQtyInfo = allQtyInfo;
    }

    public String getCsUnit() {
        if (csUnit == null) {
            return "-";
        }
        return csUnit;
    }

    public void setCsUnit(String csUnit) {
        this.csUnit = csUnit;
    }

    public String getEaUnit() {
        if (eaUnit == null) {
            return "-";
        }
        return eaUnit;
    }

    public void setEaUnit(String eaUnit) {
        this.eaUnit = eaUnit;
    }

    public String getWhMaxBarcode69() {
        return whMaxBarcode69;
    }

    public void setWhMaxBarcode69(String whMaxBarcode69) {
        this.whMaxBarcode69 = whMaxBarcode69;
    }

    public String getIsNeedScan69() {
        return isNeedScan69;
    }

    public void setIsNeedScan69(String isNeedScan69) {
        this.isNeedScan69 = isNeedScan69;
    }

    public String getConfirmedBy() {
        return confirmedBy;
    }

    public void setConfirmedBy(String confirmedBy) {
        this.confirmedBy = confirmedBy;
    }

    public boolean isSortPick() {
        return isSortPick;
    }

    public void setSortPick(boolean sortPick) {
        isSortPick = sortPick;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getShortPick() {
        return shortPick;
    }

    public void setShortPick(String shortPick) {
        this.shortPick = shortPick;
    }

    public String getDetailStatus() {
        return detailStatus;
    }

    public void setDetailStatus(String detailStatus) {
        this.detailStatus = detailStatus;
    }
}