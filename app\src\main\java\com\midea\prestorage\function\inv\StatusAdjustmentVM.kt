package com.midea.prestorage.function.inv

import android.app.Application
import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.function.inv.response.HeaderSearchResp
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody

class StatusAdjustmentVM(application: Application) : BaseViewModel(application) {
    val title = MutableLiveData<String>("订单列表")
    var finishActivity = MutableLiveData(false)
    var curOrderNo = MutableLiveData<String>("") // 单号 (入库单号或波次单号)
    val isRefreshing = MutableLiveData(false)
    val isNoData = MutableLiveData(false)
    var dayInfo = MutableLiveData<String>("1天")
    val toScanActivity = MutableLiveData<MutableList<String>>()
    var showDatas = MutableLiveData<MutableList<HeaderSearchResp>>()
    var loadMoreDatas = MutableLiveData<MutableList<HeaderSearchResp>>()
    val loadMoreComplete = MutableLiveData(0)
    var isFirstEnter = MutableLiveData(true)
    var adjustStatus = MutableLiveData<MutableList<DCBean>>()

    // 当前页码
    var pageNo = 1

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        isRefreshing.value = true
        pageNo = 1
        initOrderList()
    }

    override fun init() {
        toScanActivity.value = mutableListOf()
    }

    fun loadMore() {
        initOrderList(true)
    }

    fun onEnterOrderNo() {
        if (CheckUtil.isFastDoubleClick()) {
            if (TextUtils.isEmpty(curOrderNo.value)) {
                showNotification("单号不能为空", false)
                return
            }
            enterOrderList(curOrderNo.value.toString().trim())
        }
    }

    fun initAdjustStatus() {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {
            val result = withContext(Dispatchers.IO) {
                val direction =
                    async { RetrofitHelper.getDirectionAPI().searchDictNew("FU_ADJUST_STATUS") }
                direction.await()
            }

            if (result.code == 0.toLong()) {
                val datas = mutableListOf<DCBean>()
                result.data?.removeAll { it.enableFlag == 0 }
                result.data?.forEach {
                    datas.add(DCBean(it.code, it.name, DCBean.SHOW_VALUE))
                }
                adjustStatus.value = datas
            }
        }
    }

    fun initOrderList(isLoadMore: Boolean = false) {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf<String, Any>()
            param["pageNo"] = pageNo
            param["pageSize"] = 10
            param["whCode"] = Constants.whInfo?.whCode.toString()
            param["subDay"] = dayInfo.value.toString().split("天")[0].toInt()

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().headerSearch(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                isFirstEnter.value = false
                isRefreshing.value = false
                if (result.data != null) {
                    if (isLoadMore) {
                        loadMoreDatas.value = result.data!!.list
                        loadMoreComplete.value = 1
                    } else {
                        showDatas.value = result.data!!.list
                    }
                }
                if (pageNo >= result.data?.totalPage!!) {
                    loadMoreComplete.value = 2
                } else {
                    pageNo = result.data?.pageNo!! + 1
                }
            } else {
                isFirstEnter.value = false
                isRefreshing.value = false
                result.msg?.let { showNotification(it, false) }
            }
        }
    }


    fun enterOrderList(orderNo: String) {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf<String, Any>()
            param["pageNo"] = 1
            param["pageSize"] = 10
            param["whCode"] = Constants.whInfo?.whCode.toString()
            param["subDay"] = dayInfo.value.toString().split("天")[0].toInt()
            if(orderNo.startsWith("WTK")) {
                param["adjustCode"] = orderNo
            }else {
                param["custOrderNo"] = orderNo
            }

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().headerSearch(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                curOrderNo.value = ""
                if (result.data != null) {
                    if (result.data!!.list.size > 0) { //这里是为了判断是否是按了ENTER键，如果是的话，并且有数据则跳转到扫码页面
                        val datas = mutableListOf<String>()
                        if (!result.data!!.list[0].adjustCode.isNullOrBlank()) {
                            datas.add(result.data!!.list[0].adjustCode)
                        }
                        toScanActivity.value = datas
                    } else {
                        showNotification("单据不存在", false)
                    }
                }
            } else {
                curOrderNo.value = ""
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

}