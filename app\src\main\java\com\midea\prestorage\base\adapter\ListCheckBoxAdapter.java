package com.midea.prestorage.base.adapter;

import android.view.View;

import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.midea.prestorage.beans.base.BaseItemForPopup;

import java.util.ArrayList;
import java.util.List;

/**
 * description 多选Dialog
 * author: wangqin
 * date: 2020/6/19
 * param
 * return
 */
public class ListCheckBoxAdapter<T extends BaseItemForPopup> extends CommonAdapter<T>  {

    private OnDataChangeListener onDataChangeListener;
    private ChangeSelectStatus changeSelectStatus;
    private OnDataChangeListenerPosition onDataChangeListenerPosition;
    //指定点击的控件ID,不指定默认整个
    private int clickId = -1;

    public ListCheckBoxAdapter(int layoutResId) {
        super(layoutResId);
    }

    public void setChangeSelectStatus(ChangeSelectStatus changeSelectStatus) {
        this.changeSelectStatus = changeSelectStatus;
    }

    @Override
    protected void convert(BaseViewHolder helper, BaseItemForPopup item) {
        super.convert(helper, (T) item);

        View view;
        if (clickId != -1) {
            view = helper.itemView.findViewById(clickId);
        } else {
            view = helper.itemView;
        }
        view.setTag(item);
        view.setOnClickListener(v -> {
            BaseItemForPopup bean = (BaseItemForPopup) v.getTag();
            bean.setSelected(!bean.isSelected());

            notifyDataSetChanged();
            if (onDataChangeListener != null) {
                onDataChangeListener.dataChange(bean);
            }
            if (changeSelectStatus != null) {
                changeSelectStatus.onChangeSelectStatus();
            }
            if (onDataChangeListenerPosition != null) {
                onDataChangeListenerPosition.dataChange(bean, bean.isSelected());
            }
        });

    }

    public List<T> getReturnBeans() {
        List<T> list = new ArrayList<>();
        for (int i = 0; i < getData().size(); i++) {
            if (getData().get(i).isSelected()) {
                list.add(getData().get(i));
            }
        }
        return list;
    }

    public void allSelect(boolean isSelect) {
        List<T> data = getData();
        for (int i = 0; i < data.size(); i++) {
            data.get(i).setSelected(isSelect);
        }
        notifyDataSetChanged();
    }

    public void setClickId(int clickId) {
        this.clickId = clickId;
    }

    public <T> void setOnDataChangeListener(OnDataChangeListener<T> onDataChangeListener) {
        this.onDataChangeListener = onDataChangeListener;
    }

    public <T> void setOnDataChangeListenerPosition(OnDataChangeListenerPosition<T> onDataChangeListenerPosition) {
        this.onDataChangeListenerPosition = onDataChangeListenerPosition;
    }

    /**
     * description 数据改变后监听器
     * author: wangqin
     * date: 2020/6/29
     * param
     * return
     */
    public interface OnDataChangeListener<T> {
        void dataChange(T t);
    }

    public interface ChangeSelectStatus {
        void onChangeSelectStatus();
    }

    public interface OnDataChangeListenerPosition<T> {
        void dataChange(T t, boolean isSelected);
    }
}