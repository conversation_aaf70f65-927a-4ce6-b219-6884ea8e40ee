package com.midea.prestorage.function.inv

import CheckUtil
import android.app.Application
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.ReqGetMaterialList
import com.midea.prestorage.beans.net.RespAdjustDetailPage
import com.midea.prestorage.beans.net.RespAdjustList
import com.midea.prestorage.function.inv.response.RespMaterialList
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody

class InvReconciliationDetailListVM(application: Application) : BaseViewModel(application) {

    val title = MutableLiveData("")
    val locCode = ObservableField("")
    val serialNo = ObservableField("")
    val isRefreshing = ObservableBoolean(false)
    val isNoData = ObservableBoolean(false)
    var respAdjustList: RespAdjustList? = null
    var showGoods = MutableLiveData<MutableList<RespMaterialList>>()

    // 当前页码
    var pageNo = 1

    val loadMoreComplete = MutableLiveData(0)
    var showDatas = MutableLiveData<MutableList<RespAdjustDetailPage>>()
    var loadMoreDatas = MutableLiveData<MutableList<RespAdjustDetailPage>>()

    override fun init() {

    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        pageNo = 1
        isRefreshing.set(true)
        initOrderList()
    }

    fun onEnterLocCode() {
        if (CheckUtil.isFastDoubleClick()) {
            if (locCode.get().toString().trim().isEmpty()) {
                showNotification("库位不能为空", false)
                return
            }
            onRefreshCommand.onRefresh()
        }
    }

    fun serialKeyPress() {
        if (CheckUtil.isFastDoubleClick()) {
            if (serialNo.get().toString().trim().isEmpty()) {
                showNotification("货品条码不能为空", false)
                return
            }
            getMaterialList()
        }
    }

    fun clearLocCode() {
        if (CheckUtil.isFastDoubleClick()) {
            locCode.set("")
            onRefreshCommand.onRefresh()
        }
    }

    fun clearGoods() {
        if (CheckUtil.isFastDoubleClick()) {
            serialNo.set("")
            onRefreshCommand.onRefresh()
        }
    }

    /**
     * 货品条码查货品
     */
    private fun getMaterialList() {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getWareManageAPI()
                    .getMaterialList(
                        ReqGetMaterialList(
                        customerCode = respAdjustList?.customerCode ?: "",
                        keyWord = serialNo.get().toString().trim()
                    )
                    )
            }

            if (result.code == 0L) {
                result.data?.let {
                    when (it.size) {
                        0 -> {
                            serialNo.set("")
                            showNotification("暂无该货品", false)
                        }
                        1 -> {
                            serialNo.set(it.getOrNull(0)?.itemCode ?: "")
                            onRefreshCommand.onRefresh()
                        }
                        else -> {
                            showGoods.value = it
                        }
                    }
                }
            } else {
                serialNo.set("")
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    /**
     * 库位明细列表
     */
    fun initOrderList() {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
                isRefreshing.set(false)
            }) {

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getWareManageAPI()
                    .adDetailPage(
                        Constants.whInfo?.whCode.toString(),
                        pageNo,
                        10,
                        locCode.get().toString().trim(),
                        title.value,
                        serialNo.get().toString().trim()
                    )
            }

            if (result.code == 0L) {
                loadMoreComplete.value = 1

                result.data?.let { data ->

                    data.list?.forEach {
                        val arr = arrayListOf<String>()
                        with(it) {
                            if (!lotAtt01.isNullOrEmpty()) {
                                lotAtt01 = lotAtt01?.split(" ")?.getOrNull(0) ?: ""
                                arr.add("生产日期: $lotAtt01")
                            }
                            if (!lotAtt02.isNullOrEmpty()) {
                                lotAtt02 = lotAtt02?.split(" ")?.getOrNull(0) ?: ""
                                arr.add("失效日期: $lotAtt02")
                            }
                            if (!lotAtt03.isNullOrEmpty()) {
                                lotAtt03 = lotAtt03?.split(" ")?.getOrNull(0) ?: ""
                                arr.add("入库日期: $lotAtt03")
                            }
                            if (!lotAtt04Cn.isNullOrEmpty()) arr.add(lotAtt04Cn)
                            if (!lotAtt05.isNullOrEmpty()) arr.add("批次: $lotAtt05")
                            if (!lotAtt06.isNullOrEmpty()) arr.add("批次属性06: $lotAtt06")
                            if (!lotAtt07.isNullOrEmpty()) arr.add("批次属性07: $lotAtt07")
                            if (!lotAtt08.isNullOrEmpty()) arr.add("批次属性08: $lotAtt08")
                            if (!lotAtt09.isNullOrEmpty()) arr.add("批次属性09: $lotAtt09")
                            if (!lotAtt10.isNullOrEmpty()) arr.add("批次属性10: $lotAtt10")
                            if (!lotAtt11.isNullOrEmpty()) arr.add("批次属性11: $lotAtt11")
                            if (!lotAtt12.isNullOrEmpty()) arr.add("批次属性12: $lotAtt12")
                            lotAttStr = arr.joinToString(separator = " | ")
                        }
                    }

                    if (pageNo == 1) {
                        showDatas.value = data.list
                    } else {
                        loadMoreDatas.value = data.list
                    }
                    if (pageNo < data.totalPage) {
                        pageNo++
                    } else {
                        loadMoreComplete.value = 2
                    }
                }
            } else {
                result.msg?.let { showNotification(it, false) }
                if (pageNo == 1) {
                    val emptyList = mutableListOf<RespAdjustDetailPage>()
                    showDatas.value = emptyList
                }
            }
        }
    }

}