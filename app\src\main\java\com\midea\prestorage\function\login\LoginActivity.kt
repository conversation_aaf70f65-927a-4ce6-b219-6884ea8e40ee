package com.midea.prestorage.function.login

import android.Manifest
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.core.view.updateLayoutParams
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayoutMediator
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.App
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.net.TenantResp
import com.midea.prestorage.beans.setting.ImplWarehouse
import com.midea.prestorage.function.agreement.AgreementDialogFragment
import com.midea.prestorage.function.login.fragment.PasswordLoginFragment
import com.midea.prestorage.function.login.fragment.SmsLoginFragment
import com.midea.prestorage.function.main.ProfileUnionBinding
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.main.dialog.SaasWhChooseDialog
import com.midea.prestorage.function.main.dialog.TenantDialog
import com.midea.prestorage.function.main.dialog.WhDialog
import com.midea.prestorage.function.mainyg.MainYgActivity
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.http.constants.Servers
import com.midea.prestorage.utils.AgreementUtil.attachAgreement
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.ToastUtilsCare
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.BuildConfig
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityLoginBinding
import pub.devrel.easypermissions.AfterPermissionGranted
import pub.devrel.easypermissions.AppSettingsDialog
import pub.devrel.easypermissions.EasyPermissions


class LoginActivity : BaseActivity(), EasyPermissions.PermissionCallbacks,
    AgreementDialogFragment.AgreementListener {

    companion object {
        var HOST = Servers.LC.url
        var SERVER_NAME = Servers.LC.serverName
        const val RC_LOCATION = 100
    }

    lateinit var binding: ActivityLoginUnionBinding

    private lateinit var chooseDialog: SaasWhChooseDialog
    private lateinit var tenantDialog: TenantDialog
    private lateinit var whDialog: WhDialog

    val tenantCodeN2C = mutableMapOf<String, String>()

    private val titles = arrayOf("密码登录", "短信登录")
    private lateinit var adapter: MyAdapter


    fun getAdapter(): MyAdapter {
        return adapter
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityLoginUnionBinding.V2(DataBindingUtil.setContentView(this, R.layout.activity_login_care))
        } else {
            ActivityLoginUnionBinding.V1(DataBindingUtil.setContentView(this, R.layout.activity_login))
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.ivLogo)
            }
        }
        binding.vm = LoginVM(this)

        initServer()
        initTenantCode()
        requestPermissions()
        initDialog()
        initViewPage()
        binding.cbAgreement.attachAgreement()
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean && BuildConfig.DEBUG) {
            binding.viewPager.updateLayoutParams<ViewGroup.LayoutParams> {
                height = AppUtils.dp2px(applicationContext, 95)
            }
        }
    }

    // 租户编码列表   暂时没有接口  前端写死只有 安得  一个租户选项
    fun initTenantCode() {

        tenantCodeN2C.put("安得智联", "annto")

        val tenantCodeOptions = mutableListOf<String>()
        tenantCodeN2C.forEach {
            tenantCodeOptions.add(it.key)
        }

        //默认租户:annto
        App.tenantCode = "annto"
        binding.spinnerTenantCode.setItems(tenantCodeOptions)
        binding.spinnerTenantCode.setOnItemSelectedListener(MaterialSpinner.OnItemSelectedListener<String> { view, position, id, item ->
            val tenantName = tenantCodeOptions[position]
            App.tenantCode = tenantCodeN2C.get(tenantName)
        })

    }

    /**
     * 服务器初始化，生产没有服务器选择，直接隐藏
     */
    private fun initServer() {
        val values = Servers.values()
        val servers = mutableListOf<String>()
        values.forEach {
            servers.add(it.serverName)
        }

        when {
            BuildConfig.DEBUG -> {
                if(TextUtils.isEmpty((SPUtils["host", ""] as String))) {
                    HOST = Servers.SIT.url
                    SPUtils.put(application, "host", Servers.SIT.url)
                }else {
                    HOST = (SPUtils["host", ""] as String)
                }
            }
            else -> {
                HOST = Servers.LC.url
                SPUtils.put(application, "host", Servers.LC.url)

            }
        }


         when {
            BuildConfig.DEBUG -> {
                if(TextUtils.isEmpty((SPUtils["serverName", ""] as String))) {
                    SERVER_NAME = Servers.SIT.serverName
                    SPUtils.put(application, "serverName", Servers.SIT.serverName)
                }else {
                    SERVER_NAME = (SPUtils["serverName", ""] as String)
                }
            }
            else -> {
                SERVER_NAME = Servers.LC.serverName
                SPUtils.put(application, "serverName", Servers.LC.serverName)
            }
        }

        binding.spinnerServer.setItems(servers)

        binding.spinnerServer.setOnItemSelectedListener(MaterialSpinner.OnItemSelectedListener<String> { view, position, id, item ->
            HOST = values[position].url
            SERVER_NAME = servers[position]
        })
    }

    //带有这个注释的方法，会在某一次请求的所有权限都通过后，才回调
    @AfterPermissionGranted(RC_LOCATION)
    private fun requestPermissions() {
        val perms = arrayOf(
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA,
            Manifest.permission.BLUETOOTH,
            Manifest.permission.READ_PHONE_STATE,
            Manifest.permission.ACCESS_NETWORK_STATE,
            Manifest.permission.ACCESS_WIFI_STATE
        )
        if (EasyPermissions.hasPermissions(this, *perms)) {
        } else {
            EasyPermissions.requestPermissions(this, "请求权限", RC_LOCATION, *perms)
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        EasyPermissions.onRequestPermissionsResult(
            requestCode,
            permissions,
            grantResults, this
        )
    }

    override fun onPermissionsGranted(requestCode: Int, perms: MutableList<String>) {
    }

    override fun onPermissionsDenied(requestCode: Int, perms: MutableList<String>) {
        if (EasyPermissions.somePermissionPermanentlyDenied(this, perms)) {
            //但是这个api有个问题，他会显示一个对话框，但是这个对话框，点空白区域是可以取消的，如果用户点了空白区域，你就没办法进行后续操作了
            AppSettingsDialog.Builder(this).build().show()
        }
    }

    private fun initDialog() {

        //选择租户和仓库dialog
        chooseDialog = SaasWhChooseDialog(this)
        chooseDialog.setChooseBack(object : SaasWhChooseDialog.ChooseBack {
            override fun chooseBack(type : String) {
                if(type == "1") {
                    if (!tenantDialog.isShowing) {
                        tenantDialog.show()
                    }
                }else if(type == "2") {
                    if (!whDialog.isShowing) {
                        whDialog.show()
                    }
                }
            }

            override fun confirmBack(whCode: String, whName: String) {
                SPUtils.put(application, "tenantCode", Constants.tenantCode!!)
                SPUtils.put(application, "tenantName", Constants.tenantName!!)
                SPUtils.put(application, "host", HOST)
                SPUtils.put(application, "serverName", SERVER_NAME)
                SPUtils.put(application, "accessToken", Constants.accessToken!!)
                binding.vm?.saveUserInfo(binding.vm?.curUserCode, binding.vm?.curUserName, Constants.userInfo?.whName, Constants.userInfo?.whCode)
                binding.vm?.saveWhInfo(binding.vm?.whDatas)
                binding.vm?.saveWhInfo()
                chooseDialog.dismiss()
                startActivity(Intent(this@LoginActivity, MainYgActivity::class.java))
            }
        })

        //选择租户dialog
        tenantDialog = TenantDialog(this)
        tenantDialog.setTitle("请选择租户")
        tenantDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            chooseDialog.setTenant(it.tenantCode, it.tenantName)
            chooseDialog.setWarehouse("", "")
            binding.vm?.switchTenant(it.tenantCode)
            Constants.tenantCode = it.tenantCode
            Constants.tenantName = it.tenantName
            App.tenantCode = it.tenantCode
            tenantDialog.dismiss()
        })

        //选择仓库dialog
        whDialog = WhDialog(this)
        whDialog.setTitle("请选择仓库")
        whDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            chooseDialog.setWarehouse(it.whCode, it.cdwhName)
            Constants.whInfo = it
            Constants.userInfo?.whName = it.cdwhName
            Constants.userInfo?.whCode = it.whCode
            Constants.userInfo?.whType = 2
            whDialog.dismiss()
        })
    }

    fun resetWarehouse() {
        chooseDialog.setWarehouse("", "")
    }

    fun showWhDialog(tenantCode: String, tenantName: String) {
        if (!chooseDialog.isShowing) {
            chooseDialog.setTenant(tenantCode, tenantName)
            chooseDialog.setWarehouse("", "")
            chooseDialog.show()
        }
    }

    fun setTenant(tenantCode: String, tenantName: String) {
        chooseDialog.setTenant(tenantCode, tenantName)
    }

    fun setTenantInfo(list: MutableList<TenantResp.TenantsDTO>?) {
        tenantDialog.setNewData(list)
        tenantDialog.notifyDataChange(list)
    }

    fun setWareHouse(whCode: String, whName: String) {
        chooseDialog.setWarehouse(whCode, whName)
    }

    fun setWhInfo(list: MutableList<ImplWarehouse>?) {
        whDialog.setNewData(list)
        whDialog.notifyDataChange(list)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //EasyPermissions会有一个默认的请求码，根据这个请求码，就可以判断是不是从APP的设置界面过来的
        if (requestCode == AppSettingsDialog.DEFAULT_SETTINGS_REQ_CODE) {
            // Do something after user returned from app settings screen, like showing a Toast.
            //在这儿，你可以再对权限进行检查，从而给出提示，或进行下一步操作
            ToastUtilsCare.toastBig(this, "从设置中返回", Toast.LENGTH_SHORT)
        }
    }

    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }

    private fun initViewPage() {
        adapter = MyAdapter(this, 2)
        binding.viewPager.adapter = adapter
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
            }
        })

        var tabLayoutMediator =
            TabLayoutMediator(binding.tlLogin, binding.viewPager) { tab, position ->
                tab.text = titles[position]
            }
        tabLayoutMediator.attach()

    }

    class MyAdapter(activity: FragmentActivity, private val itemsCount: Int) :
        FragmentStateAdapter(activity) {

        val passwordLoginFragment = PasswordLoginFragment.newInstance(0)
        val smsLoginFragment = SmsLoginFragment.newInstance(1)

        override fun getItemCount(): Int {
            return itemsCount
        }

        override fun createFragment(position: Int): Fragment {
            return if (position == 0) {
                passwordLoginFragment
            } else {
                smsLoginFragment
            }
        }
    }

    override fun onAgree() {
        binding.vm?.checkAgreement?.set(true)
        binding.vm?.onLoginClick()
    }

    fun showAgreementDialog() {
        AgreementDialogFragment.newInstance().show(supportFragmentManager, "agreement")
    }

}