package com.midea.prestorage.dialog

import android.app.AlertDialog
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestorage.base.BaseActivity
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogInvReconciliationBinding

class InvReconciliationDialog(
    var mContext: BaseActivity
) : AlertDialog(mContext) {

    private var binding: DialogInvReconciliationBinding

    init {
        val contentView =
            LayoutInflater.from(mContext).inflate(R.layout.dialog_inv_reconciliation, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = InvReconciliationDialogVM(this, binding)

        setCanceledOnTouchOutside(true)
    }

    override fun show() {
        super.show()

        binding.vm!!.init()
    }

    fun setOnSettingClick(listener: SettingBack) {
        binding.vm!!.listener = listener
    }

    interface SettingBack {
        fun onConfirmClick(scan: Int, lot: Int, input: Int)
    }
}