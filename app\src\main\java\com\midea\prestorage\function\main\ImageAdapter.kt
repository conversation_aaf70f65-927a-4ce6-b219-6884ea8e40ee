package com.midea.prestorage.function.main

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.midea.prestoragesaas.R
import com.midea.prestorage.beans.help.HeaderInfo
import com.midea.prestorage.utils.SPUtils

class ImageAdapter(private val mContext: Context) : BaseAdapter() {
    private val beans: MutableList<HeaderInfo> = mutableListOf()

    fun addData(data: MutableList<HeaderInfo>?) {
        if (!data.isNullOrEmpty()) {
            beans.addAll(data)
        }
    }

    fun addData(data: HeaderInfo?) {
        if (data != null) {
            beans.add(data)
        }
    }

    fun cleanData() {
        beans.clear()
    }

    override fun getCount(): Int {
        return beans.size
    }

    override fun getItem(position: Int): HeaderInfo {
        return beans[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(
        position: Int,
        convertView: View?,
        parent: ViewGroup
    ): View {
        val viewHolder: ViewHolder?

        var convertView = convertView
        if (convertView == null) {
            convertView = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                LayoutInflater.from(mContext).inflate(R.layout.item_grid_main_func_care, parent, false)
            } else {
                LayoutInflater.from(mContext).inflate(R.layout.item_grid_main_func, parent, false)
            }
            viewHolder = ViewHolder()
            viewHolder.itemImg = convertView.findViewById(R.id.img_icon)
            viewHolder.txtName = convertView.findViewById(R.id.tv_name)
            convertView!!.tag = viewHolder
        } else {
            viewHolder = convertView.tag as ViewHolder
        }

        val bean = beans[position]
        viewHolder.itemImg?.setImageDrawable(ContextCompat.getDrawable(mContext, bean.iconResource))
        viewHolder.txtName?.text = bean.funcName
        return convertView
    }

    internal inner class ViewHolder {
        var itemImg: ImageView? = null
        var txtName: TextView? = null
    }
}