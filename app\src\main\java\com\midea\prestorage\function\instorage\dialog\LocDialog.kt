package com.midea.prestorage.function.instorage.dialog

import android.app.AlertDialog
import android.text.TextUtils
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestoragesaas.databinding.DialogLocBinding
import com.midea.prestorage.function.receivecpkx.dialog.SimpleLoadMoreView
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

class LocDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {

    private var binding: DialogLocBinding
    val adapter = LocAdapter()

    init {
        val contentView = LayoutInflater.from(mContext).inflate(R.layout.dialog_loc, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = LocDialogVM(this)

        setCanceledOnTouchOutside(true)
        // 加上这个 确保按回退键也不关闭选择框
        setCancelable(true)

        initRecycleView()
        initLoadMore()
    }

    private fun initRecycleView() {
        binding.recycle.layoutManager = LinearLayoutManager(mContext)
        binding.recycle.adapter = adapter
    }

    fun addData(info: MutableList<BaseItemShowInfo>) {
        adapter.addData(info)
        adapter.notifyDataSetChanged()
    }

    fun setOnItemClick(palletBack: LocBack) {
        adapter.setOnItemClickListener { adapter, _, position ->
            val bean = adapter.data[position] as BaseItemShowInfo
            if (bean.showInfo.isNullOrEmpty()) {
                return@setOnItemClickListener
            }
            palletBack.locBack(bean.showInfo)
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.initPalletInfo(false)
        }
        adapter.loadMoreModule.loadMoreView = SimpleLoadMoreView()
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    fun stopLoad() {
        adapter.loadMoreModule.loadMoreComplete()
    }

    fun endLoad() {
        adapter.loadMoreModule.loadMoreEnd()
    }

    fun setTitle(title: String) {
        if (!TextUtils.isEmpty(title)) {
            binding.vm!!.title.set(title)
        }
    }

    override fun show() {
        super.show()

        binding.vm?.pageNo = 1
        binding.vm?.totalPage = 10
        adapter.setNewInstance(mutableListOf())
        adapter.notifyDataSetChanged()

        binding.vm?.filterInfo?.set("")
        binding.vm?.initPalletInfo(false)
    }

    interface LocBack {
        fun locBack(locCode: String)
    }
}