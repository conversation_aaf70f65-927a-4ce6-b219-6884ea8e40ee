package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;
import java.util.List;

public class PutContainerBean implements Serializable {
    private PageResult pageResult;

    public PageResult getPageResult() {
        return pageResult;
    }

    public void setPageResult(PageResult pageResult) {
        this.pageResult = pageResult;
    }

    public class PageResult implements Serializable {
        private int totalCount;
        private int totalPage;
        private int pageNo;
        private int pageSize;
        private int offset;
        private List<PageResults> list;

        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }

        public int getTotalPage() {
            return totalPage;
        }

        public void setTotalPage(int totalPage) {
            this.totalPage = totalPage;
        }

        public int getPageNo() {
            return pageNo;
        }

        public void setPageNo(int pageNo) {
            this.pageNo = pageNo;
        }

        public int getPageSize() {
            return pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }

        public int getOffset() {
            return offset;
        }

        public void setOffset(int offset) {
            this.offset = offset;
        }

        public List<PageResults> getList() {
            return list;
        }

        public void setList(List<PageResults> list) {
            this.list = list;
        }

        public class PageResults implements Serializable {
            @ShowAnnotation
            private String containerCode;
            @ShowAnnotation
            private String skuCount;
            @ShowAnnotation
            private String checkPerson;
            @ShowAnnotation
            private String startTaskTime;
            private String id;
            private String status;
            private String sumQty;
            private String scanQty;

            public String getStatus() {
                return status;
            }

            public void setStatus(String status) {
                this.status = status;
            }

            public String getContainerCode() {
                return containerCode;
            }

            public void setContainerCode(String containerCode) {
                this.containerCode = containerCode;
            }

            public String getSkuCount() {
                return skuCount;
            }

            public void setSkuCount(String skuCount) {
                this.skuCount = skuCount;
            }

            public String getSumQty() {
                return sumQty;
            }

            public void setSumQty(String sumQty) {
                this.sumQty = sumQty;
            }

            public String getScanQty() {
                return scanQty;
            }

            public void setScanQty(String scanQty) {
                this.scanQty = scanQty;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getCheckPerson() {
                return checkPerson;
            }

            public void setCheckPerson(String checkPerson) {
                this.checkPerson = checkPerson;
            }

            public String getStartTaskTime() {
                return startTaskTime;
            }

            public void setStartTaskTime(String startTaskTime) {
                this.startTaskTime = startTaskTime;
            }
        }
    }
}
