package com.midea.prestorage.function.inv

import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData

class GoodsInfoVM(val name: String, val showArrow: Boolean = true, val hintStr: String, val digit: Int) {
    var content: ObservableField<String> = ObservableField("")
    var editable = ObservableField<Boolean>(true)
    var mEnter = MutableLiveData(false)

    fun onEnterCode() {
        if (CheckUtil.isFastDoubleClick()) {
            mEnter.value = true
        }
    }
}