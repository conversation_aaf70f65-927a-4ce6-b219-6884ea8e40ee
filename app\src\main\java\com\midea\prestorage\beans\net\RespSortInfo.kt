package com.midea.prestorage.beans.net

import com.midea.prestorage.base.annotation.ShowAnnotation
import com.midea.prestorage.beans.base.BaseItemForPopup
import java.math.BigDecimal

data class RespSortInfo(
    val whCode: String?,
    val shipToAttentionTo: String?,
    val waveNo: String?,
    val containerCode: String?,
    val pickContainerCode: String?,
    val shippingLocList: List<String>?,
    val shippingLoc: String?,
    val gridNumber: String?,
    val itemQty: BigDecimal?,
    val totalQty: BigDecimal?,
    val totalQtyConvert: String?,
    val itemList: List<SortInfoItem>?
)

data class SortInfoItem(
    @ShowAnnotation
    var index: Int,
    val custItemCode: String?,
    @ShowAnnotation
    val itemName: String?,
    @ShowAnnotation
    val sortQty: BigDecimal?,
    @ShowAnnotation
    val convert: String?,
    @ShowAnnotation
    val whBarcode69: String?,
    @ShowAnnotation
    val format: String?,
    @ShowAnnotation
    val itemCode: String?,
    @ShowAnnotation
    var waitSortQty: BigDecimal?,
    @ShowAnnotation
    val sortQtyStr: String?,
    val packagePara: BigDecimal?,
    @ShowAnnotation
    val csUnit: String?,
    @ShowAnnotation
    val eaUnit: String?,
    @ShowAnnotation
    var totalNum: BigDecimal?,
    @ShowAnnotation
    var csNum: BigDecimal?,
    @ShowAnnotation
    var eaNum: BigDecimal?,
    var isShowCs: Boolean?
): BaseItemForPopup()
