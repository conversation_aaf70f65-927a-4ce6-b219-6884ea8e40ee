package com.midea.prestorage.beans.net;

import android.text.TextUtils;

import java.math.BigDecimal;
import java.util.List;


/**
 * Created by LUCY6 on 2017-5-23.
 */
public class OutPoolStorageDetail {
    private String waveNo;
    private BigDecimal totalQty;
    private BigDecimal totalVolume;
    private BigDecimal totalWeight;
    private List<OutShipmentDetailResponses> outShipmentDetailResponses;

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }


    public List<OutShipmentDetailResponses> getOutShipmentDetailResponses() {
        return outShipmentDetailResponses;
    }

    public void setOutShipmentDetailResponses(List<OutShipmentDetailResponses> outShipmentDetailResponses) {
        this.outShipmentDetailResponses = outShipmentDetailResponses;
    }

    public int getTotalQty() {
        if (totalQty == null) {
            return 0;
        }
        return totalQty.intValue();
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public BigDecimal getTotalVolume() {
        if (totalVolume == null) {
            return new BigDecimal(0).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return totalVolume.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public void setTotalVolume(BigDecimal totalVolume) {
        this.totalVolume = totalVolume;
    }

    public BigDecimal getTotalWeight() {
        if (totalWeight == null) {
            return new BigDecimal(0).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return totalWeight.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public void setTotalWeight(BigDecimal totalWeight) {
        this.totalWeight = totalWeight;
    }

    public class OutShipmentDetailResponses {
        private OutShipmentHeader outShipmentHeader;
        private OutShipmentHeaderExt outShipmentHeaderExt;
        private List<OutShipmentDetail> outShipmentDetails;

        public OutShipmentHeader getOutShipmentHeader() {
            return outShipmentHeader;
        }

        public void setOutShipmentHeader(OutShipmentHeader outShipmentHeader) {
            this.outShipmentHeader = outShipmentHeader;
        }

        public OutShipmentHeaderExt getOutShipmentHeaderExt() {
            return outShipmentHeaderExt;
        }

        public void setOutShipmentHeaderExt(OutShipmentHeaderExt outShipmentHeaderExt) {
            this.outShipmentHeaderExt = outShipmentHeaderExt;
        }

        public List<OutShipmentDetail> getOutShipmentDetails() {
            return outShipmentDetails;
        }

        public void setOutShipmentDetails(List<OutShipmentDetail> outShipmentDetails) {
            this.outShipmentDetails = outShipmentDetails;
        }
    }

    public class OutShipmentHeader {
        private String shipmentCode;
        private BigDecimal totalQty;
        private String shipmentType;
        private String customerName;
        private String custOrderCode;
        private String shipToAddress;
        private String status;
        private String netEngineerName;
        private String netEngineerMobile;
        private String statusStr;
        private String unscanMark;



        public String getShipmentCode() {
            return shipmentCode;
        }

        public void setShipmentCode(String shipmentCode) {
            this.shipmentCode = shipmentCode;
        }


        public String getUnscanMark() {
            return unscanMark;
        }

        public void setUnscanMark(String unscanMark) {
            this.unscanMark = unscanMark;
        }

        public int getTotalQty() {
            if (totalQty == null) {
                return 0;
            }
            return totalQty.intValue();
        }

        public void setTotalQty(BigDecimal totalQty) {
            this.totalQty = totalQty;
        }

        public String getShipmentType() {
            return shipmentType;
        }

        public void setShipmentType(String shipmentType) {
            this.shipmentType = shipmentType;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public String getShipToAddress() {
            return shipToAddress;
        }

        public void setShipToAddress(String shipToAddress) {
            this.shipToAddress = shipToAddress;
        }

        public String getCustOrderCode() {
            return custOrderCode;
        }

        public void setCustOrderCode(String custOrderCode) {
            this.custOrderCode = custOrderCode;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getStatusStr() {
            return statusStr;
        }

        public void setStatusStr(String statusStr) {
            this.statusStr = statusStr;
        }

        public String getNetEngineerName() {
            return netEngineerName;
        }

        public void setNetEngineerName(String netEngineerName) {
            this.netEngineerName = netEngineerName;
        }

        public String getNetEngineerMobile() {
            return netEngineerMobile;
        }

        public void setNetEngineerMobile(String netEngineerMobile) {
            this.netEngineerMobile = netEngineerMobile;
        }
    }

    public class OutShipmentHeaderExt {
        private String shipToAddress;
        private String customerName;
        private String unscanMark;
        private String netEngineerName;
        private String netEngineerMobile;

        public String getUnscanMark() {
            return unscanMark;
        }

        public void setUnscanMark(String unscanMark) {
            this.unscanMark = unscanMark;
        }

        public String getShipToAddress() {
            return shipToAddress;
        }

        public void setShipToAddress(String shipToAddress) {
            this.shipToAddress = shipToAddress;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public String getNetEngineerName() {
            return netEngineerName;
        }

        public void setNetEngineerName(String netEngineerName) {
            this.netEngineerName = netEngineerName;
        }

        public String getNetEngineerMobile() {
            return netEngineerMobile;
        }

        public void setNetEngineerMobile(String netEngineerMobile) {
            this.netEngineerMobile = netEngineerMobile;
        }
    }

    public class OutShipmentDetail {
        private BigDecimal shipQty;
        private BigDecimal shipQtyOrigin;
        private BigDecimal planQty;
        private BigDecimal planQtyOrigin;
        private String unit;
        private String custItemCode;
        private String itemCode;
        private String itemName;
        private String lotAtt04;
        private String statusStr;

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public String getCustItemCode() {
            if (TextUtils.isEmpty(custItemCode)) {
                return "";
            }
            return custItemCode;
        }

        public void setCustItemCode(String custItemCode) {
            this.custItemCode = custItemCode;
        }

        public String getItemCode() {
            return itemCode;
        }

        public void setItemCode(String itemCode) {
            this.itemCode = itemCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getLotAtt04() {
            return lotAtt04;
        }

        public void setLotAtt04(String lotAtt04) {
            this.lotAtt04 = lotAtt04;
        }

        public BigDecimal getShipQty() {
            if (shipQty == null) {
                return new BigDecimal(0);
            }
            return shipQty;
        }

        public int getShipQtyInt() {
            if (shipQty == null) {
                return 0;
            }
            return shipQty.intValue();
        }

        public void setShipQty(BigDecimal shipQty) {
            this.shipQty = shipQty;
        }

        public BigDecimal getShipQtyOrigin() {
            return shipQtyOrigin;
        }

        public void setShipQtyOrigin(BigDecimal shipQtyOrigin) {
            this.shipQtyOrigin = shipQtyOrigin;
        }

        public String getStatusStr() {
            return statusStr;
        }

        public void setStatusStr(String statusStr) {
            this.statusStr = statusStr;
        }

        public BigDecimal getPlanQty() {
            if (planQty == null) {
                return new BigDecimal(0);
            }
            return planQty;
        }

        public int getPlanQtyInt() {
            if (planQty == null) {
                return 0;
            }
            return planQty.intValue();
        }


        public void setPlanQty(BigDecimal planQty) {
            this.planQty = planQty;
        }

        public BigDecimal getPlanQtyOrigin() {
            return planQtyOrigin;
        }

        public void setPlanQtyOrigin(BigDecimal planQtyOrigin) {
            this.planQtyOrigin = planQtyOrigin;
        }
    }
}
