package com.midea.prestorage.dialog

import com.midea.prestoragesaas.databinding.DialogContainerCodeBinding
import com.midea.prestoragesaas.databinding.DialogContainerCodeCareBinding

sealed class DialogContainerCodeUnionBinding{
    abstract var vm: ContainerCodeDialogVM?

    class V2(val binding: DialogContainerCodeCareBinding) : DialogContainerCodeUnionBinding() {
        override var vm: ContainerCodeDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
    }

    class V1(val binding: DialogContainerCodeBinding) : DialogContainerCodeUnionBinding() {
        override var vm: ContainerCodeDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
    }
}
