package com.midea.prestorage.beans.net;

import java.io.Serializable;
import java.util.List;

public class ShippingListInfo implements Serializable {

    private List<ShippingHeadList> headerShippingList;
    private List<ShippingActualList> actureShippingList;

    public List<ShippingHeadList> getHeaderShippingList() {
        return headerShippingList;
    }

    public void setHeaderShippingList(List<ShippingHeadList> headerShippingList) {
        this.headerShippingList = headerShippingList;
    }

    public List<ShippingActualList> getActureShippingList() {
        return actureShippingList;
    }

    public void setActureShippingList(List<ShippingActualList> actureShippingList) {
        this.actureShippingList = actureShippingList;
    }
}