package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.beans.base.BaseItemForPopup;

import java.math.BigDecimal;


/**
 * Created by LUCY6 on 2017-5-23.
 */

public class PointJoinList extends BaseItemForPopup {

    @ShowAnnotation
    private String custOrderCode;
    @ShowAnnotation
    private String orderStatueStr;
    @ShowAnnotation
    private String waveNo;
    @ShowAnnotation
    private String shipmentCode;
    @ShowAnnotation
    private BigDecimal requestQty;
    @ShowAnnotation
    private String requestedBy;
    @ShowAnnotation
    private String requestedTime;
    @ShowAnnotation
    private String statusStr;
    @ShowAnnotation
    private String shippingWayStr;
    @ShowAnnotation
    private String waybillNo;
    @ShowAnnotation
    private String shipBy;
    @ShowAnnotation
    private String engineerStatusStr;

    private String enginnerId;
    private String status;
    private String requestedTel;
    private String shippingWay;
    private String whCode;
    private String outShipmentId;
    private String engineerStatus;

    public String getEnginnerId() {
        return enginnerId;
    }

    public void setEnginnerId(String enginnerId) {
        this.enginnerId = enginnerId;
    }

    public String getOutShipmentId() {
        return outShipmentId;
    }

    public void setOutShipmentId(String outShipmentId) {
        this.outShipmentId = outShipmentId;
    }

    public String getCustOrderCode() {
        return custOrderCode;
    }

    public void setCustOrderCode(String custOrderCode) {
        this.custOrderCode = custOrderCode;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getShipmentCode() {
        return shipmentCode;
    }

    public void setShipmentCode(String shipmentCode) {
        this.shipmentCode = shipmentCode;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getRequestedTel() {
        return requestedTel;
    }

    public void setRequestedTel(String requestedTel) {
        this.requestedTel = requestedTel;
    }

    public String getShippingWay() {
        return shippingWay;
    }

    public void setShippingWay(String shippingWay) {
        this.shippingWay = shippingWay;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setRequestQty(BigDecimal requestQty) {
        this.requestQty = requestQty;
    }

    public int getRequestQty() {
        if (requestQty == null)
            return 0;
        return requestQty.intValue();
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getShippingWayStr() {
        return shippingWayStr;
    }

    public void setShippingWayStr(String shippingWayStr) {
        this.shippingWayStr = shippingWayStr;
    }

    public String getRequestedBy() {
        return requestedBy;
    }

    public void setRequestedBy(String requestedBy) {
        this.requestedBy = requestedBy;
    }

    public String getWaybillNo() {
        return waybillNo;
    }

    public void setWaybillNo(String waybillNo) {
        this.waybillNo = waybillNo;
    }

    public String getShipBy() {
        return shipBy;
    }

    public void setShipBy(String shipBy) {
        this.shipBy = shipBy;
    }

    public void setRequestedTime(String requestedTime) {
        this.requestedTime = requestedTime;
    }

    public String getRequestedTime() {
        return requestedTime;
    }

    public void setOrderStatueStr(String orderStatueStr) {
        this.orderStatueStr = orderStatueStr;
    }

    public String getOrderStatueStr() {
        return orderStatueStr;
    }

    public void setEngineerStatusStr(String engineerStatusStr) {
        this.engineerStatusStr = engineerStatusStr;
    }

    public String getEngineerStatusStr() {
        return engineerStatusStr;
    }

    public String getEngineerStatus() {
        return engineerStatus;
    }

    public void setEngineerStatus(String engineerStatus) {
        this.engineerStatus = engineerStatus;
    }
}
