package com.midea.prestorage.beans.net;

import androidx.annotation.Nullable;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class ContainerReceiveDetailResp implements Serializable {

    private String whCode;
    private String receiptCode;
    private BigDecimal totalQty;
    private BigDecimal totalVolume;
    private BigDecimal totalWeight;
    private String custOrderNo;
    private String dispatchNo;
    private String status;
    private String carNo;
    private String receiptType;
    private String driver;
    private String supplierName;
    private BigDecimal skuNum;
    @Nullable
    private String shipFromName;
    @Nullable
    private String relationOrderNo;
    @Nullable
    private String waveNo;
    @Nullable
    private String ownerName;
    @Nullable
    private String orderNum;

    private List<ContainerReceiveDetailListResp> itemList;
    private List<ContainerReceiveDetailListResp> respItems;

    @Nullable
    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(@Nullable String orderNum) {
        this.orderNum = orderNum;
    }

    @Nullable
    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(@Nullable String ownerName) {
        this.ownerName = ownerName;
    }

    public List<ContainerReceiveDetailListResp> getRespItems() {
        return respItems;
    }

    public void setRespItems(List<ContainerReceiveDetailListResp> respItems) {
        this.respItems = respItems;
    }

    @Nullable
    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(@Nullable String waveNo) {
        this.waveNo = waveNo;
    }

    @Nullable
    public String getRelationOrderNo() {
        return relationOrderNo;
    }

    public void setRelationOrderNo(@Nullable String relationOrderNo) {
        this.relationOrderNo = relationOrderNo;
    }

    @Nullable
    public String getShipFromName() {
        return shipFromName;
    }

    public void setShipFromName(@Nullable String shipFromName) {
        this.shipFromName = shipFromName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public BigDecimal getSkuNum() {
        return skuNum;
    }

    public void setSkuNum(BigDecimal skuNum) {
        this.skuNum = skuNum;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getReceiptCode() {
        return receiptCode;
    }

    public void setReceiptCode(String receiptCode) {
        this.receiptCode = receiptCode;
    }

    public String getCustOrderNo() {
        return custOrderNo;
    }

    public void setCustOrderNo(String custOrderNo) {
        this.custOrderNo = custOrderNo;
    }

    public String getDispatchNo() {
        return dispatchNo;
    }

    public void setDispatchNo(String dispatchNo) {
        this.dispatchNo = dispatchNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }

    public String getReceiptType() {
        return receiptType;
    }

    public void setReceiptType(String receiptType) {
        this.receiptType = receiptType;
    }

    public String getDriver() {
        return driver;
    }

    public void setDriver(String driver) {
        this.driver = driver;
    }

    public List<ContainerReceiveDetailListResp> getItemList() {
        return itemList;
    }

    public void setItemList(List<ContainerReceiveDetailListResp> itemList) {
        this.itemList = itemList;
    }

    public BigDecimal getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public BigDecimal getTotalVolume() {
        return totalVolume;
    }

    public void setTotalVolume(BigDecimal totalVolume) {
        this.totalVolume = totalVolume;
    }

    public BigDecimal getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(BigDecimal totalWeight) {
        this.totalWeight = totalWeight;
    }
}
