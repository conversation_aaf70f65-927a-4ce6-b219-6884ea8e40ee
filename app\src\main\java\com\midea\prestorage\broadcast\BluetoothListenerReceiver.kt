package com.midea.prestorage.broadcast

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.midea.prestorage.printer.SNBCConnectUtils
import com.midea.prestorage.printer.HPRTConnectUtils
import com.midea.prestorage.printer.Printer


class BluetoothListenerReceiver : BroadcastReceiver() {
    override fun onReceive(
        context: Context,
        intent: Intent
    ) {
        val action = intent.action
        if (action != null) {
            when (action) {
                BluetoothAdapter.ACTION_STATE_CHANGED -> {
                    when (intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, 0)) {
                        //蓝牙正在打开
                        BluetoothAdapter.STATE_TURNING_ON -> ""
                        //蓝牙已经接通
                        BluetoothAdapter.STATE_ON -> {
                            Printer.bluetoothConnect()
                        }
                        //蓝牙正在关闭
                        BluetoothAdapter.STATE_TURNING_OFF -> ""
                        //蓝牙已经关闭
                        BluetoothAdapter.STATE_OFF -> {
                            Printer.bluetoothClose()
                        }
                    }
                }
                //蓝牙设备已连接
                BluetoothDevice.ACTION_ACL_CONNECTED -> {
                    Printer.bluetoothOpen()
                }
                //蓝牙设备已断开
                BluetoothDevice.ACTION_ACL_DISCONNECTED -> {
                    Printer.bluetoothDivide()
                }
            }
        }
    }
}