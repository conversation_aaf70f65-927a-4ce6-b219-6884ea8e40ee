package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.utils.AppUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * Created by LUCY6 on 2017-5-23.
 */

public class PointDivideDetailBean implements Serializable {
    private EngineerSignResponse engineerSignResponse;
    private List<EngineerSignDetailResponse> engineerSignDetailResponses;

    public EngineerSignResponse getEngineerSignResponse() {
        return engineerSignResponse;
    }

    public void setEngineerSignResponse(EngineerSignResponse engineerSignResponse) {
        this.engineerSignResponse = engineerSignResponse;
    }

    public List<EngineerSignDetailResponse> getEngineerSignDetailResponses() {
        return engineerSignDetailResponses;
    }

    public void setEngineerSignDetailResponses(List<EngineerSignDetailResponse> engineerSignDetailResponses) {
        this.engineerSignDetailResponses = engineerSignDetailResponses;
    }

    public class EngineerSignResponse implements Serializable {
        private String enginnerId;
        private String outShipmentId;
        private String custOrderCode;
        private String confirmBy;
        private String waveNo;
        private String shipmentCode;
        private String whCode;
        private String requestedBy;
        private String requestedTel;
        private String requestedTime;
        private BigDecimal requestQty;
        private String waybillNo;
        private String engineerStatus;
        private String shippingWay;//ZT自提，其他送货
        private String status;
        private String address;
        private int isDisable;

        public String getEnginnerId() {
            return enginnerId;
        }

        public void setEnginnerId(String enginnerId) {
            this.enginnerId = enginnerId;
        }

        public String getOutShipmentId() {
            return outShipmentId;
        }

        public void setOutShipmentId(String outShipmentId) {
            this.outShipmentId = outShipmentId;
        }

        public String getCustOrderCode() {
            return custOrderCode;
        }

        public void setCustOrderCode(String custOrderCode) {
            this.custOrderCode = custOrderCode;
        }

        public String getWaveNo() {
            return waveNo;
        }

        public void setWaveNo(String waveNo) {
            this.waveNo = waveNo;
        }

        public String getShipmentCode() {
            return shipmentCode;
        }

        public void setShipmentCode(String shipmentCode) {
            this.shipmentCode = shipmentCode;
        }

        public String getWhCode() {
            return whCode;
        }

        public void setWhCode(String whCode) {
            this.whCode = whCode;
        }

        public String getRequestedBy() {
            return requestedBy;
        }

        public void setRequestedBy(String requestedBy) {
            this.requestedBy = requestedBy;
        }

        public String getRequestedTel() {
            return requestedTel;
        }

        public void setRequestedTel(String requestedTel) {
            this.requestedTel = requestedTel;
        }

        public String getRequestedTime() {
            return requestedTime;
        }

        public void setRequestedTime(String requestedTime) {
            this.requestedTime = requestedTime;
        }

        public String getWaybillNo() {
            return waybillNo;
        }

        public void setWaybillNo(String waybillNo) {
            this.waybillNo = waybillNo;
        }

        public String getShippingWay() {
            return shippingWay;
        }

        public void setShippingWay(String shippingWay) {
            this.shippingWay = shippingWay;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getConfirmBy() {
            return confirmBy;
        }

        public void setConfirmBy(String confirmBy) {
            this.confirmBy = confirmBy;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public int getRequestQty() {
            if (requestQty == null) {
                return 0;
            }
            return requestQty.intValue();
        }

        public void setRequestQty(BigDecimal requestQty) {
            this.requestQty = requestQty;
        }

        public String getEngineerStatus() {
            return engineerStatus;
        }

        public void setEngineerStatus(String engineerStatus) {
            this.engineerStatus = engineerStatus;
        }

        public int getIsDisable() {
            return isDisable;
        }

        public void setIsDisable(int isDisable) {
            this.isDisable = isDisable;
        }
    }

    public class EngineerSignDetailResponse implements Serializable {
        @ShowAnnotation
        private String custItemCode;
        @ShowAnnotation
        private String itemName;
        @ShowAnnotation
        private String qtyInfo;
        private BigDecimal requestQty;
        private BigDecimal shipQty;
        private String itemCode;

        public String getCustItemCode() {
            return custItemCode;
        }

        public void setCustItemCode(String custItemCode) {
            this.custItemCode = custItemCode;
        }

        public String getItemCode() {
            return itemCode;
        }

        public void setItemCode(String itemCode) {
            this.itemCode = itemCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public int getRequestQty() {
            if (requestQty == null) {
                return 0;
            }
            return requestQty.intValue();
        }

        public void setRequestQty(BigDecimal requestQty) {
            this.requestQty = requestQty;
        }

        public BigDecimal getShipQty() {
            return shipQty;
        }

        public void setShipQty(BigDecimal shipQty) {
            this.shipQty = shipQty;
        }

        public String getQtyInfo() {
            return AppUtils.getBigDecimalValue(shipQty).toPlainString() + "/"
                    + AppUtils.getBigDecimalValue(requestQty).toPlainString();
        }

        public void setQtyInfo(String qtyInfo) {
            this.qtyInfo = qtyInfo;
        }
    }
}
