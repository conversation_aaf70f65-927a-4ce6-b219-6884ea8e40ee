package com.midea.prestorage.function.containerpick.dialog

import android.widget.RadioButton
import com.midea.prestoragesaas.databinding.DialogErrorTipBinding
import com.midea.prestoragesaas.databinding.DialogErrorTipCareBinding

sealed class DialogErrorTipUnionBinding{
    abstract var vm: ErrorTipDialogVM?
    abstract val rbA: RadioButton
    abstract val rbB: RadioButton
    abstract val rbC: RadioButton

    class V2(val binding: DialogErrorTipCareBinding) : DialogErrorTipUnionBinding() {
        override var vm: ErrorTipDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val rbA = binding.rbA
        override val rbB = binding.rbB
        override val rbC = binding.rbC
    }

    class V1(val binding: DialogErrorTipBinding) : DialogErrorTipUnionBinding() {
        override var vm: ErrorTipDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val rbA = binding.rbA
        override val rbB = binding.rbB
        override val rbC = binding.rbC
    }
}
