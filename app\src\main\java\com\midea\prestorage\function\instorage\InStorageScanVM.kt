package com.midea.prestorage.function.instorage

import android.content.Intent
import android.text.TextUtils
import android.util.Log
import android.widget.Toast
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.*
import com.midea.prestorage.beans.setting.HandingInfoDb
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.instorage.InStorageScanActivity.Companion.CODE_LOAD_CONTAINER
import com.midea.prestorage.function.instorage.response.*
import com.midea.prestorage.function.inv.response.BarcodeLotDto
import com.midea.prestorage.function.inv.response.BsLocation
import com.midea.prestorage.function.inv.response.FuInvLot
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.function.unscan.UnScanActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.*
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import okhttp3.MediaType
import okhttp3.RequestBody
import org.xutils.db.sqlite.WhereBuilder
import java.text.SimpleDateFormat
import java.util.*


class InStorageScanVM(val activity: InStorageScanActivity) {
    //单号类型
    var curOrderReceiveType = ObservableField<String>("receipt")   // receipt 入库单  wave波次单

    // 单号 (入库单号或波次单号)
    var curOrderNo = ObservableField<String>("")

    // 装卸组名称
    var handlingGroupName = ObservableField<String>("")

    // 装卸组编码
    var handlingGroupCode = ObservableField<String>("")

    // 顶部 输入或扫描的 商品编码 或者其他什么xx码 反正要先做本地和后端的解析校验
    var anyCode = ObservableField<String>("")

    // 条码规则
    var ruleList: MutableList<RuleDataList>? = null

    // 批次属性规则  根据custItemCode 获取其对应的批次属性列表信息
    var mapCustItemCodeToLotList = mutableMapOf<String, MutableList<CdWhLotDetailDto>>()

    // 实收 (列表 sum (已扫))
    var realReceiveNum = ObservableField<String>("")

    // 应收 (列表 sum (数量))
    var shouldReceiveNum = ObservableField<String>("")

    // 缓存 入库单号对应的 不扫码妙计
    val mapCode2Unscank = hashSetOf<String>()

    // 当前界面右上角 用户选择的库存状态
    var curSelectLot4Name = ""

    var curListOrderDetail = mutableListOf<InOrderDetail>()

    //  商品状态
    var statueStr = ObservableField<String>("")
    private var daysDialog: FilterDialog
    val db = DbUtils.db

    var containerCode: String? = null

    init {

        //这个界面是 加载 收货容器号 对应的扫码记录
        // 所以肯定是有其他activity传了个containerCode 过来才能进到这里
        val orderNo = activity.intent.getStringExtra("orderNo")
        val orderReceiveType = activity.intent.getStringExtra("orderReceiveType")
        containerCode = activity.intent.getStringExtra("containerCode")

        if (containerCode.isNullOrEmpty()) {
            AlertDialogUtil.showOnlyOkDialog(activity, "容器号为空", AlertDialogUtil.OnOkCallback { })
        }

        if (orderNo.isNullOrEmpty()) {
            AlertDialogUtil.showOnlyOkDialog(activity, "单号为空", AlertDialogUtil.OnOkCallback { })
        } else {
            curOrderNo.set(orderNo)
        }
        /*
        单号类型可以为空 本地通过 入库单号是否相同 判断是入库单还是波次单
        else if (orderReceiveType.isNullOrEmpty()) {
             AlertDialogUtil.showOnlyOkDialog(activity, "单号类型为空", AlertDialogUtil.OnOkCallback {  })

         } */

        if (!orderReceiveType.isNullOrEmpty()) {
            curOrderReceiveType.set(orderReceiveType)  //单号类型
        }

        //加载单号 对应的商品列表
        // 这里不用加载 了， 因为onresume的时候会自动刷新加载
        //loadInOrderDatas()

        daysDialog = FilterDialog(activity)
        daysDialog.setTitle("请选择商品状态")
        daysDialog.dismissEdit()
        daysDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            statueStr.set(it.showInfo)
            curSelectLot4Name = it.showInfo
            daysDialog.dismiss()
        })

        initHandling()
    }

    fun dayClick() {
        daysDialog.show()
    }

    fun addStatueData(data: MutableList<String>) {
        val beans = mutableListOf<BaseItemShowInfo>()
        data.forEach {
            beans.add(BaseItemShowInfo(it))
        }
        daysDialog.addAllData(beans)
    }

    // 界面右上角 三个小圆点被点击 ： 直接弹窗提示是不是要申请不扫码
    fun onBtnMoreClick() {

        activity.toolMenu.show(activity, activity.binding.titleBtnMore)
        // titleBtnMore 的 左下角
        //activity.popupMenu.showAsAnchorLeftBottom(activity.binding.titleBtnMore)

    }


    // 申请不扫码操作
    fun requestUnscan() {

        activity.waitingDialogHelp.showDialog()

        Log.e("tao", "扫码界面申请不扫码前 查询容器:" + containerCode + "是否有未上架的记录")
        RetrofitHelper.getAppAPI()
            .loadContainerData(Constants.whInfo?.whCode)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<InReceiptSerial>>(activity) {
                override fun success(list: MutableList<InReceiptSerial>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (list == null || list.size == 0) {
                        AlertDialogUtil.showOkAndCancelDialog(activity, "确定申请不扫码?",
                            { dialogInterface, i ->  //点了确定
                                val intent = Intent(activity, UnScanActivity::class.java)
                                intent.putExtra("type", "in") //入库单的申请不扫码 ， 出库单那边为 out
                                if (curOrderReceiveType.get().equals("receipt")) {
                                    intent.putExtra("orderType", "order")
                                } else {
                                    intent.putExtra("orderType", "wave")
                                }
                                intent.putExtra("orderNo", curOrderNo.get()) //
                                activity.isNeedRefresh = true
                                activity.startActivity(intent)
                            },
                            { dialogInterface, i ->  //点了取消

                            })
                    } else {
                        val tipText = "当前用户存在未收货确认的扫码记录，请收货确认后再申请不扫码！"
                        ToastUtils.getInstance().showErrorToastWithSound(activity, tipText)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })

    }


    // 加载 入库单或波次单 对应的所有商品信息
    fun loadInOrderDatas() {

        val param = mutableMapOf(
            "receiptCodeOrWaveNo" to curOrderNo.get().toString(),
            "whCode" to activity.getWhCode()
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getAppAPI()
            //.loadDataByOrderNoOrWaveNo(requestBody)
            //  20211222 后端换接口 输出明细列表 由前端汇总
            .loadDatasByOrderNoOrWaveNo(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<RespInOrders>(activity) {
                override fun success(data: RespInOrders?) {
                    activity.waitingDialogHelp.hidenDialog()

                    AppUtils.requestFocus(activity.binding.etAnyCode)

                    if (data != null) {
                        shouldReceiveNum.set(data.planQty.toDouble().toInt().toString())
                        realReceiveNum.set(data.scanQty.toDouble().toInt().toString())
                        data.containerCode?.let {
                            containerCode = it
                        }

                        if (data.planQty.compareTo(data.scanQty) == 0) {
                            MySoundUtils.getInstance().finishSound()
                        }

                        mapCode2Unscank.clear()
                        activity.adapter.data.clear()

                        if (data.inReceiptDetailInfoVOS != null && data.inReceiptDetailInfoVOS.size > 0) {

                            curListOrderDetail = data.inReceiptDetailInfoVOS

                            //加载到商品信息后 把商品的itemCode都放到数据里 去mdm查一次条码规则 缓存到ruleList
                            loadServerRuleList(data.inReceiptDetailInfoVOS)
                            // 查询批次属性模板
                            loadServerLotList(data.inReceiptDetailInfoVOS)

                            // 后端返回的明细列表  按 custItemCode + lot4 汇总
                            val groupMap = mutableMapOf<String, InOrderData>()

                            data.inReceiptDetailInfoVOS.forEach {

                                if (it.isUnScanMark) {
                                    mapCode2Unscank.add(it.receiptCode)
                                }

                                val key = it.custItemCode + it.lotAtt04
                                if (groupMap.get(key) == null) {
                                    groupMap.put(key, InOrderData())
                                }

                                val inOrderData = groupMap.get(key) as InOrderData
                                inOrderData.custItemCode = it.custItemCode
                                inOrderData.receiptCode = it.receiptCode
                                inOrderData.itemCode = it.itemCode
                                inOrderData.custOrderNo = it.custOrderNo
                                inOrderData.lotAtt04 = it.lotAtt04
                                inOrderData.itemName = it.itemName
                                inOrderData.cdcmUnscanMark = it.cdcmUnscanMark

                                if (inOrderData.scanNum == null) {
                                    inOrderData.scanNum = 0
                                }

                                if (inOrderData.totalQty == null) {
                                    inOrderData.totalQty = 0.0
                                }

                                // 已扫数量
                                inOrderData.scanNum = inOrderData.scanNum + it.serialQty
                                // 总数量
                                inOrderData.totalQty = inOrderData.totalQty + it.totalQty.toDouble()
                            }

                            // map 转 list
                            activity.adapter.data.clear()
                            groupMap.forEach {
                                activity.adapter.addData(it.value)
                            }
                        }
                        /* activity.adapter.addData(it)
                         if (!it.unscanMark.isNullOrBlank() && it.unscanMark.equals("01")) {
                             mapCode2Unscank.add(it.receiptCode)
                         }*/

                        activity.adapter.notifyDataSetChanged()

                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }


    //根据 itemCode 列表  去查询条码规则  (留着用来后面做条码解析)
    private fun loadServerRuleList(list: List<InOrderDetail>) {

        // 如果已经查过了，就不用查了
        if (ruleList != null && ruleList!!.size > 0) {
            return
        }

        val param = mutableListOf<String>()
        list.forEach {
            param.add(it.itemCode)
        }


        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .barcodeRule(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<RuleDataList>>(activity) {
                override fun success(data: MutableList<RuleDataList>?) {
                    if (data != null) {
                        ruleList = data
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    //activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    //根据 itemCode 列表  查询商品批次属性信息
    private fun loadServerLotList(list: List<InOrderDetail>) {

        // 如果已经查过了，就不用查了
        if (mapCustItemCodeToLotList.size > 0) {
            return
        }

        // 拼接 当前订单 的所有商品 itemCode ，然后去查他们对应的批属性配置
        val param = mutableListOf<String>()
        list.forEach {
            param.add(it.itemCode)
        }


        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getAppAPI()
            .getCdcmLotDetails(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<RespCdWhLotDetail>>(activity) {
                override fun success(lotList: MutableList<RespCdWhLotDetail>?) {

                    lotList?.get(0)?.details?.forEach {
                        if (!it.cdcmCustMaterialNo.isNullOrEmpty()) { //
                            if (mapCustItemCodeToLotList.get(it.cdcmCustMaterialNo)
                                    .isNullOrEmpty()
                            ) {
                                mapCustItemCodeToLotList.put(it.cdcmCustMaterialNo, LinkedList())
                            }
                            if (it.inputControl.toUpperCase().equals("R")) {  //必填的批属性才需要缓存
                                mapCustItemCodeToLotList.get(it.cdcmCustMaterialNo)!!.add(it)
                            }

                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    //activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    var currentGoods: String? = null

    // 编辑框 输入了一个  货品编码或sn码或其他码
    // 先在 rf本地做一个解析
    fun onEnterAnyCode() {
        if (CheckUtil.isFastDoubleClick()) {
            // 这里要找后端执行一次  货品条码校验
            // 并根据返回的数据中 是否含有 是否需要采集货品属性 和 收货数量控制 属性 来决定

            if (anyCode.get().isNullOrEmpty()) {
                return
            }

            //本地解析  sn -> custItemCode
            val snNo = CheckUtil.localParseCode(anyCode.get().toString(), ruleList)
            var isLocalParseSnSuccess = false
            // 输入输出不同 表示 本地解析成功 (sn进去  custItemCode 出来)，
            if (!snNo.equals(anyCode.get().toString())) {
                isLocalParseSnSuccess = true
            }

            val requestParam = InReceiptSerialScanDto()
            requestParam.whCode = activity.getWhCode()
            requestParam.barcode = anyCode.get().toString()
            requestParam.containerCode = containerCode
            requestParam.receiptChannel = "101"  //收货渠道 rf固定为101

            if (isLocalParseSnSuccess) {
                requestParam.custItemCode = snNo //这里的snNo 是本地解析成功后的商品编码
                requestParam.serialNo = anyCode.get().toString()  // 原条码
                requestParam.barcodeType = 1
                requestParam.serialType = "0" // sn 码
            } else {
                // 这里的snNo 是解析失败后的码 和   anyCode.get().toString() 是一样的
                requestParam.serialNo = snNo
            }

            val date = Date()
            //请求参数  批次属性
            requestParam.barcodeLotDto = BarcodeLotDto()
            //请求参数  批次属性5 批次号
            requestParam.barcodeLotDto.lotAtt05 = getLot5FromSerialNo(requestParam.serialNo)
            // 请求参数： 批次属性4 库存状态
            var lot4Code = "Y"
            DCUtils.lot4TypeN2C.get(curSelectLot4Name)?.let {
                lot4Code = DCUtils.lot4TypeN2C.get(curSelectLot4Name).toString()
            }
            requestParam.barcodeLotDto.lotAtt04 = lot4Code // 界面上选的正品或不良品
            // 请求参数： 批次属性3 入库日期
            requestParam.barcodeLotDto.lotAtt03 = SimpleDateFormat("yyyy-MM-dd").format(date)

            // 请求参数  波次号或入库单号
            if (curOrderReceiveType.get().equals("wave")) {
                requestParam.waveNo = curOrderNo.get()  //组单收货 传波次
            } else if (curOrderReceiveType.get().equals("receipt")) {
                requestParam.receiptCode = curOrderNo.get()  //按单收货  传 入库单号
            }

            // 请求参数  波次号或入库单号
            requestParam.receiptFlag = curOrderNo.get()

            // 20211223 为了精简报文 本地规则解析成功时 数组只需要传一个和custItemCode相同的商品
            // 解析失败 才需要传整个数组 让后端去解析
            if (isLocalParseSnSuccess) {
                Log.d("tao", "本地解析成功")
                // 优先匹配  相同custItemCode 并且 总数>已扫数 的商品  给 扫码接口 收货
                val element =
                    curListOrderDetail.find { it.custItemCode.equals(requestParam.custItemCode) && (it.totalQty.toInt() > it.serialQty) }
                if (element != null) {
                    requestParam.inReceiptDetailInfoVOS = mutableListOf<InOrderDetail>()
                    requestParam.inReceiptDetailInfoVOS.add(element)
                } else {
                    // 没有匹配到就 列表全部都给扫码接口
                    requestParam.inReceiptDetailInfoVOS = curListOrderDetail
                }
            } else {
                requestParam.inReceiptDetailInfoVOS = curListOrderDetail
            }
            // 20211223 报文多余的属性 精简优化
            requestParam.itemName = null
            requestParam.scanQty = null
            requestParam.totalQty = null

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(requestParam)
            )

            activity.waitingDialogHelp.showDialog()
            // 货品条码校验
            RetrofitHelper.getAppAPI()
                .scanCodeOnReceipt(requestBody)
                .compose(NetworkScheduler.compose())
                .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
                .subscribe(object : RequestCallback<InReceiptSerialScanDto>(activity) {
                    override fun success(inReceiptSerialScanDto: InReceiptSerialScanDto?) {
                        activity.waitingDialogHelp.hidenDialog()

                        if (inReceiptSerialScanDto == null) {
                            //inReceiptSerialScanDto 为null 表示后端已经识别出了该sn码 ，并且自动填写数量做了收货操作，
                            // 不需要前端再处理，直接刷新界面即可
                            ToastUtils.getInstance()
                                .toastWithOkSound(activity, "扫码成功", Toast.LENGTH_SHORT)
                            //重置条码编辑框
                            anyCode.set("")
                            // 扫码成功 后 刷新
                            loadInOrderDatas()

                            if (!TextUtils.isEmpty(currentGoods)) {
                                if (currentGoods != snNo) {
                                    AppUtils.showToast(activity, "编码切换!")
                                    MySoundUtils.getInstance().codeChangeSound()
                                }
                                currentGoods = snNo
                            } else {
                                currentGoods = snNo
                            }
                        } else {
                            if (!TextUtils.isEmpty(currentGoods)) {
                                if (currentGoods != inReceiptSerialScanDto.custItemCode) {
                                    AppUtils.showToast(activity, "编码切换!")
                                    MySoundUtils.getInstance().codeChangeSound()
                                }
                                currentGoods = inReceiptSerialScanDto.custItemCode
                            } else {
                                currentGoods = inReceiptSerialScanDto.custItemCode
                            }
                            handleBarcode(
                                inReceiptSerialScanDto,
                                requestParam,
                                isLocalParseSnSuccess
                            )
                        }
                    }

                    override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                        activity.waitingDialogHelp.hidenDialog()
                        if (statusCode == 600013L || statusCode == 600012L) {
//                        TTSUtils.startAuto(apiErrorModel.message)
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(activity, apiErrorModel.message)
//                        ToastUtils.getInstance().showErrorToast(activity, apiErrorModel.message)
                        } else {
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(activity, apiErrorModel.message)
                        }
                        anyCode.set("")
                    }
                })
        }
    }


    // 获取容器号 (上架成功后回到这个界面要重新获取容器号)
    fun reloadContainerCode() {

        Log.e("tao", "扫码界面申请新的容器号")

        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getAppAPI()
            .initReceiptContainer(activity.getWhCode())
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ReceiptSerialInitVO>(activity) {
                override fun success(data: ReceiptSerialInitVO?) {
                    activity.waitingDialogHelp.hidenDialog()

                    if (data == null) return

                    if (data.code.equals("0")) {
                        // 后端生成了新的容器号
                        Log.e("tao", "收货扫码界面得到新容器号：" + data.containerCode)
                        containerCode = data.containerCode
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    //用户扫码后 rf会调用扫码接口，这里处理后端返回的条码信息
    fun handleBarcode(
        inReceiptSerialScanDto: InReceiptSerialScanDto,
        requestParam: InReceiptSerialScanDto,
        isLocalParseSnSuccess: Boolean
    ) {
        // 以下为后端返回 inReceiptSerialScanDto 不为null 的处理方式
        //anyCode.set("")
        inReceiptSerialScanDto.barcodeLotDto = BarcodeLotDto()
        if (!inReceiptSerialScanDto.custItemCode.isNullOrEmpty()
            && mapCustItemCodeToLotList.get(inReceiptSerialScanDto.custItemCode) != null
            && mapCustItemCodeToLotList.get(inReceiptSerialScanDto.custItemCode)!!.size > 0
        ) {
            //如果是sn码 ，则不弹框编辑批属性，三个属性默认填，
            if (isLocalParseSnSuccess || (inReceiptSerialScanDto.barcodeType != 3 && inReceiptSerialScanDto.barcodeType != 4)) {
                // 1-规则解析的条码，2-LMS接口解析的条码
                // 入库日期当前 yyymmdd，批次号5 扫码接口会返回(为空则YYMM)  和 lot4状态 (界面右上角选什么就是什么)
                // sn 码处理批属性 (自动填写默认值，不弹框编辑)
                mapCustItemCodeToLotList.get(inReceiptSerialScanDto.custItemCode)!!.forEach {
                    if (it.inputControl.toUpperCase().equals("R")) {
                        if (it.lotAtt.equals("LOT_ATT03")) {  //入库日期 默认值
                            val date = Date()
                            inReceiptSerialScanDto.barcodeLotDto.lotAtt03 =
                                SimpleDateFormat("yyyyMMdd").format(date)
                        } else if (it.lotAtt.equals("LOT_ATT05")) {  //批次号 默认值
                            inReceiptSerialScanDto.barcodeLotDto.lotAtt05 =
                                getLot5FromSerialNo(requestParam.serialNo)
                        } else if (it.lotAtt.equals("LOT_ATT04")) { // 状态 (界面右上角用户自己选的)
                            inReceiptSerialScanDto.barcodeLotDto.lotAtt04 = curSelectLot4Name
                        }
                    }
                }
                //不需要录入批属性，直接跳去判断要不要录 sku数量
                onConfirmWriteLotInfo(isLocalParseSnSuccess, inReceiptSerialScanDto)
            } else {   // 3-69码 ，4-商品编码

                var isUnscan = false

                if (mapCode2Unscank.contains(inReceiptSerialScanDto.receiptCode)) {
                    //如果后端发现这个码是商品编码，则要先判断，只有 不扫码审核通过  的入库单才可以按商品编码收货
                    isUnscan = true
                } else {
                    // 组波次扫码 时  扫码接口没返回入库单号 需要遍历入库单明细 判断支不支持不扫码
                    activity.adapter.data.forEach {
                        if (!it.custItemCode.isNullOrBlank() && it.custItemCode.equals(
                                inReceiptSerialScanDto.custItemCode
                            )
                        ) {
                            if (!it.unscanMark.isNullOrBlank() && it.unscanMark.equals("01")) {
                                isUnscan = true
                            }
                        }
                    }
                }


                // 只要入库单在前置仓有申请不扫码 或者 大物流又不扫码标记(unScanQty>0) 输入的客户商品编码就可以进行收货
                if (isUnscan == false && inReceiptSerialScanDto.unScanQty > 0) {
                    isUnscan = true
                }

                if (isUnscan) {
                    // 商品编码处理批属性 弹框编辑
                    activity.editLotInfoDialog.showDlg(
                        "货品属性采集", inReceiptSerialScanDto,
                        mapCustItemCodeToLotList.get(inReceiptSerialScanDto.custItemCode)!!
                    )
                } else {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "扫码商品必须扫SN码!")
                    anyCode.set("")   //清空条码框
                }
            }

        } else if (inReceiptSerialScanDto.custItemCode.isNullOrEmpty()) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "扫码商品未查到客户商品编码!")
            anyCode.set("")   //清空条码框
        } else {
            // 后端返回的custItemCode 没有在本地找到批属性模板 ，则 不需要录入批属性，直接跳去判断要不要录 sku数量
            //onConfirmWriteLotInfo(inReceiptSerialScanDto)
            onConfirmWriteLotInfo(false, inReceiptSerialScanDto)
        }
    }


    //完成批属性录入 (提交给后端 弹框编辑的批属性和sku数量)
    fun onConfirmWriteLotInfo(isSN: Boolean, inReceiptSerialScanDto: InReceiptSerialScanDto) {
        // 批属性4 需要先根据字典 把界面上显示的中文 转成英文
        if (!inReceiptSerialScanDto.barcodeLotDto.lotAtt04.isNullOrBlank()) {
            val dictCode =
                DCUtils.lot4TypeN2C.get(inReceiptSerialScanDto.barcodeLotDto.lotAtt04)?.toString()
            if (!dictCode.isNullOrBlank()) {  //批属性4， 中文转英文
                inReceiptSerialScanDto.barcodeLotDto.lotAtt04 = dictCode
            }
        }

        // 属性 1 2 3  的日期 格式化
        if (!inReceiptSerialScanDto.barcodeLotDto.lotAtt01.isNullOrBlank()) {
            inReceiptSerialScanDto.barcodeLotDto.lotAtt01 =
                formatYymmddHhmmss(inReceiptSerialScanDto.barcodeLotDto.lotAtt01)
        }
        if (!inReceiptSerialScanDto.barcodeLotDto.lotAtt02.isNullOrBlank()) {
            inReceiptSerialScanDto.barcodeLotDto.lotAtt02 =
                formatYymmddHhmmss(inReceiptSerialScanDto.barcodeLotDto.lotAtt02)
        }
        if (!inReceiptSerialScanDto.barcodeLotDto.lotAtt03.isNullOrBlank()) {
            inReceiptSerialScanDto.barcodeLotDto.lotAtt03 =
                formatYymmddHhmmss(inReceiptSerialScanDto.barcodeLotDto.lotAtt03)
        }


        // 校验必填的批属性有没有填
        if (!validateLotInfo(inReceiptSerialScanDto)) {
            return
        }

        activity.editLotInfoDialog.dismiss()

        if (isSN || (inReceiptSerialScanDto.barcodeType != 3 && inReceiptSerialScanDto.barcodeType != 4)) {
            //sn码， 等同于 需求文档“收货数量控制”里面 不需要弹框 数量默认=1的情况
            inReceiptSerialScanDto.qty = 1
            val isSerialNo = true
            onConfirmScanOne(isSerialNo, inReceiptSerialScanDto)
        } else {
            //barcodeType为3或4，就是 商品编码， 就等于 需求文档“收货数量控制”里面 需要弹框的情况
            activity.editReceiptNumDialog.showDlg("", inReceiptSerialScanDto) //默认空
        }

    }


    fun validateLotInfo(inReceiptSerialScanDto: InReceiptSerialScanDto): Boolean {
        //校验必填属性是否为空
        mapCustItemCodeToLotList.get(inReceiptSerialScanDto.custItemCode)?.forEach {
            if (it.inputControl.toUpperCase().equals("R")) {
                if (it.lotAtt.equals("LOT_ATT01") && inReceiptSerialScanDto.barcodeLotDto.lotAtt01.isNullOrBlank()) {
                    ToastUtils.getInstance().taostWithErrorSound(
                        activity,
                        "必填属性:" + it.title + "  不能为空",
                        Toast.LENGTH_SHORT
                    )  // 生产日期
                    return false
                } else if (it.lotAtt.equals("LOT_ATT02") && inReceiptSerialScanDto.barcodeLotDto.lotAtt02.isNullOrBlank()) {
                    ToastUtils.getInstance().taostWithErrorSound(
                        activity,
                        "必填属性:" + it.title + "  不能为空",
                        Toast.LENGTH_SHORT
                    ) // 失效日期
                    return false
                } else if (it.lotAtt.equals("LOT_ATT03") && inReceiptSerialScanDto.barcodeLotDto.lotAtt03.isNullOrBlank()) {
                    ToastUtils.getInstance().taostWithErrorSound(
                        activity,
                        "必填属性:" + it.title + "  不能为空",
                        Toast.LENGTH_SHORT
                    )  // 入库日期
                    return false
                } else if (it.lotAtt.equals("LOT_ATT04") && inReceiptSerialScanDto.barcodeLotDto.lotAtt04.isNullOrBlank()) {
                    ToastUtils.getInstance().taostWithErrorSound(
                        activity,
                        "必填属性:" + it.title + "  不能为空",
                        Toast.LENGTH_SHORT
                    )  // 状态
                    return false
                } else if (it.lotAtt.equals("LOT_ATT05") && inReceiptSerialScanDto.barcodeLotDto.lotAtt05.isNullOrBlank()) {
                    ToastUtils.getInstance().taostWithErrorSound(
                        activity,
                        "必填属性:" + it.title + "  不能为空",
                        Toast.LENGTH_SHORT
                    )  //批次号
                    return false
                } else if (it.lotAtt.equals("LOT_ATT06") && inReceiptSerialScanDto.barcodeLotDto.lotAtt06.isNullOrBlank()) {
                    ToastUtils.getInstance().taostWithErrorSound(
                        activity,
                        "必填属性:" + it.title + "  不能为空",
                        Toast.LENGTH_SHORT
                    )
                    return false
                } else if (it.lotAtt.equals("LOT_ATT07") && inReceiptSerialScanDto.barcodeLotDto.lotAtt07.isNullOrBlank()) {
                    ToastUtils.getInstance().taostWithErrorSound(
                        activity,
                        "必填属性:" + it.title + "  不能为空",
                        Toast.LENGTH_SHORT
                    )
                    return false
                } else if (it.lotAtt.equals("LOT_ATT08") && inReceiptSerialScanDto.barcodeLotDto.lotAtt08.isNullOrBlank()) {
                    ToastUtils.getInstance().taostWithErrorSound(
                        activity,
                        "必填属性:" + it.title + "  不能为空",
                        Toast.LENGTH_SHORT
                    )
                    return false
                } else if (it.lotAtt.equals("LOT_ATT09") && inReceiptSerialScanDto.barcodeLotDto.lotAtt09.isNullOrBlank()) {
                    ToastUtils.getInstance().taostWithErrorSound(
                        activity,
                        "必填属性:" + it.title + "  不能为空",
                        Toast.LENGTH_SHORT
                    )
                    return false
                } else if (it.lotAtt.equals("LOT_ATT10") && inReceiptSerialScanDto.barcodeLotDto.lotAtt10.isNullOrBlank()) {
                    ToastUtils.getInstance().taostWithErrorSound(
                        activity,
                        "必填属性:" + it.title + "  不能为空",
                        Toast.LENGTH_SHORT
                    )
                    return false
                } else if (it.lotAtt.equals("LOT_ATT11") && inReceiptSerialScanDto.barcodeLotDto.lotAtt11.isNullOrBlank()) {
                    ToastUtils.getInstance().taostWithErrorSound(
                        activity,
                        "必填属性:" + it.title + "  不能为空",
                        Toast.LENGTH_SHORT
                    )
                    return false
                } else if (it.lotAtt.equals("LOT_ATT12") && inReceiptSerialScanDto.barcodeLotDto.lotAtt12.isNullOrBlank()) {
                    ToastUtils.getInstance().taostWithErrorSound(
                        activity,
                        "必填属性:" + it.title + "  不能为空",
                        Toast.LENGTH_SHORT
                    )
                    return false
                }
            }
        }
        return true
    }

    // 把 类似 20211027 的字符串 转成   2021-10-27 00:00:00 (方便传给后端)
    fun formatYymmddHhmmss(str: String): String {
        val df = SimpleDateFormat("yyyyMMdd")
        var date = df.parse(str)
        val df2 = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        return df2.format(date)
    }


    // 完成批属性采集和sku数量编辑
    // 同样是调用入库扫码接口，但是这里是完成扫码和弹框编辑操作
    fun onConfirmScanOne(isSerialNo: Boolean, inReceiptSerialScanDto: InReceiptSerialScanDto) {

        inReceiptSerialScanDto.receiptFlag = curOrderNo.get()
        inReceiptSerialScanDto.containerCode = containerCode
        inReceiptSerialScanDto.receiptChannel = "101" // rf端收货渠道号固定为 101

        if (curOrderReceiveType.get().equals("wave")) {
            inReceiptSerialScanDto.waveNo = curOrderNo.get()
        } else if (curOrderReceiveType.get().equals("receipt")) {
            inReceiptSerialScanDto.receiptCode = curOrderNo.get()
        }

        //后端联调要求 完成一次扫码时 参数serialNo要为空值
        if (!isSerialNo) {
            inReceiptSerialScanDto.serialNo = null
        } else {
            inReceiptSerialScanDto.serialNo = anyCode.get()
            //1-规则解析的条码，2-LMS接口解析的条码，3-69码 ，4-商品编码
            inReceiptSerialScanDto.barcodeType = 1
        }

        //补齐属性3和属性4参数
        if (inReceiptSerialScanDto.barcodeLotDto == null) {
            inReceiptSerialScanDto.barcodeLotDto = BarcodeLotDto()
        }

        if (inReceiptSerialScanDto.barcodeLotDto.lotAtt04.isNullOrBlank()) {
            var lot4Code = "Y"
            DCUtils.lot4TypeN2C.get(curSelectLot4Name)?.let {
                lot4Code = DCUtils.lot4TypeN2C.get(curSelectLot4Name).toString()
            }
            inReceiptSerialScanDto.barcodeLotDto.lotAtt04 = lot4Code
        }

        if (inReceiptSerialScanDto.barcodeLotDto.lotAtt03.isNullOrBlank()) {
            inReceiptSerialScanDto.barcodeLotDto.lotAtt03 =
                SimpleDateFormat("yyyy-MM-dd").format(Date())
        }

        inReceiptSerialScanDto.inReceiptDetailInfoVOS = curListOrderDetail

        // 20211223 报文多余的属性 精简优化
        inReceiptSerialScanDto.itemName = null
        inReceiptSerialScanDto.scanQty = null
        inReceiptSerialScanDto.totalQty = null

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(inReceiptSerialScanDto)
        )


        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getAppAPI()
            .scanCodeOnReceipt(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<InReceiptSerialScanDto>(activity) {
                override fun success(inReceiptSerialScanDto: InReceiptSerialScanDto?) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .toastWithFinishSound(activity, "扫码成功", Toast.LENGTH_SHORT)
                    anyCode.set("")
                    // 扫码成功 后 刷新
                    loadInOrderDatas()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    anyCode.set("")
                    activity.waitingDialogHelp.hidenDialog()
//                    TTSUtils.startAuto(apiErrorModel.message)
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }


    fun initHandlingGroupList() {

        RetrofitHelper.getBasicDataAPI()
            .queryPageHandlingGroup(Constants.tenantCode, activity.getWhCode(), "01")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PageResult<HandlingGroup>>(activity) {
                override fun success(data: PageResult<HandlingGroup>?) {
                    if (data != null && data.list != null && data.list.size > 0) {
                        val beans = mutableListOf<BaseItemShowInfo>()
                        data.list.forEach {
                            it.showInfo = it.handlingName
                            if (!it.showInfo.isNullOrBlank()) {
                                beans.add(it)
                            }
                        }
                        if (activity.getWhCode() == "W00514") {
                            val handlingGroup = HandlingGroup()
                            handlingGroup.showInfo = "虚拟装卸组"
                            handlingGroup.handlingCode = "zx0001"
                            handlingGroup.handlingName = "虚拟装卸组"
                            beans.add(0, handlingGroup)
                        }

                        if (beans.isEmpty()) {
                            ToastUtils.getInstance().showErrorToastWithSound(activity, "必须配置装卸组!")
                            return
                        }
                        activity.handlingGroupDialog.addAllData(beans)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun initHandling() {
        Observable.create<HandingInfoDb> {
            var handingInfo =
                db.selector(HandingInfoDb::class.java).where("userId", "==", Constants.userInfo?.id)
                    .and(WhereBuilder.b("mode", "==", 3))
                    .findFirst()

            if (handingInfo == null) {
                it.onNext(HandingInfoDb())
            } else {
                it.onNext(handingInfo)
            }
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<HandingInfoDb> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: HandingInfoDb) {
                    if (t.userId != null) {
                        handlingGroupName.set(t.handlingName)
                        handlingGroupCode.set(t.handlingCode)
                    }
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    fun getLot5FromSerialNo(serialNo: String?): String {
        var prdDate = ""
        var lotAtt05 = ""
        if (serialNo!!.length == 15) {
            prdDate = "20" + serialNo.substring(6, 8) + "-" + serialNo.substring(8, 10) + "-" + "01"
            lotAtt05 = serialNo.substring(6, 10)
        } else if (serialNo.length == 16) {
            val year = serialNo.substring(4, 6)
            var month = ""
            if ("A" == serialNo.substring(6, 7))
                month = "10"
            else if ("B" == serialNo.substring(6, 7))
                month = "11"
            else if ("C" == serialNo.substring(6, 7))
                month = "12"
            else
                month = "0" + serialNo.substring(6, 7)
            lotAtt05 = year + month
            prdDate = "20" + year + "-" + month + "-" + serialNo.substring(7, 9)
        } else if (serialNo.length == 22) {
            if ("D" == serialNo.substring(0, 1)) {
                val year = serialNo.substring(11, 13)
                var month = ""
                if ("A" == serialNo.substring(13, 14))
                    month = "10"
                else if ("B" == serialNo.substring(13, 14))
                    month = "11"
                else if ("C" == serialNo.substring(13, 14))
                    month = "12"
                else
                    month = "0" + serialNo.substring(13, 14)
                lotAtt05 = year + month
                prdDate = "20" + year + "-" + month + "-" + serialNo.substring(14, 16)
            } else {
                var year = serialNo.substring(11, 12).toUpperCase()
                if ("01234".contains(year)) {
                    lotAtt05 = "2" + year
                    year = "202" + year
                } else if ("56789".contains(year)) {
                    lotAtt05 = "1" + year
                    year = "201" + year
                } else {
                    val a = year[0]
                    var b = 0
                    if ("ABCD".contains(year))
                        b = 2024 + a.toByte().toInt() - 64
                    else if ("FGH".contains(year))
                        b = 2024 + a.toByte().toInt() - 65
                    else if ("JKLMN".contains(year))
                        b = 2024 + a.toByte().toInt() - 66
                    else if ("PQRSTUVWXYZ".contains(year)) b = 2024 + a.toByte().toInt() - 67
                    year = b.toString()
                    lotAtt05 = year.substring(2, 4)
                }
                var month = ""
                if ("A" == serialNo.substring(12, 13))
                    month = "10"
                else if ("B" == serialNo.substring(12, 13))
                    month = "11"
                else if ("C" == serialNo.substring(12, 13))
                    month = "12"
                else
                    month = "0" + serialNo.substring(12, 13)
                prdDate = year + "-" + month + "-" + serialNo.substring(13, 15)
                lotAtt05 += month
            }
        }
        val sdf = SimpleDateFormat("yyyy-MM-dd")
        try {
            sdf.isLenient = false
            sdf.parse(prdDate)
        } catch (e: Exception) {
            prdDate = ""
        }

        return lotAtt05
    }

    fun selectHandlingGroup() {
        activity.handlingGroupDialog.show()
    }


    fun afterSelectLocation(location: BsLocation) {
        AlertDialogUtil.showOnlyOkDialog(activity, location.locCode, null)
    }


    // 点击  收货确认  按钮 ，带着当期的containerCode 跳到 收货确认界面 InStorageConfirmActivity
    fun confirmReceive() {
        if (handlingGroupName.get().isNullOrEmpty()) {
            AppUtils.showToast(activity, "请先选择装卸组!")
            selectHandlingGroup()
            return
        }
        if (!containerCode.isNullOrEmpty()) {
            activity.isNeedRefresh = true
            val it = Intent(activity, InStorageConfirmActivity::class.java)
            it.putExtra("handlingGroupName", handlingGroupName.get())
            it.putExtra("handlingGroupCode", handlingGroupCode.get())
            it.putExtra("curOrderReceiveType", curOrderReceiveType.get())
            it.putExtra("containerCode", containerCode)
            activity.startActivityForResult(it, CODE_LOAD_CONTAINER)
        }
    }


    // 点击了  左下角  删除条码
    fun toDeleteActivity() {
        if (!containerCode.isNullOrEmpty()) {
            val it = Intent(activity, InStorageDeleteActivity::class.java)
            // 这里塞一个 收货容器号
            it.putExtra("orderNo", curOrderNo.get())
            it.putExtra("orderReceiveType", curOrderReceiveType.get())
            it.putExtra("containerCode", containerCode)
            activity.startActivity(it)
        } else {
            ToastUtils.getInstance().toastNoSound(activity, "容器号为空", Toast.LENGTH_SHORT)
        }
    }

    fun back() {
        activity.finish()
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        result?.let {
            anyCode.set(result)
            onEnterAnyCode()
        }

    }

}