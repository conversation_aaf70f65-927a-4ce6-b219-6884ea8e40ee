package com.midea.prestorage.function.containerpick

import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestoragesaas.databinding.ActivityContainerReprintBinding
import com.midea.prestoragesaas.databinding.ActivityContainerReprintCareBinding

sealed class ActivityContainerReprintUnionBinding {
    abstract var vm: ContainerReprintVM?
    abstract val llTitleBar: RelativeLayout
    abstract val rv: RecyclerView
    abstract val tvNotification: TextView

    class V2(val binding: ActivityContainerReprintCareBinding) :
        ActivityContainerReprintUnionBinding() {
        override var vm: ContainerReprintVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val rv = binding.rv
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityContainerReprintBinding) :
        ActivityContainerReprintUnionBinding() {
        override var vm: ContainerReprintVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val rv = binding.rv
        override val tvNotification = binding.tvNotification
    }
}
