package com.midea.prestorage.beans.net;

import java.io.Serializable;

public class ReceiptListBean implements Serializable {
    private String itemCode;
    private String itemName;
    private String batch;
    private String totalQty;
    private String actualQty;
    private String inventorySts;
    private String inventoryStsName;
    private String expirationDate;

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getBatch() {
        return batch;
    }

    public void setBatch(String batch) {
        this.batch = batch;
    }

    public String getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(String totalQty) {
        this.totalQty = totalQty;
    }

    public String getActualQty() {
        return actualQty;
    }

    public void setActualQty(String actualQty) {
        this.actualQty = actualQty;
    }

    public String getInventorySts() {
        return inventorySts;
    }

    public void setInventorySts(String inventorySts) {
        this.inventorySts = inventorySts;
    }

    public String getInventoryStsName() {
        return inventoryStsName;
    }

    public void setInventoryStsName(String inventoryStsName) {
        this.inventoryStsName = inventoryStsName;
    }

    public String getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }
}
