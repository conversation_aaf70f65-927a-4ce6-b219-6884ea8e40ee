package com.midea.prestorage.function.instorage

import android.widget.TextView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.PutAwayTask
import com.midea.prestoragesaas.R

class InStoragePutAwayTaskAdapter(private val viewModel: InStoragePutAwayTaskSettingViewModel) :
    CommonAdapter<PutAwayTask>(R.layout.item_in_storage_put_away_task) {

    override fun convert(holder: BaseViewHolder?, item: PutAwayTask?) {
        super.convert(holder, item)

        holder?.setText(R.id.tv_cust_item_code_value,
            (item?.custItemCode ?: "") + if (item?.whBarcode69?.isNotEmpty() == true) " (${item.whBarcode69})" else "")

        holder?.setText(R.id.tv_status, item?.lotAtt04Str ?: "")

        val dynamicText1 = holder?.getView<TextView>(R.id.tv_dynamic_1)
        val dynamicValueText1 = holder?.getView<TextView>(R.id.tv_dynamic_1_value)
        val dynamicText2 = holder?.getView<TextView>(R.id.tv_dynamic_2)
        val dynamicValueText2 = holder?.getView<TextView>(R.id.tv_dynamic_2_value)
        val dynamicText3 = holder?.getView<TextView>(R.id.tv_dynamic_3)
        val dynamicValueText3 = holder?.getView<TextView>(R.id.tv_dynamic_3_value)
        val dynamicText4 = holder?.getView<TextView>(R.id.tv_dynamic_4)
        val dynamicValueText4 = holder?.getView<TextView>(R.id.tv_dynamic_4_value)

        viewModel.updateDynamicShowView(
            dynamicText1,
            dynamicValueText1,
            dynamicText2,
            dynamicValueText2,
            dynamicText3,
            dynamicValueText3,
            dynamicText4,
            dynamicValueText4,
            item
        )
    }

}