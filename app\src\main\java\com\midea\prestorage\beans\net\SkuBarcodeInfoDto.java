package com.midea.prestorage.beans.net;

public class SkuBarcodeInfoDto {
    /**
     * 主键
     */
    private String id;
    /**
     * 登录Id，序列号生成
     * logId
     */
    private String logId;
    /**
     *
     * fgId
     */
    private String fgId;
    /**
     *
     * invOrgId
     */
    private String invOrgId;
    /**
     * 客户商品编码
     * pn
     */
    private String pn;
    /**
     * 托盘编码
     * palletBarcode
     */
    private String palletBarcode;
    /**
     * 大箱条码
     * cartonBarcode
     */
    private String cartonBarcode;
    /**
     * 小箱条码
     * sn
     */
    private String sn;
    /**
     * 板箱生成时间
     * datetimeCreated
     */
    private String datetimeCreated;
    /**
     *
     * datetimePallet
     */
    private String datetimePallet;
    /**
     * 工单号
     * moName
     */
    private String moName;
    /**
     * 产线
     * orgName
     */
    private String orgName;
    /**
     *
     * packingBoxMaxVolume
     */
    private Long packingBoxMaxVolume;
    /**
     * 箱包规格
     * cartonMaxVolume
     */
    private Long cartonMaxVolume;
    /**
     * 板箱规格
     * palletMaxVolume
     */
    private Long palletMaxVolume;
    /**
     *
     * datetimeDl
     */
    private String datetimeDl;
    /**
     *
     * srcSys
     */
    private String srcSys;
    //外码
    private String outCode;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLogId() {
        return logId;
    }

    public void setLogId(String logId) {
        this.logId = logId;
    }

    public String getFgId() {
        return fgId;
    }

    public void setFgId(String fgId) {
        this.fgId = fgId;
    }

    public String getInvOrgId() {
        return invOrgId;
    }

    public void setInvOrgId(String invOrgId) {
        this.invOrgId = invOrgId;
    }

    public String getPn() {
        return pn;
    }

    public void setPn(String pn) {
        this.pn = pn;
    }

    public String getPalletBarcode() {
        return palletBarcode;
    }

    public void setPalletBarcode(String palletBarcode) {
        this.palletBarcode = palletBarcode;
    }

    public String getCartonBarcode() {
        return cartonBarcode;
    }

    public void setCartonBarcode(String cartonBarcode) {
        this.cartonBarcode = cartonBarcode;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getDatetimeCreated() {
        return datetimeCreated;
    }

    public void setDatetimeCreated(String datetimeCreated) {
        this.datetimeCreated = datetimeCreated;
    }

    public String getDatetimePallet() {
        return datetimePallet;
    }

    public void setDatetimePallet(String datetimePallet) {
        this.datetimePallet = datetimePallet;
    }

    public String getMoName() {
        return moName;
    }

    public void setMoName(String moName) {
        this.moName = moName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Long getPackingBoxMaxVolume() {
        return packingBoxMaxVolume;
    }

    public void setPackingBoxMaxVolume(Long packingBoxMaxVolume) {
        this.packingBoxMaxVolume = packingBoxMaxVolume;
    }

    public Long getCartonMaxVolume() {
        return cartonMaxVolume;
    }

    public void setCartonMaxVolume(Long cartonMaxVolume) {
        this.cartonMaxVolume = cartonMaxVolume;
    }

    public Long getPalletMaxVolume() {
        return palletMaxVolume;
    }

    public void setPalletMaxVolume(Long palletMaxVolume) {
        this.palletMaxVolume = palletMaxVolume;
    }

    public String getDatetimeDl() {
        return datetimeDl;
    }

    public void setDatetimeDl(String datetimeDl) {
        this.datetimeDl = datetimeDl;
    }

    public String getSrcSys() {
        return srcSys;
    }

    public void setSrcSys(String srcSys) {
        this.srcSys = srcSys;
    }

    public String getOutCode() {
        return outCode;
    }

    public void setOutCode(String outCode) {
        this.outCode = outCode;
    }
}
