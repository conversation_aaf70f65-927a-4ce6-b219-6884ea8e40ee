package com.midea.prestorage.beans.net;

import java.io.Serializable;
import java.util.List;

/**
 * Created by LIXK5 on 2019/4/28.
 */

public class NetDivideWaitBean implements Serializable {

    private List<NetDivideWaitListBean> list;

    public List<NetDivideWaitListBean> getList() {
        return list;
    }

    public void setList(List<NetDivideWaitListBean> list) {
        this.list = list;
    }

    public class NetDivideWaitListBean {
        private String id;
        private String pageSize;
        private String erpOrderCode;
        private String shipBy;
        private String requestedBy;
        private String states;
        private String requestedTime;
        private String requestQty;
        private String waveId;
        private String shipTime;
        private String waybillNo;
        private String trailingSts;
        private String shipToAddress1;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getPageSize() {
            return pageSize;
        }

        public void setPageSize(String pageSize) {
            this.pageSize = pageSize;
        }

        public String getErpOrderCode() {
            return erpOrderCode;
        }

        public void setErpOrderCode(String erpOrderCode) {
            this.erpOrderCode = erpOrderCode;
        }

        public String getShipBy() {
            return shipBy;
        }

        public void setShipBy(String shipBy) {
            this.shipBy = shipBy;
        }

        public String getStates() {
            return states;
        }

        public void setStates(String states) {
            this.states = states;
        }

        public String getRequestedTime() {
            return requestedTime;
        }

        public void setRequestedTime(String requestedTime) {
            this.requestedTime = requestedTime;
        }

        public String getRequestQty() {
            return requestQty;
        }

        public void setRequestQty(String requestQty) {
            this.requestQty = requestQty;
        }

        public String getWaveId() {
            return waveId;
        }

        public void setWaveId(String waveId) {
            this.waveId = waveId;
        }

        public String getShipTime() {
            return shipTime;
        }

        public void setShipTime(String shipTime) {
            this.shipTime = shipTime;
        }

        public String getRequestedBy() {
            return requestedBy;
        }

        public void setRequestedBy(String requestedBy) {
            this.requestedBy = requestedBy;
        }

        public String getWaybillNo() {
            return waybillNo;
        }

        public void setWaybillNo(String waybillNo) {
            this.waybillNo = waybillNo;
        }

        public String getTrailingSts() {
            return trailingSts;
        }

        public void setTrailingSts(String trailingSts) {
            this.trailingSts = trailingSts;
        }

        public String getShipToAddress1() {
            return shipToAddress1;
        }

        public void setShipToAddress1(String shipToAddress1) {
            this.shipToAddress1 = shipToAddress1;
        }
    }
}
