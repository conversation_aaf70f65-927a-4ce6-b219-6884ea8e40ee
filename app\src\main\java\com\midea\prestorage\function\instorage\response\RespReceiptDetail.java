package com.midea.prestorage.function.instorage.response;

import java.io.Serializable;
import java.math.BigDecimal;

public class RespReceiptDetail implements Serializable {


    private String id;
    private String createTime;
    private String updateTime;
    private String tenantCode;
    private String createUserCode;
    private String createUserName;
    private String updateUserCode;
    private String updateUserName;
    private String remark;
    private int version;
    private int deleteFlag;
    private int pageNo;
    private int pageSize;
    private String offset;
    private String orderBy;
    private String orderByType;
    private String ids;
    private String tenantCodes;
    private String count;
    private String startTime;
    private String endTime;
    private String receiptCode;
    private String whCode;
    private String ownerCode;
    private String custItemCode;
    private String itemCode;
    private String itemName;
    private String lotAtt01;
    private String lotAtt02;
    private String lotAtt03;
    private String lotAtt04;
    private String lotAtt05;
    private String lotAtt06;
    private String lotAtt07;
    private String lotAtt08;
    private String lotAtt09;
    private String lotAtt10;
    private String lotAtt11;
    private String lotAtt12;
    private BigDecimal totalQty;
    private BigDecimal receiptQty;
    private String scanNum;
    private String custOrderLineNum;
    private String unit;
    private BigDecimal volume;
    private BigDecimal weight;
    private BigDecimal netWeight;
    private BigDecimal itemSuiteQty;
    private String itemSuiteCode;
    private String itemClass;
    private String userDef1;
    private String userDef2;
    private String userDef3;
    private String userDef4;
    private String userDef5;
    private int status;
    private String stackLevel;
    private BigDecimal layerQty;
    private boolean isNeedScan69;
    private boolean isValidity;
    private String periodOfValidity;
    private String validityUnit;
    private String whBarcode69;
    private String inLifeDays;
    private String outLifeDays;
    private String inLifePercentage;
    private String outLifePercentage;
    private BigDecimal serialQty;
    private boolean isNotScan;
    private String startReceiveDate;
    private String endReceiveDate;

    public String getStartReceiveDate() {
        return startReceiveDate;
    }

    public void setStartReceiveDate(String startReceiveDate) {
        this.startReceiveDate = startReceiveDate;
    }

    public String getEndReceiveDate() {
        return endReceiveDate;
    }

    public void setEndReceiveDate(String endReceiveDate) {
        this.endReceiveDate = endReceiveDate;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public int getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(int deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getOffset() {
        return offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderByType() {
        return orderByType;
    }

    public void setOrderByType(String orderByType) {
        this.orderByType = orderByType;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getTenantCodes() {
        return tenantCodes;
    }

    public void setTenantCodes(String tenantCodes) {
        this.tenantCodes = tenantCodes;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getReceiptCode() {
        return receiptCode;
    }

    public void setReceiptCode(String receiptCode) {
        this.receiptCode = receiptCode;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getLotAtt01() {
        return lotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        this.lotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return lotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        this.lotAtt02 = lotAtt02;
    }

    public String getLotAtt03() {
        return lotAtt03;
    }

    public void setLotAtt03(String lotAtt03) {
        this.lotAtt03 = lotAtt03;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getLotAtt05() {
        return lotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        this.lotAtt05 = lotAtt05;
    }

    public String getLotAtt06() {
        return lotAtt06;
    }

    public void setLotAtt06(String lotAtt06) {
        this.lotAtt06 = lotAtt06;
    }

    public String getLotAtt07() {
        return lotAtt07;
    }

    public void setLotAtt07(String lotAtt07) {
        this.lotAtt07 = lotAtt07;
    }

    public String getLotAtt08() {
        return lotAtt08;
    }

    public void setLotAtt08(String lotAtt08) {
        this.lotAtt08 = lotAtt08;
    }

    public String getLotAtt09() {
        return lotAtt09;
    }

    public void setLotAtt09(String lotAtt09) {
        this.lotAtt09 = lotAtt09;
    }

    public String getLotAtt10() {
        return lotAtt10;
    }

    public void setLotAtt10(String lotAtt10) {
        this.lotAtt10 = lotAtt10;
    }

    public String getLotAtt11() {
        return lotAtt11;
    }

    public void setLotAtt11(String lotAtt11) {
        this.lotAtt11 = lotAtt11;
    }

    public String getLotAtt12() {
        return lotAtt12;
    }

    public void setLotAtt12(String lotAtt12) {
        this.lotAtt12 = lotAtt12;
    }

    public BigDecimal getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public BigDecimal getReceiptQty() {
        return receiptQty;
    }

    public void setReceiptQty(BigDecimal receiptQty) {
        this.receiptQty = receiptQty;
    }

    public String getScanNum() {
        return scanNum;
    }

    public void setScanNum(String scanNum) {
        this.scanNum = scanNum;
    }

    public String getCustOrderLineNum() {
        return custOrderLineNum;
    }

    public void setCustOrderLineNum(String custOrderLineNum) {
        this.custOrderLineNum = custOrderLineNum;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    public BigDecimal getItemSuiteQty() {
        return itemSuiteQty;
    }

    public void setItemSuiteQty(BigDecimal itemSuiteQty) {
        this.itemSuiteQty = itemSuiteQty;
    }

    public String getItemSuiteCode() {
        return itemSuiteCode;
    }

    public void setItemSuiteCode(String itemSuiteCode) {
        this.itemSuiteCode = itemSuiteCode;
    }

    public String getItemClass() {
        return itemClass;
    }

    public void setItemClass(String itemClass) {
        this.itemClass = itemClass;
    }

    public String getUserDef1() {
        return userDef1;
    }

    public void setUserDef1(String userDef1) {
        this.userDef1 = userDef1;
    }

    public String getUserDef2() {
        return userDef2;
    }

    public void setUserDef2(String userDef2) {
        this.userDef2 = userDef2;
    }

    public String getUserDef3() {
        return userDef3;
    }

    public void setUserDef3(String userDef3) {
        this.userDef3 = userDef3;
    }

    public String getUserDef4() {
        return userDef4;
    }

    public void setUserDef4(String userDef4) {
        this.userDef4 = userDef4;
    }

    public String getUserDef5() {
        return userDef5;
    }

    public void setUserDef5(String userDef5) {
        this.userDef5 = userDef5;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getStackLevel() {
        return stackLevel;
    }

    public void setStackLevel(String stackLevel) {
        this.stackLevel = stackLevel;
    }

    public BigDecimal getLayerQty() {
        return layerQty;
    }

    public void setLayerQty(BigDecimal layerQty) {
        this.layerQty = layerQty;
    }

    public boolean isNeedScan69() {
        return isNeedScan69;
    }

    public void setNeedScan69(boolean needScan69) {
        isNeedScan69 = needScan69;
    }

    public boolean isValidity() {
        return isValidity;
    }

    public void setValidity(boolean validity) {
        isValidity = validity;
    }

    public String getPeriodOfValidity() {
        return periodOfValidity;
    }

    public void setPeriodOfValidity(String periodOfValidity) {
        this.periodOfValidity = periodOfValidity;
    }

    public String getValidityUnit() {
        return validityUnit;
    }

    public void setValidityUnit(String validityUnit) {
        this.validityUnit = validityUnit;
    }

    public String getWhBarcode69() {
        return whBarcode69;
    }

    public void setWhBarcode69(String whBarcode69) {
        this.whBarcode69 = whBarcode69;
    }

    public String getInLifeDays() {
        return inLifeDays;
    }

    public void setInLifeDays(String inLifeDays) {
        this.inLifeDays = inLifeDays;
    }

    public String getOutLifeDays() {
        return outLifeDays;
    }

    public void setOutLifeDays(String outLifeDays) {
        this.outLifeDays = outLifeDays;
    }

    public String getInLifePercentage() {
        return inLifePercentage;
    }

    public void setInLifePercentage(String inLifePercentage) {
        this.inLifePercentage = inLifePercentage;
    }

    public String getOutLifePercentage() {
        return outLifePercentage;
    }

    public void setOutLifePercentage(String outLifePercentage) {
        this.outLifePercentage = outLifePercentage;
    }

    public BigDecimal getSerialQty() {
        return serialQty;
    }

    public void setSerialQty(BigDecimal serialQty) {
        this.serialQty = serialQty;
    }

    public boolean isNotScan() {
        return isNotScan;
    }

    public void setNotScan(boolean notScan) {
        isNotScan = notScan;
    }
}
