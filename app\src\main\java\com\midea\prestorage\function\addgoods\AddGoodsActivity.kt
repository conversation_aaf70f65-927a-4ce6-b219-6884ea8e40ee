package com.midea.prestorage.function.addgoods

import android.os.Bundle
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.viewpager.widget.ViewPager
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.mideadspda.module.electro.fragment.AddFinishFragment
import com.midea.mideadspda.module.electro.fragment.AddMyTaskFragment
import com.midea.mideadspda.module.electro.fragment.AddPoolFragment
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.PackageResp
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.sendcheck.ActivitySerialCheckReprintUnionBinding
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.TTSUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityAddGoodsBinding


/**
 * Created by LIXK5 on 2019/4/9.
 */
class AddGoodsActivity : BaseActivity(), View.OnClickListener {

    private lateinit var binding: ActivityAddGoodsUnionBinding
    lateinit var vm: AddGoodsVM
    private lateinit var titleView: List<LinearLayout>
    private var currentTitle: LinearLayout? = null
    val fragmentList = mutableListOf<Fragment>()
    private lateinit var putTaskPoolFragment: AddPoolFragment
    private lateinit var putMyTaskFragment: AddMyTaskFragment
    private lateinit var putFinishFragment: AddFinishFragment

    var packageResp: PackageResp? = null
    var batchResp: PackageResp? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityAddGoodsUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_add_goods_care
                )
            )
        } else {
            ActivityAddGoodsUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_add_goods
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        vm = AddGoodsVM(this)
        initView()
        binding.vm = vm
        TTSUtils.initTts(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        TTSUtils.shutdown()
        TTSUtils.stop()
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    private fun initView() {
        putTaskPoolFragment = AddPoolFragment.newInstance()
        putMyTaskFragment = AddMyTaskFragment.newInstance()
        putFinishFragment = AddFinishFragment.newInstance()
        fragmentList.add(putTaskPoolFragment)
        fragmentList.add(putMyTaskFragment)
        fragmentList.add(putFinishFragment)

        titleView = mutableListOf(binding.llWaitSend, binding.llWaitSign, binding.llSign)
        titleView.forEach {
            it.setOnClickListener(this)
        }
        currentTitle = binding.llWaitSend

        binding.vp.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                setTitleUnChecked(currentTitle!!)
                setTitleChecked(titleView[position])
                setTitleChange(titleView[position])
                (fragmentList[binding.vp.currentItem] as OnRightClick).onPageEnter()
            }

            override fun onPageScrollStateChanged(state: Int) {}
        })

        binding.vp.adapter = MyAdapter(supportFragmentManager)
        binding.vp.offscreenPageLimit = 2
    }

    inner class MyAdapter(fm: FragmentManager?) : FragmentPagerAdapter(fm!!) {
        override fun getItem(position: Int): Fragment {
            return fragmentList[position]
        }

        override fun getCount(): Int {
            return fragmentList.size
        }
    }

    fun setPoolDataNum(num: Int) {
        vm.poolDataNum.set("任务池($num)")
    }

    fun setMyDataNum(num: Int) {
        vm.myDataNum.set("我的任务($num)")
    }

    fun setCompleteDataNum(num: Int) {
        vm.completeDataNum.set("已完成($num)")
    }

    /**
     * description 设置标题选中
     * author: wangqin
     * date: 2020/7/22
     */
    private fun setTitleChecked(linearLayout: LinearLayout) {
        val tvCheck = linearLayout.getChildAt(0) as TextView
        val line = linearLayout.getChildAt(1)
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            tvCheck.setTextColor(ContextCompat.getColor(this, R.color.colorWhite))
        } else {
            tvCheck.setTextColor(ContextCompat.getColor(this, R.color.black))
        }
        line.visibility = View.VISIBLE
    }

    /**
     * description 设置标题未选中
     * author: wangqin
     * date: 2020/7/22
     */
    private fun setTitleUnChecked(linearLayout: LinearLayout) {
        val tvCheck = linearLayout.getChildAt(0) as TextView
        val line = linearLayout.getChildAt(1)
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            tvCheck.setTextColor(ContextCompat.getColor(this, R.color.colorWhite_care))
        } else {
            tvCheck.setTextColor(ContextCompat.getColor(this, R.color.colorGrayTitle))
        }
        line.visibility = View.INVISIBLE
    }

    override fun onClick(v: View?) {
        binding.vp.currentItem = Integer.parseInt(v!!.tag as String)
    }

    /**
     * description 转换title状态
     * author: wangqin
     * date: 2020/6/15
     */
    private fun setTitleChange(v: LinearLayout) {
        currentTitle = v
    }

    interface OnRightClick {
        fun onRightClick()
        fun onPageEnter()
    }

    interface OnUnitInitOk {
        fun unitOk()
    }
}