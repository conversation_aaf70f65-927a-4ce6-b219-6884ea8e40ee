package com.midea.prestorage.function.barcode

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelProvider.AndroidViewModelFactory
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.utils.AppUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityBarcodeOrderListBinding

class BarcodeOrderListActivity : BaseViewModelActivity<BarcodeOrderListViewModel>() {

    private lateinit var binding: ActivityBarcodeOrderListBinding
    private var adapter: BarcodeOrderListAdapter? = null

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_barcode_order_list)
        immersionBar {
            titleBarMarginTop(binding.clTitleLayout)
        }
        vm = ViewModelProvider(this, AndroidViewModelFactory(application)).get(
            BarcodeOrderListViewModel::class.java
        )
        binding.vm = vm
        binding.lifecycleOwner = this
        initView()
    }

    override fun onResume() {
        super.onResume()
        vm.refresh()
    }

    private fun initView() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        binding.rv.apply {
            <EMAIL> = BarcodeOrderListAdapter()
            adapter = <EMAIL>
        }
        adapter?.setOnItemClickListener { _, _, position ->
            vm.queryBarcodeCollectionInfo(adapter?.data?.getOrNull(position)?.cjNo ?: "")
        }
        vm.orderListLiveData.observe(this) {
            adapter?.setList(it)
        }
        vm.toBarcodeCollectionPage.observe(this) {
            startActivity(BarcodeCollectionActivity.newIntent(this, it, vm.sumTotal.value!!))
        }
        AppUtils.requestFocus(binding.etCustItemCode)
    }

}