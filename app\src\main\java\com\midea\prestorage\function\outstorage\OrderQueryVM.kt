package com.midea.prestorage.function.outstorage

import android.app.Application
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.ContainerPickDetailListV2
import com.midea.prestorage.function.outstorage.response.RespOrderQuery
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody
import java.util.*

class OrderQueryVM(application: Application) : BaseViewModel(application) {
    var searchType = ObservableField("客户订单号")
    var curCode = ObservableField("")
    val isNoData = ObservableBoolean(false)
    var showDatas = MutableLiveData<MutableList<RespOrderQuery>>()

    override fun init() {

    }

    fun onEnterCode() {
        if (CheckUtil.isFastDoubleClick()) {
            if (curCode.get().toString().trim().isNullOrEmpty()) {
                if ("客户订单号" == searchType.get().toString()) {
                    showNotification("客户订单号不能为空", false)
                    return
                }else {
                    showNotification("出库单号不能为空", false)
                    return
                }
            }
            initList()
        }
    }

    private fun initList() {

        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            var custOrderNo: String? = null
            var shipmentCode: String? = null
            if ("客户订单号" == searchType.get().toString()) {
                custOrderNo = curCode.get().toString().trim()
            }else {
                shipmentCode = curCode.get().toString().trim()
            }
            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getOutStorageAPI().orderQuery(Constants.whInfo?.whCode, custOrderNo, shipmentCode)
            }

            if(result.code == 0L) {
                if (result.data.isNullOrEmpty()) {
                    isNoData.set(true)
                } else {
                    isNoData.set(false)
                    showDatas.value = result.data!!
                }
            }else {
                curCode.set("")
                isNoData.set(true)
                result.msg?.let {showNotification(it, false)}
            }
        }
    }

    fun clearCode() {
        curCode.set("")
    }
}