package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;
import java.math.BigDecimal;

public class SerialCheckInfo implements Serializable {

    @ShowAnnotation
    private int index;
    @ShowAnnotation
    private String qtyInfo;
    @ShowAnnotation
    private int status;
    @ShowAnnotation
    private String custItemCode;
    @ShowAnnotation
    private double pickExpQty;
    @ShowAnnotation
    private String lotAtt01;
    @ShowAnnotation
    private String itemName;

    private String id;
    private String createTime;
    private String updateTime;
    private String tenantCode;
    private String createUserCode;
    private String createUserName;
    private String updateUserCode;
    private String updateUserName;
    private String remark;
    private String version;
    private String deleteFlag;
    private String pageNo;
    private String pageSize;
    private String offset;
    private String orderBy;
    private String orderByType;
    private String ids;
    private String tenantCodes;
    private String count;
    private String startTime;
    private String endTime;
    private String taskCode;
    private String taskType;
    private String whCode;
    private String ownerCode;
    private String itemCode;
    private double fromQty;
    private String packageAllocQty;
    private String packageAllocUm;
    private double toQty;
    private String fromZone;
    private String fromLoc;
    private String toLoc;
    private String lotNum;
    private String referenceCode;
    private String referenceLineId;
    private String referenceReqId;
    private String traceId;
    private String taskStartTime;
    private String taskEndTime;
    private String pickSeqNum;
    private String backTaskId;
    private String shipToCustomerCode;
    private String shipToCustomerName;
    private String taskDetailId;
    private String whBarcode69;
    private String csBarcode69;
    private String unit;
    private String lotAtt02;
    private String lotAtt03;
    private String lotAtt04;
    private String lotAtt05;
    private BigDecimal packagePara;
    private String taskHeaderIds;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getPageNo() {
        return pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getOffset() {
        return offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderByType() {
        return orderByType;
    }

    public void setOrderByType(String orderByType) {
        this.orderByType = orderByType;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getTenantCodes() {
        return tenantCodes;
    }

    public void setTenantCodes(String tenantCodes) {
        this.tenantCodes = tenantCodes;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getPackageAllocQty() {
        return packageAllocQty;
    }

    public void setPackageAllocQty(String packageAllocQty) {
        this.packageAllocQty = packageAllocQty;
    }

    public String getPackageAllocUm() {
        return packageAllocUm;
    }

    public void setPackageAllocUm(String packageAllocUm) {
        this.packageAllocUm = packageAllocUm;
    }

    public String getFromZone() {
        return fromZone;
    }

    public void setFromZone(String fromZone) {
        this.fromZone = fromZone;
    }

    public String getFromLoc() {
        return fromLoc;
    }

    public void setFromLoc(String fromLoc) {
        this.fromLoc = fromLoc;
    }

    public String getToLoc() {
        return toLoc;
    }

    public void setToLoc(String toLoc) {
        this.toLoc = toLoc;
    }

    public String getLotNum() {
        return lotNum;
    }

    public void setLotNum(String lotNum) {
        this.lotNum = lotNum;
    }

    public String getReferenceCode() {
        return referenceCode;
    }

    public void setReferenceCode(String referenceCode) {
        this.referenceCode = referenceCode;
    }

    public String getReferenceLineId() {
        return referenceLineId;
    }

    public void setReferenceLineId(String referenceLineId) {
        this.referenceLineId = referenceLineId;
    }

    public String getReferenceReqId() {
        return referenceReqId;
    }

    public void setReferenceReqId(String referenceReqId) {
        this.referenceReqId = referenceReqId;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getTaskStartTime() {
        return taskStartTime;
    }

    public void setTaskStartTime(String taskStartTime) {
        this.taskStartTime = taskStartTime;
    }

    public String getTaskEndTime() {
        return taskEndTime;
    }

    public void setTaskEndTime(String taskEndTime) {
        this.taskEndTime = taskEndTime;
    }

    public String getPickSeqNum() {
        return pickSeqNum;
    }

    public void setPickSeqNum(String pickSeqNum) {
        this.pickSeqNum = pickSeqNum;
    }

    public String getBackTaskId() {
        return backTaskId;
    }

    public void setBackTaskId(String backTaskId) {
        this.backTaskId = backTaskId;
    }

    public String getShipToCustomerCode() {
        return shipToCustomerCode;
    }

    public void setShipToCustomerCode(String shipToCustomerCode) {
        this.shipToCustomerCode = shipToCustomerCode;
    }

    public String getShipToCustomerName() {
        return shipToCustomerName;
    }

    public void setShipToCustomerName(String shipToCustomerName) {
        this.shipToCustomerName = shipToCustomerName;
    }

    public String getTaskDetailId() {
        return taskDetailId;
    }

    public void setTaskDetailId(String taskDetailId) {
        this.taskDetailId = taskDetailId;
    }

    public String getWhBarcode69() {
        return whBarcode69;
    }

    public void setWhBarcode69(String whBarcode69) {
        this.whBarcode69 = whBarcode69;
    }

    public String getCsBarcode69() {
        return csBarcode69;
    }

    public void setCsBarcode69(String csBarcode69) {
        this.csBarcode69 = csBarcode69;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getLotAtt01() {
        return lotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        this.lotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return lotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        this.lotAtt02 = lotAtt02;
    }

    public String getLotAtt03() {
        return lotAtt03;
    }

    public void setLotAtt03(String lotAtt03) {
        this.lotAtt03 = lotAtt03;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getLotAtt05() {
        return lotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        this.lotAtt05 = lotAtt05;
    }

    public String getTaskHeaderIds() {
        return taskHeaderIds;
    }

    public void setTaskHeaderIds(String taskHeaderIds) {
        this.taskHeaderIds = taskHeaderIds;
    }

    public double getFromQty() {
        return fromQty;
    }

    public void setFromQty(double fromQty) {
        this.fromQty = fromQty;
    }

    public double getToQty() {
        return toQty;
    }

    public void setToQty(double toQty) {
        this.toQty = toQty;
    }

    public double getPickExpQty() {
        return pickExpQty;
    }

    public void setPickExpQty(double pickExpQty) {
        this.pickExpQty = pickExpQty;
    }

    public String getQtyInfo() {
        return qtyInfo;
    }

    public void setQtyInfo(String qtyInfo) {
        this.qtyInfo = qtyInfo;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public BigDecimal getPackagePara() {
        return packagePara;
    }

    public void setPackagePara(BigDecimal packagePara) {
        this.packagePara = packagePara;
    }
}