package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.utils.AppUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

public class Put<PERSON>ontainerInfo implements Serializable {
    @ShowAnnotation
    private String containerCode;
    @ShowAnnotation
    private String skuQty;
    @ShowAnnotation
    private String checkBy;
    @ShowAnnotation
    private String statusStr;

    private String status;
    private String receiptBy;
    private BigDecimal qty;
    private BigDecimal checkQty;
    private Map<String, String> recommendLocations;
    private Map<String, String> recommendZones;
    private Map<String, String> recommendAreas;

    public String getContainerCode() {
        return containerCode;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReceiptBy() {
        return receiptBy;
    }

    public void setReceiptBy(String receiptBy) {
        this.receiptBy = receiptBy;
    }

    public String getCheckBy() {
        return checkBy;
    }

    public void setCheckBy(String checkBy) {
        this.checkBy = checkBy;
    }

    public BigDecimal getQty() {
        return AppUtils.getBigDecimalValue(qty);
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getCheckQty() {
        return AppUtils.getBigDecimalValue(checkQty);
    }

    public void setCheckQty(BigDecimal checkQty) {
        this.checkQty = checkQty;
    }

    public String getSkuQty() {
        return skuQty;
    }

    public void setSkuQty(String skuQty) {
        this.skuQty = skuQty;
    }

    public Map<String, String> getRecommendLocations() {
        return recommendLocations;
    }

    public void setRecommendLocations(Map<String, String> recommendLocations) {
        this.recommendLocations = recommendLocations;
    }

    public Map<String, String> getRecommendZones() {
        return recommendZones;
    }

    public void setRecommendZones(Map<String, String> recommendZones) {
        this.recommendZones = recommendZones;
    }

    public Map<String, String> getRecommendAreas() {
        return recommendAreas;
    }

    public void setRecommendAreas(Map<String, String> recommendAreas) {
        this.recommendAreas = recommendAreas;
    }

    public String getStatusStr() {
        if ("300".equals(status)) {
            return "待上架";
        } else {
            return "上架中";
        }
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }
}
