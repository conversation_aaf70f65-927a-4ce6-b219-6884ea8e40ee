package com.midea.prestorage.function.outstorage

import android.widget.EditText
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.LifecycleOwner
import com.midea.prestoragesaas.databinding.ActivityOutStorageBackTaskDetailBinding
import com.midea.prestoragesaas.databinding.ActivityOutStorageBackTaskDetailCareBinding

sealed class ActivityOutStorageBackTaskDetailUnionBinding{
    abstract var vm: OutStorageBackTaskDetailViewModel?
    abstract val clTitleLayout: ConstraintLayout
    abstract val etLoc: EditText
    abstract val etQty4: EditText
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: ActivityOutStorageBackTaskDetailCareBinding) : ActivityOutStorageBackTaskDetailUnionBinding() {
        override var vm: OutStorageBackTaskDetailViewModel?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val clTitleLayout = binding.clTitleLayout
        override val etLoc = binding.etLoc
        override val etQty4 = binding.etQty4
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: ActivityOutStorageBackTaskDetailBinding) : ActivityOutStorageBackTaskDetailUnionBinding() {
        override var vm: OutStorageBackTaskDetailViewModel?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val clTitleLayout = binding.clTitleLayout
        override val etLoc = binding.etLoc
        override val etQty4 = binding.etQty4
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
