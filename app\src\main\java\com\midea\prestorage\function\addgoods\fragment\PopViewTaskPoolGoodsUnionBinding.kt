package com.midea.prestorage.function.addgoods.fragment

import android.view.View
import android.widget.*
import com.midea.prestoragesaas.databinding.PopViewTaskPoolGoodsBinding
import com.midea.prestoragesaas.databinding.PopViewTaskPoolGoodsCareBinding

sealed class PopViewTaskPoolGoodsUnionBinding{
    abstract val root: View
    abstract val tvOpenLaunchArea: TextView
    abstract val linOpenLaunchClose: LinearLayout
    abstract val edGoodsInfo: EditText
    abstract val tvDayNum: TextView
    abstract val relaOpenLaunchDate: RelativeLayout
    abstract val relaOpenLaunchArea: RelativeLayout
    abstract val imgClean: ImageView
    abstract val linOpenLaunchSelectCom: LinearLayout
    abstract val ivOpenLaunchSelectCom: ImageView
    abstract val tvCheckName: TextView

    class V2(val binding: PopViewTaskPoolGoodsCareBinding) : PopViewTaskPoolGoodsUnionBinding() {

        override val root = binding.root
        override val tvOpenLaunchArea = binding.tvOpenLaunchArea
        override val linOpenLaunchClose = binding.linOpenLaunchClose
        override val edGoodsInfo = binding.edGoodsInfo
        override val tvDayNum = binding.tvDayNum
        override val relaOpenLaunchDate = binding.relaOpenLaunchDate
        override val relaOpenLaunchArea = binding.relaOpenLaunchArea
        override val imgClean = binding.imgClean
        override val linOpenLaunchSelectCom = binding.linOpenLaunchSelectCom
        override val ivOpenLaunchSelectCom = binding.ivOpenLaunchSelectCom
        override val tvCheckName = binding.tvCheckName
    }

    class V1(val binding: PopViewTaskPoolGoodsBinding) : PopViewTaskPoolGoodsUnionBinding() {

        override val root = binding.root
        override val tvOpenLaunchArea = binding.tvOpenLaunchArea
        override val linOpenLaunchClose = binding.linOpenLaunchClose
        override val edGoodsInfo = binding.edGoodsInfo
        override val tvDayNum = binding.tvDayNum
        override val relaOpenLaunchDate = binding.relaOpenLaunchDate
        override val relaOpenLaunchArea = binding.relaOpenLaunchArea
        override val imgClean = binding.imgClean
        override val linOpenLaunchSelectCom = binding.linOpenLaunchSelectCom
        override val ivOpenLaunchSelectCom = binding.ivOpenLaunchSelectCom
        override val tvCheckName = binding.tvCheckName
    }
}
