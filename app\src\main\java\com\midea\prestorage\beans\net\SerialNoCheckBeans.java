package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;
import java.math.BigDecimal;

public class SerialNoCheckBeans implements Serializable {

    @ShowAnnotation
    private String updateTime;
    @ShowAnnotation
    private String updateUserName;
    @ShowAnnotation
    private String serialCs;
    @ShowAnnotation
    private String serialPl;
    @ShowAnnotation
    private String custItemCode;
    @ShowAnnotation
    private String itemName;
    @ShowAnnotation
    private String whName;
    @ShowAnnotation
    private String lotAtt02;
    @ShowAnnotation
    private String lotAtt04Str;
    @ShowAnnotation
    private String lotAtt05;
    @ShowAnnotation
    private BigDecimal qty;
    @ShowAnnotation
    private String referenceCode;
    @ShowAnnotation
    private String operateTypeStr;
    @ShowAnnotation
    private String whCode;
    @ShowAnnotation
    private String serialNo;

    private String lotAtt04;
    private String createTime;
    private String createUserCode;
    private String createUserName;
    private String updateUserCode;
    private String version;
    private String deleteFlag;
    private String pageSize;
    private String referenceDetailId;
    private String operateType;
    private String operateNo;
    private String itemCode;
    private String lotNum;
    private String lotAtt01;

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getReferenceCode() {
        return referenceCode;
    }

    public void setReferenceCode(String referenceCode) {
        this.referenceCode = referenceCode;
    }

    public String getReferenceDetailId() {
        return referenceDetailId;
    }

    public void setReferenceDetailId(String referenceDetailId) {
        this.referenceDetailId = referenceDetailId;
    }

    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }

    public String getOperateNo() {
        return operateNo;
    }

    public void setOperateNo(String operateNo) {
        this.operateNo = operateNo;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getSerialCs() {
        return serialCs;
    }

    public void setSerialCs(String serialCs) {
        this.serialCs = serialCs;
    }

    public String getSerialPl() {
        return serialPl;
    }

    public void setSerialPl(String serialPl) {
        this.serialPl = serialPl;
    }

    public int getQty() {
        if (qty == null) {
            return 0;
        }
        return qty.intValue();
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getWhName() {
        return whName;
    }

    public void setWhName(String whName) {
        this.whName = whName;
    }

    public String getLotNum() {
        return lotNum;
    }

    public void setLotNum(String lotNum) {
        this.lotNum = lotNum;
    }

    public String getLotAtt01() {
        return lotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        this.lotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return lotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        this.lotAtt02 = lotAtt02;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getLotAtt05() {
        return lotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        this.lotAtt05 = lotAtt05;
    }

    public void setLotAtt04Str(String lotAtt04Str) {
        this.lotAtt04Str = lotAtt04Str;
    }

    public String getLotAtt04Str() {
        return lotAtt04Str;
    }

    public void setOperateTypeStr(String operateTypeStr) {
        this.operateTypeStr = operateTypeStr;
    }

    public String getOperateTypeStr() {
        return operateTypeStr;
    }
}
