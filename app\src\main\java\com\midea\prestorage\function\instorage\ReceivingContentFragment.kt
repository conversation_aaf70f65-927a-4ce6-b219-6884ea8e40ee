package com.midea.prestorage.function.instorage

import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.bigkoo.pickerview.TimePickerView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseViewModelFragment
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickPositionAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestoragesaas.databinding.FragmentReceivingContentBinding
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.instorage.dialog.LocDialog
import com.midea.prestorage.function.instorage.response.ReceivingDetailsData
import com.midea.prestorage.function.instorage.response.RespNoReceived
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import kotlinx.coroutines.*
import okhttp3.Dispatcher
import java.text.SimpleDateFormat
import androidx.lifecycle.Observer
import java.util.*

class ReceivingContentFragment(var itemInfo: RespNoReceived) :
    BaseViewModelFragment<ReceivingContentVM>() {
    private lateinit var binding: FragmentReceivingContentBinding
    var adapter = ReceivingDetailsAdapter()
    var format = SimpleDateFormat("yyyy-MM-dd")
    private lateinit var statueDialog: FilterDialog
    val lot4Options = mutableListOf<String>()
    private var locDialog: LocDialog? = null
    private var productionDate = 0
    private var expirationDate = 0

    override fun beforeOnCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        vm = ViewModelProvider.AndroidViewModelFactory(requireActivity().application)
            .create(ReceivingContentVM::class.java)
        binding = DataBindingUtil.inflate(
            inflater,
            R.layout.fragment_receiving_content,
            container,
            false
        )
        binding.vm = vm
        binding.lifecycleOwner = this

        vm.itemCode.set(itemInfo.custItemCode)
        vm.totalQty.set(AppUtils.getBigDecimalValueStr(itemInfo.totalQty))
        vm.itemName.set(itemInfo.itemName)

        vm.lotAttStr.observe(this, Observer<MutableList<String>> {
            addStatueData(it)
            statueDialog.show()
        })

        initRecycleView()
        initSpinner()

        return binding.root
    }

    fun initRecycleView() {
        binding.recyclerView.layoutManager = LinearLayoutManager(activity)
        //binding.recyclerView.addItemDecoration(DividerItemDecoration(activity, DividerItemDecoration.VERTICAL))
        binding.recyclerView.adapter = adapter

        //  点击监听
        adapter.setOnCheckListener { it, position ->
            when (it.itemType) {
                "1" -> {
                    if ("LOT_ATT04" == it.lotAtt) {
                        initDialog(position)
                        addStatueData(lot4Options)
                        statueDialog.show()
                    }else {
                        initDialog(position)
                        if(it.info.key.isNullOrBlank()) {
                            val beans = mutableListOf<BaseItemShowInfo>()
                            statueDialog.addAllData(beans)
                            statueDialog.show()
                        }else {
                            vm.getLotAttStr(it.info.key)
                        }
                    }
                }
                "2" -> {
                    showTimePick(position)
                }
                "4" -> {
                    //ToastUtilsCare.toastBig(activity, datas[position].itemValue, Toast.LENGTH_SHORT)
                    //itemName = datas[position].itemValue
                }
                "3" -> {
                    initLocDialog(position)
                    locDialog?.show()
                }
            }
        }

        adapter.addChildClickViewIds(R.id.btn_01, R.id.btn_02, R.id.btn_03, R.id.btn_04)
        adapter.setOnItemChildClickListener { adapter, view, position ->
            itemInfo.datas[itemInfo.datas.lastIndex].itemValue = (view as Button).text as String?
            adapter.notifyDataSetChanged()
        }

        if (itemInfo.datas.size > 0) {
            adapter.addData(itemInfo.datas)
            adapter.notifyDataSetChanged()
        } else {
            itemInfo.cdWhLotDetail?.forEachIndexed { index, it ->
                if ("LOT_ATT04" == it.lotAtt) {
                    when (it.fieldType) {
                        "PopField" -> {
                            itemInfo.datas.add(0, ReceivingDetailsData("1", it.title, getLotAttStr(itemInfo.lotAtt04), it.lotAtt, it))
                        }
                        "ComboBox" -> {
                            itemInfo.datas.add(0, ReceivingDetailsData("1", it.title, getLotAttStr(itemInfo.lotAtt04), it.lotAtt, it))
                        }
                        "DateField" -> {
                            itemInfo.datas.add(0, ReceivingDetailsData("2", it.title, getLotAttStr(itemInfo.lotAtt04), it.lotAtt, it))
                        }
                        "DateTimeField" -> {
                            itemInfo.datas.add(0, ReceivingDetailsData("2", it.title, getLotAttStr(itemInfo.lotAtt04), it.lotAtt, it))
                        }
                        "TextInput" -> {
                            itemInfo.datas.add(0, ReceivingDetailsData("4", it.title, getLotAttStr(itemInfo.lotAtt04), it.lotAtt, it))
                        }
                        else -> {
                            itemInfo.datas.add(0, ReceivingDetailsData("4", it.title, getLotAttStr(itemInfo.lotAtt04), it.lotAtt, it))
                        }
                    }
                } else {
                    when (it.fieldType) {
                        "PopField" -> {
                            itemInfo.datas.add(ReceivingDetailsData("1", it.title, getlotAtt(it), it.lotAtt, it))
                        }
                        "ComboBox" -> {
                            itemInfo.datas.add(ReceivingDetailsData("1", it.title, getlotAtt(it), it.lotAtt, it))
                        }
                        "DateField" -> {
                            itemInfo.datas.add(ReceivingDetailsData("2", it.title, getlotAtt(it), it.lotAtt, it))
                        }
                        "DateTimeField" -> {
                            itemInfo.datas.add(ReceivingDetailsData("2", it.title, getlotAtt(it), it.lotAtt, it))
                        }
                        "TextInput" -> {
                            itemInfo.datas.add(ReceivingDetailsData("4", it.title, getlotAtt(it), it.lotAtt, it))
                        }
                        else -> {
                            itemInfo.datas.add(ReceivingDetailsData("4", it.title, getlotAtt(it), it.lotAtt, it))
                        }
                    }
                }
            }
            //下面这3个是固定在最下面的
            itemInfo.datas.add(
                ReceivingDetailsData(
                    "4",
                    "收货数量",
                    AppUtils.getBigDecimalValueStr(itemInfo.totalQty),
                    "QTY",
                    null
                )
            )
            itemInfo.datas.add(ReceivingDetailsData("5", "推荐库位", "", "RECO", null))
            itemInfo.datas.add(ReceivingDetailsData("4", "上架库位", "", "PLANLOC", null))

            adapter.addData(itemInfo.datas)
            adapter.notifyDataSetChanged()
        }

    }

    fun getLotAttStr(lotAtt: String?): String {
        if (lotAtt == null) {
            return ""
        }
        DCUtils.goodsStatue?.forEach {
            if (it.value == lotAtt) {
                return it.key.toString()
            }
        }
        return ""
    }

    private fun getlotAtt(data: RespNoReceived.CdWhLotDetailDTO): String {
        when (data.lotAtt) {
            "LOT_ATT01" -> {
                if (itemInfo.lotAtt01 != null) {
                    return if (itemInfo.lotAtt01.length > 10) {
                        itemInfo.lotAtt01.substring(0, 10)
                    } else {
                        itemInfo.lotAtt01
                    }
                }
            }
            "LOT_ATT02" -> {
                if (itemInfo.lotAtt02 != null) {
                    return if (itemInfo.lotAtt02.length > 10) {
                        itemInfo.lotAtt02.substring(0, 10)
                    } else {
                        itemInfo.lotAtt02
                    }
                }
            }
            "LOT_ATT03" -> {
                if (itemInfo.lotAtt03 != null && !TextUtils.isEmpty(itemInfo.lotAtt03)) {
                    return if (itemInfo.lotAtt03.length > 10) {
                        itemInfo.lotAtt03.substring(0, 10)
                    } else {
                        itemInfo.lotAtt03
                    }
                } else {
                    val date = Date()
                    return format.format(date)
                }
            }
            "LOT_ATT04" -> {
                if (itemInfo.lotAtt04 != null) {
                    return itemInfo.lotAtt04
                }
            }
            "LOT_ATT05" -> {
                if (itemInfo.lotAtt05 != null) {
                    return itemInfo.lotAtt05
                }
            }
            "LOT_ATT06" -> {
                if (itemInfo.lotAtt06 != null) {
                    return itemInfo.lotAtt06
                }
            }
            "LOT_ATT07" -> {
                if (itemInfo.lotAtt07 != null) {
                    return itemInfo.lotAtt07
                }
            }
            "LOT_ATT08" -> {
                if (itemInfo.lotAtt08 != null) {
                    return itemInfo.lotAtt08
                }
            }
            "LOT_ATT09" -> {
                if (itemInfo.lotAtt09 != null) {
                    return itemInfo.lotAtt09
                }
            }
            "LOT_ATT10" -> {
                if (itemInfo.lotAtt10 != null) {
                    return itemInfo.lotAtt10
                }
            }
            "LOT_ATT11" -> {
                if (itemInfo.lotAtt11 != null) {
                    return itemInfo.lotAtt11
                }
            }
            "LOT_ATT12" -> {
                if (itemInfo.lotAtt12 != null) {
                    return itemInfo.lotAtt12
                }
            }

        }
        return ""
    }

    private fun initDialog(position: Int) {
        //商品状态dialog
        statueDialog = FilterDialog(activity as RxAppCompatActivity)
        statueDialog.setTitle("请选择" + itemInfo.datas[position].itemName)
        statueDialog.dismissEdit()
        statueDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            vm.statueStr.value = it.showInfo
            vm.curSelectLot4Name = it.showInfo
            itemInfo.datas[position].itemValue = it.showInfo
            adapter.notifyItemChanged(position)
            statueDialog.dismiss()
        })

    }

    private fun initLocDialog(position: Int) {
        locDialog = LocDialog(activity as RxAppCompatActivity)
        locDialog?.setOnItemClick(object : LocDialog.LocBack {
            override fun locBack(locCode: String) {
                itemInfo.datas[itemInfo.datas.lastIndex].itemValue = locCode
                adapter.notifyItemChanged(position)
                locDialog?.dismiss()
            }
        })
    }

    // 商品库存状态 下拉框
    private fun initSpinner() {

        DCUtils.goodsStatue(activity as RxAppCompatActivity, object : DCUtils.DCBack {

            override fun dcBack(statusDC: MutableList<DCBean>) {
                statusDC.forEach {
                    lot4Options.add(it.key)
                }

                //默认选第一个
                if (lot4Options.size > 0) {
                    binding.vm!!.curSelectLot4Name = lot4Options[0]
                    binding.vm!!.statueStr.value = lot4Options[0]
                }

                //addStatueData(lot4Options)
            }
        })
    }

    private fun addStatueData(data: MutableList<String>) {
        val beans = mutableListOf<BaseItemShowInfo>()
        data.forEach {
            beans.add(BaseItemShowInfo(it))
        }
        statueDialog.addAllData(beans)
    }

    private fun showTimePick(position: Int) {
        val pvTime = TimePickerView.Builder(
            activity,
            TimePickerView.OnTimeSelectListener { date2, _ ->
                itemInfo.datas[position].itemValue = format.format(date2)
                adapter.notifyItemChanged(position)
                if (itemInfo.isValidity == "Y" && itemInfo.periodOfValidity != null) {//判断是否开启了自动计算失效日期的功能
                    if (position == productionDate && expirationDate != 0) {//判断是否选择了生产日期，则动态更新失效日期
                        val rightNow = Calendar.getInstance()
                        rightNow.time = date2
                        rightNow.add(
                            Calendar.DAY_OF_YEAR,
                            itemInfo.unitDay * AppUtils.getBigDecimalValueStr(itemInfo.periodOfValidity)
                                .toInt()
                        )
                        val time = rightNow.time
                        itemInfo.datas[expirationDate].itemValue = format.format(time)
                        adapter.notifyItemChanged(expirationDate)
                    } else if (position == expirationDate && productionDate != 0) {//判断是否选择了失效日期，则动态更新生产日期
                        val rightNow = Calendar.getInstance()
                        rightNow.time = date2
                        rightNow.add(
                            Calendar.DAY_OF_YEAR,
                            -(itemInfo.unitDay * AppUtils.getBigDecimalValueStr(itemInfo.periodOfValidity)
                                .toInt())
                        )
                        val time = rightNow.time
                        itemInfo.datas[productionDate].itemValue = format.format(time)
                        adapter.notifyItemChanged(productionDate)
                    }
                }
            })
        pvTime.setType(TimePickerView.Type.YEAR_MONTH_DAY)//默认全部显示
            .setLabel("年", "月", "日", ":", ":", "")
            .setCancelText("取消")//取消按钮文字
            .setSubmitText("确定")//确认按钮文字
            .setSubCalSize(14)
            .setContentSize(14)//滚轮文字大小
            .setTitleSize(14)//标题文字大小
            .setTitleColor(ContextCompat.getColor(requireActivity(), R.color.title))//标题文字颜色
            .setOutSideCancelable(true)//点击屏幕，点在控件外部范围时，是否取消显示
            .setTitleText(itemInfo.datas[position].itemName)
            .setTextColorOut(ContextCompat.getColor(requireActivity(), R.color.ui_font_color_tag))
            .setTextColorCenter(ContextCompat.getColor(requireActivity(), R.color.title))//设置选中项的颜色
            .setSubmitColor(
                ContextCompat.getColor(
                    requireActivity(),
                    R.color.button_blue
                )
            )//确定按钮文字颜色
            .setCancelColor(
                ContextCompat.getColor(
                    requireActivity(),
                    R.color.in_send_all
                )
            )//取消按钮文字颜色
            .setTitleBgColor(
                ContextCompat.getColor(
                    requireActivity(),
                    R.color.colorPrimary
                )
            )//标题背景颜色 Night mode
            .setBgColor(ContextCompat.getColor(requireActivity(), R.color.pv_bg))//滚轮背景颜色 Night mode
            .isDialog(false)
            .setDate(Calendar.getInstance())
            .isCenterLabel(false) //是否只显示中间选中项的label文字，false则每项item全部都带有label。
            .build()
        pvTime.build().show()

        initDate()
    }

    fun initDate() {
        itemInfo.cdWhLotDetail?.forEachIndexed { index, it ->
            if ("DateField" == it.fieldType) {
                if ("LOT_ATT01" == it.lotAtt) { //标识生产日期的index
                    productionDate = index + 1
                }
                if ("LOT_ATT02" == it.lotAtt) { //标识失效日期的index
                    expirationDate = index + 1
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()

        binding.recyclerView.scrollToPosition(adapter.data.size - 1)
        binding.recyclerView.post {
            adapter.getViewByPosition(adapter.data.size - 1, R.id.edit_text)
                ?.findViewById<EditText>(R.id.edit_text)?.requestFocus()
        }
    }

    class ReceivingDetailsAdapter :
        ListChoiceClickPositionAdapter<ReceivingDetailsData>(R.layout.item_receiving_details) {

        override fun convert(holder: BaseViewHolder, item: ReceivingDetailsData) {
            super.convert(holder, item)

            holder.setText(R.id.tv_name, item.itemName)

            when (item.itemType) {
                "1" -> {
                    holder.setGone(R.id.tv_choose, false)
                    holder.setText(R.id.tv_choose, item.itemValue)
                    holder.setGone(R.id.tv_word, true)
                    holder.setGone(R.id.edit_text, true)
                    holder.setGone(R.id.ll_btn, true)
                    holder.setGone(R.id.iv_riqi, true)
                    holder.setGone(R.id.iv_bianji, true)
                }
                "2" -> {
                    holder.setGone(R.id.tv_choose, true)
                    holder.setGone(R.id.tv_word, false)
                    holder.setText(R.id.tv_word, item.itemValue)
                    holder.setGone(R.id.edit_text, true)
                    holder.setGone(R.id.ll_btn, true)
                    holder.setGone(R.id.iv_riqi, false)
                    holder.setGone(R.id.iv_bianji, true)
                }
                "3" -> {
                    holder.setGone(R.id.tv_choose, true)
                    holder.setGone(R.id.tv_word, false)
                    holder.setText(R.id.tv_word, item.itemValue)
                    holder.setGone(R.id.edit_text, true)
                    holder.setGone(R.id.ll_btn, true)
                    holder.setGone(R.id.iv_riqi, true)
                    holder.setGone(R.id.iv_bianji, true)
                }
                "4" -> {
                    holder.setGone(R.id.tv_choose, true)
                    holder.setGone(R.id.tv_word, true)
                    holder.setGone(R.id.edit_text, false)
                    holder.setText(R.id.edit_text, item.itemValue)
                    holder.setGone(R.id.ll_btn, true)
                    holder.setGone(R.id.iv_riqi, true)
                    holder.setGone(R.id.iv_bianji, false)

                    val editText: EditText = holder.getView(R.id.edit_text)
                    val container: LinearLayout = holder.getView(R.id.ll_tag)
                    if (editText.tag is TextWatcher) {
                        editText.removeTextChangedListener(editText.tag as TextWatcher)
                    }
                    editText.setText(item.itemValue)

                    val watcher: TextWatcher = object : TextWatcher {
                        override fun beforeTextChanged(
                            s: CharSequence,
                            start: Int,
                            count: Int,
                            after: Int
                        ) {
                        }

                        override fun onTextChanged(
                            s: CharSequence,
                            start: Int,
                            before: Int,
                            count: Int
                        ) {
                        }

                        override fun afterTextChanged(editable: Editable) {
                            val innerItem = container.tag as ReceivingDetailsData
                            if (!TextUtils.isEmpty(editable)) {
                                innerItem.itemValue = editable.toString()
                            } else {
                                innerItem.itemValue = ""
                            }
                        }
                    }

                    editText.addTextChangedListener(watcher)
                    editText.tag = watcher
                    container.tag = item
                }
                "5" -> {
                    holder.setGone(R.id.tv_choose, true)
                    holder.setGone(R.id.tv_word, true)
                    holder.setGone(R.id.edit_text, true)
                    holder.setGone(R.id.ll_btn, true)
                    holder.setGone(R.id.iv_riqi, true)
                    holder.setGone(R.id.iv_bianji, true)
                }
            }

        }
    }
}