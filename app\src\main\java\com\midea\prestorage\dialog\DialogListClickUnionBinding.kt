package com.midea.prestorage.dialog

import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestorage.function.receive.dialog.ContainerDeleteDialogVM
import com.midea.prestorage.function.receive.dialog.DialogContainerDeleteUnionBinding
import com.midea.prestoragesaas.databinding.DialogContainerDeleteBinding
import com.midea.prestoragesaas.databinding.DialogContainerDeleteCareBinding
import com.midea.prestoragesaas.databinding.DialogListClickBinding
import com.midea.prestoragesaas.databinding.DialogListClickCareBinding

sealed class DialogListClickUnionBinding {
    abstract var vm: FilterDialogVM?
    abstract val recycle: RecyclerView
    abstract val imgClose: ImageView

    class V2(val binding: DialogListClickCareBinding) : DialogListClickUnionBinding() {
        override var vm: FilterDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val recycle = binding.recycle
        override val imgClose = binding.imgClose
    }

    class V1(val binding: DialogListClickBinding) : DialogListClickUnionBinding() {
        override var vm: FilterDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val recycle = binding.recycle
        override val imgClose = binding.imgClose
    }
}
