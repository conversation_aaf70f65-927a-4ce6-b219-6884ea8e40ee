package com.midea.prestorage.function.inv.response;

import java.math.BigDecimal;

public class SubmitInvLocationInventory {


    private String whCode;
    private String whName;
    private String transferType;
    private BigDecimal amount;
    private String ownerCode;
    private String ownerName;
    private String reason;
    private String locCode;
    private String traceId;
    private String toTraceId;
    private String itemCode;
    private String lotNum;
    private FmInvLotDTO fmInvLot;
    private ToInvLotDTO toInvLot;

    public String getLotNum() {
        return lotNum;
    }

    public void setLotNum(String lotNum) {
        this.lotNum = lotNum;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getWhName() {
        return whName;
    }

    public void setWhName(String whName) {
        this.whName = whName;
    }

    public String getTransferType() {
        return transferType;
    }

    public void setTransferType(String transferType) {
        this.transferType = transferType;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getLocCode() {
        return locCode;
    }

    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getToTraceId() {
        return toTraceId;
    }

    public void setToTraceId(String toTraceId) {
        this.toTraceId = toTraceId;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public FmInvLotDTO getFmInvLot() {
        return fmInvLot;
    }

    public void setFmInvLot(FmInvLotDTO fmInvLot) {
        this.fmInvLot = fmInvLot;
    }

    public ToInvLotDTO getToInvLot() {
        return toInvLot;
    }

    public void setToInvLot(ToInvLotDTO toInvLot) {
        this.toInvLot = toInvLot;
    }

    public static class FmInvLotDTO {
        private String lotNum;
        private String lotAtt01;
        private String lotAtt02;
        private String lotAtt03;
        private String lotAtt04;
        private String lotAtt05;
        private String lotAtt06;
        private String lotAtt07;
        private String lotAtt08;
        private String lotAtt09;
        private String lotAtt10;
        private String lotAtt11;
        private String lotAtt12;

        public String getLotNum() {
            return lotNum;
        }

        public void setLotNum(String lotNum) {
            this.lotNum = lotNum;
        }

        public String getLotAtt01() {
            return lotAtt01;
        }

        public void setLotAtt01(String lotAtt01) {
            this.lotAtt01 = lotAtt01;
        }

        public String getLotAtt02() {
            return lotAtt02;
        }

        public void setLotAtt02(String lotAtt02) {
            this.lotAtt02 = lotAtt02;
        }

        public String getLotAtt03() {
            return lotAtt03;
        }

        public void setLotAtt03(String lotAtt03) {
            this.lotAtt03 = lotAtt03;
        }

        public String getLotAtt04() {
            return lotAtt04;
        }

        public void setLotAtt04(String lotAtt04) {
            this.lotAtt04 = lotAtt04;
        }

        public String getLotAtt05() {
            return lotAtt05;
        }

        public void setLotAtt05(String lotAtt05) {
            this.lotAtt05 = lotAtt05;
        }

        public String getLotAtt06() {
            return lotAtt06;
        }

        public void setLotAtt06(String lotAtt06) {
            this.lotAtt06 = lotAtt06;
        }

        public String getLotAtt07() {
            return lotAtt07;
        }

        public void setLotAtt07(String lotAtt07) {
            this.lotAtt07 = lotAtt07;
        }

        public String getLotAtt08() {
            return lotAtt08;
        }

        public void setLotAtt08(String lotAtt08) {
            this.lotAtt08 = lotAtt08;
        }

        public String getLotAtt09() {
            return lotAtt09;
        }

        public void setLotAtt09(String lotAtt09) {
            this.lotAtt09 = lotAtt09;
        }

        public String getLotAtt10() {
            return lotAtt10;
        }

        public void setLotAtt10(String lotAtt10) {
            this.lotAtt10 = lotAtt10;
        }

        public String getLotAtt11() {
            return lotAtt11;
        }

        public void setLotAtt11(String lotAtt11) {
            this.lotAtt11 = lotAtt11;
        }

        public String getLotAtt12() {
            return lotAtt12;
        }

        public void setLotAtt12(String lotAtt12) {
            this.lotAtt12 = lotAtt12;
        }
    }

    public static class ToInvLotDTO {
        private String lotAtt01;
        private String lotAtt02;
        private String lotAtt03;
        private String lotAtt04;
        private String lotAtt05;
        private String lotAtt06;
        private String lotAtt07;
        private String lotAtt08;
        private String lotAtt09;
        private String lotAtt10;
        private String lotAtt11;
        private String lotAtt12;

        public String getLotAtt01() {
            return lotAtt01;
        }

        public void setLotAtt01(String lotAtt01) {
            this.lotAtt01 = lotAtt01;
        }

        public String getLotAtt02() {
            return lotAtt02;
        }

        public void setLotAtt02(String lotAtt02) {
            this.lotAtt02 = lotAtt02;
        }

        public String getLotAtt03() {
            return lotAtt03;
        }

        public void setLotAtt03(String lotAtt03) {
            this.lotAtt03 = lotAtt03;
        }

        public String getLotAtt04() {
            return lotAtt04;
        }

        public void setLotAtt04(String lotAtt04) {
            this.lotAtt04 = lotAtt04;
        }

        public String getLotAtt05() {
            return lotAtt05;
        }

        public void setLotAtt05(String lotAtt05) {
            this.lotAtt05 = lotAtt05;
        }

        public String getLotAtt06() {
            return lotAtt06;
        }

        public void setLotAtt06(String lotAtt06) {
            this.lotAtt06 = lotAtt06;
        }

        public String getLotAtt07() {
            return lotAtt07;
        }

        public void setLotAtt07(String lotAtt07) {
            this.lotAtt07 = lotAtt07;
        }

        public String getLotAtt08() {
            return lotAtt08;
        }

        public void setLotAtt08(String lotAtt08) {
            this.lotAtt08 = lotAtt08;
        }

        public String getLotAtt09() {
            return lotAtt09;
        }

        public void setLotAtt09(String lotAtt09) {
            this.lotAtt09 = lotAtt09;
        }

        public String getLotAtt10() {
            return lotAtt10;
        }

        public void setLotAtt10(String lotAtt10) {
            this.lotAtt10 = lotAtt10;
        }

        public String getLotAtt11() {
            return lotAtt11;
        }

        public void setLotAtt11(String lotAtt11) {
            this.lotAtt11 = lotAtt11;
        }

        public String getLotAtt12() {
            return lotAtt12;
        }

        public void setLotAtt12(String lotAtt12) {
            this.lotAtt12 = lotAtt12;
        }
    }
}
