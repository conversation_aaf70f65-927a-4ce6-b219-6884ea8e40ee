package com.midea.prestorage.function.put

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.PutContainerInfo
import com.midea.prestorage.function.inv.InventorySearchUnionBinding
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.databinding.ActivityInStorageScanBinding
import com.xuexiang.xqrcode.XQRCode

// 入库订单池
class InStorageScanPutActivity : BaseActivity() {

    lateinit var binding: InStorageScanUnionBinding
    private var vm = InStorageScanPutVM(this)
    val adapter = OutPoolStorageAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            InStorageScanUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_in_storage_scan_care
                )
            )
        } else {
            InStorageScanUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_in_storage_scan
                )
            )
        }
        binding.vm = vm

        binding.edOrderNo.requestFocus()
        initRecycle()
    }

    override fun onResume() {
        super.onResume()
        if (!vm.jumpFlag) {
            vm.init()
        } else {
            vm.jumpFlag = false
        }
    }

    private fun initRecycle() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter

        adapter.setOnItemClickListener { adapter, _, position ->
            val bean = adapter.data[position] as PutContainerInfo
            vm.onItemClick(bean)
        }
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            vm.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    class OutPoolStorageAdapter :
        CommonAdapter<PutContainerInfo>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_put_goods_care else R.layout.item_put_goods) {
        override fun convert(holder: BaseViewHolder?, item: PutContainerInfo) {
            super.convert(holder, item)
//            "Y" -> holder?.setBackgroundResource(R.id.tv_status, R.drawable.bg_bt_green)
//            else -> holder?.setBackgroundResource(R.id.tv_status, R.drawable.bg_bt_red_no_circle)

            var statusStr = ""
            when (item.status) {
                "300" -> {
                    statusStr = "待上架"
                    holder?.setBackgroundColor(
                        R.id.ll_background,
                        ContextCompat.getColor(
                            context,
                            R.color.colorWhite
                        )
                    )
                    holder?.setBackgroundResource(
                        R.id.tv_container,
                        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.drawable.bg_bt_orange_no_circle_care else R.drawable.bg_bt_orange_no_circle
                    )
                }
                else -> {
                    statusStr = "上架中"
                    holder?.setBackgroundColor(
                        R.id.ll_background,
                        ContextCompat.getColor(
                            context,
                            R.color.colorPinkOne
                        )
                    )
                    holder?.setBackgroundResource(R.id.tv_container, R.drawable.bg_bt_green)
                }
            }
            holder?.setText(R.id.tv_container, statusStr)
            holder?.setText(
                R.id.tv_put_num,
                item.checkQty.toPlainString() + "/" + item.qty.toPlainString()
            )
            holder?.setGone(R.id.ll_bottom, TextUtils.isEmpty(item.checkBy))
        }
    }
}