package com.midea.prestorage.function.instorage.response;

public class ReceiptSerialInitVO {

    private String code;

    private String containerCode;

    private String receiptChannel;

    private String msg;

    private String receiptCodeOrWaveNo;


    public String getReceiptCodeOrWaveNo() {
        return receiptCodeOrWaveNo;
    }

    public void setReceiptCodeOrWaveNo(String receiptCodeOrWaveNo) {
        this.receiptCodeOrWaveNo = receiptCodeOrWaveNo;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public String getReceiptChannel() {
        return receiptChannel;
    }

    public void setReceiptChannel(String receiptChannel) {
        this.receiptChannel = receiptChannel;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}