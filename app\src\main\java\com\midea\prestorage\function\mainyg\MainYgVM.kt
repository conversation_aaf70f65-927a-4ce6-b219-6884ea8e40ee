package com.midea.prestorage.function.mainyg

import android.annotation.SuppressLint
import android.content.Intent
import android.text.TextUtils
import android.util.Log
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.App
import com.midea.prestoragesaas.R
import com.midea.prestorage.beans.help.HeaderInfo
import com.midea.prestorage.beans.net.*
import com.midea.prestorage.beans.setting.HandingInfoDb
import com.midea.prestorage.beans.setting.ImplWarehouse
import com.midea.prestorage.dialog.TipNoCancelDialog
import com.midea.prestorage.function.addgoods.AddGoodsActivity
import com.midea.prestorage.function.addgoods.ReplenishmentActivity
import com.midea.prestorage.function.barcode.BarcodeOrderListActivity
import com.midea.prestorage.function.collection.CollectionActivity
import com.midea.prestorage.function.containerpick.CombinedPickActivity
import com.midea.prestorage.function.containerpick.ContainerPickActivity
import com.midea.prestorage.function.instorage.InStorageNewActivity
import com.midea.prestorage.function.instorage.InStoragePutAwayTaskActivity
import com.midea.prestorage.function.inv.*
import com.midea.prestorage.function.main.MainActivity
import com.midea.prestorage.function.main.ProfileActivity
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.outstorage.*
import com.midea.prestorage.function.picktask.PickTaskActivity
import com.midea.prestorage.function.planstock.PlanStockActivity
import com.midea.prestorage.function.planstock.PlanStockListActivity
import com.midea.prestorage.function.planstockold.PlanStockOldActivity
import com.midea.prestorage.function.put.InStorageScanPutActivity
import com.midea.prestorage.function.receivecpkx.ContainerListActivity
import com.midea.prestorage.function.receivecpkx.ContainerWaveListActivity
import com.midea.prestorage.function.review.ScanReviewActivity
import com.midea.prestorage.function.send.SendActivity
import com.midea.prestorage.function.sendcheck.SerialCheckFirstActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.*
import com.midea.prestorage.worker.ApkDownloadWorker
import com.midea.prestoragesaas.BuildConfig
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import okhttp3.MediaType
import okhttp3.RequestBody
import org.xutils.db.sqlite.WhereBuilder
import java.text.SimpleDateFormat
import java.util.*


/**
 * Created by LIXK5 on 2019/4/4.
 */
class MainYgVM(val activity: MainYgActivity) {
    val storage = ObservableField<String>("请选择仓库")

    val db = DbUtils.db
    val codes = mutableListOf<String>()

    private var tipNoCancelDialog: TipNoCancelDialog? = null

    fun init() {
        initData()
        initAuth()

        tipNoCancelDialog = TipNoCancelDialog(activity)

//        activity.adapterIn.addData(authInStorage)
//        activity.adapterOut.addData(authOutStorage)
//        activity.adapterStorage.addData(authStorage)
    }

    private fun initData() {
        Observable.create<ImplWarehouse> {
            if (Constants.userInfo?.whCode.isNullOrEmpty()) {
                it.onNext(ImplWarehouse())
            } else {
                val findFirst = db.selector(ImplWarehouse::class.java)
                    .where("whCode", "==", Constants.userInfo?.whCode).findFirst()
                if (findFirst != null) {
                    //清除掉所有的已经选中的数据
                    val allCheckBeans = db.selector(ImplWarehouse::class.java)
                        .where("isChecked", "==", true).findAll()
                    allCheckBeans?.forEach { item ->
                        item.isChecked = false
                    }
                    findFirst.isChecked = true
                    if (!allCheckBeans.isNullOrEmpty()) {
                        db.saveOrUpdate(allCheckBeans)
                    }

                    db.saveOrUpdate(findFirst)
                    it.onNext(findFirst)
                } else {
                    it.onNext(
                        ImplWarehouse(
                            Constants.userInfo?.whName,
                            Constants.userInfo?.whCode
                        )
                    )
                }
            }
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<ImplWarehouse?> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: ImplWarehouse) {
                    if (!TextUtils.isEmpty(t.whCode)) {
                        storage.set(t.cdwhName)
                        activity.whCancelAble(true)
                        t.isChecked = true
                        Constants.whInfo = t

                        initWhDc(t)
                        uploadDevicesInfo()
                    } else {
                        // 弹框选仓库
                        activity.whCancelAble(false)
                        initWhDc(null)
                    }
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    private fun initWhDc(t: ImplWarehouse?) {
        activity.waitingDialogHelp.showDialog()
        if (t != null) {
            compareWhCodeInfo(t)
        } else {
            resetWhCodeInfo(true)
        }
    }

    fun showWhCode() {
        val allWh = db.findAll(ImplWarehouse::class.java)
        if (allWh.isNullOrEmpty()) {
            resetWhCodeInfo(true)
        } else {
            showStorage(allWh)
        }
    }

    // 上报登录成功记录
    @SuppressLint("SimpleDateFormat")
    fun uploadDevicesInfo() {
        if (!Constants.userInfo?.whCode.isNullOrEmpty()) {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

            val map = mutableMapOf(
                "deviceId" to AppUtils.getDevicesId(activity),
                "deviceName" to AppUtils.getDeviceName(),
                "deviceModel" to AppUtils.getDeviceModel(),
                "deviceType" to AppUtils.getDeviceType(activity),
                "systemVersion" to AppUtils.getDeviceAndroidVersion(),
                "appVersion" to AppUtils.getVersionCode(activity),
                "deviceStatus" to "00",
                "sourceSystem" to "AWMS",
                "appLoginTime" to dateFormat.format(Date()),
                "whCode" to Constants.userInfo?.whCode,
                "whName" to Constants.userInfo?.whName,
                "appLoginUser" to Constants.userInfo?.name,
                "appLoginUserName" to Constants.userInfo?.userName
            )
            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(map)
            )
            RetrofitHelper.getBasicDataAPI()
                .saveLoginInfo(requestBody)
                .compose(NetworkScheduler.compose())
                .bindUntilEvent(activity, ActivityEvent.DESTROY)
                .subscribe(object : RequestCallback<Any>(activity) {
                    override fun success(data: Any?) {
                        Log.i("wms", "记录登录记录----成功")

                        getLogIn()
                    }

                    override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                        Log.e("wms", "上报登录记录失败")
                        Log.e("wms", statusCode.toString() + ":" + apiErrorModel.message)

                        getLogIn()
                    }
                })
        }
    }

    //每次进去主页调getLogin，还要在全局拦截http请求的地方，拦截只要401都要触发退出
    fun getLogIn() {
        RetrofitHelper.getBasicDataAPI()
            .getLogIn()
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<RespLogIn>(activity) {
                override fun success(data: RespLogIn?) {
                    Constants.mobile = data?.mobile ?: ""
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                }
            })
    }

    /**
     * 重新拉取网络数据
     */
    fun compareWhCodeInfo(
        whInfo: ImplWarehouse
    ) {
        activity.waitingDialogHelp.showDialog()
        val map = mutableMapOf<String, Any?>()
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )
        RetrofitHelper.getBasicDataAPI()
            .getCodeAndName(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<ImplWarehouse>>(activity) {
                override fun success(result: MutableList<ImplWarehouse>?) {
                    var data = result?.filter { it.cdwhIsStop == 0 }?.toMutableList()
                    if (data.isNullOrEmpty()) {
                        tipNoCancelDialog?.setTitle("提示")
                        tipNoCancelDialog?.setMsg("暂无仓库权限，请先开通!")
                        tipNoCancelDialog?.setOnTipBackListener(object :
                            TipNoCancelDialog.OnTipNoCancelBack {
                            override fun onConfirmClick() {
                                MySoundUtils.getInstance().dingSound()
                                activity.finish()
                            }
                        })
                        tipNoCancelDialog?.show()
                    }
                    data?.forEach {
                        it.whSystem = "2"
                    }

                    //不要ALL的仓库
                    val allWh =
                        data?.find { it.whCode.toLowerCase(Locale.getDefault()) == "all" }
                    data?.remove(allWh)

                    data?.let {
                        val result = it.find { item -> item.whCode == whInfo.whCode }
                        if (result?.whSystem != whInfo.whSystem) {
                            Constants.whInfo = result
                            result?.isChecked = true

                            saveWhInfo(data)
                            saveUserInfo()
                        }
                    }

                    activity.waitingDialogHelp.hidenDialog()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    /**
     * 重新拉取网络数据
     */
    fun resetWhCodeInfo(isShowDialog: Boolean) {
        if (isShowDialog) {
            activity.waitingDialogHelp.showDialog()
        }
        val map = mutableMapOf<String, Any?>()
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )
        RetrofitHelper.getBasicDataAPI()
            .getCodeAndName(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<ImplWarehouse>>(activity) {
                override fun success(result: MutableList<ImplWarehouse>?) {
                    var data = result?.filter { it.cdwhIsStop == 0 }?.toMutableList()
                    if (data.isNullOrEmpty()) {
                        tipNoCancelDialog?.setTitle("提示")
                        tipNoCancelDialog?.setMsg("暂无仓库权限，请先开通!")
                        tipNoCancelDialog?.setOnTipBackListener(object :
                            TipNoCancelDialog.OnTipNoCancelBack {
                            override fun onConfirmClick() {
                                MySoundUtils.getInstance().dingSound()
                                activity.finish()
                            }
                        })
                        tipNoCancelDialog?.show()
                    }
                    data?.forEach {
                        it.whSystem = "2"
                    }
                    saveWhInfo(data)
                    //不要ALL的仓库
                    val result =
                        data?.find { it.whCode.toLowerCase(Locale.getDefault()) == "all" }
                    data?.remove(result)
                    if (isShowDialog) {
                        showStorage(data)
                    }
                    activity.waitingDialogHelp.hidenDialog()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    private fun initInStorage(auths: AuthBean?) {
        activity.adapterIn.cleanData()
        auths?.subResources?.forEach {
            val result = authInStorage.find { item -> it.frontUrl == item.tag }
            if (result != null) {
                activity.adapterIn.addData(result)
            }
        }
        activity.adapterIn.notifyDataSetChanged()
    }

    private fun initOutStorage(auths: AuthBean?) {
        activity.adapterOut.cleanData()
        auths?.subResources?.forEach {
            val result = authOutStorage.find { item -> it.frontUrl == item.tag }
            if (result != null) {
                activity.adapterOut.addData(result)
            }
        }
        activity.adapterOut.notifyDataSetChanged()

        //封箱逻辑特殊，写在这里
        Constants.isShowCloseContainer = true == auths?.subResources?.any {
            it.frontUrl == "/container/collectionReview/close"
        }

        //容器拣货报异常按钮是否显示控制按钮
        Constants.isShowUpError = true == auths?.subResources?.any {
            it.frontUrl == "/container/collectionPick/upError"
        }
    }

    private fun initStorage(auths: AuthBean?) {
        activity.adapterStorage.cleanData()
        auths?.subResources?.forEach {
            val result = authStorage.find { item -> it.frontUrl == item.tag }
            if (result != null) {
                activity.adapterStorage.addData(result)
            }
        }
        activity.adapterStorage.notifyDataSetChanged()
    }

    fun initMenu(menus: MutableList<AuthBean>) {
        val resourceUrlTag = if (Constants.whInfo?.bearingSystem != "3") {
            "RF2"
        } else {
            "RF3"
        }

        val auth = getMenuFind(menus, resourceUrlTag)
        if (auth != null) {
            if (!auth.subResources.isNullOrEmpty()) {
                val authInStorage =  //入库
                    auth.subResources.find { it.resourceUrl == "receipt" }
                val authOutStorage =  //出库
                    auth.subResources.find { it.resourceUrl == "shipment" }
                val authStorage =  //库内
                    auth.subResources.find { it.resourceUrl == "manage" }

                if (authInStorage?.subResources.isNullOrEmpty()
                    && authOutStorage?.subResources.isNullOrEmpty()
                    && authStorage?.subResources.isNullOrEmpty()
                ) {
                    tipNoCancelDialog?.setTitle("提示")
                    tipNoCancelDialog?.setMsg("暂无仓库权限，请先开通!")
                    tipNoCancelDialog?.setOnTipBackListener(object :
                        TipNoCancelDialog.OnTipNoCancelBack {
                        override fun onConfirmClick() {
                            MySoundUtils.getInstance().dingSound()
                            activity.finish()
                        }
                    })
                    tipNoCancelDialog?.show()
                    return
                }

                initInStorage(authInStorage)
                initOutStorage(authOutStorage)
                initStorage(authStorage)
            }
        }
    }

    private fun getMenuFind(menus: MutableList<AuthBean>, resourceUrlTag: String): AuthBean? {
        if (!menus.isNull()) {
            val it = menus.iterator()
            while (it.hasNext()) {
                val auth = it.next()
                if (auth.resourceUrl == resourceUrlTag) {
                    return auth
                } else {
                    if (auth.subResources != null && auth.subResources.isNotEmpty()) {
                        val result = getMenuFind(auth.subResources, resourceUrlTag)
                        if (result != null) {
                            return result
                        }
                    }
                }
            }
        }
        return null
    }

    fun initAuth() {
        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getBarcodeAPI()
            .getAllResources(Constants.userInfo?.name, BuildConfig.APPLICATION_CODE)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<AuthBean>>(activity) {
                override fun success(data: MutableList<AuthBean>?) {
                    if (!data.isNullOrEmpty()) {
                        initMenu(data)
                    }
                    activity.waitingDialogHelp.hidenDialog()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    private fun saveWhInfo(list: MutableList<ImplWarehouse>?) {
        Observable.create<String> {
            try {
                db.delete(ImplWarehouse::class.java)
                db.saveOrUpdate(list)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    fun checkUpGrade(isClick: Boolean) {
        ApkDownloadWorker.getInstance().checkApkVersion(activity, isClick)
    }

    fun toProfileActivity() {
        val intent = Intent(activity, ProfileActivity::class.java)
        activity.startActivity(intent)
    }

    fun onItemClick(item: HeaderInfo) {
        if ((item.cls == OutStorageNewActivity::class.java || item.cls == InStorageNewActivity::class.java) && App.tenantCode != null && App.tenantCode == "annto") {
            Observable.create<HandingInfoDb> {
                var handingInfo =
                    db.selector(HandingInfoDb::class.java)
                        .where("userId", "==", Constants.userInfo?.id)
                        .and(WhereBuilder.b("mode", "==", 5))
                        .findFirst()

                if (handingInfo == null) {
                    it.onNext(HandingInfoDb())
                } else {
                    it.onNext(handingInfo)
                }
                it.onComplete()
            }.compose(NetworkScheduler.compose())
                .bindUntilEvent(activity, ActivityEvent.DESTROY)
                .subscribe(object : Observer<HandingInfoDb> {
                    override fun onComplete() {
                    }

                    override fun onSubscribe(d: Disposable) {
                    }

                    override fun onNext(t: HandingInfoDb) {
                        if (t.userId == null) {//还没设置装卸组
                            tipNoCancelDialog?.setTitle("提示")
                            tipNoCancelDialog?.setMsg("请先设置装卸组")
                            tipNoCancelDialog?.setOnTipBackListener(object :
                                TipNoCancelDialog.OnTipNoCancelBack {
                                override fun onConfirmClick() {
                                    MySoundUtils.getInstance().dingSound()
                                    tipNoCancelDialog!!.dismiss()
                                }
                            })
                            tipNoCancelDialog?.show()
                        } else {
                            val intent = Intent(activity, item.cls)
                            activity.startActivity(intent)
                        }
                    }

                    override fun onError(e: Throwable) {
                    }
                })
        } else if (item.cls == ContainerListActivity::class.java
            || item.cls == ContainerPickActivity::class.java
            || item.cls == SerialCheckFirstActivity::class.java
            || item.cls == InventorySearchActivity::class.java
            || item.cls == GoodsSearchActivity::class.java
            || item.cls == CombinedPickActivity::class.java
        ) {
            //容器收货、拣货、复核、库存查询、货品采集，先查询是否开启蓝牙打印开关
            //RF蓝牙打印开关:收货(1)、拣货（2）、复核（3）、库存查询（4）、货品采集（5）、合并拣货（6）
            activity.waitingDialogHelp.showDialogUnCancel()
            RetrofitHelper.getBarcodeAPI()
                .queryInfoByCode(activity.getWhCode(), "BLUETOOTH_SWITCH")
                .compose(NetworkScheduler.compose())
                .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
                .subscribe(object : RequestCallback<InfoByCodeBean>(activity) {
                    override fun success(data: InfoByCodeBean?) {
                        activity.waitingDialogHelp.hidenDialog()
                        data?.run {
                            Constants.isShowContainerReceive = configValue?.contains("1") == true
                            Constants.isShowContainerPick =
                                if (item.cls == CombinedPickActivity::class.java) configValue?.contains(
                                    "6"
                                ) == true else configValue?.contains("2") == true
                            Constants.isShowSerialCheck = configValue?.contains("3") == true
                            Constants.isShowInventorySearch = configValue?.contains("4") == true
                            Constants.isShowGoodsSearch = configValue?.contains("5") == true
                        }
                        val intent = Intent(activity, item.cls)
                        activity.startActivity(intent)
                    }

                    override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                        activity.waitingDialogHelp.hidenDialog()
                        val intent = Intent(activity, item.cls)
                        activity.startActivity(intent)
                    }
                })
        } else {
            val intent = Intent(activity, item.cls)
            activity.startActivity(intent)
        }
    }

    fun showStorage(list: MutableList<ImplWarehouse>?) {
        list?.forEach {
            it.showInfo = it.cdwhName
        }
        activity.showStorageInfo(list)
    }

    fun back() {
        activity.finish()
    }

    fun whInfoCheck(it: ImplWarehouse) {
        storage.set(it.cdwhName)
        Constants.whInfo = it
        Constants.userInfo?.whName = it.cdwhName
        Constants.userInfo?.whCode = it.whCode
        Constants.userInfo?.whType =
            if (TextUtils.isEmpty(it.getWhSystem())) 4 else it.getWhSystem().toInt()

        activity.whCancelAble(true)
        saveUserInfo()
    }

    private fun saveUserInfo() {
        Observable.create<String> {
            try {
                if (Constants.whInfo != null) {
                    db.saveOrUpdate(Constants.whInfo)
                    db.saveOrUpdate(Constants.userInfo)
                }
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                    jumpToFunction()
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    fun jumpToFunction() {
        if (Constants.userInfo?.whType != 2) {
            activity.startActivity(Intent(activity, MainActivity::class.java))
            activity.finish()
        }
    }

    //入库
    private val authInStorage = mutableListOf(
        HeaderInfo(
            "/receipt/scan",
            R.drawable.qianzhichangshouhuo,
            "入库扫码",
            InStorageNewActivity::class.java
        ),
        HeaderInfo(
            "/container/receive",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_ronqishouhuo else R.mipmap.ronqishouhuo,
            "容器收货",
            ContainerListActivity::class.java
        ),
        HeaderInfo(
            "/container/put",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_ronqishangjia else R.mipmap.ronqishangjia,
            "容器上架",
            InStorageScanPutActivity::class.java
        ),
        HeaderInfo(
            "/container/wave/receive",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_qianzhichangshouhuo else R.drawable.qianzhichangshouhuo,
            "波次收货",
            ContainerWaveListActivity::class.java
        ),
        HeaderInfo(
            "/receipt/putaway/task",
            R.drawable.rongqishangjia,
            "上架任务",
            InStoragePutAwayTaskActivity::class.java
        )
    )

    //出库
    private val authOutStorage = mutableListOf(
        HeaderInfo(
            "/shipment/scan",
            R.drawable.jihuapandian,
            "出库扫码",
            OutStorageNewActivity::class.java
        ),
        HeaderInfo(
            "/container/review",
            R.mipmap.fuhezhuangxiang,
            "整箱复核",
            ScanReviewActivity::class.java
        ),
        HeaderInfo(
            "/container/collectionToSend",
            R.drawable.jianhuo,
            "集货发运",
            CollectionActivity::class.java
        ),
        HeaderInfo(
            "/container/collectionPick",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_jianhuo else R.drawable.jianhuo,
            "拣货",
            ContainerPickActivity::class.java
        ),
        HeaderInfo(
            "/container/collectionReview",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_fuhe else R.drawable.shishipandian,
            "复核",
            SerialCheckFirstActivity::class.java
        ),
        HeaderInfo(
            "/container/collectionSend",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_fahuojiaojie else R.drawable.fahuojiaojie,
            "发运",
            SendActivity::class.java
        ),
        HeaderInfo(
            "/shipment/pick/task",
            R.drawable.jianhuo,
            "拣货任务",
            PickTaskActivity::class.java
        ),
        HeaderInfo(
            "/inventory/setPackage",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_ic_collect_sn else R.drawable.ic_collect_sn,
            "集托",
            SetPackageActivity::class.java
        ),
        HeaderInfo(
            "/container/typeIn",
            R.drawable.typein,
            "柜号录入",
            ContainerTypeInActivity::class.java
        ),
        HeaderInfo(
            "/order/query",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_kucunchaxun else R.drawable.kucunchaxun,
            "订单查询",
            OrderQueryActivity::class.java
        ),
        HeaderInfo(
            "/order/shippingLoc",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_jianhuo else R.drawable.jianhuo,
            "绑定集货位",
            BindShippingLocActivity::class.java
        ),
        HeaderInfo(
            "/container/secondPick",
            R.drawable.ic_fenjian,
            "分货",
            SortGoodsActivity::class.java
        ),
        HeaderInfo(
            "/container/combined/pick",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_jianhuo else R.drawable.jianhuo,
            "合并拣货",
            CombinedPickActivity::class.java
        ),
        HeaderInfo(
            "/out/back/task",
            R.drawable.rongqishangjia,
            "返库",
            OutStorageBackTaskActivity::class.java
        )
    )

    //库内
    private val authStorage = mutableListOf(
        HeaderInfo(
            "/inventory/replenishment",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_buhuo else R.mipmap.buhuo,
            "补货",
            AddGoodsActivity::class.java
        ),
        HeaderInfo(
            "/inventory/replenishment/bwms",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_buhuo else R.mipmap.buhuo,
            "补货",
            AddGoodsActivity::class.java
        ),
        HeaderInfo(
            "/inventory/query",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_kucunchaxun else R.drawable.kucunchaxun,
            "库存查询",
            InventorySearchActivity::class.java
        ),
        HeaderInfo(
            "/inventory/transfer",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_kuneiyidong else R.drawable.kuneiyidong,
            "库存移动",
            TransferActivity::class.java
        ),
        HeaderInfo(
            "/inventory/attributeAdjustment",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_fahuojiaojie else R.drawable.fahuojiaojie,
            "批次调整",
            LotSearchActivity::class.java
        ),
        HeaderInfo(
            "/inventory/stocktake/new",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_ic_collect_sn else R.drawable.ic_collect_sn,
            "计划盘点新",
            PlanStockListActivity::class.java
        ),
        HeaderInfo(
            "/inventory/stocktake",
            R.drawable.ic_collect_sn,
            "计划盘点",
            PlanStockOldActivity::class.java
        ),
        HeaderInfo(
            "/inventory/inventoryCheck",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_shishipandian else R.drawable.shishipandian,
            "实时盘点",
            CountInTimeActivity::class.java
        ),
        HeaderInfo(
            "/barcode/serialTrace",
            R.drawable.ic_search_sn,
            "条码流向",
            SerialNoCheckActivity::class.java
        ),
        HeaderInfo(
            "/inventory/statusAdjustment",
            R.drawable.fahuojiaojie,
            "状态调整",
            StatusAdjustmentActivity::class.java
        ),
        HeaderInfo(
            "/goods/search",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_kucunchaxun else R.drawable.kucunchaxun,
            "货品采集",
            GoodsSearchActivity::class.java
        ),
        HeaderInfo(
            "/inventory/replenishment/new",
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.mipmap.care_buhuo else R.mipmap.buhuo,
            "快速补货",
            ReplenishmentActivity::class.java
        ),
        HeaderInfo(
            "/awms/barcode/collect/order",
            R.drawable.ic_barcode_collect,
            "条码采集",
            BarcodeOrderListActivity::class.java
        ),
        HeaderInfo(
            "/inventory/reconciliation",
            R.drawable.tuopankucun,
            "库存调账",
            InvReconciliationActivity::class.java
        )
    )
}