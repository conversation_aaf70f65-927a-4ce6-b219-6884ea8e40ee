package com.midea.prestorage.function.containerpick

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.WindowManager
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.App
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.ContainerPickList
import com.midea.prestorage.beans.net.ContainerReceiveListResp
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.sendcheck.ActivitySerialCheckFirstUnionBinding
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.CareLoadMoreView
import com.midea.prestoragesaas.databinding.ActivityOutContainerPickBinding
import com.xuexiang.xqrcode.XQRCode
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class ContainerPickActivity : BaseActivity() {

    private lateinit var binding: ActivityOutContainerPickUnionBinding
    private var vm = ContainerPickVM(this)
    val adapter = OutStorageListAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityOutContainerPickUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_out_container_pick_care
                )
            )
        } else {
            ActivityOutContainerPickUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_out_container_pick
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        binding.vm = vm

        lifecycleScope.launch {
            while (Constants.isShowContainerPick) {
                delay(60 * 1000)
                binding.vm?.bluetoothOpen(true)
            }
        }
        initRecycle()
        initLoadMore()

        inputRequest()
    }

    override fun onResume() {
        super.onResume()
        if (Constants.isShowContainerPick) {
            binding.vm?.bluetoothOpen(true)
        }
        if (!vm.jumpFlag) {
            vm.onRefreshCommand.onRefresh()
        } else {
            vm.jumpFlag = false
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Printer.closeBluetooth()
    }

    private fun initRecycle() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter

        adapter.setOnItemClickListener { adapter, _, position ->
            val bean = adapter.data[position] as ContainerPickList
            vm.onItemClick(bean)
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.initList(vm.orderNoStr)
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            adapter.loadMoreModule.loadMoreView = CareLoadMoreView()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = true
    }

    fun inputRequest() {
        binding.etSearchOrderNo.requestFocus()
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun showData(data: MutableList<ContainerPickList>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
    }

    fun addData(data: MutableList<ContainerPickList>) {
        adapter.addData(data)
        adapter.notifyDataSetChanged()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            vm.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    class OutStorageListAdapter :
        CommonAdapter<ContainerPickList>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_container_out_care else R.layout.item_container_out),
        LoadMoreModule {

        @SuppressLint("SetTextI18n")
        override fun convert(holder: BaseViewHolder, item: ContainerPickList) {
            super.convert(holder, item)

            if (item.taskStartTime.isNullOrEmpty()) {
                holder.setGone(R.id.ll_pick, true)
            } else {
                holder.setGone(R.id.ll_pick, false)
            }

            holder.setText(R.id.tv_from_zone, item.fromZone ?: "")

            when (item.status) {
                100 -> {
                    holder.setTextColor(
                        R.id.tv_status,
                        ContextCompat.getColor(App.mInstance, R.color.colorTextGray)
                    )
                    holder.setBackgroundResource(R.id.tv_status, R.drawable.bg_bt_litter_gray)
                }
                600 -> {
                    holder.setTextColor(
                        R.id.tv_status,
                        ContextCompat.getColor(App.mInstance, R.color.colorWhite)
                    )
                    holder.setBackgroundResource(R.id.tv_status, R.drawable.bg_bt_green)
                }
                750 -> {
                    holder.setTextColor(
                        R.id.tv_status,
                        ContextCompat.getColor(App.mInstance, R.color.colorWhite)
                    )
                    holder.setBackgroundResource(R.id.tv_status, R.drawable.bg_bt_blue)
                }
                900 -> {
                    holder.setTextColor(
                        R.id.tv_status,
                        ContextCompat.getColor(App.mInstance, R.color.colorWhite)
                    )
                    holder.setBackgroundResource(R.id.tv_status, R.drawable.bg_bt_red)
                }
            }

            holder?.setGone(
                R.id.ll_label,
                "LABEL" != item.commonFlag && "PROCESSING" != item.commonFlag
            )
            if ("LABEL" == item.commonFlag) {
                holder?.setText(R.id.tv_label, "卷标")
            } else if ("PROCESSING" == item.commonFlag) {
                holder?.setText(R.id.tv_label, "加工单")
            }

            holder.setText(R.id.tv_shippingLoc, item.shippingLoc?.replace(",", ",\n") ?: "")
        }
    }
}