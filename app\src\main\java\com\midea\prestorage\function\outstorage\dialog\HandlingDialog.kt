package com.midea.prestorage.function.outstorage.dialog

import android.app.AlertDialog
import android.text.TextUtils
import android.view.LayoutInflater
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogHandlingBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

class HandlingDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {
    private var binding: DialogHandlingBinding
    private var confirmBack: ConfirmBack? = null

    init {
        val contentView = LayoutInflater.from(mContext).inflate(R.layout.dialog_handling, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.lifecycleOwner = mContext
        binding.vm = HandlingDialogVM(this)

        setCanceledOnTouchOutside(true)
    }

    fun setStartTime(time: String) {
        if (!TextUtils.isEmpty(time) && time != null && "null" != time) {
            binding!!.vm!!.startTime.value = time
            binding.tvStartTime.setBackgroundColor(ContextCompat.getColor(mContext, R.color.colorGrayTitle))
        }else {
            binding!!.vm!!.startTime.value = "装卸开始时间"
            binding.tvStartTime.setBackgroundColor(ContextCompat.getColor(mContext, R.color.bg_blue))
        }
    }

    fun setEndTime(time: String) {
        if (!TextUtils.isEmpty(time) && time != null && "null" != time) {
            binding!!.vm!!.endTime.value = time
            binding.tvEndTime.setBackgroundColor(ContextCompat.getColor(mContext, R.color.colorGrayTitle))
        }else {
            binding!!.vm!!.endTime.value = "装卸结束时间"
            binding.tvEndTime.setBackgroundColor(ContextCompat.getColor(mContext, R.color.bg_blue))
        }
    }

    fun setConfirmBack(backImpl: ConfirmBack) {
        confirmBack = backImpl
    }

    fun backConfirm(type: Int, time: String) {
        if (confirmBack != null) {
            if(type == 1) {
                binding.tvStartTime.text = time
                binding.tvStartTime.setBackgroundColor(ContextCompat.getColor(mContext, R.color.colorGrayTitle))
            }else {
                binding.tvEndTime.text = time
                binding.tvEndTime.setBackgroundColor(ContextCompat.getColor(mContext, R.color.colorGrayTitle))
            }
            confirmBack!!.confirmBack(type, time)
        }
    }

    interface ConfirmBack {
        fun confirmBack(type: Int, time: String)
    }

}