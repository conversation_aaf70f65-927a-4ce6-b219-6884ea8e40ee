package com.midea.prestorage.function.containerpick.adapter

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.TextView
import com.midea.prestoragesaas.R
import com.midea.prestorage.beans.net.ContainerPickTaskTypeList
import com.midea.prestorage.utils.AppUtils

class TaskGridAdapter(private val mContext: Context) : BaseAdapter() {
    private val beans: MutableList<String> = mutableListOf()

    fun addData(data: MutableList<ContainerPickTaskTypeList>?) {
        if (!data.isNullOrEmpty()) {
            var str = ""
            data.forEachIndexed { index, bean ->
                if (index % 2 == 0) {
                    str = "【" + bean.gridNumber + "】" + "号" + AppUtils.getBigDecimalValueStr(bean.fromQty) + bean.eaUnit + ";"
                    if(index == data.lastIndex) {
                        beans.add(str)
                    }
                }else {
                    str = str + "【" + bean.gridNumber + "】" + "号" + AppUtils.getBigDecimalValueStr(bean.fromQty) + bean.eaUnit
                    beans.add(str)
                }
            }
        }
    }

    override fun getCount(): Int {
        return beans.size
    }

    override fun getItem(position: Int): String {
        return beans[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(
        position: Int,
        convertView: View?,
        parent: ViewGroup
    ): View {
        val viewHolder: ViewHolder?

        var convertView = convertView
        if (convertView == null) {
            convertView =
                LayoutInflater.from(mContext).inflate(R.layout.item_task_grid, parent, false)
            viewHolder = ViewHolder()
            viewHolder.txtName = convertView.findViewById(R.id.tv_name)
            convertView!!.tag = viewHolder
        } else {
            viewHolder = convertView.tag as ViewHolder
        }

        val bean = beans[position]
        viewHolder.txtName?.text = bean
        return convertView
    }

    internal inner class ViewHolder {
        var txtName: TextView? = null
    }
}