package com.midea.prestorage.function.instorage

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.beans.net.BwmsInPutAwayLocationRequest
import com.midea.prestorage.beans.net.PutAwayTask
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityInStoragePutawayTaskDetailBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class InStoragePutAwayTaskDetailActivity :
    BaseViewModelActivity<InStoragePutAwayTaskDetailViewModel>() {

    companion object {

        private const val KEY_TASK = "TASK"

        fun newIntent(context: Context, putAwayTask: PutAwayTask) =
            Intent(context, InStoragePutAwayTaskDetailActivity::class.java).also {
                it.putExtra(KEY_TASK, putAwayTask)
            }

    }

    private lateinit var binding: ActivityInStoragePutawayTaskDetailBinding

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding =
            DataBindingUtil.setContentView(this, R.layout.activity_in_storage_putaway_task_detail)
        immersionBar {
            titleBarMarginTop(binding.clTitleLayout)
        }
        vm = ViewModelProvider(this).get(InStoragePutAwayTaskDetailViewModel::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this
        vm.finishEvent.observe(this) {
            finish()
        }
        vm.locLiveData.observe(this) {
            lifecycleScope.launch {
                val text = binding.etLoc.text
                if (text.isNotEmpty()) {
                    binding.etLoc.setSelection(text.length)
                }
            }
        }
        vm.settingViewModel.showLotAttCount.observe(this) {}
        vm.settingViewModel.firstOpenLotAtt.observe(this) {}
        vm.settingViewModel.secondOpenLotAtt.observe(this) {}
        vm.settingViewModel.thirdOpenLotAtt.observe(this) {}
        vm.settingViewModel.fourthOpenLotAtt.observe(this) {}
        val putAwayTask = intent.getParcelableExtra<PutAwayTask>(KEY_TASK)
        putAwayTask?.let {
            vm.putAwayTask = it
            vm.update(it)
            vm.updateGoodStatus(it)
        }
        vm.initSetting()
        lifecycleScope.launch {
            delay(500)
            withContext(Dispatchers.Main) {
                vm.settingViewModel.updateDynamicShowView(
                    binding.tvDynamic1,
                    binding.tvDynamic1Value,
                    binding.tvDynamic2,
                    binding.tvDynamic2Value,
                    binding.tvDynamic3,
                    binding.tvDynamic3Value,
                    binding.tvDynamic4,
                    binding.tvDynamic4Value,
                    putAwayTask
                )
            }
        }
    }

}