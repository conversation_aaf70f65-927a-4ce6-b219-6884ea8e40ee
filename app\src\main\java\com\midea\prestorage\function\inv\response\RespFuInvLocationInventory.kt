package com.midea.prestorage.function.inv.response

import com.midea.prestorage.base.annotation.ShowAnnotation
import java.math.BigDecimal
import java.io.Serializable

data class RespFuInvLocationInventory(
    @ShowAnnotation
    var index: Int = 0,
    @ShowAnnotation
    var custItemCode: String? = null,
    @ShowAnnotation
    var whBarcode69: String? = null,
    @ShowAnnotation
    var isValidity: String? = null,
    @ShowAnnotation
    var itemName: String? = null,
    @ShowAnnotation
    var onHandQty: BigDecimal? = null,
    @ShowAnnotation
    var allocatedQty: BigDecimal? = null,
    @ShowAnnotation
    var usableQty: BigDecimal? = null,
    var whCsBarcode69: String? = null,
    var whMaxBarcode69: String? = null,
    var isNeedScan69: Int = 0,
    var periodOfValidity: BigDecimal? = null,
    var validityUnit: String? = null,
    var inLifePercentage: String? = null,
    var outLifePercentage: String? = null,
    var inLifeDays: BigDecimal? = null,
    var outLifeDays: BigDecimal? = null,
    var earlyWarning: BigDecimal? = null,
    var earlyTakedown: BigDecimal? = null,
    var itemCode: String? = null

) : Serializable {
    fun getValidity(): String {
        return if ("Y".equals(isValidity, true)) {
            "是"
        } else {
            "否"
        }
    }

    fun getNeedScan69(): String {
        return if (isNeedScan69 == 1) {
            "是"
        } else {
            "否"
        }
    }

    fun getUnit(): String {
        return when {
            "Y".equals(validityUnit, true) -> {
                "年"
            }
            "M".equals(validityUnit, true) -> {
                "月"
            }
            "W".equals(validityUnit, true) -> {
                "周"
            }
            "D".equals(validityUnit, true) -> {
                "日"
            }
            else -> {
                ""
            }
        }
    }
}
