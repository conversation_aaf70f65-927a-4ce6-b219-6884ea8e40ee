package com.midea.prestorage.function.inv.dialog

import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestorage.function.receive.ContainerReceiveOrderDetailUnionBinding
import com.midea.prestorage.function.receive.ContainerReceiveOrderDetailVM
import com.midea.prestoragesaas.databinding.ActivityContainerReceiveOrderDetailBinding
import com.midea.prestoragesaas.databinding.ActivityContainerReceiveOrderDetailCareBinding
import com.midea.prestoragesaas.databinding.DialogPrintNumBinding
import com.midea.prestoragesaas.databinding.DialogPrintNumCareBinding

sealed class DialogPrintNumUnionBinding {
    abstract var vm: PrintNumDialogVM?

    class V2(val binding: DialogPrintNumCareBinding) : DialogPrintNumUnionBinding() {
        override var vm: PrintNumDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
    }

    class V1(val binding: DialogPrintNumBinding) : DialogPrintNumUnionBinding() {
        override var vm: PrintNumDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
    }
}
