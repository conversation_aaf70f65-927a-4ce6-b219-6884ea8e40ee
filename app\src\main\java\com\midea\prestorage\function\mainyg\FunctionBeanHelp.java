package com.midea.prestorage.function.mainyg;

import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.midea.prestorage.beans.help.HeaderInfo;


/**
 * Created by LUCY6 on 2017-5-23.
 */

public class FunctionBeanHelp implements MultiItemEntity {
    public static final int TITLE = 4;
    public static final int CHILD = 1;

    public String title;
    public HeaderInfo child;
    private int spanSize;

    public FunctionBeanHelp(String title, int spanSize) {
        this.title = title;
        this.spanSize = spanSize;
    }

    public FunctionBeanHelp(HeaderInfo child, int spanSize) {
        this.child = child;
        this.spanSize = spanSize;
    }

    @Override
    public int getItemType() {
        if (title != null) {
            return 0;
        } else {
            return 1;
        }
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public HeaderInfo getChild() {
        return child;
    }

    public void setChild(HeaderInfo child) {
        this.child = child;
    }

    public int getSpanSize() {
        return spanSize;
    }

    public void setSpanSize(int spanSize) {
        this.spanSize = spanSize;
    }
}
