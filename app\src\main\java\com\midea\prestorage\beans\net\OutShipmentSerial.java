package com.midea.prestorage.beans.net;

import java.math.BigDecimal;

public class OutShipmentSerial {

    /**
     * 小箱条码
     */
    private String serialNo;

    /**
     * 大箱条码
     */
    private String serialCs;

    /**
     * 发货箱号
     */
    private String shipmentContainerCode;

    /**
     * 100：复核中；600：复核完成；默认100
     */
    private String status;

    /**
     * 货主
     */
    private String ownerCode;

    /**
     * 客户订单号
     */
    private String custOrderNo;

    /**
     * 出库单号
     */
    private String shipmentCode;

    /**
     * 关联单号
     */
    private String referenceNo;

    /**
     * 出库明细行id
     */
    private String shipmentDetailId;

    /**
     * 拣货明细id
     */
    private String containerDetailId;

    /**
     * 客户货品编码
     */
    private String custItemCode;

    /**
     * 安得货品编码
     */
    private String itemCode;

    /**
     * 货品描述
     */
    private String itemName;

    /**
     * 条码类型0 实物条码 1 虚拟条码，默认实物条码0
     */
    private String serialType;

    /**
     * 数量
     */
    private BigDecimal qty;

    /**
     * 单位
     */
    private String unit;

    /**
     * 库位
     */
    private String toLoc;

    /**
     * 批次属性号
     */
    private String lotNum;

    /**
     * 仓库
     */
    private String whCode;

    /**
     * 扫码值
     */
    private String barcode;


    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getSerialCs() {
        return serialCs;
    }

    public void setSerialCs(String serialCs) {
        this.serialCs = serialCs;
    }

    public String getShipmentContainerCode() {
        return shipmentContainerCode;
    }

    public void setShipmentContainerCode(String shipmentContainerCode) {
        this.shipmentContainerCode = shipmentContainerCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getCustOrderNo() {
        return custOrderNo;
    }

    public void setCustOrderNo(String custOrderNo) {
        this.custOrderNo = custOrderNo;
    }

    public String getShipmentCode() {
        return shipmentCode;
    }

    public void setShipmentCode(String shipmentCode) {
        this.shipmentCode = shipmentCode;
    }

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public String getShipmentDetailId() {
        return shipmentDetailId;
    }

    public void setShipmentDetailId(String shipmentDetailId) {
        this.shipmentDetailId = shipmentDetailId;
    }

    public String getContainerDetailId() {
        return containerDetailId;
    }

    public void setContainerDetailId(String containerDetailId) {
        this.containerDetailId = containerDetailId;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getSerialType() {
        return serialType;
    }

    public void setSerialType(String serialType) {
        this.serialType = serialType;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getToLoc() {
        return toLoc;
    }

    public void setToLoc(String toLoc) {
        this.toLoc = toLoc;
    }

    public String getLotNum() {
        return lotNum;
    }

    public void setLotNum(String lotNum) {
        this.lotNum = lotNum;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }
}