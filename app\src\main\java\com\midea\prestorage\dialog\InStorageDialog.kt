package com.midea.prestorage.dialog

import android.app.AlertDialog
import android.text.TextUtils
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogInStorageBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

class InStorageDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {
    private var binding: DialogInStorageBinding
    private var back: ConfirmFilterChooseBack? = null
    private var deleteback: DeleteBarcodeBack? = null

    init {
        val contentView = LayoutInflater.from(mContext).inflate(R.layout.dialog_in_storage, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = InStorageDialogVM(this)

        setCanceledOnTouchOutside(true)
    }

    fun setTitle(title: String) {
        if (!TextUtils.isEmpty(title)) {
            binding!!.vm!!.title.set(title)
        }
    }

    fun displayEdit() {
        binding!!.vm!!.editUnable()
    }

    fun displayCheckBox() {
        binding!!.vm!!.checkBoxUnable()
    }

    fun setCbStatus(isChecked: Boolean) {
        binding.cbSelect.isChecked = isChecked
    }

    fun setBack(backImpl: ConfirmFilterChooseBack) {
        back = backImpl
    }

    fun setDeleteBack(backImpl: DeleteBarcodeBack) {
        deleteback = backImpl
    }

    fun backConfirm() {
        if (back != null) {
            if(binding.cbSelect.isChecked) {
                back!!.confirmFilterChooseBack(true)
            }else {
                back!!.confirmFilterChooseBack(false)
            }
        }
    }

    fun backDeleteBarcode(barcode : String) {
        if (deleteback != null) {
            deleteback!!.deleteBarcodeBack(barcode)
            binding!!.vm!!.etInfo.set("")
        }
    }

    interface ConfirmFilterChooseBack {
        fun confirmFilterChooseBack(isChecked : Boolean)
    }

    interface DeleteBarcodeBack {
        fun deleteBarcodeBack(barcode : String)
    }
}