package com.midea.prestorage.function.pick;

import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.midea.prestorage.beans.net.OutPickCancelBean;
import com.midea.prestorage.beans.net.OutPoolStorageDetail;


/**
 * Created by LUCY6 on 2017-5-23.
 */

public class OutCancelDetailHelp implements MultiItemEntity {

    public OutPickCancelBean.OutShipmentHeader title;
    public OutPickCancelBean.OutShipmentDetails child;

    public OutCancelDetailHelp(OutPickCancelBean.OutShipmentHeader title) {
        this.title = title;
    }

    public OutCancelDetailHelp(OutPickCancelBean.OutShipmentDetails child) {
        this.child = child;
    }

    @Override
    public int getItemType() {
        if (title != null) {
            return 0;
        } else {
            return 1;
        }
    }

    public void setTitle(OutPickCancelBean.OutShipmentHeader title) {
        this.title = title;
    }

    public OutPickCancelBean.OutShipmentHeader getTitle() {
        return title;
    }

    public void setChild(OutPickCancelBean.OutShipmentDetails child) {
        this.child = child;
    }

    public OutPickCancelBean.OutShipmentDetails getChild() {
        return child;
    }
}
