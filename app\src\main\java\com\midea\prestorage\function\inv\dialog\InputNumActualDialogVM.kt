package com.midea.prestorage.function.inv.dialog

import android.text.TextUtils
import android.view.View
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.net.OutStorageScan
import com.midea.prestorage.function.inv.CountInTimeSettingVM
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.LotAttUnit
import com.midea.prestorage.utils.isNull
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.midea.prestoragesaas.R
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal

class InputNumActualDialogVM(val dialog: InputNumActualDialog) {

    val title = ObservableField<String>("盘点数量录入确认")
    val itemName = ObservableField<String>("")
    val goodsNo = ObservableField<String>("")
    val explain = ObservableField<String>("")
    val itemCode = ObservableField<String>("")
    val itemCode69 = ObservableField<String>("")

    fun show() {
        itemName.set(dialog.deleteInfo?.itemName)
        itemCode.set(
            dialog.deleteInfo?.custItemCode
        )
        if (dialog.deleteInfo?.whCsBarcode69.isNullOrEmpty() && dialog.deleteInfo?.whBarcode69.isNullOrEmpty()) {
            dialog.binding.tvCarcode69.visibility  = View.GONE
        } else {
            dialog.binding.tvCarcode69.visibility  = View.VISIBLE
            itemCode69.set(
                LotAttUnit.formatWhBarcode69(dialog.deleteInfo?.whCsBarcode69, dialog.deleteInfo?.whBarcode69)
            )
        }
    }

    val goodsEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                if (!TextUtils.isEmpty(goodsNo.get())) {
                    confirm()
                }
            }
        }
    }

    fun close() {
        dialog.inputBack?.inputFail()
        dialog.dismiss()
    }

    fun confirm() {
        if (CountInTimeSettingVM.quantityEntryMode == CountInTimeSettingVM.BOXES_NUM) {
            var num = BigDecimal.ZERO
            dialog.adapterNumber?.getData().forEach {
                try {
                    if (!it.num.isNullOrEmpty() && it.cdprQuantity != null && it.cdprQuantity?.compareTo(
                            BigDecimal.ZERO) == 1) {
                        num += AppUtils.getBigDecimalValue(it.num).multiply(it.cdprQuantity)
                    }
                }catch (e: NumberFormatException) {
                    AppUtils.showToast(dialog.mContext, "数量格式错误")
                }
            }
            if (!AppUtils.isZero(num)) {
                goodsNo.set(AppUtils.getBigDecimalValueStr(num))
            }else {
                goodsNo.set("0")
            }
        }
        if (goodsNo.get().isNullOrEmpty()) {
            AppUtils.showToast(dialog.mContext, "请输入数量!")
            return
        }
        dialog.inputBack?.inputOk(dialog.deleteInfo!!, AppUtils.getBigDecimalValue(goodsNo.get()!!))
        dialog.dismiss()
    }
}