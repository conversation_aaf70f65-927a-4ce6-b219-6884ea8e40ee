package com.midea.prestorage.function.planstock

import android.content.Intent
import android.os.Handler
import android.text.InputFilter
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.FuShipmentStatue
import com.midea.prestorage.beans.net.PlanStockList
import com.midea.prestorage.beans.net.ReqCreateEmptyLoc
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody


class PlanStockVM(val activity: PlanStockActivity) {

    val title = MutableLiveData<String>("盘点任务")
    val locNum = MutableLiveData<String>("总库位数：")
    val usedLocNum = MutableLiveData<String>("已盘库位：")
    val stocktakeSchedule = MutableLiveData<String>("进度：")
    val isRefreshing = ObservableBoolean(false)
    val isNoData = ObservableBoolean(false)

    // 搜索条件 单号
    var searchOrderNo = ObservableField<String>("")
    var checkStatue: FuShipmentStatue = FuShipmentStatue("All", "全部")

    var taskStatues: MutableList<FuShipmentStatue>? = null
    var taskRanges: MutableList<FuShipmentStatue>? = null
    var taskTypes: MutableList<FuShipmentStatue>? = null

    // 当前页码
    var pageNo = 1

    var stocktakeCode: String? = null

    val filter = InputFilter { source, _, _, _, _, _ ->
        source.toString().replace("\n", "").replace("\r", "")
    }

    companion object {
        const val FU_INV_TASK_STATUS = "FU_INV_TASK_STATUS"//盘点任务状态字典名称
        const val STOCKTAKE_RANGE = "STOCKTAKE_RANGE"//盘点范围字典名称
        const val STOCKTAKE_STRATEGY_TYPE = "STOCKTAKE_STRATEGY_TYPE"//盘点类型字典名称
    }

    init {
        getDict(FU_INV_TASK_STATUS)
        getDict(STOCKTAKE_RANGE)
        getDict(STOCKTAKE_STRATEGY_TYPE)
    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        isRefreshing.set(true)
        initData(true)
    }

    fun initData(isRefresh: Boolean, isEnter: Boolean? = false) {
        if (isRefresh) {
            pageNo = 1
        }

        val map = mutableMapOf(
            "locCode" to searchOrderNo.get(),
            "whCode" to activity.getWhCode(),
            "statusList" to if ("All" == checkStatue.code) mutableListOf(
                "100",
                "200"
            ) else mutableListOf(
                checkStatue.code
            ),
            "pageSize" to "10",
            "pageNo" to pageNo.toString(),
            "stocktakeCode" to (stocktakeCode ?: "")
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        RetrofitHelper.getWareManageAPI()
            .taskPage(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PageResult<PlanStockList>>(activity) {
                override fun success(data: PageResult<PlanStockList>?) {
                    if (pageNo == 1) {
                        isRefreshing.set(false)
                    }
                    activity.adapter.loadMoreModule.loadMoreComplete()

                    data?.let {
                        if (pageNo < it.totalPage) {
                            pageNo++
                        } else {
                            Handler(activity.mainLooper).post{
                                activity.adapter.loadMoreModule.loadMoreEnd()
                            }
                        }
                        if (data.pageNo == 1) {
                            activity.adapter.setNewInstance(it.list)
                            if (it.list?.size == 1 && isEnter == true) {
                                it.list.getOrNull(0)?.let { it1 -> onItemClick(it1) }
                            }
                        } else {
                            activity.adapter.addData(it.list)
                        }
                        isNoData.set(it.totalCount == 0)
                        setDict()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isRefreshing.set(false)
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    val serialKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                initData(true, isEnter = true)
            }
        }
    }

    /**
     * 新增盘点空库位
     */
    fun createEmptyLoc(locCode: String) {
        activity.waitingDialogHelp.showDialogUnCancel()
        RetrofitHelper.getWareManageAPI()
            .createEmptyLoc(
                ReqCreateEmptyLoc(
                    locCode = locCode,
                    stocktakeCode = stocktakeCode ?: ""
                )
            )
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PlanStockList>(activity) {
                override fun success(data: PlanStockList?) {
                    activity.waitingDialogHelp.hidenDialog()
                    AppUtils.showToast(activity, "新增盘点空库位成功")
                    //新增成功后自动跳转到盘点操作界面扫描商品
                    data?.let {
                        val intent = Intent(activity, PlanStockDetailActivity::class.java)
                        intent.putExtra("bean", it)
                        activity.startActivity(intent)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    AppUtils.showToast(activity, apiErrorModel.message)
                }
            })
    }

    private fun getDict(dictName: String) {
        RetrofitHelper.getDirectionAPI()
            .fuShipmentStatus(dictName)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<FuShipmentStatue>>(activity) {
                override fun success(data: MutableList<FuShipmentStatue>?) {
                    data?.removeAll { it.enableFlag == 0 }
                    if (data != null) {
                        when (dictName) {
                            FU_INV_TASK_STATUS -> taskStatues = data
                            STOCKTAKE_RANGE -> taskRanges = data
                            STOCKTAKE_STRATEGY_TYPE -> taskTypes = data
                        }

                        setDict()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    AppUtils.showToast(activity, apiErrorModel.message)
                }
            })
    }

    fun setDict() {
        if (taskStatues == null || taskRanges == null || taskTypes == null) {
            return
        }

        activity.adapter.data.forEach {
            val resultStatue = taskStatues?.find { item -> it.status == item.code }
            if (resultStatue != null) {
                it.status = resultStatue.name
            }

            val resultRange = taskRanges?.find { item -> it.stocktakeRange == item.code }
            if (resultRange != null) {
                it.stocktakeRange = resultRange.name
            }

            val resultType = taskTypes?.find { item -> it.strategyType == item.code }
            if (resultType != null) {
                it.strategyType = resultType.name
            }
        }
        activity.adapter.notifyDataSetChanged()
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        result?.let {
            searchOrderNo.set(result)
            serialKeyPress.onEnterKey()
        }
    }

    fun back() {
        activity.finish()
    }

    fun clearOrderNo() {
        searchOrderNo.set("")
        initData(true)
    }

    fun onItemClick(bean: PlanStockList) {
        val it = Intent(activity, PlanStockDetailActivity::class.java)
        it.putExtra("bean", bean)
        activity.startActivity(it)
    }

    fun startSearch() {
        if (pageNo > 1) {
            initData(false)
        }
    }

    fun onChangeStatue(statue: FuShipmentStatue) {
        checkStatue = statue
        initData(true)
    }

    fun addStockLoc() {
        if (CheckUtil.isFastDoubleClick()) {
            activity.showAddStockLocDialog()
        }
    }
}