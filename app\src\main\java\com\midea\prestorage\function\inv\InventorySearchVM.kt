package com.midea.prestorage.function.inv

import android.view.View
import android.widget.Toast
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.LocalInfo
import com.midea.prestorage.beans.net.SerialScanDto
import com.midea.prestorage.function.instorage.response.RespReceiptHeader
import com.midea.prestorage.function.inv.response.FuInvLocationInventory
import com.midea.prestorage.function.inv.response.PackageRelation
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.printer.BluetoothConnectBack
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.MySoundUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.utils.isNull
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody
import retrofit2.http.Query
import java.math.BigDecimal

class InventorySearchVM(val activity: InventorySearchActivity) {

    val sumInvOnHandCount = ObservableField("") //账面库存数 列表adatper sum(on_hand_qty)
    val skuNum = ObservableField("")

    val isRefreshing = ObservableBoolean(false)

    // 当前页码
    var pageNo = 1

    var sum = 0

    val isPrintOk = ObservableField(false)

    var currentItemFlag = ObservableField<Int>(0)

    var itemCode = ""

    // 需求是 要记录上次的查询模式，所以这里用 静态变量
    companion object {
        // 查询模式
        // checkQty=true 货品加状态
        // checkQty=false 货品加状态加批次
        var checkQty = false
    }

    init {

    }

    private val blueBack = object : BluetoothConnectBack {
        override fun success() {
            activity.waitingDialogHelp.hidenDialog()
            isPrintOk.set(true)
        }

        override fun fail() {
            isPrintOk.set(false)
            AppUtils.showToast(activity, "打印机连接失败，请重启打印机!")
        }

        override fun connect() {
            bluetoothOpen()
        }
    }

    fun bluetoothOpen(isAuto: Boolean = true) {
        if (!Printer.isPrintOk()) {
            Printer.openBluetooth(activity, blueBack, isAuto)
        }
    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        isRefreshing.set(true)
        pageNo = 1
        sum = 0
        sumInvOnHandCount.set("")
        skuNum.set("")
        startSearch()
    }

    fun loadMore() {
        startSearch(true)
    }

    //后退键
    val back = View.OnClickListener {
        activity.finish()
    }

    val inputTextChange = object : ViewBindingAdapter.TextChangedListener {

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            if (s?.length == 1) {
                txtChange(s.toString())
            }
        }
    }

    fun clearLocCode() {
        activity.binding.etLocCode.setText("")
    }

    fun clearCustItemCode() {
        activity.binding.etCustItemCode.setText("")
        itemCode = ""
    }

    fun txtChange(str: String) {
        if (str.isNullOrEmpty()) {
            return
        }
        val param = mutableMapOf(
            "locCode" to str,
            "whCode" to activity.getWhCode()
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        // 货品条码校验
        RetrofitHelper.getAppAPI()
            .queryLocationCodeLike(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<LocalInfo>(activity) {
                override fun success(data: LocalInfo?) {
                    if (!data?.list.isNullOrEmpty()) {
                        val list = mutableListOf<String>()
                        data?.list?.forEach {
                            list.add(it.locCode)
                        }
                        activity.setData(list)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }


    fun showSearchTypeMenu() {
        activity.popMenuBinding.cbSearchType1.isChecked = checkQty
        activity.popMenuBinding.cbSearchType2.isChecked = !checkQty
        activity.popupMenuWindow.showAsDropDown(activity.binding.titleBtnMore)
    }

    //重置查询条件
    fun resetSearch() {
        activity.binding.etLocCode.setText("")
        activity.binding.etCustItemCode.setText("")
        itemCode = ""
        sumInvOnHandCount.set("")
        skuNum.set("")
        activity.adapter.data.clear()
        activity.adapter.notifyDataSetChanged()
    }

    //点击了查询按钮
    fun startSearch(isLoadMore: Boolean = false) {

        // 清除前后空格
        val locCode = activity.binding.etLocCode.text.toString().trim()
        //val custItemCode = activity.binding.etCustItemCode.text.toString().trim()

        // 先清空显示结果
//        sumInvOnHandCount.set("")
//        activity.adapter.data.clear()
//        activity.adapter.notifyDataSetChanged()

        val ownerCode = "" //货主编码 留空

//        sumInvOnHandCount.set("") //返回的库存总数，查询前清空

        activity.waitingDialogHelp.showDialogUnCancel()
        val param = mutableMapOf(
            "ownerCode" to ownerCode,
            "locCode" to locCode,
            //"custItemCode" to custItemCode,
            "itemCode" to itemCode,
            "checkQty" to !activity.binding.mSwitch.isChecked,
            "whCode" to activity.getWhCode(),
            "pageNo" to pageNo,
            "pageSize" to 10
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getInventoryAPI()
            .fuInvLocationInventoryList(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PageResult<FuInvLocationInventory>>(activity) {
                override fun success(data: PageResult<FuInvLocationInventory>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    isRefreshing.set(false)
                    //成功提示音
                    MySoundUtils.getInstance().dingSound()

                    if (data != null && data.list != null) {
                        data.list?.forEachIndexed { index, it ->
                            if (!isLoadMore) {
                                it.index = 1 + index
                            } else {
                                it.index = activity.adapter.data.size + 1 + index
                            }
                            it.onHandQtyStr =
                                handleNum(it.onHandQty, it.packageRelationList)
                            //sum += it.onHandQty as Int  //汇总库存数
                            if (index == 0) {
                                sumInvOnHandCount.set(AppUtils.getBigDecimalValueStr(data.list[index].onHandQtyTotal))
                                skuNum.set(AppUtils.getBigDecimalValueStr(data.list[index].skuNum))
                            }
                        }

                        if (isLoadMore) {
                            activity.adapter.data.addAll(data.list)
                            activity.adapter.notifyDataSetChanged()
                            activity.adapter.loadMoreModule.loadMoreComplete()
                        } else {
                            if (itemCode.isNotBlank() && data.list.isEmpty()) {
                                ToastUtils.getInstance()
                                    .showErrorToastWithSound(activity, "该条码系统无库存")
                            }
                            activity.adapter.setNewInstance(data.list)
                            activity.adapter.notifyDataSetChanged()
                        }


                        if (pageNo >= data?.totalPage!!) {
                            activity.adapter.loadMoreModule.loadMoreEnd()
                        } else {
                            pageNo = data?.pageNo!! + 1
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    isRefreshing.set(false)
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun toSearch(isEnter: Boolean = true) {
        if (CheckUtil.isFastDoubleClick(isEnter)) {
            // 清除前后空格
            val locCode = activity.binding.etLocCode.text.toString().trim()
            val custItemCode = activity.binding.etCustItemCode.text.toString().trim()

            if (locCode.isBlank() && custItemCode.isBlank()) {
                //两个条件至少要填一个
                ToastUtils.getInstance().showErrorToastWithSound(activity, "未输入查询条件")
                return
            }

            if (custItemCode.isEmpty()) {
                onRefreshCommand.onRefresh()
            } else {
                onEnterCustItemCode()
            }
        }
    }

    fun onEnterLocCode() {
        if (CheckUtil.isFastDoubleClick()) {
            activity.binding.etCustItemCode.requestFocus()
        }
    }

    private fun onEnterCustItemCode() {
        // 清除前后空格
        //先校验 当前输入的 货品编码 或sn码
        val snNo = activity.binding.etCustItemCode.text.toString().trim()

        val param = mutableMapOf(
            "serialNo" to snNo,
            "whCode" to activity.getWhCode()
        )
        if (activity.binding.etLocCode.text.toString().trim().isNotBlank()) {
            param["locCode"] = activity.binding.etLocCode.text.toString().trim()
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialogUnCancel()

        // 货品条码校验
        RetrofitHelper.getAppAPI()
            .scanCodeOnTransferGood(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<SerialScanDto>(activity) {
                override fun success(data: SerialScanDto?) {
                    // success 表示识别成功
                    activity.waitingDialogHelp.hidenDialog()

                    data?.let {
                        //成功提示音
                        MySoundUtils.getInstance().dingSound()
                        if (activity.binding.etLocCode.text.toString().trim().isNotBlank() && data.itemRfVOS.isNullOrEmpty()) {
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(activity, "该库位上没有该货品的库存数据！")
                        }else {
                            ToastUtils.getInstance()
                                .toastWithOkSound(activity, "条码识别成功", Toast.LENGTH_SHORT)
                        }
                        activity.binding.llToolbar.requestFocus()

                        if (!data.itemRfVOS.isNullOrEmpty() && data.itemRfVOS.size == 1) {
                            itemCode = data.itemRfVOS[0].itemCode ?: ""
                            onRefreshCommand.onRefresh()
                        } else if (!data.itemRfVOS.isNullOrEmpty() && data.itemRfVOS.size > 1) {
                            //扫69码 后端没有返回 custItemCode  如果有custItemCode数组，就弹框选择
                            activity.dlgSelectCustItemCode.show()
                            activity.popAdapterSelectCustItemCode.data.clear()
                            data.itemRfVOS.forEach {
                                activity.popAdapterSelectCustItemCode.addData(it)
                            }
                            activity.popAdapterSelectCustItemCode.notifyDataSetChanged()

                        } else if (!data.serialNo.isNullOrBlank()) {
                            activity.binding.etCustItemCode.setText(data.serialNo)
                        }

                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (activity.binding.etLocCode.text.toString().trim().isNotBlank()) {
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, "该库位上没有该货品的库存数据！")
                    }else {
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, apiErrorModel.message)
                    }
                    itemCode = ""
                    AppUtils.requestFocus(activity.binding.etCustItemCode)

                }
            })
    }


    private var curStep = 0
    val SCAN_STEP_LOCATION = 1
    val SCAN_STEP_ANY_CODE = 2


    fun scanLocation() {
        curStep = SCAN_STEP_LOCATION
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanAnyCode() {
        curStep = SCAN_STEP_ANY_CODE
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }


    fun scanResult(code: String?) {
        code?.let {
            if (curStep == SCAN_STEP_ANY_CODE) {
                activity.binding.etCustItemCode.setText(code)
                onEnterCustItemCode()
            } else if (curStep == SCAN_STEP_LOCATION) {
                activity.binding.etLocCode.setText(code)
                AppUtils.requestFocus(activity.binding.etCustItemCode)
            }
        }
    }

    private fun handleNum(
        sumQtyBig: BigDecimal,
        packageRelationList: List<PackageRelation>?
    ): String {
        val csUnit = packageRelationList?.find { it.cdprUnit == "CS" }?.cdprDesc
        val ipUnit = packageRelationList?.find { it.cdprUnit == "IP" }?.cdprDesc
        val eaUnit = packageRelationList?.find { it.cdprUnit == "EA" }?.cdprDesc ?: ""

        val packageParaCs =
            packageRelationList?.find { it.cdprUnit == "CS" }?.cdprQuantity
                ?: BigDecimal.ZERO
        val packageParaIp =
            packageRelationList?.find { it.cdprUnit == "IP" }?.cdprQuantity
                ?: BigDecimal.ZERO

        val sumCsQty = if (packageParaCs > BigDecimal.ZERO) {
            sumQtyBig.divide(packageParaCs, BigDecimal.ROUND_DOWN)
                .setScale(0, BigDecimal.ROUND_DOWN)
        } else {
            BigDecimal.ZERO
        }

        var sumQty = sumQtyBig.subtract(sumCsQty.multiply(packageParaCs))

        val sumIpQty = if (packageParaIp > BigDecimal.ZERO) {
            sumQty.divide(packageParaIp, BigDecimal.ROUND_DOWN).setScale(0, BigDecimal.ROUND_DOWN)
        } else {
            BigDecimal.ZERO
        }

        sumQty = sumQty.subtract(sumIpQty.multiply(packageParaIp))

        val sumEaQty = sumQty

        val qtyInfo = mutableListOf<String>()

        if (sumCsQty > BigDecimal.ZERO) {
            qtyInfo.add("${sumCsQty.stripTrailingZeros().toPlainString()}$csUnit")
        }

        if (sumIpQty > BigDecimal.ZERO) {
            qtyInfo.add("${sumIpQty.stripTrailingZeros().toPlainString()}$ipUnit")
        }

        if (sumEaQty > BigDecimal.ZERO) {
            qtyInfo.add("${sumEaQty.stripTrailingZeros().toPlainString()}$eaUnit")
        }

        return if (qtyInfo.isEmpty()) {
            "${AppUtils.getBigDecimalValueStr(sumQtyBig)}$eaUnit"
        } else {
            "${
                AppUtils.getBigDecimalValueStr(sumQtyBig)
            }$eaUnit (${qtyInfo.joinToString("")})"
        }
    }
}