package com.midea.prestorage.function.inv.response;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class InvStockTakeTaskHeader implements Serializable {

    private BigDecimal totalFromQty;
    private BigDecimal totalToQty;

    private List<InvStockTakeTaskDetail> bwmsRfPickTaskDetailResponses;

    public BigDecimal getTotalFromQty() {
        return totalFromQty;
    }

    public void setTotalFromQty(BigDecimal totalFromQty) {
        this.totalFromQty = totalFromQty;
    }

    public BigDecimal getTotalToQty() {
        return totalToQty;
    }

    public void setTotalToQty(BigDecimal totalToQty) {
        this.totalToQty = totalToQty;
    }

    public List<InvStockTakeTaskDetail> getBwmsRfPickTaskDetailResponses() {
        return bwmsRfPickTaskDetailResponses;
    }

    public void setBwmsRfPickTaskDetailResponses(List<InvStockTakeTaskDetail> bwmsRfPickTaskDetailResponses) {
        this.bwmsRfPickTaskDetailResponses = bwmsRfPickTaskDetailResponses;
    }
}
