package com.midea.prestorage.function.outstorage

import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.OutStorageBackTaskResp
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.LotAttUnit
import com.midea.prestorage.utils.MathUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R

class OutStorageBackTaskAdapter :
    CommonAdapter<OutStorageBackTaskResp>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_out_storage_back_task_care else R.layout.item_out_storage_back_task) {

    override fun convert(holder: BaseViewHolder?, item: OutStorageBackTaskResp?) {
        super.convert(holder, item)
        val eaUnit = item?.packageRelationList?.find { "EA" == it.cdprUnit }?.cdprDesc ?: ""
        holder?.setText(
            R.id.tv_plan_qty_value,
            "${AppUtils.getBigDecimalValueStr(item?.fromQty)}$eaUnit"
        )
        holder?.setText(
            R.id.tv_barcode_value,
            LotAttUnit.formatWhBarcode69(item?.whCsBarcode69, item?.whBarcode69)
        )

        if (!(SPUtils[ProfileVM.CARE_MODE, false] as Boolean)) {
            when (item?.status?.toIntOrNull()) {
                100 -> holder?.setTextColor(
                    R.id.tv_status,
                    ContextCompat.getColor(holder.itemView.context, R.color.colorGreen)
                )
                150 -> holder?.setTextColor(
                    R.id.tv_status,
                    ContextCompat.getColor(holder.itemView.context, R.color.btn_blue)
                )
                300 -> holder?.setTextColor(
                    R.id.tv_status,
                    ContextCompat.getColor(holder.itemView.context, R.color.colorOrange)
                )
                350 -> holder?.setTextColor(
                    R.id.tv_status,
                    ContextCompat.getColor(holder.itemView.context, R.color.colorOrange)
                )
                900 -> holder?.setTextColor(
                    R.id.tv_status,
                    ContextCompat.getColor(holder.itemView.context, R.color.colorRed2)
                )
                999 -> holder?.setTextColor(
                    R.id.tv_status,
                    ContextCompat.getColor(holder.itemView.context, R.color.colorRed2)
                )
            }
        }
    }

}