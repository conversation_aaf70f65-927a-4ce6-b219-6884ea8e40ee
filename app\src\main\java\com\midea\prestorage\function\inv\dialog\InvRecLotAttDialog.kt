package com.midea.prestorage.function.inv.dialog

import android.app.AlertDialog
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.inv.response.LotDetail
import com.midea.prestorage.function.inv.response.RespMaterialList
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogInvRecLotAttBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

class InvRecLotAttDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {
    var binding: DialogInvRecLotAttBinding
    private lateinit var statueDialog: FilterDialog
    var tag: String? = null
    var materialList: RespMaterialList? = null

    init {
        val contentView =
            LayoutInflater.from(mContext).inflate(R.layout.dialog_inv_rec_lot_att, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = InvRecLotAttDialogVM(this)

        setCanceledOnTouchOutside(false)
        // 加上这个 确保按回退键也不关闭选择框
        setCancelable(false)
    }

    fun setData(templates: MutableList<LotDetail>) {
        binding.vm!!.templates = templates
    }

    override fun show() {
        super.show()

        binding.vm!!.initData()
        initListener()
    }

    private fun initListener() {
        binding.vm!!.lotAttVMList.forEach { item ->
            item.clickItem.observe(mContext, Observer { click ->
                if (click) {
                    when (item.lotAtt) {
                        "LOT_ATT01" -> if (binding.vm!!.checkTimePick(item.fieldType)) binding.vm!!.showTimePick(
                            1
                        )
                        "LOT_ATT02" -> if (binding.vm!!.checkTimePick(item.fieldType)) binding.vm!!.showTimePick(
                            2
                        )
                        "LOT_ATT03" -> if (binding.vm!!.checkTimePick(item.fieldType)) binding.vm!!.showTimePick(
                            3
                        )
                        "LOT_ATT05", "LOT_ATT06", "LOT_ATT07", "LOT_ATT08", "LOT_ATT09", "LOT_ATT10", "LOT_ATT11", "LOT_ATT12" -> if (binding.vm!!.checkPopField(
                                item.fieldType
                            )
                        ) handlePopField(item)
                    }
                }
            })
        }

        binding.vm!!.lotAttStr.observe(mContext, {
            addStatueData(it)
            statueDialog.show()
        })
    }

    private fun handlePopField(data: LotAttDialogVM) {
        initDialog(data)
        if (data.key.isNullOrBlank()) {
            val beans = mutableListOf<BaseItemShowInfo>()
            statueDialog.addAllData(beans)
            statueDialog.show()
        } else {
            binding.vm!!.getLotAttStr(data.key ?: "")
        }
    }

    private fun initDialog(data: LotAttDialogVM) {
        //商品状态dialog
        statueDialog = FilterDialog(mContext)
        statueDialog.setTitle("请选择" + data.title)
        statueDialog.dismissEdit()
        statueDialog.setOnCheckListener {
            data.content.set(it.showInfo)
            statueDialog.dismiss()
        }
    }

    private fun addStatueData(data: MutableList<String>) {
        val beans = mutableListOf<BaseItemShowInfo>()
        data.forEach {
            beans.add(BaseItemShowInfo(it))
        }
        statueDialog.addAllData(beans)
    }

    fun setBackData(backData: BackData) {
        binding.vm!!.backDataListener = backData
    }

    interface BackData {
        fun onConfirmClick(templates: MutableList<LotDetail>?)
        fun onCancelClick()
    }
}