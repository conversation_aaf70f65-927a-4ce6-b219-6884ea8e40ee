package com.midea.prestorage.function.picktaskdetail

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseViewModelDialog
import com.midea.prestoragesaas.databinding.ActivityPickTaskFinishDialogBinding
import com.midea.prestorage.function.inv.response.InvStockTakeTaskDetail
import com.midea.prestorage.function.picktask.PickTaskDialog
import com.midea.prestorage.utils.AppUtils
import java.math.BigDecimal


class PickTaskFinishDialog : BaseViewModelDialog<PickTaskFinishDialogVM>() {

    private lateinit var binding: ActivityPickTaskFinishDialogBinding
    private var allData: MutableList<InvStockTakeTaskDetail>? = null
    private var totalPickQty: BigDecimal? = null
    private var back: PickTaskFinishDialogBack? = null

    override fun beforeOnCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        vm = ViewModelProvider.AndroidViewModelFactory(requireActivity().application)
            .create(PickTaskFinishDialogVM::class.java)
        binding = DataBindingUtil.inflate(
            inflater,
            R.layout.activity_pick_task_finish_dialog,
            container,
            false
        )
        binding.vm = vm
        binding.lifecycleOwner = this

        initView()
        initObserver()
        return binding.root
    }

    private fun initView() {
        vm.isResultOk.observe(this, Observer<Boolean> {
            if (it) {
                back?.pickTaskFinishDialogBack()
            }
        })
    }

    fun setData(
        allDataFun: MutableList<InvStockTakeTaskDetail>,
        totalPickQtyFun: BigDecimal
    ) {
        allData = allDataFun
        totalPickQty = totalPickQtyFun
    }

    override fun showNow(manager: FragmentManager, tag: String?) {
        super.showNow(manager, tag)

        vm.totalPickQty.value = totalPickQty.toString()
        vm.dataList.value = allData
    }

    fun setPickTaskDialogBack(pickTaskFinishDialogBack: PickTaskFinishDialogBack) {
        back = pickTaskFinishDialogBack
    }

    private fun initObserver() {
        binding.vm?.isFinishiDialog?.observe(this, Observer<Boolean> {
            if (it) {
                dismiss()
            }
        })
    }

    interface PickTaskFinishDialogBack {
        fun pickTaskFinishDialogBack()
    }
}