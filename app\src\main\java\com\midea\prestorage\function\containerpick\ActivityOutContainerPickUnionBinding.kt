package com.midea.prestorage.function.containerpick

import android.widget.EditText
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestorage.function.sendcheck.ActivitySerialCheckFirstUnionBinding
import com.midea.prestorage.function.sendcheck.SerialCheckFirstVM
import com.midea.prestoragesaas.databinding.ActivityOutContainerPickBinding
import com.midea.prestoragesaas.databinding.ActivityOutContainerPickCareBinding
import com.midea.prestoragesaas.databinding.ActivitySerialCheckFirstBinding
import com.midea.prestoragesaas.databinding.ActivitySerialCheckFirstCareBinding

sealed class ActivityOutContainerPickUnionBinding {
    abstract var vm: ContainerPickVM?
    abstract val llTitleBar: RelativeLayout
    abstract val srl: SwipeRefreshLayout
    abstract val etSearchOrderNo: EditText
    abstract val recycle: RecyclerView
    abstract val tvNotification: TextView

    class V2(val binding: ActivityOutContainerPickCareBinding) :
        ActivityOutContainerPickUnionBinding() {
        override var vm: ContainerPickVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val srl = binding.srl
        override val etSearchOrderNo = binding.etSearchOrderNo
        override val recycle = binding.recycle
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityOutContainerPickBinding) :
        ActivityOutContainerPickUnionBinding() {
        override var vm: ContainerPickVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val srl = binding.srl
        override val etSearchOrderNo = binding.etSearchOrderNo
        override val recycle = binding.recycle
        override val tvNotification = binding.tvNotification
    }
}
