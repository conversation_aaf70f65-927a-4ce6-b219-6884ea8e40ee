package com.midea.prestorage.dialog

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.midea.prestorage.event.LiveEvent

class SettingOptionDialogViewModel(application: Application) : AndroidViewModel(application) {

    val confirmEvent = LiveEvent<Boolean>()
    val closeEvent = LiveEvent<Boolean>()

    fun confirm() {
        confirmEvent.value = true
    }

    fun close() {
        closeEvent.value = true
    }

}