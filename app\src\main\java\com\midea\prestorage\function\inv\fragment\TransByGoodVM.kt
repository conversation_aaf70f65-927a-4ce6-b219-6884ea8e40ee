package com.midea.prestorage.function.inv.fragment

import android.app.AlertDialog
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.databinding.ObservableField
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.beans.net.RespRecommendLoc
import com.midea.prestorage.beans.net.SerialScanDto
import com.midea.prestoragesaas.databinding.PopViewForSelectCustemItemCodeBinding
import com.midea.prestoragesaas.databinding.PopViewForTransferLotnumBinding
import com.midea.prestorage.function.inv.TransferActivity
import com.midea.prestorage.function.inv.response.BsLocation
import com.midea.prestorage.function.inv.response.FuInvLocationInventory
import com.midea.prestorage.function.inv.response.ItemRfVO
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.MathUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal
import java.util.*


class TransByGoodVM(val fragment: TransByGoodFragment) {

    val bgColor = ObservableField<Drawable>()

    var curLotInfo = ObservableField<String>("")  //批次属性信息
    var totalMoveNum = ObservableField<String>("0")//总共移库数量

    //商品描述 (根据后端查询结果拼接出来的)
    var transferDataInfo = ObservableField<String>("")

    //货品编码，有69码链接
    var custItemCode = ObservableField<String>("")

    // 步骤常量 根据不同的步骤 开启或关闭不同的editText
    val STEP_SELECT_OWNER = 0
    val STEP_SELECT_FROM_LOC = 1
    val STEP_ENTER_CUST_ITEM_CODE = 2
    val STEP_ENTER_MOVE_NUM = 3
    val STEP_ENTER_TO_LOC = 4
    val STEP_ENTER_FOR_SUBMIT = 5

    var locGlobe: String? = null
    var itemCodeGlobe: String? = null
    var firstSelectedPosition = -1

    init {
        resetDefault()
        locGlobe = fragment.activity?.intent?.getStringExtra("loc")
        itemCodeGlobe = fragment.activity?.intent?.getStringExtra("itemCode")

        if (!locGlobe.isNullOrEmpty() && !itemCodeGlobe.isNullOrEmpty()) {
            fragment.binding.etFromLocCode.setText(locGlobe)
            onEnterFromLocCode()
        }
    }

    fun setTotalNum(totalNum: String) {
        totalMoveNum.set(totalNum)
    }

    fun resetDefault(isComeFromMove: Boolean = false) {
        transferDataInfo.set("")
        fragment.binding.etTargetLocCode.setText("")
        fragment.binding.etFromLocCode.setText("")
        fragment.binding.etCustItemCode.setText("")

        if (isComeFromMove) {//如果是移库成功就清空
            fragment.getTransByGoodAdpter().data?.clear()
            fragment.getTransByGoodAdpter().notifyDataSetChanged()
            fragment.getTransByGoodAdpter().calculatedTotalNub()
        }

        setStep(STEP_SELECT_FROM_LOC)//当已经选择了货主
    }

    //获取推荐库位
    fun getRecommendLoc(bean: FuInvLocationInventory) {
        (fragment.activity as BaseActivity).waitingDialogHelp.showDialog()
        val map = mutableMapOf(
            "whCode" to (fragment.activity as BaseActivity).getWhCode(),
            "ownerCode" to bean.ownerCode,
            "scenario" to "move",
            "orderType" to "",
            "qty" to bean.usableQty,
            "packageUnit" to "EA",
            "lotAtt01" to bean.lotAtt01,
            "lotAtt02" to bean.lotAtt02,
            "lotAtt04" to bean.lotAtt04,
            "lotAtt05" to bean.lotAtt05,
            "lotAtt06" to bean.lotAtt06,
            "containerCode" to "",
            "itemCode" to bean.itemCode,
            "locCode" to fragment.binding.etFromLocCode.text.toString().trim()
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        RetrofitHelper.getAddGoodsService()
            .getRecommendLoc(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<RespRecommendLoc>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: RespRecommendLoc?) {
                    data?.locCode?.let {
                        if (!it.isNullOrEmpty()) {
                            fragment.binding.etTargetLocCode.setText(it)
                            onEnterToLocCode()
                        }
                    } ?: run {
                        (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(fragment.activity, apiErrorModel.message)
                }
            })
    }

    fun search(isUseCurLotNum: Boolean) {
        transferDataInfo.set("")  //商品描述
        curLotInfo.set("")  //批次属性信息

        (fragment.activity as BaseActivity).waitingDialogHelp.showDialog()
        RetrofitHelper.getWareManageAPI()
            .getFuInvLocationInventory(
                "",
                (fragment.activity as BaseActivity).getWhCode(),
                fragment.binding.etCustItemCode.text.toString().trim(),
                fragment.binding.etFromLocCode.text.toString().trim(),
                "",
                1,
                1000
            )
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<MutableList<FuInvLocationInventory>>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: MutableList<FuInvLocationInventory>?) {
                    (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()

                    //计算可以移动的库存
                    data?.forEach {
                        it.usableQty = BigDecimal.ZERO

                        var canUserNum: BigDecimal =
                            it.onHandQty.subtract(it.allocatedQty).subtract(it.inTransitQty).subtract(it.lockedQty)
                        if (it.repOutQty != null) {
                            // 2022年4月14日 星期四 再减去补货下架数
                            canUserNum = canUserNum.subtract(it.repOutQty)
                        }

                        //有数量的才要处理，为0的就删除
                        if (canUserNum.compareTo(BigDecimal.ZERO) == 1) {
                            it.usableQty = AppUtils.getBigDecimalValue(canUserNum.stripTrailingZeros().toPlainString())
                            it.tempMoveNum = AppUtils.getBigDecimalValue(canUserNum.stripTrailingZeros().toPlainString())
                        }

                        it.packageRelationList = MathUtils.handleInvPackageRelation(
                            AppUtils.getBigDecimalValue(it.usableQty),
                            it.packageRelationList
                        )

                        it.packageRelationList?.let { list ->
                            for (bean in list) {
                                when (bean.cdprUnit) {
                                    "OT" -> {
                                        it.editNum1 = AppUtils.getBigDecimalValue(bean.num)
                                        it.otQuantity = bean.cdprQuantity
                                    }
                                    "PL" -> {
                                        it.editNum2 = AppUtils.getBigDecimalValue(bean.num)
                                        it.plQuantity = bean.cdprQuantity
                                    }
                                    "CS" -> {
                                        it.editNum3 = AppUtils.getBigDecimalValue(bean.num)
                                        it.csQuantity = bean.cdprQuantity
                                    }
                                    "IP" -> {
                                        it.editNum4 = AppUtils.getBigDecimalValue(bean.num)
                                        it.ipQuantity = bean.cdprQuantity
                                    }
                                    "EA" -> {
                                        it.editNum = AppUtils.getBigDecimalValue(bean.num)
                                        it.eaQuantity = bean.cdprQuantity
                                    }
                                }
                            }
                        }
                    }

                    val it = data?.iterator()
                    while (it != null && it.hasNext()) {
                        val item = it.next()
                        if (AppUtils.isZero(item.usableQty)) {
                            it.remove()
                        }
                    }

                    fragment.getTransByGoodAdpter().apply {
                        setNewInstance(data)
                        notifyDataSetChanged()
                    }
                    setStep(STEP_ENTER_TO_LOC)
                    fragment.binding.etFromLocCode.isEnabled = false//禁止
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(fragment.activity, apiErrorModel.message)
                    // 后端接口报  716020 库存不存在 时 要清空条码编辑框让用户重新填
                    if (statusCode.equals(716020L)) {
                        fragment.binding.etCustItemCode.setText("")
                        AppUtils.requestFocus(fragment.binding.etCustItemCode)
                    }
                }
            })
    }

    fun onEnterFromLocCode() {
        // 去除前后空格 校验库位
        val fromLocCode = fragment.binding.etFromLocCode.text.toString().trim()
        if (fromLocCode.isBlank()) {
            ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "来源库位编码不能为空")
            AppUtils.requestFocus(fragment.binding.etFromLocCode)
        } else {
//            if (fromLocCode == "IRA") {
//                ToastUtils.getInstance()
//                    .showErrorToastWithSound(fragment.activity, "IRA库位调出需走移库申请单审批")
//                return
//            }
            validateLocationCode("from", fromLocCode)
        }
    }

    fun onEnterToLocCode() {
        // 去除前后空格
        val fromLocCode = fragment.binding.etFromLocCode.text.toString().trim()
        val toLocCode = fragment.binding.etTargetLocCode.text.toString().trim()

        if (toLocCode.isEmpty()) {
            ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "目标库位编码不能为空")
            (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()
        } else if (fromLocCode.equals(toLocCode)) {
            ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "源库位与目标库位不能相同")
            fragment.binding.etTargetLocCode.setText("")
            (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()
            return
        } else {
            validateLocationCode("to", toLocCode)
        }
    }

    // 校验库位是否存在
    //type :  from 表示 要检查的是来源库位     to 表示 要检查的是目标库位
    fun validateLocationCode(type: String, locCode: String) {
        (fragment.activity as BaseActivity).waitingDialogHelp.showDialog()

        // 检查 库位编码是否正确 (即检查仓库里是否有该库位 库位数量>0表示存在)
        RetrofitHelper.getBasicDataAPI()
            .getLocationDetail((fragment.activity as BaseActivity).getWhCode(), locCode)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<BsLocation>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: BsLocation?) {
                    (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()
                    if (data == null) {
                        if (type.equals("from")) {
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(fragment.activity, "来源库位[$locCode]不存在")
                            AppUtils.clearAndFocus(fragment.binding.etFromLocCode)
                        } else if (type.equals("to")) {
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(fragment.activity, "目标库位[$locCode]不存在")
                            AppUtils.clearAndFocus(fragment.binding.etTargetLocCode)
                        }
                    } else {
                        val fromLocCode = fragment.binding.etFromLocCode.text.toString().trim()
                        val toLocCode = fragment.binding.etTargetLocCode.text.toString().trim()

                        if (fromLocCode.equals(toLocCode)) {
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(fragment.activity, "源库位与目标库位不能相同")
                            fragment.binding.etTargetLocCode.setText("")
                        } else if (type.equals("from")) {
                            if (data.locUseType.toUpperCase().equals("BH")) {
                                ToastUtils.getInstance().showErrorToastWithSound(
                                    fragment.activity,
                                    "备货库位[" + locCode + "]不能移动"
                                )
                                //清空重填
                                AppUtils.clearAndFocus(fragment.binding.etFromLocCode)
                            } else {
                                setStep(STEP_ENTER_CUST_ITEM_CODE)
                                fragment.binding.etFromLocCode.isEnabled = false//禁止
                            }
                        } else if (type.equals("to")) {
                            setStep(STEP_ENTER_FOR_SUBMIT)
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()
                    if (type.equals("from")) {
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(fragment.activity, "来源库位[$locCode]不存在")
                        //清空重填
                        AppUtils.clearAndFocus(fragment.binding.etFromLocCode)

                    } else if (type.equals("to")) {
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(fragment.activity, "目标库位[$locCode]不存在")
                        //清空重填
                        AppUtils.clearAndFocus(fragment.binding.etTargetLocCode)
                    }

                }

            })
    }


    // 输入了  商品条码  并回车
    fun onEnterItemCode(isClick: Boolean? = false) {
        transferDataInfo.set("")  // 商品描述

        if (fragment.binding.etFromLocCode.text.isNullOrEmpty()) {
            ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "来源库位不能为空")
            return
        }

        if (fragment.binding.etCustItemCode.text.isNullOrEmpty()) {
            ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "货品条码不能为空")
            return
        }

        //清除前后空格 先校验 当前输入的 货品编码 或sn码
        val snNo = fragment.binding.etCustItemCode.text.toString().trim()

        val param = mutableMapOf(
            "serialNo" to snNo,
            "whCode" to (fragment.activity as BaseActivity).getWhCode()
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        (fragment.activity as BaseActivity).waitingDialogHelp.showDialog()

        // 货品条码校验
        RetrofitHelper.getAppAPI()
            .scanCodeOnTransferGood(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<SerialScanDto>(fragment.activity as RxAppCompatActivity) {
                override fun success(serialScanDto: SerialScanDto?) {
                    // success 表示识别成功
                    (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()

                    serialScanDto?.let {
                        if (!serialScanDto.itemRfVOS.isNullOrEmpty() && serialScanDto.itemRfVOS.size == 1) {
                            fragment.binding.etCustItemCode.setText(serialScanDto.itemRfVOS.get(0).custItemCode)
                            search(false)
                        } else if (!serialScanDto.itemRfVOS.isNullOrEmpty() && serialScanDto.itemRfVOS.size > 1) {
                            if (isClick == true) {
                                val result =
                                    serialScanDto.itemRfVOS.find { it.custItemCode == snNo }
                                if (result != null) {
                                    fragment.binding.etCustItemCode.setText(result.custItemCode)
                                    search(false)
                                    return
                                }
                            }
                            //扫69码 后端没有返回 custItemCode  如果有custItemCode数组，就弹框选择
                            dlgSelectCustItemCode.show()
                            popAdapterSelectCustItemCode.data.clear()
                            serialScanDto.itemRfVOS.forEach {
                                popAdapterSelectCustItemCode.addData(it)
                            }
                            popAdapterSelectCustItemCode.notifyDataSetChanged()

                        } else {
                            fragment.binding.etCustItemCode.setText("")
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(fragment.activity, "无法识别条码")
                        }

                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    (fragment.activity as BaseActivity).waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(fragment.activity, apiErrorModel.message)
                    fragment.binding.etCustItemCode.setText("")
                    AppUtils.requestFocus(fragment.binding.etCustItemCode)

                }
            })
    }

    // 调用后端接口执行移库
    fun submitTransfer() {
        val fromLocCode = fragment.binding.etFromLocCode.text.toString().trim()
        val toLocCode = fragment.binding.etTargetLocCode.text.toString().trim()

        if (fromLocCode.isEmpty()) {
            ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "请输入来源库位")
            return
        }

        if (fragment.binding.etCustItemCode.text.isNullOrEmpty()) {
            ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "请输入货品条码")
            return
        }

        if (toLocCode.isEmpty()) {
            ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "请输入目标库位")
            return
        }

        if (fromLocCode.equals(toLocCode)) {
            ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "源库位与目标库位不能相同")
            fragment.binding.etTargetLocCode.setText("")
            return
        }

        if (totalMoveNum.get().toString() == "0" || totalMoveNum.get().toString() == "") {
            ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "请勾选需要移库的明细行")
            return
        }

        val list = mutableListOf<Map<String, String>>()
        fragment.apply {
            getTransByGoodAdpter().data.forEach {
                if (it.isSelected) {
                    val param: HashMap<String, String> = HashMap<String, String>()
                    param.put("whCode", (fragment.activity as BaseActivity).getWhCode())
                    param.put("movQty", it.moveNum.toString())
                    param.put(
                        "custItemCode",
                        fragment.binding.etCustItemCode.text.toString().trim()
                    )
                    param.put("lotNum", it.lotNum)
                    param.put("toLocCode", toLocCode.trim().toUpperCase())
                    param.put("fmLocCode", fromLocCode.trim().toUpperCase())
                    param.put("ownerCode", it.ownerCode)
                    param.put("ownerName", it.ownerName)
                    param.put("traceId", it.traceId)
                    list.add(param)
                }
            }
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(list)
        )

        (fragment.activity as TransferActivity).waitingDialogHelp.showDialog()
        fragment.binding.btnSubmitTransfer.setBackgroundResource(R.drawable.bg_bt_gray)
        fragment.binding.btnSubmitTransfer.isClickable = false

        RetrofitHelper.getWareManageAPI()
            .movWareByItemCode(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: Any?) {
                    (fragment.activity as TransferActivity).waitingDialogHelp.hidenDialog()
                    fragment.binding.btnSubmitTransfer.setBackgroundResource(R.drawable.bg_bt_blue)
                    fragment.binding.btnSubmitTransfer.isClickable = true
                    ToastUtils.getInstance().showSuccessToastWithSound(fragment.activity, "移库成功")
                    resetDefault(true)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    (fragment.activity as TransferActivity).waitingDialogHelp.hidenDialog()
                    fragment.binding.btnSubmitTransfer.setBackgroundResource(R.drawable.bg_bt_blue)
                    fragment.binding.btnSubmitTransfer.isClickable = true
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(fragment.activity, apiErrorModel.message)
                    if (statusCode.equals(716038L)) {
                        // 目标库位不合法的情况下 清空目标库位
                        fragment.binding.etTargetLocCode.setText("")
                    }
                }
            })
    }


    init {
        val position = fragment.arguments?.getInt("position")
        if (position == 0) {
            bgColor.set(
                ColorDrawable(
                    ContextCompat.getColor(
                        fragment.requireActivity(),
                        R.color.bg_orange
                    )
                )
            )
        } else {
            bgColor.set(
                ColorDrawable(
                    ContextCompat.getColor(
                        fragment.requireActivity(),
                        R.color.btn_red
                    )
                )
            )
        }

        initPopWinSelectLot()
        initPopWinSelectCustItemCode()
    }


    //根据步骤决定哪些editText能编辑
    fun setStep(step: Int) {
        if (step == STEP_SELECT_OWNER) {
            // 初始状态 只能选货主
//            fragment.binding.tvSelectOwner.isClickable = true
            fragment.binding.etFromLocCode.isEnabled = false
            fragment.binding.etCustItemCode.isEnabled = false
//            fragment.binding.etMoveNum.isEnabled = false
            fragment.binding.etTargetLocCode.isEnabled = false
//            moveNum.set("1")

        } else if (step == STEP_SELECT_FROM_LOC) {
//            fragment.binding.tvSelectOwner.isClickable = true
            // 选了货主后 可以选来源库位
            fragment.binding.etFromLocCode.isEnabled = true
            AppUtils.requestFocus(fragment.binding.etFromLocCode)
            fragment.binding.etCustItemCode.isEnabled = false
//            fragment.binding.etMoveNum.isEnabled = false
            fragment.binding.etTargetLocCode.isEnabled = false
        } else if (step == STEP_ENTER_CUST_ITEM_CODE) {
//            fragment.binding.tvSelectOwner.isClickable = true
            fragment.binding.etFromLocCode.isEnabled = true
            // 选了来源库位后 可以输货品编码
            fragment.binding.etCustItemCode.isEnabled = true
            AppUtils.requestFocus(fragment.binding.etCustItemCode)
//            fragment.binding.etMoveNum.isEnabled = false
            fragment.binding.etTargetLocCode.isEnabled = false

            if (!itemCodeGlobe.isNullOrEmpty()) {
                fragment.binding.etCustItemCode.setText(itemCodeGlobe)
                onEnterItemCode(true)
            }
        } else if (step == STEP_ENTER_MOVE_NUM) {
//            fragment.binding.tvSelectOwner.isClickable = true
            fragment.binding.etFromLocCode.isEnabled = true
            fragment.binding.etCustItemCode.isEnabled = true
            // 输货品编码后 可以编辑数量
//            fragment.binding.etMoveNum.isEnabled = true
//            AppUtils.requestFocus(fragment.binding.etMoveNum)
            fragment.binding.etTargetLocCode.isEnabled = false
        } else if (step == STEP_ENTER_TO_LOC) {
//            fragment.binding.tvSelectOwner.isClickable = true
            fragment.binding.etFromLocCode.isEnabled = true
            fragment.binding.etCustItemCode.isEnabled = true
//            fragment.binding.etMoveNum.isEnabled = true  //数量仍然可以编辑，避免回头修改时不能修改
            // 编辑数量 后  可以填目标库位
            fragment.binding.etTargetLocCode.isEnabled = true
            AppUtils.requestFocus(fragment.binding.etTargetLocCode)
        } else if (step == STEP_ENTER_FOR_SUBMIT) {
//            fragment.binding.tvSelectOwner.isClickable = true
            fragment.binding.etFromLocCode.isEnabled = true
            fragment.binding.etCustItemCode.isEnabled = true
//            fragment.binding.etMoveNum.isEnabled = true
            fragment.binding.etTargetLocCode.isEnabled = true
            // 移除edittext的焦点
//            fragment.binding.llyCountNumber.requestFocus()
        }
    }

    //选择批属性的弹窗
    private lateinit var dlgSelectLot: AlertDialog

    //69码 查询出多个custItemCodes的时候 弹框让用户选择custItemCode
    private lateinit var dlgSelectCustItemCode: AlertDialog

    private lateinit var popBindingSelectLot: PopViewForTransferLotnumBinding
    private lateinit var popBindingSelectCustItemCode: PopViewForSelectCustemItemCodeBinding

    private lateinit var popAdapterSelectLot: CommonAdapter<BaseItemForPopup>
    private lateinit var popAdapterSelectCustItemCode: TransByGoodFragment.PopListAdapter

    // 初始化选择批属性的弹窗
    private fun initPopWinSelectLot() {

        val popViewSelectLot = LayoutInflater.from(fragment.context)
            .inflate(R.layout.pop_view_for_transfer_lotnum, null)
        popBindingSelectLot = DataBindingUtil.bind(popViewSelectLot)!!

        val alertDialogBuilder = AlertDialog.Builder(fragment.activity)
        alertDialogBuilder.setView(popViewSelectLot)
        dlgSelectLot = alertDialogBuilder.create()

        popAdapterSelectLot = ListLotInfoAdapter()
        popBindingSelectLot.recyclerViewInPop.layoutManager = LinearLayoutManager(fragment.context)
        popBindingSelectLot.recyclerViewInPop.adapter = popAdapterSelectLot

        popAdapterSelectLot.setOnItemClickListener { adapter, view, position ->
            val locationInventory = adapter.getItem(position) as FuInvLocationInventory
            val lotNum = locationInventory.lotNum
//            curLotNum.set(lotNum) //用户选择要移库的库存 的 批次属性号

            var info = ""
            if (!locationInventory.itemName.isNullOrBlank()) info += locationInventory.itemName
            if (locationInventory.onHandQty != null) info += "  /在库数量：" + AppUtils.getBigDecimalValue(locationInventory.onHandQty)
            if (locationInventory.allocatedQty != null) info += "  /分配数量：" + AppUtils.getBigDecimalValue(locationInventory.allocatedQty)
            if (locationInventory.inTransitQty != null) info += "  /在途数量：" + AppUtils.getBigDecimalValue(locationInventory.inTransitQty)
            if (locationInventory.lockedQty != null) info += "  /冻结数量：" + AppUtils.getBigDecimalValue(locationInventory.lockedQty)
            if (locationInventory.repOutQty != null) info += "  /补货下架数：" + AppUtils.getBigDecimalValue(locationInventory.repOutQty)

            //商品描述
            transferDataInfo.set(info)


            //设置货品编码
            custItemCode.set(locationInventory.custItemCode + if (locationInventory.whBarcode69 == "" || locationInventory.whBarcode69 == null) "" else "/" + locationInventory.whBarcode69)

            //批属性信息
            curLotInfo.set(getLotInfo(locationInventory))

            // 可移库数量  sum(on_hand_qty - allocated_qty - locked_qty)
            // 可移库数量 2022年4月13日 星期三 还要减去 补货下架数
            var canMoveInt: BigDecimal =
                locationInventory.onHandQty.subtract(locationInventory.allocatedQty).subtract(locationInventory.lockedQty).subtract(locationInventory.inTransitQty)
            if (locationInventory.repOutQty != null) {
                canMoveInt = canMoveInt.subtract(locationInventory.repOutQty)
            }

            dlgSelectLot.dismiss()
        }

        popBindingSelectLot.closePop.setOnClickListener {
            dlgSelectLot.dismiss()
        }

    }

    // 初始化选择客户商品编码的弹窗
    private fun initPopWinSelectCustItemCode() {

        val popViewSelectCustItemCode = LayoutInflater.from(fragment.context)
            .inflate(R.layout.pop_view_for_select_custem_item_code, null)
        popBindingSelectCustItemCode = DataBindingUtil.bind(popViewSelectCustItemCode)!!

        val alertDialogBuilder = AlertDialog.Builder(fragment.activity)
        alertDialogBuilder.setView(popViewSelectCustItemCode)
        dlgSelectCustItemCode = alertDialogBuilder.create()

        popAdapterSelectCustItemCode = TransByGoodFragment.PopListAdapter()
        popBindingSelectCustItemCode.recyclerViewInPop.layoutManager =
            LinearLayoutManager(fragment.context)
        popBindingSelectCustItemCode.recyclerViewInPop.adapter = popAdapterSelectCustItemCode

        popAdapterSelectCustItemCode.setOnItemClickListener { adapter, view, position ->
            val item = adapter.getItem(position) as ItemRfVO
            fragment.binding.etCustItemCode.setText(item.custItemCode)
            onEnterItemCode(true)
            dlgSelectCustItemCode.dismiss()
        }

        popBindingSelectCustItemCode.closePop.setOnClickListener {
            dlgSelectCustItemCode.dismiss()
        }

    }

    // 批次属性号和批次属性信息  adapter
    class ListLotInfoAdapter :
        CommonAdapter<BaseItemForPopup>(R.layout.item_pop_view_for_transfer_lotnum) {
        override fun convert(helper: BaseViewHolder, item: BaseItemForPopup) {
            super.convert(helper, item as FuInvLocationInventory)

            var result =
                item.custItemCode + if (item.whBarcode69 == null || item.whBarcode69 == "") "" else "/" + item.whBarcode69
            helper.setText(R.id.tvCustItemCode, result)

            // 批次属性号 暂不显示
            helper.setText(R.id.lotNum, item.lotNum)

            if (item.usableQty != null) {
                helper.setText(R.id.tvUseAbleQty, item.usableQty.toString())
            }

            var info = ""
            item.let {
                val arr = arrayListOf<String>()

                if (!item.lotAtt04.isNullOrBlank()) {  //有中文就显示中文
                    if (DCUtils.lot4TypeC2N.size > 0) {  //正品或不良品
                        arr.add("品质: " + DCUtils.lot4TypeC2N.get(item.lotAtt04))
                    } else {
                        arr.add("品质: " + item.lotAtt04)
                    }

                }

                if (!item.lotAtt05.isNullOrBlank()) {
                    arr.add("批次: " + item.lotAtt05)  // 批号  比如2109
                }

                if (!item.lotAtt01.isNullOrBlank()) {
                    var strDate = item.lotAtt01 // 把日期格式转成 yyyymmdd
                    if (strDate.split(" ").size > 0) {
                        strDate = item.lotAtt01.split(" ")[0]
                    }
                    arr.add("生产日期: " + strDate)
                }

                if (!item.lotAtt02.isNullOrBlank()) {
                    var strDate = item.lotAtt02 // 把日期格式转成 yyyymmdd
                    if (strDate.split(" ").size > 0) {
                        strDate = item.lotAtt02.split(" ")[0]
                    }
                    arr.add("失效日期: " + strDate)
                }

                if (!item.lotAtt03.isNullOrBlank()) {
                    var strDate = item.lotAtt03 // 把日期格式转成 yyyymmdd
                    if (strDate.split(" ").size > 0) {
                        strDate = item.lotAtt03.split(" ")[0]
                    }
                    arr.add("入库日期: " + strDate)
                }


                if (!item.lotAtt06.isNullOrBlank()) {
                    arr.add("属性6: " + item.lotAtt06)
                }
                if (!item.lotAtt07.isNullOrBlank()) {
                    arr.add("属性7: " + item.lotAtt07)
                }
                if (!item.lotAtt08.isNullOrBlank()) {
                    arr.add("属性8: " + item.lotAtt08)
                }
                if (!item.lotAtt09.isNullOrBlank()) {
                    arr.add("属性9: " + item.lotAtt09)
                }
                if (!item.lotAtt10.isNullOrBlank()) {
                    arr.add("属性10: " + item.lotAtt10)
                }
                if (!item.lotAtt11.isNullOrBlank()) {
                    arr.add("属性11: " + item.lotAtt11)
                }
                if (!item.lotAtt12.isNullOrBlank()) {
                    arr.add("属性12: " + item.lotAtt12)
                }

                info = arr.joinToString(separator = " / ")
            }

            helper.setText(R.id.lotInfo, info)
            //    .setText(R.id.lotInfo, item.lotInfo) //属性信息

        }
    }


    // 选择custItemCode的list 的  adapter
    class ListCustItemCodeAdapter :
        CommonAdapter<BaseItemForPopup>(R.layout.item_pop_view_for_select_cust_item_code) {
        override fun convert(helper: BaseViewHolder, item: BaseItemForPopup) {
            super.convert(helper, item as ItemRfVO)
            // 客户商品编码
            helper.setText(R.id.tvCustItemCode, item.custItemCode)
        }
    }


    // 拼接批次属性信息
    fun getLotInfo(locationInventory: FuInvLocationInventory): String {
        var info = ""
        locationInventory.let {
            val arr = arrayListOf<String>()

            if (!locationInventory.lotAtt04.isNullOrBlank()) {  //有中文就显示中文
                if (DCUtils.lot4TypeC2N.size > 0) {
                    arr.add("品质: " + DCUtils.lot4TypeC2N.get(locationInventory.lotAtt04))
                } else {
                    arr.add("品质: " + locationInventory.lotAtt04)
                }

            } //正品或不良品

            if (!locationInventory.lotAtt05.isNullOrBlank()) {
                arr.add("批次: " + locationInventory.lotAtt05)
            }  // 批号  比如2109
            /*
             if (!locationInventory.lotAtt01.isNullOrBlank()) {
                 arr.add("生产日期: " + locationInventory.lotAtt01)
             }
             if (!locationInventory.lotAtt02.isNullOrBlank()) {
                 arr.add("失效日期: " + locationInventory.lotAtt02)
             }
             if (!locationInventory.lotAtt03.isNullOrBlank()) {
                 arr.add("入库日期: " + locationInventory.lotAtt03)
             }*/


            if (!locationInventory.lotAtt01.isNullOrEmpty()) {
                // 去除时分秒
                var info = locationInventory.lotAtt01
                if (info.split(" ").isNotEmpty()) {
                    info = info.split(" ")[0]
                }
                arr.add("生产日期: $info")
            }
            if (!locationInventory.lotAtt02.isNullOrEmpty()) {
                // 去除时分秒
                var info = locationInventory.lotAtt02 // 把日期格式转成 yyyymmdd
                if (info.split(" ").isNotEmpty()) {
                    info = info.split(" ")[0]
                }
                arr.add("失效日期: $info")
            }
            if (!locationInventory.lotAtt03.isNullOrEmpty()) {
                // 去除时分秒
                var info = locationInventory.lotAtt03
                if (info.split(" ").isNotEmpty()) {
                    info = info.split(" ")[0]
                }
                arr.add("入库日期: $info")
            }

            if (!locationInventory.lotAtt06.isNullOrBlank()) {
                arr.add("属性6: " + locationInventory.lotAtt06)
            }
            if (!locationInventory.lotAtt07.isNullOrBlank()) {
                arr.add("属性7: " + locationInventory.lotAtt07)
            }
            if (!locationInventory.lotAtt08.isNullOrBlank()) {
                arr.add("属性8: " + locationInventory.lotAtt08)
            }
            if (!locationInventory.lotAtt09.isNullOrBlank()) {
                arr.add("属性9: " + locationInventory.lotAtt09)
            }
            if (!locationInventory.lotAtt10.isNullOrBlank()) {
                arr.add("属性10: " + locationInventory.lotAtt10)
            }
            if (!locationInventory.lotAtt11.isNullOrBlank()) {
                arr.add("属性11: " + locationInventory.lotAtt11)
            }
            if (!locationInventory.lotAtt12.isNullOrBlank()) {
                arr.add("属性12: " + locationInventory.lotAtt12)
            }


            info = arr.joinToString(separator = " / ")
            return info
        }
    }


    fun isEmpty(text: String?): Boolean {
        if (text.isNullOrBlank()) {
            return true
        } else {
            return false
        }
    }


    fun startScanFromLoc() {
        if (fragment.binding.etFromLocCode.isEnabled) {
            (fragment.activity as TransferActivity).currentStep =
                (fragment.activity as TransferActivity).STEP_TRANS_GOOD_FROM_LOC
            XQRCode.startScan(fragment.activity, BaseActivity.QR_CODE_BACK)
        }
    }

    fun startScanToLoc() {
        if (fragment.binding.etTargetLocCode.isEnabled) {
            (fragment.activity as TransferActivity).currentStep =
                (fragment.activity as TransferActivity).STEP_TRANS_GOOD_TO_LOC
            XQRCode.startScan(fragment.activity, BaseActivity.QR_CODE_BACK)
        }
    }

    fun startScanItemCode() {
        if (fragment.binding.etCustItemCode.isEnabled) {
            (fragment.activity as TransferActivity).currentStep =
                (fragment.activity as TransferActivity).STEP_TRANS_GOOD_ITEM_CODE
            XQRCode.startScan(fragment.activity, BaseActivity.QR_CODE_BACK)
        }
    }


    fun scanResult(result: String?, step: Int) {
        result?.let {
            if (step == (fragment.activity as TransferActivity).STEP_TRANS_GOOD_FROM_LOC) {
                // 扫来源库位
                fragment.binding.etFromLocCode.setText(result)
                onEnterFromLocCode()
            } else if (step == (fragment.activity as TransferActivity).STEP_TRANS_GOOD_ITEM_CODE) {
                // 扫商品条码
                fragment.binding.etCustItemCode.setText(result)
                onEnterItemCode()
            } else if (step == (fragment.activity as TransferActivity).STEP_TRANS_GOOD_TO_LOC) {
                // 扫目标库位
                fragment.binding.etTargetLocCode.setText(result)
                onEnterToLocCode()
            }
        }
    }

}