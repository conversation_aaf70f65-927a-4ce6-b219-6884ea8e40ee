package com.midea.prestorage.function.instorage

import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestoragesaas.databinding.ActivityInStorageBinding
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.xuexiang.xqrcode.XQRCode

// 按单收货/组单收货 的 Activity
class InStorageActivity : BaseActivity() {

    lateinit var binding: ActivityInStorageBinding
    var isNeedFinish = false

    companion object {
        var curContainerCode = ""
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_in_storage)
        binding.vm = InStorageVM(this)

        DCUtils.initLot4dicts(this,null)

        // 初始化收货容器
        binding.vm!!.initContainer()

        AppUtils.requestFocus(binding.etOrderNo)
    }

    override fun onResume() {
        super.onResume()
        if(isNeedFinish) {
            isNeedFinish = false;
            this.finish()
        }
    }

    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == BaseActivity.QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果
            binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }


}