package com.midea.prestorage.function.addgoods

import CheckUtil
import android.app.Application
import android.content.Intent
import android.text.TextUtils
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.ContainerPickSecondList
import com.midea.prestorage.beans.net.ReplenishmentBean.ReplenishmentListBean
import com.midea.prestorage.event.LiveEvent
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.launch
import com.midea.prestorage.widgets.ViewBindingAdapter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody

class ReplenishmentActivityVM(application: Application) : BaseViewModel(application) {
    val title = MutableLiveData("快速补货")
    val isRefreshing = ObservableBoolean(false)
    val isNoData = ObservableBoolean(false)
    val orderNo = ObservableField("")
    val fromInfo = ObservableField("")
    val toInfo = ObservableField("")
    val fromInfoStr = ObservableField("")
    val toInfoStr = ObservableField("")
    val scanLiveEvent = LiveEvent<Unit>()
    val showFromZoneLiveEvent = LiveEvent<Unit>()
    val showToZoneLiveEvent = LiveEvent<Unit>()
    var containerPickSecondList: ContainerPickSecondList? = null
    var toFlag : String? = null //null是正常进来的，1是从合并拣货进来的

    // 当前页码
    var pageNo = 1

    val loadMoreComplete = MutableLiveData(0)
    var showDatas = MutableLiveData<MutableList<ReplenishmentListBean>>()
    var loadMoreDatas = MutableLiveData<MutableList<ReplenishmentListBean>>()

    override fun init() {
    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        pageNo = 1
        isRefreshing.set(true)
        initOrderList()
    }

    val orderEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                if (TextUtils.isEmpty(orderNo.get())) {
                    return
                }
                pageNo = 1
                isRefreshing.set(true)
                initOrderList(true)
            }
        }
    }

    fun initOrderList(isEnter: Boolean = false) {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val map = mutableMapOf(
                "whCode" to Constants.whInfo?.whCode,
                "day" to "7",
                "queryType" to 1,
                "pageNo" to pageNo,
                "pageSize" to 10,
                "orderBy" to "task_level desc,id",
                "orderByType" to "asc",
                "queryCode" to orderNo.get().toString().trim(),
                "fromZone" to fromInfo.get().toString().trim(),
                "toZone" to toInfo.get().toString().trim(),
                "taskDetailIdList" to if("1" == toFlag) (containerPickSecondList?.repTaskDetaiIdList ?: null) else (containerPickSecondList?.taskDetailIdList ?: null)
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(map)
            )

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getAddGoodsService()
                    .queryReplenishmentList(requestBody)
            }

            if (result.code == 0L) {
                isRefreshing.set(false)
                loadMoreComplete.value = 1

                result.data?.let {
                    if (pageNo == 1) {
                        showDatas.value = it.list
                    } else {
                        loadMoreDatas.value = it.list
                    }
                    if (pageNo < it.totalPage) {
                        pageNo++
                    } else {
                        loadMoreComplete.value = 2
                    }

                    //如果是回车查询并且仅有一条记录，则直接跳转到补货任务详情界面
                    if (isEnter && it.list.size == 1) {
                        onItemClick(it.list[0])
                    }
                }
            } else {
                isRefreshing.set(false)
                result.msg?.let { showNotification(it, false) }
                if (pageNo == 1) {
                    val emptyList = mutableListOf<ReplenishmentListBean>()
                    showDatas.value = emptyList
                }
            }
        }
    }

    fun showErrorNotification(msg: String, isSuccess: Boolean) {
        showNotification(msg, isSuccess)
    }

    fun onItemClick(bean: ReplenishmentListBean) {
        val it = Intent()
        it.putExtra("ReplenishmentListBean", bean)
        toActivity(it, ReplConfirmActivity::class.java)
    }

    fun startScan() {
        if (CheckUtil.isFastDoubleClick()) {
            scanLiveEvent.value = Unit
        }
    }

    fun scanResult(result: String?) {
        orderNo.set(result)
        orderEnterKeyPress.onEnterKey()
    }

    fun recommendFromZone() {
        if (CheckUtil.isFastDoubleClick()) {
            showFromZoneLiveEvent.value = Unit
        }
    }

    fun recommendToZone() {
        if (CheckUtil.isFastDoubleClick()) {
            showToZoneLiveEvent.value = Unit
        }
    }

    fun cleanOrderNo() {
        orderNo.set("")
    }

}