package com.midea.prestorage.dialog

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogReceivingSettingBinding
import com.midea.prestoragesaas.databinding.ItemReceivingSettingBinding

class SettingOptionDialogFragment : DialogFragment() {

    interface ReceivingSettingListener {
        fun updateReceivingSetting(setting: Map<String, Boolean>)
    }

    companion object {

        private const val KEY_SETTING = "KEY_SETTING"
        private const val KEY_NAME = "KEY_NAME"

        fun newInstance(
            setting: Map<String, Boolean>,
            names: Map<String, String>
        ): SettingOptionDialogFragment {
            val arg = Bundle()
            arg.putSerializable(KEY_SETTING, HashMap(setting))
            arg.putSerializable(KEY_NAME, HashMap(names))
            val fragment = SettingOptionDialogFragment()
            fragment.arguments = arg
            return fragment
        }
    }

    private var binding: DialogReceivingSettingBinding? = null
    private var listener: ReceivingSettingListener? = null

    private val setting = mutableMapOf<String, Boolean>()
    private val names = mutableMapOf<String, String>()

    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (context is ReceivingSettingListener) {
            listener = context
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DataBindingUtil.inflate(layoutInflater, R.layout.dialog_receiving_setting, null, false)
        dialogSetting()
        val viewModel = ViewModelProvider.AndroidViewModelFactory(requireActivity().application)
            .create(SettingOptionDialogViewModel::class.java)
        binding?.vm = viewModel
        binding?.lifecycleOwner = this
        observeLiveData(viewModel)
        initData()
        bindSetting()
        return binding?.root
    }

    private fun dialogSetting() {
        dialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog?.window?.setGravity(Gravity.CENTER)
        dialog?.window?.attributes?.run {
            gravity = Gravity.CENTER
        }
    }

    private fun initData() {
        setting.clear()
        setting.putAll(
            arguments?.getSerializable(KEY_SETTING) as? HashMap<String, Boolean> ?: emptyMap()
        )
        names.clear()
        names.putAll(arguments?.getSerializable(KEY_NAME) as? HashMap<String, String> ?: emptyMap())
    }

    private fun bindSetting() {
        setting.keys.forEach { key ->
            val itemBinding = DataBindingUtil.inflate<ItemReceivingSettingBinding>(
                layoutInflater,
                R.layout.item_receiving_setting,
                binding!!.llSettings,
                false
            )
            itemBinding.liveData = MutableLiveData<Boolean>().also {
                it.value = setting[key]
                it.observe(this) { settingValue ->
                    setting[key] = settingValue
                }
            }
            itemBinding.name = names[key] ?: ""
            binding!!.llSettings.addView(itemBinding.root)
        }
    }

    private fun observeLiveData(viewModel: SettingOptionDialogViewModel) {
        viewModel.confirmEvent.observe(this) {
            listener?.updateReceivingSetting(setting)
            dismiss()
        }
        viewModel.closeEvent.observe(this) {
            dismiss()
        }
    }

    override fun onDetach() {
        super.onDetach()
        listener = null
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

}