package com.midea.prestorage.base.adapter;

import android.view.View;
import android.widget.ImageView;

import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.midea.prestorage.function.main.ProfileVM;
import com.midea.prestorage.utils.SPUtils;
import com.midea.prestoragesaas.R;
import com.midea.prestorage.beans.base.BaseItemForPopup;

import java.util.ArrayList;
import java.util.List;

/**
 * description 多选Dialog
 * author: wangqin
 * date: 2020/6/19
 * param
 * return
 */
public class ListCheckBoxAdapterV2<T extends BaseItemForPopup> extends CommonAdapter<T> {

    private OnDataChangeListener onDataChangeListener;
    private ChangeSelectStatus changeSelectStatus;
    //指定点击的控件ID,不指定默认整个
    private int clickId = -1;

    public ListCheckBoxAdapterV2(int layoutResId) {
        super(layoutResId);
    }

    public void setChangeSelectStatus(ChangeSelectStatus changeSelectStatus) {
        this.changeSelectStatus = changeSelectStatus;
    }

    @Override
    protected void convert(BaseViewHolder helper, BaseItemForPopup item) {
        super.convert(helper, (T) item);

        View view;
        if (clickId != -1) {
            view = helper.itemView.findViewById(clickId);
        } else {
            view = helper.itemView;
        }
        view.setTag(item);
        view.setOnClickListener(v -> {
            BaseItemForPopup bean = (BaseItemForPopup) v.getTag();
            bean.setTempSelected(!bean.isTempSelected());

            notifyDataSetChanged();
            if (onDataChangeListener != null) {
                onDataChangeListener.dataChange(bean);
            }
            if (changeSelectStatus != null) {
                changeSelectStatus.onChangeSelectStatus();
            }
        });

        ImageView check = helper.getView(R.id.img_select);

        if (item.isTempSelected()) {
            if ((Boolean) SPUtils.INSTANCE.get(ProfileVM.CARE_MODE, false)) {
                check.setImageResource(R.drawable.select_selected_care);
            } else {
                check.setImageResource(R.mipmap.select_selected);
            }
        } else {
            if ((Boolean) SPUtils.INSTANCE.get(ProfileVM.CARE_MODE, false)) {
                check.setImageResource(R.drawable.select_normal_care);
            } else {
                check.setImageResource(R.mipmap.select_normal);
            }
        }
    }

    public List<T> getReturnBeans() {
        List<T> list = new ArrayList<>();
        for (int i = 0; i < getData().size(); i++) {
            if (getData().get(i).isTempSelected()) {
                list.add(getData().get(i));
            }
        }
        return list;
    }

    public void allSelect(boolean isSelect) {
        List<T> data = getData();
        for (int i = 0; i < data.size(); i++) {
            data.get(i).setTempSelected(isSelect);
        }
        notifyDataSetChanged();
    }

    public void setClickId(int clickId) {
        this.clickId = clickId;
    }

    /**
     * description 数据改变后监听器
     * author: wangqin
     * date: 2020/6/29
     * param
     * return
     */
    public interface OnDataChangeListener<T> {
        void dataChange(T t);
    }

    public interface ChangeSelectStatus {
        void onChangeSelectStatus();
    }
}