package com.midea.prestorage.function.inv

import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.SerialScanDto
import com.midea.prestoragesaas.databinding.ActivityCountInTimeBinding
import com.midea.prestoragesaas.databinding.PopViewForSelectCustemItemCodeBinding
import com.midea.prestorage.dialog.TipDialog
import com.midea.prestorage.function.inv.dialog.CountInTimeSettingDialog
import com.midea.prestorage.function.inv.dialog.InputNumActualDialog
import com.midea.prestorage.function.inv.fragment.TransByGoodVM
import com.midea.prestorage.function.inv.response.ItemRfVO
import com.midea.prestorage.function.inv.response.InvStocktakeDetail
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.*
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.xuexiang.xqrcode.XQRCode
import java.math.BigDecimal

// 实时盘点
class CountInTimeActivity : BaseActivity() {

    lateinit var binding: ActivityCountInTimeUnionBinding
    var adapter = ListCountTaskAdapter()

    // 商品状态选项
    val lot4Options = mutableListOf<String>()

    private lateinit var tipDialog: TipDialog

    //69码或sn码 查询出多个custItemCodes的时候 弹框让用户选择custItemCode
    lateinit var dlgSelectCustItemCode: AlertDialog
    private lateinit var popBindingSelectCustItemCode: PopViewForSelectCustemItemCodeBinding
    lateinit var popAdapterSelectCustItemCode: PopListAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityCountInTimeUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_count_in_time_care
                )
            )
        } else {
            ActivityCountInTimeUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_count_in_time
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        binding.vm = CountInTimeVM(this)

        initLo4Spinner()
        initRecycleView()

        //光标默认定位到  盘点库位
        AppUtils.requestFocus(binding.etLocationCode)

        tipDialog = TipDialog(this)
        tipDialog.setTitle("提示")
        tipDialog.setOnTipBackListener(object : TipDialog.OnTipBack {
            override fun onConfirmClick() {
                binding.vm!!.removeItem(tipDialog.tag as InvStocktakeDetail)
            }

            override fun onDismissClick() {
            }
        })

        adapter.setOnItemLongClickListener { adapter, _, position ->
            val item = adapter.getItem(position) as InvStocktakeDetail
            //binding.etCustItemCode.setText(item.custItemCode)
            //adapter.data.remove(item)
            tipDialog.tag = item
            tipDialog.setMsg("是否重置货品${item.custItemCode}的盘点记录?")
            tipDialog.show()
            return@setOnItemLongClickListener true
        }

        adapter.setOnItemClickListener { adapter, _, position ->
            binding.vm!!.onItemClick(adapter.data[position] as InvStocktakeDetail)
        }
    }

    private fun initLo4Spinner() {
        //初始化 字典(库存状态(正品不良品))
        DCUtils.goodsStatue(this, object : DCUtils.DCBack {

            override fun dcBack(statusDC: MutableList<DCBean>) {
                statusDC.forEach {
                    lot4Options.add(it.key)
                }

                val result = statusDC.find { it.value == "Y" }
                if (result != null) {
                    binding.vm!!.curSelectLot4Name = result.key
                } else {
                    //默认选第一个
                    if (lot4Options.size > 0) {
                        binding.vm!!.curSelectLot4Name = lot4Options[0]
                    }
                }

                binding.spinnerLot4.setItems(lot4Options)
                if (result != null) {
                    binding.spinnerLot4.selectedIndex = statusDC.indexOf(result)
                }
                binding.spinnerLot4.setOnItemSelectedListener(MaterialSpinner.OnItemSelectedListener<String> { view, position, id, item ->
                    binding.vm!!.curSelectLot4Name =
                        lot4Options.get(binding.spinnerLot4.selectedIndex)
                })
            }
        })
    }

    // 初始化选择客户商品编码的弹窗
    private fun initPopWinSelectCustItemCode() {

        val popViewSelectCustItemCode =
            LayoutInflater.from(this).inflate(R.layout.pop_view_for_select_custem_item_code, null)
        popBindingSelectCustItemCode = DataBindingUtil.bind(popViewSelectCustItemCode)!!

        val alertDialogBuilder = AlertDialog.Builder(this)
        alertDialogBuilder.setView(popViewSelectCustItemCode)
        dlgSelectCustItemCode = alertDialogBuilder.create()

        popAdapterSelectCustItemCode = PopListAdapter()
        popBindingSelectCustItemCode.recyclerViewInPop.layoutManager = LinearLayoutManager(this)
        popBindingSelectCustItemCode.recyclerViewInPop.adapter = popAdapterSelectCustItemCode

        popAdapterSelectCustItemCode.setOnItemClickListener { adapter, view, position ->
            val data = adapter.getItem(position) as ItemRfVO
            binding.etCustItemCode.setText(data.custItemCode)
            dlgSelectCustItemCode.dismiss()
            var item = InvStocktakeDetail()
            item.custItemCode = data.custItemCode
            item.itemCode = data.itemCode
            item.itemName = data.itemName
            item.isDecimal = data.isDecimal
            item.packageRelationList = data.packageRelationList
            item.whCode = getWhCode()
            item.sysQty = BigDecimal.ZERO  //系统库存数量，由于是新扫描的商品(盘赢) ，所以库存中数量为0
            if (!binding.vm?.curRespCountInTimeTask?.stocktakeCode.isNullOrBlank()) {
                item.stocktakeCode = binding.vm?.curRespCountInTimeTask?.stocktakeCode
            }
            if (DCUtils.goodsStatueN2C.get(binding.vm?.curSelectLot4Name) != null) {
                item.lotAtt04 = DCUtils.goodsStatueN2C.get(binding.vm?.curSelectLot4Name).toString()
            }
            item.cdpaFormat = data.cdpaFormat
            item.whCsBarcode69 = data.whCsBarcode69
            item.whBarcode69 = data.whBarcode69
            binding.vm?.curDetail = item
            binding.vm?.displayItemName?.set(data.itemName)
            if (CountInTimeSettingVM.countingMethod == CountInTimeSettingVM.ONE) {
                //逐件扫码模式 扫一个上传一个并刷新列表 (即自动触发  数量编辑框回车)
                binding.vm?.onEnterCountNum(false)
            } else {
                showInputNumActualDialog(item)
            }
        }

        popBindingSelectCustItemCode.closePop.setOnClickListener {
            dlgSelectCustItemCode.dismiss()
        }

    }

    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }

    fun initRecycleView() {
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter

    }

    fun toSetttingPage() {
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            showSettingDialog()
        } else {
            val intent = Intent(this, CountInTimeSettingActivity::class.java)
            startActivity(intent)
        }
    }

    fun showInputNumActualDialog(deleteInfo: InvStocktakeDetail) {
        //新增盘点库位dialog
        val inputDialog = InputNumActualDialog(this)
        inputDialog.inputBack = object : InputNumActualDialog.InputNumActualBack {
            override fun inputOk(data: InvStocktakeDetail, num: BigDecimal) {
                binding.vm?.submitTask(data, num)
            }

            override fun inputFail() {
            }
        }
        inputDialog.deleteInfo = deleteInfo
        inputDialog.show()
    }

    fun showSettingDialog() {
        val settingDialog = CountInTimeSettingDialog(this)
        settingDialog.show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == BaseActivity.QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    class PopListAdapter :
        CommonAdapter<ItemRfVO>(R.layout.item_pop_view_for_select_cust_item_code_pop) {
        override fun convert(holder: BaseViewHolder?, item: ItemRfVO) {
            super.convert(holder, item)

            holder?.setGone(R.id.tv_item_name, item.itemName.isNullOrEmpty())
        }
    }

    class ListCountTaskAdapter :
        CommonAdapter<InvStocktakeDetail>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_count_task_care else R.layout.item_count_task) {

        override fun convert(holder: BaseViewHolder, item: InvStocktakeDetail) {
            super.convert(holder, item)

            //货品编码
            holder.setText(R.id.tvCustItemCode, item.custItemCode)

            holder?.setText(
                R.id.tvBarcode69,
                LotAttUnit.formatBarcode69(item.whCsBarcode69, item.whBarcode69, item.whIpBarcode69)
            )

            holder.setText(R.id.tv_item_name, item.itemName)
            holder.setText(R.id.tv_lott04, item.lotAtt04Str)
            holder.setText(R.id.tv_owner_name, item.ownerName)

            if (item.lotAtt04 == "Y") {
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    holder.setTextColor(
                        R.id.tv_lott04,
                        ContextCompat.getColor(context, R.color.btn_blue_care)
                    )
                } else {
                    holder.setTextColor(
                        R.id.tv_lott04,
                        ContextCompat.getColor(context, R.color.colorGreen)
                    )
                }
            } else {
                holder.setTextColor(R.id.tv_lott04, ContextCompat.getColor(context, R.color.orange))
            }

            holder.setText(R.id.tvFirstQty, AppUtils.getBigDecimalValueStr(item.firstQty))  //实盘数量
        }
    }
}