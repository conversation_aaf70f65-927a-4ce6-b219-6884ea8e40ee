package com.midea.prestorage.function.picktask

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.launch
import com.midea.prestorage.widgets.ViewBindingAdapter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal


class PickTaskDialogVM(application: Application) : BaseViewModel(application) {
    var isDissmiss = MutableLiveData<Boolean>(false)
    var isResultOk = MutableLiveData(false)
    var custItemCode = MutableLiveData<String>("")
    var fromQty = MutableLiveData<BigDecimal>()
    var advance = MutableLiveData<String>("")
    var pickQty = MutableLiveData<String>("")
    var ids = MutableLiveData<String>("")
    var isEnableSure = MutableLiveData<Boolean>(true)

    override fun init() {

    }

    val inputTextChange = object : ViewBindingAdapter.TextChangedListener {

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            if (s.toString() == "") return
            if (s.toString().toDouble() > fromQty.value?.toDouble() as Double) {
                showNotification("超过了待拣数量", false)
            } else {
                pickQty.value = s.toString()
            }
        }
    }


    fun onClose() {
        isDissmiss.value = true
    }

    fun onConfirm() {
        launch(showDialog = true, error = {
        }, finish = {}) {
            isEnableSure.value = false

            if (pickQty.value == "") {
                showNotification("数量不能为空!", false)
                return@launch
            }

            if (BigDecimal(pickQty.value) > fromQty.value) {
                showNotification("超过了待拣数量", false)
                return@launch
            }

            val mutableList = mutableListOf<String>()
            val idsSplit = ids.value?.split(",")
            idsSplit?.forEach {
                mutableList.add(it.trim())
            }

            val param = mutableMapOf(
                "toQty" to pickQty.value,
                "ids" to mutableList,
                "whCode" to Constants.whInfo?.whCode
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getInventoryAPI().confirm(requestBody)
            }

            isEnableSure.value = true

            if (result.code == 0L) {
                isResultOk.value = true
                isDissmiss.value = true//关闭对话框
            } else {
                pickQty.value = ""
                showNotification(result.msg as String, false)
            }
        }
    }
}