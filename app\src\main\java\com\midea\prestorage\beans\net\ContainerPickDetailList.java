package com.midea.prestorage.beans.net;

import java.io.Serializable;

public class ContainerPickDetailList implements Serializable {

    private String id;
    private String createTime;
    private String updateTime;
    private String tenantCode;
    private String createUserCode;
    private String createUserName;
    private String updateUserCode;
    private String updateUserName;
    private String remark;
    private String version;
    private String deleteFlag;
    private String pageNo;
    private String offset;
    private String orderBy;
    private String orderByType;
    private String ids;
    private String tenantCodes;
    private String count;
    private String startTime;
    private String endTime;
    private String pickContainerHeaderId;
    private String taskCode;
    private String ownerCode;
    private String custItemCode;
    private String itemCode;
    private String itemName;
    private String pickedQty;
    private String pickedCsQty;
    private String pickedEaQty;
    private String packagePara;
    private String waveNo;
    private String lotNum;
    private String fromLoc;
    private String traceId;
    private String whCode;
    private String oqcCsQty;
    private String oqcEaQty;
    private String oqcTime;
    private String lotAtt01;
    private String lotAtt02;
    private String lotAtt03;
    private String lotAtt04;
    private String lotAtt05;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getPageNo() {
        return pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public String getOffset() {
        return offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderByType() {
        return orderByType;
    }

    public void setOrderByType(String orderByType) {
        this.orderByType = orderByType;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getTenantCodes() {
        return tenantCodes;
    }

    public void setTenantCodes(String tenantCodes) {
        this.tenantCodes = tenantCodes;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getPickContainerHeaderId() {
        return pickContainerHeaderId;
    }

    public void setPickContainerHeaderId(String pickContainerHeaderId) {
        this.pickContainerHeaderId = pickContainerHeaderId;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getPickedQty() {
        return pickedQty;
    }

    public void setPickedQty(String pickedQty) {
        this.pickedQty = pickedQty;
    }

    public String getPickedCsQty() {
        return pickedCsQty;
    }

    public void setPickedCsQty(String pickedCsQty) {
        this.pickedCsQty = pickedCsQty;
    }

    public String getPickedEaQty() {
        return pickedEaQty;
    }

    public void setPickedEaQty(String pickedEaQty) {
        this.pickedEaQty = pickedEaQty;
    }

    public String getPackagePara() {
        return packagePara;
    }

    public void setPackagePara(String packagePara) {
        this.packagePara = packagePara;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getLotNum() {
        return lotNum;
    }

    public void setLotNum(String lotNum) {
        this.lotNum = lotNum;
    }

    public String getFromLoc() {
        return fromLoc;
    }

    public void setFromLoc(String fromLoc) {
        this.fromLoc = fromLoc;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getOqcCsQty() {
        return oqcCsQty;
    }

    public void setOqcCsQty(String oqcCsQty) {
        this.oqcCsQty = oqcCsQty;
    }

    public String getOqcEaQty() {
        return oqcEaQty;
    }

    public void setOqcEaQty(String oqcEaQty) {
        this.oqcEaQty = oqcEaQty;
    }

    public String getOqcTime() {
        return oqcTime;
    }

    public void setOqcTime(String oqcTime) {
        this.oqcTime = oqcTime;
    }

    public String getLotAtt01() {
        return lotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        this.lotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return lotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        this.lotAtt02 = lotAtt02;
    }

    public String getLotAtt03() {
        return lotAtt03;
    }

    public void setLotAtt03(String lotAtt03) {
        this.lotAtt03 = lotAtt03;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getLotAtt05() {
        return lotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        this.lotAtt05 = lotAtt05;
    }
}