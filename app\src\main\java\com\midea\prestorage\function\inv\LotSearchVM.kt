package com.midea.prestorage.function.inv

import android.view.View
import android.widget.Toast
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.net.BsOwner
import com.midea.prestorage.beans.net.SerialScanDto
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.dialog.TipDialog
import com.midea.prestorage.function.inv.response.BsLocation
import com.midea.prestorage.function.inv.response.FuInvLocationInventory
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody


class LotSearchVM(val activity: LotSearchActivity) {


    private var ownerDialog: FilterDialog  //货主选择对话框

    // 货主
    var textOwnerCode = ObservableField<String>("")
    var custItemCode = ObservableField<String>("")
    var locCode = ObservableField<String>("")

    init {

        ownerDialog = FilterDialog(activity as RxAppCompatActivity)
        //隐藏编辑框
        ownerDialog.dismissEdit()
        ownerDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            it as BsOwner
            textOwnerCode.set(it.ownerCode)
            ownerDialog.dismiss()
        })
    }

    fun initOwnerList() {
        RetrofitHelper.getBasicDataAPI()
            .queryOwnerList(activity.getWhCode())
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<MutableList<BsOwner>>(activity as RxAppCompatActivity) {
                override fun success(list: MutableList<BsOwner>?) {
                    list?.let {
                        list.forEach {
                            it.showInfo = it.ownerCode + " (" + it.ownerName + ")"
                        }
                        ownerDialog.changeDataNotify(list)
                        //2021年11月17日 星期三  只有一个货主时 默认选中
                        if (list.size == 1) {
                            val owner = list.get(0)
                            textOwnerCode.set(owner.ownerCode)
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }

            })
    }

    fun onEnterSearch() {
        if (locCode.get().isNullOrBlank() && custItemCode.get().isNullOrBlank()) {
            if (locCode.get().isNullOrBlank()) {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "货位和产品编码不能同时为空")
                return
            }
        }

        if (textOwnerCode.get().isNullOrBlank()) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "查询条件[货主编码]不能为空")
            return
        }

        locCode.set(locCode.get()!!.trim())
        custItemCode.set(custItemCode.get()!!.trim())

        //调接口查询
        queryLots()
    }

    fun onEnterLocCode() {
        if (CheckUtil.isFastDoubleClick()) {
            AppUtils.requestFocus(activity.binding.etCustItemCode)
        }
    }

    fun onEnterCustItemCode() {
        if (CheckUtil.isFastDoubleClick()) {
            if (locCode.get().isNullOrBlank() && custItemCode.get().isNullOrBlank()) {
                if (locCode.get().isNullOrBlank()) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "货位和产品编码不能同时为空")
                    return
                }
            }

            //清除前后空格 先校验 当前输入的 货品编码 或sn码
            val snNo = activity.binding.etCustItemCode.text.toString().trim()

            val param = mutableMapOf(
                "serialNo" to snNo,
                "whCode" to activity.getWhCode()
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )


            activity.waitingDialogHelp.showDialog()


            // 货品条码校验
            RetrofitHelper.getAppAPI()
                .scanCodeOnTransferGood(requestBody)
                .compose(NetworkScheduler.compose())
                .bindUntilEvent(activity, ActivityEvent.DESTROY)
                .subscribe(object : RequestCallback<SerialScanDto>(activity) {
                    override fun success(data: SerialScanDto?) {
                        // success 表示识别成功
                        activity.waitingDialogHelp.hidenDialog()

                        data?.let {
                            ToastUtils.getInstance()
                                .toastWithOkSound(activity, "条码识别成功", Toast.LENGTH_SHORT)

                            if (!data.itemRfVOS.isNullOrEmpty() && data.itemRfVOS.size == 1) {
                                activity.binding.etCustItemCode.setText(data.itemRfVOS.get(0).custItemCode)
                                AppUtils.requestFocus(activity.binding.etCustItemCode)
                                onEnterSearch()
                            } else if (!data.itemRfVOS.isNullOrEmpty() && data.itemRfVOS.size > 1) {
                                //扫69码 后端没有返回 custItemCode  如果有custItemCode数组，就弹框选择
                                activity.dlgSelectCustItemCode.show()
                                activity.popAdapterSelectCustItemCode.data.clear()
                                data.itemRfVOS.forEach {
                                    activity.popAdapterSelectCustItemCode.addData(it)
                                }
                                activity.popAdapterSelectCustItemCode.notifyDataSetChanged()

                            } else if (!data.serialNo.isNullOrBlank()) {
                                activity.binding.etCustItemCode.setText(data.serialNo)
                                AppUtils.requestFocus(activity.binding.etCustItemCode)
                                onEnterSearch()
                            }
                        }
                    }

                    override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                        activity.waitingDialogHelp.hidenDialog()
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, apiErrorModel.message)
                        activity.binding.etCustItemCode.setText("")
                        AppUtils.requestFocus(activity.binding.etCustItemCode)

                    }
                })
        }
    }

    //查询批属性
    private fun queryLots() {

        activity.waitingDialogHelp.showDialog()

        RetrofitHelper.getInventoryAPI()
            .fuInvLocationInventoryGetList(
                textOwnerCode.get(),
                activity.getWhCode(),
                locCode.get(),
                custItemCode.get()?.trim()
            )
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<PageResult<FuInvLocationInventory>>(activity) {
                override fun success(data: PageResult<FuInvLocationInventory>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    // 先清空上一次的查询结果
                    activity.adapter.data.clear()
                    if (data != null && data.list != null) {
                        if (data.list.size == 0) {
                            ToastUtils.getInstance()
                                .showSuccessToastWithSound(activity, "查询完成,未找到数据")
                        } else {
                            data.list?.forEach {
                                activity.adapter.addData(it)
                            }
                        }
                    }

                    activity.adapter.notifyDataSetChanged()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()

                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })

    }


    private var curStep = 0
    private val scanStepLocation = 1
    private val scanStepAnyCode = 2

    fun scanAnyCode() {
        curStep = scanStepAnyCode
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanLocCode() {
        curStep = scanStepLocation
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }


    fun scanResult(code: String?) {
        code?.let {
            if (curStep == scanStepAnyCode) {
                activity.binding.etCustItemCode.setText(code)
                onEnterCustItemCode()
            } else if (curStep == scanStepLocation) {
                activity.binding.etLocCode.setText(code)
                AppUtils.requestFocus(activity.binding.etCustItemCode)
            }
        }

    }

    fun validateLocationCode(locCode: String) {
        // 检查 库位编码是否正确 (即检查仓库里是否有该库位 库位数量>0表示存在)
        RetrofitHelper.getBasicDataAPI()
            .countLocation(activity.getWhCode(), locCode, "")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<BsLocation>(activity as RxAppCompatActivity) {
                override fun success(data: BsLocation?) {
                    data.let {
                        if (data!!.count < 1) {
                            AlertDialogUtil.showOnlyOkDialog(activity,
                                "库位[" + locCode + "]不存在", AlertDialogUtil.OnOkCallback { //清空重填
                                    //activity.binding.etLocCode.setText("")
                                    //activity.binding.etLocCode.requestFocus()
                                })
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }

            })
    }

    //重置查询条件
    fun resetSearch() {
        locCode.set("")
        custItemCode.set("")
        textOwnerCode.set("")
        activity.adapter.data.clear()
        activity.adapter.notifyDataSetChanged()
    }


    fun onSelectOwner() {
        ownerDialog.show()
    }

    //后退键
    val back = View.OnClickListener {
        activity.finish()
    }
}