package com.midea.prestorage.function.containerpick.provider

import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.CompoundButton
import android.widget.EditText
import android.widget.LinearLayout
import com.chad.library.adapter.base.entity.node.BaseNode
import com.chad.library.adapter.base.provider.BaseNodeProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestorage.beans.net.BulkPackingDetail
import com.midea.prestorage.function.containerpick.adapter.BulkToBePackedAdapter
import com.midea.prestorage.function.containerpick.fragment.BulkToBePackedVM
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R

class BulkPickDetailProvider(private val vm: BulkToBePackedVM) :
    BaseNodeProvider() {
    override val itemViewType: Int
        get() = BulkToBePackedAdapter.TYPE_DETAIL

    override val layoutId: Int
        get() = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_to_be_bulk_pick_detail_care else R.layout.item_to_be_bulk_pick_detail

    private val checkBoxMap =
        mutableMapOf<BulkPickToBeDetailWrap, CompoundButton.OnCheckedChangeListener>()

    private val editMap =
        mutableMapOf<BulkPickToBeDetailWrap, TextWatcher>()

    override fun convert(helper: BaseViewHolder, item: BaseNode) {
        val wrap = item as BulkPickToBeDetailWrap
        val task = wrap.task
        helper.setText(R.id.tv_item_name, task.itemName)
        helper.setText(R.id.tv_unit, task.unit)

        val checkBox = helper.getView<CheckBox>(R.id.cb_select)
        checkBox.setOnCheckedChangeListener(null)
        checkBox.isChecked = wrap.selected
        val checkedChangeListener = if (!checkBoxMap.containsKey(wrap)) {
            CompoundButton.OnCheckedChangeListener { _, isChecked ->
                wrap.selected = isChecked
                vm.onItemCheckedChang(wrap)
            }.also {
                checkBoxMap[wrap] = it
            }
        } else {
            checkBoxMap[wrap]
        }
        checkBox.setOnCheckedChangeListener(checkedChangeListener)

        val editText: EditText = helper.getView(R.id.tv_num)
        val container: LinearLayout = helper.getView(R.id.rl_tag)

        editText?.setOnEditorActionListener { v, actionId, event ->
            if (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER) {
                editText?.requestFocus()
            }
            true
        }

        if (editText.tag is TextWatcher) {
            editText.removeTextChangedListener(editText.tag as TextWatcher)
        }

        helper.setText(R.id.tv_num, AppUtils.getBigDecimalValueStrNull(task.packQty))

        val watcher: TextWatcher = object : TextWatcher {
            override fun beforeTextChanged(
                s: CharSequence,
                start: Int,
                count: Int,
                after: Int
            ) {
            }

            override fun onTextChanged(
                s: CharSequence,
                start: Int,
                before: Int,
                count: Int
            ) {
            }

            override fun afterTextChanged(editable: Editable) {
                val innerItem = container.tag as BulkPickToBeDetailWrap
                if (!TextUtils.isEmpty(editable)) {
                    try {
                        if (AppUtils.getBigDecimalValue(editable.toString()).compareTo(innerItem.task.waitPackQty) == 1) {
                            vm.errorToaster.showError("数量不能超过待打包数${AppUtils.getBigDecimalValueStrNull(innerItem.task.waitPackQty)}")
                            editText.setText(AppUtils.getBigDecimalValueStr(innerItem.task.waitPackQty))
                            editText.setSelection(AppUtils.getBigDecimalValueStr(innerItem.task.waitPackQty).length)
                            task.packQty = innerItem.task.waitPackQty
                        } else {
                            task.packQty = AppUtils.getBigDecimalValue(editable.toString())
                        }
                    }catch (e: NumberFormatException) {
                        vm.errorToaster.showError("数量格式错误")
                        editText.setText(AppUtils.getBigDecimalValueStr(innerItem.task.waitPackQty))
                        editText.setSelection(AppUtils.getBigDecimalValueStr(innerItem.task.waitPackQty).length)
                        task.packQty = innerItem.task.waitPackQty
                    }

                } else {
                    task.packQty = null
                }
            }
        }

        editText.addTextChangedListener(watcher)
        editText.tag = watcher
        container.tag = item
    }
}