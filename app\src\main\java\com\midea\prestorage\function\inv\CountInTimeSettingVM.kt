package com.midea.prestorage.function.inv

import android.app.Application
import com.midea.prestorage.base.BaseViewModel

class CountInTimeSettingVM(application: Application) : BaseViewModel(application) {

    // 需求是 要记录上次的选项，所以这里用 静态变量
    companion object {
        const val COVER = 0
        const val ADD = 1
        const val ONE = 2
        const val MIN_UNITS = 0
        const val BOXES_NUM = 1

        var countingMethod = COVER
        var quantityEntryMode = MIN_UNITS
    }

    override fun init() {

    }

}