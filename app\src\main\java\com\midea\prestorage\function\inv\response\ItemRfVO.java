package com.midea.prestorage.function.inv.response;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.beans.base.BaseItemForPopup;

import java.math.BigDecimal;
import java.util.List;

public class ItemRfVO extends BaseItemForPopup {

    @ShowAnnotation
    private String custItemCode;
    @ShowAnnotation
    private String itemName;
    @ShowAnnotation
    private String ownerName;
    private String ownerCode;
    private String isValidity;
    private BigDecimal periodOfValidity;
    private String validityUnit;

    private String itemCode;
    private String serialNo;

    private List<PackageRelation> packageRelationList;

    private int isDecimal;
    private String whCsBarcode69;
    private String whBarcode69;

    public String getWhCsBarcode69() {
        return whCsBarcode69;
    }

    public void setWhCsBarcode69(String whCsBarcode69) {
        this.whCsBarcode69 = whCsBarcode69;
    }

    public String getWhBarcode69() {
        return whBarcode69;
    }

    public void setWhBarcode69(String whBarcode69) {
        this.whBarcode69 = whBarcode69;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getIsValidity() {
        return isValidity;
    }

    public void setIsValidity(String isValidity) {
        this.isValidity = isValidity;
    }

    public BigDecimal getPeriodOfValidity() {
        return periodOfValidity;
    }

    public void setPeriodOfValidity(BigDecimal periodOfValidity) {
        this.periodOfValidity = periodOfValidity;
    }

    public String getValidityUnit() {
        return validityUnit;
    }

    public void setValidityUnit(String validityUnit) {
        this.validityUnit = validityUnit;
    }

    private String cdpaFormat;

    public String getCdpaFormat() {
        return cdpaFormat;
    }

    public void setCdpaFormat(String cdpaFormat) {
        this.cdpaFormat = cdpaFormat;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public int getIsDecimal() {
        return isDecimal;
    }

    public void setIsDecimal(int isDecimal) {
        this.isDecimal = isDecimal;
    }

    public List<PackageRelation> getPackageRelationList() {
        return packageRelationList;
    }

    public void setPackageRelationList(List<PackageRelation> packageRelationList) {
        this.packageRelationList = packageRelationList;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }


    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }
}
