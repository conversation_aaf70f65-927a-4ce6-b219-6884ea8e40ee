package com.midea.prestorage.function.pointjoin

import android.content.Intent
import androidx.databinding.ObservableField
import com.midea.prestorage.function.main.MainActivity

//import com.readystatesoftware.chuck.internal.ui.MainActivity //这个可以看报文

class PointJoinDetailVM(val activity: PointJoinDetailActivity) {

    val isWaiting = ObservableField(true)
    val isShowNext = ObservableField(true)

    fun init() {
        val position = activity.intent.getIntExtra("position", 0)
        isShowNext.set(position == 0)
    }

    fun back() {
        activity.finish()
    }

    fun next() {
        activity.next()
    }

    fun homePage() {
        activity.startActivity(Intent(activity, MainActivity::class.java))
    }
}