package com.midea.prestorage.function.put

import CheckUtil
import android.app.Activity
import android.content.Intent
import android.text.TextUtils
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.PutContainerInfo
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody


@Suppress("UNCHECKED_CAST")
class InStorageScanPutVM(val activity: InStorageScanPutActivity) {

    val isRefreshing = ObservableBoolean(false)
    val isNoData = ObservableBoolean(false)
    val orderNo = ObservableField("")
    val localInfo = ObservableField("")
    val areaInfo = ObservableField("")

    var beans: MutableList<PutContainerInfo>? = null

    private var checkLocal: DCBean? = null
    private var checkArea: DCBean? = null

    private lateinit var localDialog: FilterDialog
    private lateinit var areaDialog: FilterDialog

    var jumpFlag = false //判断是否跳转到扫码页面

    private fun filterResult() {
        if (beans != null) {
            val resultFirst = if (checkLocal != null) {
                beans?.filter { it.recommendZones.containsKey(checkLocal?.key) }
            } else {
                beans
            }

            val resultFinal = if (checkArea != null) {
                resultFirst?.filter { it.recommendAreas.containsValue(checkArea?.value) }
            } else {
                resultFirst
            }
            activity.adapter.setNewInstance(resultFinal?.toMutableList())
            activity.adapter.notifyDataSetChanged()

            if (activity.adapter.data.isEmpty()) {
                isNoData.set(true)
            } else {
                isNoData.set(false)
            }
        }
    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        orderNo.set("")
        isRefreshing.set(true)
        initOrderList()
    }

    val orderEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                if (TextUtils.isEmpty(orderNo.get())) {
                    onRefreshCommand.onRefresh()
                    return
                }
                filterResultEnter()
            }
        }
    }

    private fun filterResultEnter() {
        if (beans != null) {
            val resultFinal = beans?.filter { it.containerCode == orderNo.get().toString() }
            if (resultFinal?.size == 1) {
                onItemClick(resultFinal[0])
            } else {
                activity.adapter.setNewInstance(resultFinal?.toMutableList())
                activity.adapter.notifyDataSetChanged()
            }

            if (activity.adapter.data.isEmpty()) {
                isNoData.set(true)
            } else {
                isNoData.set(false)
            }
        }
    }

    fun init() {
        onRefreshCommand.onRefresh()

        localDialog = FilterDialog(activity)
        localDialog.setTitle("请选择上架库区")
        localDialog.dismissEdit()
        localDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            checkLocal = it as DCBean
            localInfo.set(it.key)
            activity.binding.ivLocalInfo.visibility = View.VISIBLE
            filterResult()
            localDialog.dismiss()
        })

        areaDialog = FilterDialog(activity)
        areaDialog.setTitle("请选择区域类型")
        areaDialog.dismissEdit()
        areaDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            checkArea = it as DCBean
            areaInfo.set(it.key.split(" ")[0])
            activity.binding.ivAreaInfo.visibility = View.VISIBLE
            filterResult()
            areaDialog.dismiss()
        })
    }

    private fun initOrderList() {
        val param = mutableMapOf(
            "whCode" to activity.getWhCode()
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getAddGoodsService()
            .containerList(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<PutContainerInfo>>(activity) {
                override fun success(data: MutableList<PutContainerInfo>?) {
                    isRefreshing.set(false)
                    if (data != null && data.isNotEmpty()) {
                        isNoData.set(false)

                        beans = data
                        activity.adapter.setNewInstance(data)

                        initDialog(data)
                        localInfo.set("")
                        areaInfo.set("")

                        checkLocal = null
                        checkArea = null

                        activity.binding.ivAreaInfo.visibility = View.GONE
                        activity.binding.ivLocalInfo.visibility = View.GONE
                    } else {
                        isNoData.set(true)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isRefreshing.set(false)
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun initDialog(data: MutableList<PutContainerInfo>) {
        val localBeans = mutableSetOf<DCBean>()
        data.forEach { item ->
            item.recommendZones.forEach {
                if (localBeans.filter { item -> item.key == it.key }.isNullOrEmpty()) {
                    localBeans.add(DCBean(it.key, it.value, DCBean.SHOW_KEY))
                }
            }
        }
        localDialog.addAllData(localBeans.toMutableList() as MutableList<BaseItemShowInfo>)
        areaDialog.addAllData(
            mutableListOf(
                DCBean("高层存储区       H-STORAGE", "H-STORAGE", DCBean.SHOW_KEY),
                DCBean("低层存储区       L-STORAGE", "L-STORAGE", DCBean.SHOW_KEY),
                DCBean("拣货区               PICK", "PICK", DCBean.SHOW_KEY)
            )
        )
    }

    fun back() {
        activity.finish()
    }

    private val startActivity =
        activity.registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            //此处是跳转的result回调方法
            if (it.data != null && it.resultCode == Activity.RESULT_OK) {
                val result = it.data?.getIntExtra("result", -1)
                if (result != -1) {
                    activity.finish()
                }
            }
        }

    fun onItemClick(bean: PutContainerInfo) {
        val it = Intent(activity, InStorageScanPutOperationActivity::class.java)
        it.putExtra("bean", bean)
        startActivity.launch(it)
    }

    fun startScan() {
        jumpFlag = true
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        orderNo.set(result)
        orderEnterKeyPress.onEnterKey()
    }

    fun recommendLocal() {
        localDialog.show()
    }

    fun recommendArea() {
        areaDialog.show()
    }

    fun clearAreaInfo() {
        areaInfo.set("")
        activity.binding.ivAreaInfo.visibility = View.GONE
        checkArea = null
        filterResult()
    }

    fun clearLocalInfo() {
        localInfo.set("")
        activity.binding.ivLocalInfo.visibility = View.GONE
        checkLocal = null
        filterResult()
    }
}