package com.midea.prestorage.function.instorage.response;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;
import java.math.BigDecimal;

//  InOrderData  是 InOrderDetail 的汇总
public class InOrderData implements Serializable {

    @ShowAnnotation
    String custItemCode;    //客户货品编码 "21038730000022",
    @ShowAnnotation
    String itemName; //商品描述 MGJ90-02W 衣物护理柜 高温杀菌 　 　 除湿 　 　 　 　 220V,1Ph 50Hz",
    @ShowAnnotation
    Double totalQty; //总数量 ": 150.0000,
    @ShowAnnotation
    String lotAtt04; //批属性(是否正品): "Y",
    @ShowAnnotation
    Integer scanNum; //扫码次数 : 0,
    @ShowAnnotation
    String unscanMark; // 单据申请不扫码标记（00:已申请,01:已审核）
    @ShowAnnotation
    String receiptCode; // 入库单号  ": "TK17C681BE687239870163"

    String status; //状态码

    String itemCode; //商品编码

    String ownerCode;
    String whCode;
    String siteCode;  // siteCode
    String siteName;  // siteName

    String custOrderNo;

    // 大物流不扫码标识
    String cdcmUnscanMark;

    Double receiptQty;

    public Double getReceiptQty() {
        return receiptQty;
    }

    public void setReceiptQty(Double receiptQty) {
        this.receiptQty = receiptQty;
    }

    public String getCdcmUnscanMark() {
        return cdcmUnscanMark;
    }

    public void setCdcmUnscanMark(String cdcmUnscanMark) {
        this.cdcmUnscanMark = cdcmUnscanMark;
    }

    public String getCustOrderNo() {
        return custOrderNo;
    }

    public void setCustOrderNo(String custOrderNo) {
        this.custOrderNo = custOrderNo;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public Double getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(Double totalQty) {
        this.totalQty = totalQty;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public Integer getScanNum() {
        return scanNum;
    }

    public void setScanNum(Integer scanNum) {
        this.scanNum = scanNum;
    }

    public String getUnscanMark() {
        return unscanMark;
    }

    public void setUnscanMark(String unscanMark) {
        this.unscanMark = unscanMark;
    }

    public String getReceiptCode() {
        return receiptCode;
    }

    public void setReceiptCode(String receiptCode) {
        this.receiptCode = receiptCode;
    }
}
