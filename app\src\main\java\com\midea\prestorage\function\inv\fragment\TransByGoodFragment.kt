package com.midea.prestorage.function.inv.fragment

import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.*
import android.widget.EditText
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.function.addgoods.fragment.FragmentAddPoolUnionBinding
import com.midea.prestorage.function.inv.response.FuInvLocationInventory
import com.midea.prestorage.function.inv.response.ItemRfVO
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.*
import com.midea.prestorage.widgets.ScrollingLinearLayoutManager
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.FragmentAddPoolBinding
import com.midea.prestoragesaas.databinding.FragmentAddPoolCareBinding
import com.midea.prestoragesaas.databinding.FragmentTransByGoodBinding
import com.midea.prestoragesaas.databinding.FragmentTransByGoodCareBinding
import com.trello.rxlifecycle2.components.support.RxFragment
import java.math.BigDecimal
import java.math.RoundingMode


// 按商品移库 的 fragment
class TransByGoodFragment : RxFragment() {

    companion object {
        const val TAG = "TransByGoodFragment"

        fun newInstance(position: Int): TransByGoodFragment {
            val bundle = Bundle()
            bundle.putInt("position", position)
            val fragment = TransByGoodFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    lateinit var binding: FragmentTransByGoodUnionBinding
    private var vm: TransByGoodVM? = null
    private lateinit var transByGoodAdpter: TarsByGoodAdapter
    val selectedPositions = mutableSetOf<Int>()

    fun getTransByGoodAdpter(): TarsByGoodAdapter {
        return transByGoodAdpter
    }


    fun getVm(): TransByGoodVM? {
        return vm
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            FragmentTransByGoodUnionBinding.V2(inflater.let {
                FragmentTransByGoodCareBinding.inflate(
                    it,
                    container,
                    false
                )
            })
        } else {
            FragmentTransByGoodUnionBinding.V1(inflater.let {
                FragmentTransByGoodBinding.inflate(
                    it,
                    container,
                    false
                )
            })
        }

        initView()
        initRecycleView()

        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
    }

    //初始化
    private fun initRecycleView() {
        binding.rv.apply {
            layoutManager = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                ScrollingLinearLayoutManager(this.context)
            } else {
                LinearLayoutManager(this.context)
            }
            transByGoodAdpter = TarsByGoodAdapter(vm)
            adapter = transByGoodAdpter
        }

        transByGoodAdpter.setOnDataChangeListenerPosition<FuInvLocationInventory> { it: FuInvLocationInventory, isSelected: Boolean ->
            transByGoodAdpter.data.filter { item -> item.isJustClick }.forEach { item ->
                item.isJustClick = false
                if (AppUtils.isZero(item.moveNum)) {
                    item.isSelected = false
                }
            }

            if (it.isSelected) {
                if (AppUtils.isZero(it.moveNum)) {
                    it.moveNum = it.tempMoveNum
                }
                it.isJustClick = true
            } else {
                it.moveNum = BigDecimal.ZERO
            }
            transByGoodAdpter.calculatedTotalNub()

            if (isSelected) {
                selectedPositions.add(transByGoodAdpter.data.indexOf(it))
            } else {
                selectedPositions.remove(transByGoodAdpter.data.indexOf(it))
            }

            if (vm?.firstSelectedPosition != (selectedPositions.firstOrNull()
                    ?: -1) && (selectedPositions.firstOrNull() ?: -1) != -1
            ) {
                vm?.firstSelectedPosition = selectedPositions.firstOrNull() ?: -1
                vm?.let { viewModel ->
                    val position = viewModel.firstSelectedPosition
                    if (position >= 0 && position < transByGoodAdpter.data.size) {
                        viewModel.getRecommendLoc(transByGoodAdpter.data[position])
                    }
                }
            }
        }
    }

    private fun initView() {
        if (vm == null) {
            vm = TransByGoodVM(this)
            binding.vm = vm
        }
    }

    override fun onResume() {
        super.onResume()

        if (binding.etFromLocCode.isEnabled) {
            binding.etFromLocCode.post {
                AppUtils.requestFocus(binding.etFromLocCode)
            }
        }
    }

    class TarsByGoodAdapter(private val vm: TransByGoodVM?) :
        ListCheckBoxAdapter<FuInvLocationInventory>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_for_transfer_by_good_big_logistics_care else R.layout.item_for_transfer_by_good_big_logistics) {

        init {
            setClickId(R.id.view_click)
        }

        //计算总的移库数量
        fun calculatedTotalNub() {
            if (data.isNotEmpty()) {
                vm?.setTotalNum(data.map { it.moveNum }.reduce { acc, num -> acc + num }
                    .stripTrailingZeros().toPlainString())
            } else {
                vm?.setTotalNum("0")
            }
        }

        fun filterNonDigit(limitDecimalPlaces: Int, inputText: String): String {
            val sb = StringBuilder()
            var allowDot = limitDecimalPlaces > 0
            inputText.forEach { c ->
                if (Character.isDigit(c) || (c == '.' && allowDot)) {
                    sb.append(c)
                    if (c == '.') allowDot = false
                } else {
                    AppUtils.showToast(context, "只能输入正整数")
                }
            }
            return sb.toString()
        }

        fun filterNumber(limitDecimalPlaces: Int, input: String): String {
            val dotIndex = input.indexOf(".")
            return if (dotIndex == -1) {
                input
            } else {
                val integerPart = input.substring(0, dotIndex)
                var decimalPart = input.substring(dotIndex + 1)
                if (decimalPart.length > limitDecimalPlaces) {
                    AppUtils.showToast(context, "最高输入小数点后" + limitDecimalPlaces + "位")
                    decimalPart = decimalPart.substring(0, limitDecimalPlaces)
                    "$integerPart.$decimalPart"
                } else {
                    input
                }
            }
        }

        override fun convert(helper: BaseViewHolder?, item: BaseItemForPopup) {
            super.convert(helper, item)
            item as FuInvLocationInventory

            helper?.setGone(R.id.ll_moveNum01, true)
            helper?.setGone(R.id.ll_moveNum02, true)
            helper?.setGone(R.id.ll_moveNum03, true)
            helper?.setGone(R.id.ll_moveNum04, true)
            helper?.setGone(R.id.ll_moveNum05, true)
            helper?.setGone(R.id.ll_moveNum06, true)


            val editTextNum1 = helper?.getView<EditText>(R.id.moveNum01)
            val editTextNum2 = helper?.getView<EditText>(R.id.moveNum02)
            val editTextNum3 = helper?.getView<EditText>(R.id.moveNum03)
            val editTextNum4 = helper?.getView<EditText>(R.id.moveNum04)
            val editTextNum5 = helper?.getView<EditText>(R.id.moveNum05)
            val tvTotalNum = helper?.getView<TextView>(R.id.tv_total_num)
            val tvMoveTag = helper?.getView<TextView>(R.id.tv_move_tag)

            if (editTextNum5?.tag is TextWatcher) {
                editTextNum5.removeTextChangedListener(editTextNum5.tag as TextWatcher)
            }

            if (editTextNum1?.tag is TextWatcher) {
                editTextNum1.removeTextChangedListener(editTextNum1.tag as TextWatcher)
            }

            if (editTextNum2?.tag is TextWatcher) {
                editTextNum2.removeTextChangedListener(editTextNum2.tag as TextWatcher)
            }

            if (editTextNum3?.tag is TextWatcher) {
                editTextNum3.removeTextChangedListener(editTextNum3.tag as TextWatcher)
            }

            if (editTextNum4?.tag is TextWatcher) {
                editTextNum4.removeTextChangedListener(editTextNum4.tag as TextWatcher)
            }

            item.packageRelationList?.let { list ->
                for (bean in list) {
                    when (bean.cdprUnit) {
                        "OT" -> {
                            helper?.setGone(R.id.ll_moveNum01, false)
                            helper?.setText(R.id.tv_desc01, bean.cdprDesc ?: "")
                        }
                        "PL" -> {
                            helper?.setGone(R.id.ll_moveNum02, false)
                            helper?.setText(R.id.tv_desc02, bean.cdprDesc ?: "")
                        }
                        "CS" -> {
                            helper?.setGone(R.id.ll_moveNum03, false)
                            helper?.setText(R.id.tv_desc03, bean.cdprDesc ?: "")
                        }
                        "IP" -> {
                            helper?.setGone(R.id.ll_moveNum04, false)
                            helper?.setText(R.id.tv_desc04, bean.cdprDesc ?: "")
                        }
                        "EA" -> {
                            helper?.setGone(R.id.ll_moveNum05, false)
                            helper?.setText(R.id.tv_desc05, bean.cdprDesc ?: "")
                        }
                    }
                }
            }

            if (item.packageRelationList.size == 1) {
                helper?.setGone(R.id.ll_moveNum06, false)
            }

            val watcher = object : TextWatcher {

                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(
                    s: CharSequence?,
                    start: Int,
                    before: Int,
                    count: Int
                ) {
                }

                override fun afterTextChanged(s: Editable?) {
                    //如果存在就给与数值，否则这个项目就是清空的
                    val tvItem = tvMoveTag?.tag as FuInvLocationInventory
                    if (s.toString().isNotEmpty()) {
                        var tempStr = s.toString()
                        var filterText =
                            filterNonDigit(if (tvItem.isDecimal != 0) 4 else 0, s.toString())
                        if (tvItem.isDecimal != 0) {
                            filterText = filterNumber(4, filterText)
                        }
                        if (filterText != tempStr) {
                            editTextNum5?.setText(filterText)
                            editTextNum5?.setSelection(editTextNum5?.text.length)
                        }

                        tvItem.moveNum = AppUtils.getBigDecimalValue(tvItem.editNum1)
                            .multiply(
                                tvItem.otQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(
                            tvItem.ipQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            filterText
                        ).multiply(tvItem.eaQuantity ?: BigDecimal.ZERO)

                        tvItem.tempMoveNum = AppUtils.getBigDecimalValue(tvItem.editNum1)
                            .multiply(
                                tvItem.otQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(
                            tvItem.ipQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            filterText
                        ).multiply(tvItem.eaQuantity ?: BigDecimal.ZERO)

                        tvItem.editNum = AppUtils.getBigDecimalValue(filterText)
                    } else {
                        tvItem.moveNum = AppUtils.getBigDecimalValue(tvItem.editNum1)
                            .multiply(
                                tvItem.otQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.tempMoveNum = AppUtils.getBigDecimalValue(tvItem.editNum1)
                            .multiply(
                                tvItem.otQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.editNum = BigDecimal.ZERO
                    }

                    if (tvItem.usableQty == null) {
                        tvItem.usableQty = BigDecimal.ZERO
                    }

                    if (tvItem.moveNum.compareTo(tvItem.usableQty) == 1) {
                        val tag = editTextNum5?.tag as TextWatcher
                        editTextNum5?.removeTextChangedListener(tag)
                        editTextNum5?.setText("")
                        editTextNum5?.addTextChangedListener(tag)

                        tvItem.moveNum = AppUtils.getBigDecimalValue(tvItem.editNum1)
                            .multiply(
                                tvItem.otQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.tempMoveNum = AppUtils.getBigDecimalValue(tvItem.editNum1)
                            .multiply(
                                tvItem.otQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.editNum = BigDecimal.ZERO
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(context, "输入数量必须大于0且小于等于可用数量!")

                        if (tvItem.packageRelationList.isEmpty()) {
                            tvTotalNum?.text = "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}"
                        } else {
                            tvTotalNum?.text =
                                "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}" + tvItem.packageRelationList.find { it.cdprUnit == "EA" }?.cdprDesc +
                                        "(规格:" + tvItem.cdpaFormat + ")"
                        }
                    } else {
                        if (tvItem.packageRelationList.isEmpty()) {
                            tvTotalNum?.text = "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}"
                        } else {
                            tvTotalNum?.text =
                                "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}" + tvItem.packageRelationList.find { it.cdprUnit == "EA" }?.cdprDesc +
                                        "(规格:" + tvItem.cdpaFormat + ")"
                        }
                    }

                    tvItem.isJustClick = true

                    //计算总移库数量
                    calculatedTotalNub()
                }
            }

            val watcher1 = object : TextWatcher {

                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(
                    s: CharSequence?,
                    start: Int,
                    before: Int,
                    count: Int
                ) {
                }

                override fun afterTextChanged(s: Editable?) {
                    //如果存在就给与数值，否则这个项目就是清空的
                    val tvItem = tvMoveTag?.tag as FuInvLocationInventory
                    if (s.toString().isNotEmpty()) {
                        var tempStr = s.toString()

                        tvItem.moveNum = AppUtils.getBigDecimalValue(tempStr)
                            .multiply(
                                tvItem.otQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(
                            tvItem.ipQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum
                        ).multiply(tvItem.eaQuantity ?: BigDecimal.ZERO)

                        tvItem.tempMoveNum = AppUtils.getBigDecimalValue(tempStr)
                            .multiply(
                                tvItem.otQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(
                            tvItem.ipQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum
                        ).multiply(tvItem.eaQuantity ?: BigDecimal.ZERO)

                        tvItem.editNum1 = AppUtils.getBigDecimalValue(tempStr)
                    } else {
                        tvItem.moveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.tempMoveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.editNum1 = BigDecimal.ZERO
                    }

                    if (tvItem.usableQty == null) {
                        tvItem.usableQty = BigDecimal.ZERO
                    }

                    if (tvItem.moveNum.compareTo(tvItem.usableQty) == 1) {
                        val tag = editTextNum1?.tag as TextWatcher
                        editTextNum1?.removeTextChangedListener(tag)
                        editTextNum1?.setText("")
                        editTextNum1?.addTextChangedListener(tag)

                        tvItem.moveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.tempMoveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.editNum1 = BigDecimal.ZERO
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(context, "输入数量必须大于0且小于等于可用数量!")

                        if (tvItem.packageRelationList.isEmpty()) {
                            tvTotalNum?.text = "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}"
                        } else {
                            tvTotalNum?.text =
                                "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}" + tvItem.packageRelationList.find { it.cdprUnit == "EA" }?.cdprDesc +
                                        "(规格:" + tvItem.cdpaFormat + ")"
                        }
                    } else {
                        if (tvItem.packageRelationList.isEmpty()) {
                            tvTotalNum?.text = "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}"
                        } else {
                            tvTotalNum?.text =
                                "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}" + tvItem.packageRelationList.find { it.cdprUnit == "EA" }?.cdprDesc +
                                        "(规格:" + tvItem.cdpaFormat + ")"
                        }
                    }

                    tvItem.isJustClick = true

                    //计算总移库数量
                    calculatedTotalNub()
                }
            }

            val watcher2 = object : TextWatcher {

                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(
                    s: CharSequence?,
                    start: Int,
                    before: Int,
                    count: Int
                ) {
                }

                override fun afterTextChanged(s: Editable?) {
                    //如果存在就给与数值，否则这个项目就是清空的
                    val tvItem = tvMoveTag?.tag as FuInvLocationInventory
                    if (s.toString().isNotEmpty()) {
                        var tempStr = s.toString()

                        tvItem.moveNum = AppUtils.getBigDecimalValue(tempStr)
                            .multiply(
                                tvItem.plQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(
                            tvItem.ipQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum
                        ).multiply(tvItem.eaQuantity ?: BigDecimal.ZERO)

                        tvItem.tempMoveNum = AppUtils.getBigDecimalValue(tempStr)
                            .multiply(
                                tvItem.plQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(
                            tvItem.ipQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum
                        ).multiply(tvItem.eaQuantity ?: BigDecimal.ZERO)

                        tvItem.editNum2 = AppUtils.getBigDecimalValue(tempStr)
                    } else {
                        tvItem.moveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.tempMoveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.editNum2 = BigDecimal.ZERO
                    }

                    if (tvItem.usableQty == null) {
                        tvItem.usableQty = BigDecimal.ZERO
                    }

                    if (tvItem.moveNum.compareTo(tvItem.usableQty) == 1) {
                        val tag = editTextNum2?.tag as TextWatcher
                        editTextNum2?.removeTextChangedListener(tag)
                        editTextNum2?.setText("")
                        editTextNum2?.addTextChangedListener(tag)

                        tvItem.moveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.tempMoveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.editNum2 = BigDecimal.ZERO
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(context, "输入数量必须大于0且小于等于可用数量!")

                        if (tvItem.packageRelationList.isEmpty()) {
                            tvTotalNum?.text = "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}"
                        } else {
                            tvTotalNum?.text =
                                "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}" + tvItem.packageRelationList.find { it.cdprUnit == "EA" }?.cdprDesc +
                                        "(规格:" + tvItem.cdpaFormat + ")"
                        }
                    } else {
                        if (tvItem.packageRelationList.isEmpty()) {
                            tvTotalNum?.text = "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}"
                        } else {
                            tvTotalNum?.text =
                                "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}" + tvItem.packageRelationList.find { it.cdprUnit == "EA" }?.cdprDesc +
                                        "(规格:" + tvItem.cdpaFormat + ")"
                        }
                    }

                    tvItem.isJustClick = true

                    //计算总移库数量
                    calculatedTotalNub()
                }
            }

            val watcher3 = object : TextWatcher {

                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(
                    s: CharSequence?,
                    start: Int,
                    before: Int,
                    count: Int
                ) {
                }

                override fun afterTextChanged(s: Editable?) {
                    //如果存在就给与数值，否则这个项目就是清空的
                    val tvItem = tvMoveTag?.tag as FuInvLocationInventory
                    if (s.toString().isNotEmpty()) {
                        var tempStr = s.toString()

                        tvItem.moveNum = AppUtils.getBigDecimalValue(tempStr)
                            .multiply(
                                tvItem.csQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(
                            tvItem.ipQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum
                        ).multiply(tvItem.eaQuantity ?: BigDecimal.ZERO)

                        tvItem.tempMoveNum = AppUtils.getBigDecimalValue(tempStr)
                            .multiply(
                                tvItem.csQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(
                            tvItem.ipQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum
                        ).multiply(tvItem.eaQuantity ?: BigDecimal.ZERO)

                        tvItem.editNum3 = AppUtils.getBigDecimalValue(tempStr)
                    } else {
                        tvItem.moveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.tempMoveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.editNum3 = BigDecimal.ZERO
                    }

                    if (tvItem.usableQty == null) {
                        tvItem.usableQty = BigDecimal.ZERO
                    }

                    if (tvItem.moveNum.compareTo(tvItem.usableQty) == 1) {
                        val tag = editTextNum3?.tag as TextWatcher
                        editTextNum3?.removeTextChangedListener(tag)
                        editTextNum3?.setText("")
                        editTextNum3?.addTextChangedListener(tag)

                        tvItem.moveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.tempMoveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum4
                        ).multiply(tvItem.ipQuantity ?: BigDecimal.ZERO)

                        tvItem.editNum3 = BigDecimal.ZERO
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(context, "输入数量必须大于0且小于等于可用数量!")

                        if (tvItem.packageRelationList.isEmpty()) {
                            tvTotalNum?.text = "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}"
                        } else {
                            tvTotalNum?.text =
                                "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}" + tvItem.packageRelationList.find { it.cdprUnit == "EA" }?.cdprDesc +
                                        "(规格:" + tvItem.cdpaFormat + ")"
                        }
                    } else {
                        if (tvItem.packageRelationList.isEmpty()) {
                            tvTotalNum?.text = "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}"
                        } else {
                            tvTotalNum?.text =
                                "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}" + tvItem.packageRelationList.find { it.cdprUnit == "EA" }?.cdprDesc +
                                        "(规格:" + tvItem.cdpaFormat + ")"
                        }
                    }

                    tvItem.isJustClick = true

                    //计算总移库数量
                    calculatedTotalNub()
                }
            }

            val watcher4 = object : TextWatcher {

                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(
                    s: CharSequence?,
                    start: Int,
                    before: Int,
                    count: Int
                ) {
                }

                override fun afterTextChanged(s: Editable?) {
                    //如果存在就给与数值，否则这个项目就是清空的
                    val tvItem = tvMoveTag?.tag as FuInvLocationInventory
                    if (s.toString().isNotEmpty()) {
                        var tempStr = s.toString()

                        tvItem.moveNum = AppUtils.getBigDecimalValue(tempStr)
                            .multiply(
                                tvItem.ipQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum
                        ).multiply(tvItem.eaQuantity ?: BigDecimal.ZERO)

                        tvItem.tempMoveNum = AppUtils.getBigDecimalValue(tempStr)
                            .multiply(
                                tvItem.ipQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(
                            tvItem.csQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum
                        ).multiply(tvItem.eaQuantity ?: BigDecimal.ZERO)

                        tvItem.editNum4 = AppUtils.getBigDecimalValue(tempStr)
                    } else {
                        tvItem.moveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(tvItem.csQuantity ?: BigDecimal.ZERO)

                        tvItem.tempMoveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(tvItem.csQuantity ?: BigDecimal.ZERO)

                        tvItem.editNum4 = BigDecimal.ZERO
                    }

                    if (tvItem.usableQty == null) {
                        tvItem.usableQty = BigDecimal.ZERO
                    }

                    if (tvItem.moveNum.compareTo(tvItem.usableQty) == 1) {
                        val tag = editTextNum4?.tag as TextWatcher
                        editTextNum4?.removeTextChangedListener(tag)
                        editTextNum4?.setText("")
                        editTextNum4?.addTextChangedListener(tag)

                        tvItem.moveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(tvItem.csQuantity ?: BigDecimal.ZERO)

                        tvItem.tempMoveNum = AppUtils.getBigDecimalValue(tvItem.editNum)
                            .multiply(
                                tvItem.eaQuantity ?: BigDecimal.ZERO
                            ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum1
                        ).multiply(
                            tvItem.otQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum2
                        ).multiply(
                            tvItem.plQuantity ?: BigDecimal.ZERO
                        ) + AppUtils.getBigDecimalValue(
                            tvItem.editNum3
                        ).multiply(tvItem.csQuantity ?: BigDecimal.ZERO)

                        tvItem.editNum4 = BigDecimal.ZERO
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(context, "输入数量必须大于0且小于等于可用数量!")

                        if (tvItem.packageRelationList.isEmpty()) {
                            tvTotalNum?.text = "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}"
                        } else {
                            tvTotalNum?.text =
                                "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}" + tvItem.packageRelationList.find { it.cdprUnit == "EA" }?.cdprDesc +
                                        "(规格:" + tvItem.cdpaFormat + ")"
                        }
                    } else {
                        if (tvItem.packageRelationList.isEmpty()) {
                            tvTotalNum?.text = "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}"
                        } else {
                            tvTotalNum?.text =
                                "共${AppUtils.getBigDecimalValueStr(tvItem.moveNum)}" + tvItem.packageRelationList.find { it.cdprUnit == "EA" }?.cdprDesc +
                                        "(规格:" + tvItem.cdpaFormat + ")"
                        }
                    }

                    tvItem.isJustClick = true

                    //计算总移库数量
                    calculatedTotalNub()
                }
            }

            if (item.isSelected) {
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    helper?.setImageResource(R.id.img_select, R.drawable.select_selected_care)
                } else {
                    helper?.setImageResource(R.id.img_select, R.drawable.ic_check_selected)
                }
                helper?.setEnabled(R.id.moveNum01, true)
                helper?.setEnabled(R.id.moveNum02, true)
                helper?.setEnabled(R.id.moveNum03, true)
                helper?.setEnabled(R.id.moveNum04, true)
                helper?.setEnabled(R.id.moveNum05, true)

                helper?.setText(
                    R.id.moveNum01,
                    AppUtils.getBigDecimalValueStrNullZero(item.editNum1)
                )
                helper?.setText(
                    R.id.moveNum02,
                    AppUtils.getBigDecimalValueStrNullZero(item.editNum2)
                )
                helper?.setText(
                    R.id.moveNum03,
                    AppUtils.getBigDecimalValueStrNullZero(item.editNum3)
                )
                helper?.setText(
                    R.id.moveNum04,
                    AppUtils.getBigDecimalValueStrNullZero(item.editNum4)
                )
                helper?.setText(
                    R.id.moveNum05,
                    AppUtils.getBigDecimalValueStrNullZero(item.editNum)
                )

                helper?.setGone(R.id.tv_total_num, false)

                if (item.isJustClick) {
                    editTextNum5?.requestFocus()
                    editTextNum5?.setSelection(editTextNum5?.text.toString().length)
                }

                if (item.packageRelationList.isEmpty()) {
                    tvTotalNum?.text = "共${AppUtils.getBigDecimalValueStr(item.moveNum)}"
                } else {
                    tvTotalNum?.text =
                        "共${AppUtils.getBigDecimalValueStr(item.moveNum)}" + item.packageRelationList.find { it.cdprUnit == "EA" }?.cdprDesc +
                                "(规格:" + item.cdpaFormat + ")"
                }
            } else {
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    helper?.setImageResource(R.id.img_select, R.drawable.select_normal_care)
                } else {
                    helper?.setImageResource(R.id.img_select, R.drawable.ic_check_unselect)
                }
                helper?.setEnabled(R.id.moveNum01, false)
                helper?.setEnabled(R.id.moveNum02, false)
                helper?.setEnabled(R.id.moveNum03, false)
                helper?.setEnabled(R.id.moveNum04, false)
                helper?.setEnabled(R.id.moveNum05, false)
                helper?.setText(R.id.moveNum01, "")
                helper?.setText(R.id.moveNum02, "")
                helper?.setText(R.id.moveNum03, "")
                helper?.setText(R.id.moveNum04, "")
                helper?.setText(R.id.moveNum05, "")
                helper?.setGone(R.id.tv_total_num, true)
                helper?.setText(R.id.tv_total_num, "")
            }

            //初始化默认值
            helper?.setText(R.id.usableQty, "")
            helper?.setText(R.id.tv_status, "")

            //批属性处理
            var info = ""//批属性字段
            var joinStr = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                "\n"
            } else {
                " /"
            }
            if (!item.lotAtt04.isNullOrBlank()) {  //有中文就显示中文
                info += "批属性:${DCUtils.goodsStatue?.find { it.value == item.lotAtt04 }?.key}"
            } //正品或不良品

            //批次处理
            if (!item.lotAtt05.isNullOrBlank()) {
                info += "${joinStr}批次:${item.lotAtt05}"
            }

            //生产日期
            if (!item.lotAtt01.isNullOrEmpty()) {
                // 去除时分秒
                info += "${joinStr}生产日期:"

                val lotAtt01 = item.lotAtt01
                if (lotAtt01?.split(" ")!!.isNotEmpty()) {
                    info += lotAtt01.split(" ")[0]
                }
            }

            //失效日期
            if (!item.lotAtt02.isNullOrEmpty()) {
                // 去除时分秒
                info += "${joinStr}失效日期:"
                val lotAttr02 = item.lotAtt02
                if (lotAttr02?.split(" ")!!.isNotEmpty()) {
                    info += lotAttr02.split(" ")[0]
                }
            }

            val spannableString = SpannableString(info)
            val indexOf1 = info.indexOf("批属性:")
            val indexOf2 = info.indexOf("${joinStr}批次:")
            val indexOf3 = info.indexOf("${joinStr}生产日期:")
            val indexOf4 = info.indexOf("${joinStr}失效日期:")

            if (info.contains("批属性:")) {
                val endIndex = if (indexOf2 > 0) {
                    indexOf2
                } else {
                    info.length
                }

//                spannableString.setSpan(
//                    StyleSpan(Typeface.BOLD),
//                    indexOf1 + 4,
//                    endIndex,
//                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
//                )

                spannableString.setSpan(
                    ForegroundColorSpan(Color.BLACK),
                    indexOf1 + 4,
                    endIndex,
                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
                )
            }

            if (info.contains("批次:")) {
                val endIndex = if (indexOf3 > 0) {
                    indexOf3
                } else {
                    info.length
                }

//                spannableString.setSpan(
//                    StyleSpan(Typeface.BOLD),
//                    indexOf2 + 5,
//                    endIndex,
//                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
//                )

                spannableString.setSpan(
                    ForegroundColorSpan(Color.BLACK),
                    indexOf2 + 4,
                    endIndex,
                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
                )
            }

            if (info.contains("生产日期:")) {
                val endIndex = if (indexOf4 > 0) {
                    indexOf4
                } else {
                    info.length
                }
//                spannableString.setSpan(
//                    StyleSpan(Typeface.BOLD),
//                    indexOf3 + 7,
//                    endIndex,
//                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
//                )

                spannableString.setSpan(
                    ForegroundColorSpan(Color.BLACK),
                    indexOf3 + 6,
                    endIndex,
                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
                )
            }

            if (info.contains("失效日期:")) {
//                spannableString.setSpan(
//                    StyleSpan(Typeface.BOLD),
//                    indexOf4 + 7,
//                    info.length,
//                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
//                )

                spannableString.setSpan(
                    ForegroundColorSpan(Color.BLACK),
                    indexOf4 + 6,
                    info.length,
                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
                )
            }

            helper?.setText(R.id.infoLotAtt, spannableString)

            if (item?.whCsBarcode69.isNullOrEmpty() && item?.whBarcode69.isNullOrEmpty()) {
                helper?.setGone(R.id.tv_carcode69, true)
            } else {
                helper?.setGone(R.id.tv_carcode69, false)
                helper?.setText(
                    R.id.tv_carcode69,
                    LotAttUnit.formatWhBarcode69(item?.whCsBarcode69, item?.whBarcode69)
                )
            }
            var str = "总数:  ${AppUtils.getBigDecimalValueStr(item.onHandQty)}  占用数:  ${
                AppUtils.getBigDecimalValueStr(item.allocatedQty + item.lockedQty + item.repOutQty)
            }"

            if (item.packageRelationList.isNotEmpty()) {
                helper?.setText(
                    R.id.usableQty,
                    AppUtils.getBigDecimalValueStr(item.usableQty) + item.packageRelationList.find { it.cdprIsMain == "Y" }?.cdprDesc + "  (" + str + ")"
                )
            } else {
                helper?.setText(
                    R.id.usableQty,
                    AppUtils.getBigDecimalValueStr(item.usableQty) + "  (" + str + ")"
                )
            }

            if (item.packageRelationList.size > 3) {
                helper?.setGone(R.id.tv_move_tag, false)
                helper?.setGone(R.id.tv_move_tag_botton, true)
            } else {
                helper?.setGone(R.id.tv_move_tag, true)
                helper?.setGone(R.id.tv_move_tag_botton, false)
            }

            helper?.setText(R.id.tv_status, DCUtils.goodsStatue?.find { it.value == item.lotAtt04 }?.key)

            //数量文本改变汇总
            editTextNum5?.addTextChangedListener(watcher)
            editTextNum5?.tag = watcher
            editTextNum4?.addTextChangedListener(watcher4)
            editTextNum4?.tag = watcher4
            editTextNum3?.addTextChangedListener(watcher3)
            editTextNum3?.tag = watcher3
            editTextNum2?.addTextChangedListener(watcher2)
            editTextNum2?.tag = watcher2
            editTextNum1?.addTextChangedListener(watcher1)
            editTextNum1?.tag = watcher1
            tvMoveTag?.tag = item //控件绑定数据，复用的时候用到
        }
    }

    class PopListAdapter :
        CommonAdapter<ItemRfVO>(R.layout.item_pop_view_for_select_cust_item_code_pop) {
        override fun convert(holder: BaseViewHolder?, item: ItemRfVO) {
            super.convert(holder, item)

            holder?.setGone(R.id.tv_item_name, item.itemName.isNullOrEmpty())
        }
    }
}