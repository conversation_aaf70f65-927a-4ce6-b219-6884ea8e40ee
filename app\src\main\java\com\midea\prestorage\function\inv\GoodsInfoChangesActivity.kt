package com.midea.prestorage.function.inv

import android.os.Bundle
import android.text.TextWatcher
import android.widget.EditText
import androidx.core.content.ContextCompat
import androidx.core.widget.addTextChangedListener
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.beans.net.SerialPickContainerInfo
import com.midea.prestorage.function.inv.response.RespFuInvLocationInventory
import com.midea.prestorage.function.outstorage.dialog.DeleteBarcodeDialog
import com.midea.prestorage.function.outstorage.dialog.GoodsInfoChangesDialog
import com.midea.prestorage.function.outstorage.dialog.HandlingDialog
import com.midea.prestorage.function.outstorage.dialog.SendTipDialog
import com.midea.prestorage.function.picktaskdetail.PickTaskDetailActivity
import com.midea.prestorage.utils.AppUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityGoodsInfoChangesBinding
import java.math.BigDecimal
import java.math.RoundingMode

class GoodsInfoChangesActivity : BaseViewModelActivity<GoodsInfoChangesVM>() {
    private lateinit var binding: ActivityGoodsInfoChangesBinding
    private lateinit var changesDialog: GoodsInfoChangesDialog

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_goods_info_changes)
        vm = ViewModelProvider.AndroidViewModelFactory(application)
            .create(GoodsInfoChangesVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        vm.mBackNotification.observe(this, Observer<Boolean> {
            if (it) {
                if (vm.isModified()) {
                    changesDialog.setContent("是否保存修改信息？")
                    changesDialog.show()
                }else {
                    finish()
                }
            }
        })

        initSpinner()
        initListener()
        initDialog()
        initData()
        item69CodeRequestFocus()
    }

    fun initData() {
        vm.respFuInvLocationInventory =
            intent.getSerializableExtra("RespFuInvLocationInventory") as RespFuInvLocationInventory

        vm.respFuInvLocationInventory?.let {
            vm.itemCustItemCodeVM.content.set(it.custItemCode)
            vm.item69CodeVM.content.set(it.whBarcode69 ?: "")
            vm.itemCs69CodeVM.content.set(it.whCsBarcode69 ?: "")
            vm.itemMax69CodeVM.content.set(it.whMaxBarcode69 ?: "")
            if (it.isNeedScan69 == 1) {
                binding.spinnerStatus.selectedIndex = 1
                vm.needScanCode.set("是")
            }else {
                binding.spinnerStatus.selectedIndex = 0
                vm.needScanCode.set("否")
            }

            //下面是先把接口返回的数据保存，后面根据是否开启效期来进行是否展示
            if (!AppUtils.getBigDecimalValueStrNull(it.periodOfValidity).isNullOrEmpty() && it.periodOfValidity?.compareTo(BigDecimal(9999)) != 1) {
                vm.itemGoodsExpiryDate = AppUtils.getBigDecimalValueStrNull(it.periodOfValidity)
            }
            if (it.validityUnit.isNullOrEmpty()) {
                it.validityUnit = "D"
            }
            when {
                "y".equals(it.validityUnit, true) -> {
                    binding.spinnerExpiryDateUnit.selectedIndex = 0
                    vm.validityUnit.set("年")
                    vm.goodsExpiryDate = 365
                }
                "m".equals(it.validityUnit, true) -> {
                    binding.spinnerExpiryDateUnit.selectedIndex = 1
                    vm.validityUnit.set("月")
                    vm.goodsExpiryDate = 30
                }
                "w".equals(it.validityUnit, true) -> {
                    binding.spinnerExpiryDateUnit.selectedIndex = 2
                    vm.validityUnit.set("周")
                    vm.goodsExpiryDate = 7
                }
                else -> {
                    binding.spinnerExpiryDateUnit.selectedIndex = 3
                    vm.validityUnit.set("日")
                    vm.goodsExpiryDate = 1
                }
            }
            if (it.inLifePercentage.isNullOrEmpty() && AppUtils.getBigDecimalValueStrNull(it.inLifeDays).isNullOrEmpty()) {
                it.inLifePercentage = AppUtils.getBigDecimalValueStr(BigDecimal(66))
            }
            vm.itemInPercentage = it.inLifePercentage.toString()
            if (it.outLifePercentage.isNullOrEmpty() && AppUtils.getBigDecimalValueStrNull(it.outLifeDays).isNullOrEmpty()) {
                it.outLifePercentage = AppUtils.getBigDecimalValueStr(BigDecimal(33))
            }
            vm.itemOutPercentage = it.outLifePercentage.toString()
            if (AppUtils.getBigDecimalValueStrNull(it.inLifeDays).isNullOrEmpty()) {
                it.inLifeDays = BigDecimal.ZERO
            }
            vm.itemInDate = AppUtils.getBigDecimalValueStrNull(it.inLifeDays)
            if (AppUtils.getBigDecimalValueStrNull(it.outLifeDays).isNullOrEmpty()) {
                it.outLifeDays = BigDecimal.ZERO
            }
            vm.itemOutDate = AppUtils.getBigDecimalValueStrNull(it.outLifeDays)
            vm.itemWarningDate = AppUtils.getBigDecimalValueStrNull(it.earlyWarning)
            vm.itemSoldOutDate = AppUtils.getBigDecimalValueStrNull(it.earlyTakedown)

            //这里获取是否效期控制
            if ("Y".equals(it.isValidity, true)) {
                binding.spinnerExpiryDate.selectedIndex = 0
                vm.validityControl.set("是")
                vm.setExpiryDateData()
                binding.spinnerExpiryDateUnit.text = vm.validityUnit.get().toString()
                periodOfValidity(true)
            }else {
                binding.spinnerExpiryDate.selectedIndex = 1
                vm.validityControl.set("否")
                vm.clearExpiryDateData()
                binding.spinnerExpiryDateUnit.text = ""
                periodOfValidity(false)
            }
        }
    }

    fun initSpinner() {
        val scanBeans = mutableListOf<String>(
            "否",
            "是"
        )
        binding.spinnerStatus.setItems(scanBeans)
        binding.spinnerStatus.selectedIndex = 0

        binding.spinnerStatus.setOnItemSelectedListener { _, position, _, _ ->
            vm.needScanCode.set(scanBeans[position])
        }

        val expiryDateBeans = mutableListOf<String>(
            "是",
            "否"
        )
        binding.spinnerExpiryDate.setItems(expiryDateBeans)
        binding.spinnerExpiryDate.selectedIndex = 0

        binding.spinnerExpiryDate.setOnItemSelectedListener { _, position, _, _ ->
            vm.validityControl.set(expiryDateBeans[position])
            if (position == 0) {
                vm.setExpiryDateData()
                binding.spinnerExpiryDateUnit.text = vm.validityUnit.get().toString()
            }else {
                vm.getExpiryDateData()
                binding.spinnerExpiryDateUnit.text = ""
            }
            periodOfValidity(position == 0)
        }

        val expiryDateUnitBeans = mutableListOf<String>(
            "年",
            "月",
            "周",
            "日"
        )
        binding.spinnerExpiryDateUnit.setItems(expiryDateUnitBeans)
        binding.spinnerExpiryDateUnit.selectedIndex = 3

        binding.spinnerExpiryDateUnit.setOnItemSelectedListener { _, position, _, _ ->
            vm.validityUnit.set(expiryDateUnitBeans[position])
            when (position) {//效期单位代表的天数,年：365天；月：30天；周：7天，日：1天
                0 -> {
                    vm.goodsExpiryDate = 365
                }
                1 -> {
                    vm.goodsExpiryDate = 30
                }
                2 -> {
                    vm.goodsExpiryDate = 7
                }
                3 -> {
                    vm.goodsExpiryDate = 1
                }
            }
            handleDate(vm.itemGoodsExpiryDateVM.content.get())
        }
    }

    private fun initDialog() {
        //信息如有改动时弹出的提示
        changesDialog = GoodsInfoChangesDialog(this)
        changesDialog.setTitle("提示")
        changesDialog.setConfirmBack(object : GoodsInfoChangesDialog.ConfirmBack {
            override fun cancelBack() {
                changesDialog.dismiss()
                finish()
            }

            override fun confirmBack() {
                changesDialog.dismiss()
                vm.startSave(true)
            }

        })
    }

    private fun periodOfValidity(isValidity: Boolean) {
        val bgColor = if (isValidity) R.color.colorWhite else R.color.colorBg
        val etBgDrawable = if (isValidity) R.drawable.common_et_bg_gray02 else R.drawable.common_et_bg_gray01

        vm.setEditable(isValidity)
        binding.flExpiryDateUnit.background = ContextCompat.getDrawable(this, etBgDrawable)
        binding.spinnerExpiryDateUnit.setBackgroundColor(ContextCompat.getColor(this, bgColor))
        binding.spinnerExpiryDateUnit.isClickable = isValidity
    }

    private fun initListener() {
        createLWHWatcher(binding.itemLength.etContent)
        createLWHWatcher(binding.itemWidth.etContent)
        createLWHWatcher(binding.itemHeigh.etContent)

        createPercentageWatcher(binding.itemInPercentage.etContent, 100, "入库效期百分比要小于100")
        createPercentageWatcher(binding.itemOutPercentage.etContent, 100, "出库效期百分比要小于100")
        createPercentageWatcher(binding.itemGoodsExpiryDate.etContent, 10000, "商品效期不能超过9999")
//        createPercentageWatcher(binding.itemOutDate.etContent, 9999, "出库效期要小于9999")

        binding.itemGoodsExpiryDate.etContent.addTextChangedListener { text ->
            handleDate(text?.toString())
        }

        //每个输入框回车跳到下一个输入框
        val viewModelMap = mapOf(
            vm.item69CodeVM to binding.itemCs69Code.etContent,
            vm.itemCs69CodeVM to binding.itemMax69Code.etContent,
            vm.itemMax69CodeVM to binding.itemGoodsExpiryDate.etContent,
            vm.itemGoodsExpiryDateVM to binding.itemInPercentage.etContent,
            vm.itemInPercentageVM to binding.itemOutPercentage.etContent,
            vm.itemOutPercentageVM to binding.itemInDate.etContent,
            vm.itemInDateVM to binding.itemOutDate.etContent,
            vm.itemOutDateVM to binding.itemWarningDate.etContent,
            vm.itemWarningDateVM to binding.itemSoldOutDate.etContent,
            vm.itemSoldOutDateVM to binding.itemSoldOutDate.etContent
        )

        for ((viewModel, view) in viewModelMap) {
            viewModel.mEnter.observe(this, Observer<Boolean> { enter ->
                if (enter) {
                    view.post {
                        view.requestFocus()
                        view.setSelection(view.text.toString().length)
                    }
                }
            })
        }
    }

    private fun createLWHWatcher(view: EditText) {
        view.addTextChangedListener {
            vm.calculateVolume()
        }
    }

    private fun createPercentageWatcher(view: EditText, maxNum: Int, message: String) {
        view.addTextChangedListener { text ->
            val isItemInPercentage = view == binding.itemInPercentage.etContent
            val isItemOutPercentage = view == binding.itemOutPercentage.etContent
            val value = text?.toString()?.toBigDecimalOrNull()

            if (text?.toString().isNullOrEmpty()) {
                if (isItemInPercentage) {
                    if (vm.itemInDateVM.editable.get() == false) {
                        vm.itemInDateVM.content.set("")
                    }
                    vm.setItemInDateEditable(true)
                }
                if (isItemOutPercentage) {
                    if (vm.itemOutDateVM.editable.get() == false) {
                        vm.itemOutDateVM.content.set("")
                    }
                    vm.setItemOutDateEditable(true)
                }
            } else if (value != null && value >= BigDecimal(maxNum)) {
                view.setText("")
                vm.showNotification(message, false)
                if (isItemInPercentage) vm.setItemInDateEditable(true)
                if (isItemOutPercentage) vm.setItemOutDateEditable(true)
            } else {
                if (isItemInPercentage) {
                    vm.setItemInDateEditable(false)
                    if (value != null) {
                        vm.itemInDateVM.content.set(
                            calculateDays(
                                AppUtils.getBigDecimalValue(vm.itemGoodsExpiryDateVM.content.get()),
                                value
                            )
                        )
                    }
                }
                if (isItemOutPercentage) {
                    vm.setItemOutDateEditable(false)
                    if (value != null) {
                        vm.itemOutDateVM.content.set(
                            calculateDays(
                                AppUtils.getBigDecimalValue(vm.itemGoodsExpiryDateVM.content.get()),
                                value
                            )
                        )
                    }
                }
            }
        }
    }

    private fun item69CodeRequestFocus() {
        binding.item69Code.etContent.post {
            binding.item69Code.etContent.requestFocus()
            binding.item69Code.etContent.setSelection(binding.item69Code.etContent.text.toString().length)
        }
    }

    private fun handleDate(text: String?) {
        val value = text?.toBigDecimalOrNull()

        if (text.isNullOrEmpty()) {
            if (!vm.itemInPercentageVM.content.get().isNullOrEmpty()) {
                vm.itemInDateVM.content.set("0")
            }
            if (!vm.itemOutPercentageVM.content.get().isNullOrEmpty()) {
                vm.itemOutDateVM.content.set("0")
            }
        } else if (value != null) {
            if (!vm.itemInPercentageVM.content.get().isNullOrEmpty()) {
                vm.itemInDateVM.content.set(
                    calculateDays(
                        value,
                        AppUtils.getBigDecimalValue(vm.itemInPercentageVM.content.get())
                    )
                )
            }
            if (!vm.itemOutPercentageVM.content.get().isNullOrEmpty()) {
                vm.itemOutDateVM.content.set(
                    calculateDays(
                        value,
                        AppUtils.getBigDecimalValue(vm.itemOutPercentageVM.content.get())
                    )
                )
            }
        }
    }

    private fun calculateDays(value: BigDecimal, percent: BigDecimal): String {
        return value.multiply(BigDecimal(vm.goodsExpiryDate)).multiply(percent)
            .divide(BigDecimal("100"), 0, RoundingMode.CEILING).toString()
    }

    override fun onBackPressed() {
        if (vm.isModified()) {
            // 如果界面信息被修改，则弹出保存提示框
            changesDialog.setContent("是否保存修改信息？")
            changesDialog.show()
        } else {
            // 如果界面信息未被修改，则直接跳转回商品查询界面
            finish()
        }
    }
}