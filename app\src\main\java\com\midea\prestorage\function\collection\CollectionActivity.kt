package com.midea.prestorage.function.collection

import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestoragesaas.databinding.ActivityCollectionBinding
import com.midea.prestorage.utils.AppUtils
import com.xuexiang.xqrcode.XQRCode

// 按单收货/组单收货 的 Activity
class CollectionActivity : BaseActivity() {

    lateinit var binding: ActivityCollectionBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_collection)
        binding.vm = CollectionVM(this)

        // 初始化收货容器
        AppUtils.requestFocus(binding.etOrderNo)
    }

    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果
            binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }
}