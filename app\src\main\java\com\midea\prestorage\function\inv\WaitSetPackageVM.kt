package com.midea.prestorage.function.inv

import android.content.Intent
import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.net.InvSetDetailList
import com.midea.prestorage.beans.net.InvSetList
import com.midea.prestorage.beans.net.SetPackageConfirmReq
import com.midea.prestorage.beans.net.SetPackageConfirmResp
import com.midea.prestorage.function.containerpick.ContainerPickFourActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody

class WaitSetPackageVM(val activity: WaitSetPackageActivity) {

    var flag = MutableLiveData<String>("") // 标识是从哪个页面进入的，main：主列表页面，detail：详情页
    val isNoData = ObservableBoolean(true)
    var isPalletEnter = MutableLiveData(false)
    val hasCode = ObservableBoolean(false)
    var setCode = ObservableField("")
    var orderNo = ObservableField("")
    var goodsNo = ObservableField("")
    var wholeNum = ObservableField("0")
    var partNum = ObservableField("0")
    val allInvSetDetailList = mutableListOf<InvSetDetailList>()
    var bean: InvSetList? = null

    fun init() {
        if (!orderNo.get().toString().isNullOrEmpty()) {
            initList(orderNo.get().toString())
        }
        if(!setCode.get().toString().isNullOrEmpty()) {
            getSetList(setCode.get().toString())
        }
    }

    fun back() {
        activity.finish()
    }

    fun onEnterOrderNo() {
        if (CheckUtil.isFastDoubleClick()) {
            if (orderNo.get().isNullOrEmpty()) {
                ToastUtils.getInstance()
                    .showErrorToastWithSound(activity, "查询信息不能为空")
                return
            }
            activity.waitingDialogHelp.showDialog()
            initList(orderNo.get().toString())
        }
    }

    fun onEnterGoodsNo() {
        if (CheckUtil.isFastDoubleClick()) {
            if (goodsNo.get().isNullOrEmpty()) {
                ToastUtils.getInstance()
                    .showErrorToastWithSound(activity, "货品编码/69码不能为空")
                return
            }
            val result =
                allInvSetDetailList.filter { it.custItemCode == goodsNo.get()!! || it.itemCode == goodsNo.get()!! || it.whBarcode69 == goodsNo.get()!! }
            if (result != null && result.isNotEmpty()) {
                result.forEachIndexed { index, invSetDetailList ->
                    invSetDetailList.index = index + 1
                }
                activity.showData(result.toMutableList())
            } else {
                ToastUtils.getInstance()
                    .showErrorToastWithSound(activity, "待集托信息不存在")
            }

        }
    }

    private fun initList(setCode: String) {
        val param = mutableMapOf(
            "setCode" to setCode,
            "whCode" to Constants.whInfo?.whCode
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getInventoryAPI()
            .waitSetDetailList(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<InvSetDetailList>>(activity) {
                override fun success(data: MutableList<InvSetDetailList>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    goodsNo.set("")
                    if (data != null) {
                        if (data.size > 0) {
                            isNoData.set(false)
                            activity.binding.etSearchGoodsNo.requestFocus()
                        } else {
                            isNoData.set(true)
                            orderNo.set("")
                            activity.binding.etSearchOrderNo.requestFocus()
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(activity, "暂无待集托信息！")
                        }
                        data.forEachIndexed { index, invSetDetailList ->
                            invSetDetailList.index = index + 1
                            invSetDetailList.opNum = AppUtils.getBigDecimalValueStr(invSetDetailList.num)
                        }
                        allInvSetDetailList.clear()
                        allInvSetDetailList.addAll(data)
                        activity.showData(data)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    orderNo.set("")
                    goodsNo.set("")
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun getSetList(code: String) {
        val param = mutableMapOf(
            "setCode" to code,
            "whCode" to Constants.whInfo?.whCode
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getInventoryAPI()
            .loadSetDetailList(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<InvSetDetailList>>(activity) {
                override fun success(data: MutableList<InvSetDetailList>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (data != null) {
                        if (data.size > 0) {

                        } else {
                            setCode.set("")
                            hasCode.set(false)
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    private fun setConfirm() {
        val results = activity.adapter.data.filter { it.isSelected }
        val beans = mutableListOf<SetPackageConfirmReq>()
        results.forEach {
            val bean = SetPackageConfirmReq()
            bean.ids = it.ids
            if(it.opNum.isNullOrEmpty()) {
                bean.opNum = 0
            }else {
                bean.opNum = it.opNum.toInt()
            }
            beans.add(bean)
        }


        val param = mutableMapOf(
            "setCode" to setCode.get().toString(),
            "whCode" to Constants.whInfo?.whCode,
            "whName" to Constants.whInfo?.cdwhName,
            "setUserName" to Constants.userInfo?.userName,
            "setUserCode" to Constants.userInfo?.name,
            "opNumRequestList" to beans
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getInventoryAPI()
            .setConfirm(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<SetPackageConfirmResp>(activity) {
                override fun success(data: SetPackageConfirmResp?) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (data != null) {
                        ToastUtils.getInstance().showSuccessToastWithSound(activity, "集托成功！")
                        setCode.set(data.setCode)
                        hasCode.set(true)
                        val tempBean = InvSetList()
                        tempBean.setCode = data.setCode
                        tempBean.setArea = data.setArea
                        tempBean.setUserName = data.setUserName
                        tempBean.setStartTime = data.setStartTime
                        tempBean.setEndTime = data.setEndTime
                        if(data.status == "150") {
                            tempBean.status = "集托中"
                        }else if (data.status == "200") {
                            tempBean.status = "集托完成"
                        }
                        bean = tempBean
                        initList(orderNo.get().toString())
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun clearOrderNo() {
        orderNo.set("")
    }

    fun clearGoodsNo() {
        goodsNo.set("")
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        orderNo.set(result)
        onEnterOrderNo()
    }

    fun toDetail() {
        if (bean?.status == "集托中") {
            val it = Intent(activity, SetPackageDetailUnActivity::class.java)
            it.putExtra("flag", "wait") //标识是从待集托页面进去的
            it.putExtra("status", bean?.status) //标识集托的状态
            it.putExtra("setCode", bean?.setCode) //集托号
            it.putExtra("bean", bean)
            activity.startActivity(it)
        } else {
            val it = Intent(activity, SetPackageDetailActivity::class.java)
            it.putExtra("flag", "wait") //标识是从待集托页面进去的
            it.putExtra("status", bean?.status) //标识集托的状态
            it.putExtra("setCode", bean?.setCode) //集托号
            it.putExtra("bean", bean)
            activity.startActivity(it)
        }
    }

    /**
     * 确定集托
     */
    fun setPackageSure() {
        if (CheckUtil.isFastDoubleClick()) {
            val results = activity.adapter.data.filter { it.isSelected }
            if (results.isEmpty()) {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "请先选择待集托信息!")
                return
            }
            activity.waitingDialogHelp.showDialog()
            setConfirm()
        }
    }
}