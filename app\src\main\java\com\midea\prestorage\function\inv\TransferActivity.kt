package com.midea.prestorage.function.inv

import android.content.Intent
import android.os.Bundle
import android.view.WindowManager
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayoutMediator
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestoragesaas.databinding.ActivityTransferBinding
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.function.inv.fragment.TransByGoodFragment
import com.midea.prestorage.function.inv.fragment.TransByLocFragment
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.SPUtils
import com.xuexiang.xqrcode.XQRCode


class TransferActivity : BaseActivity() {

    private lateinit var binding: ActivityTransferUnionBinding
    private val titles = arrayOf("按商品移库", "按库位移库")
    private lateinit var adapter: MyAdapter


    fun getAdapter(): MyAdapter {
        return adapter
    }

    fun getBinding():ActivityTransferUnionBinding {
        return binding
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //setContentView(R.layout.activity_transfer)

        window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)

        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityTransferUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_transfer_care
                )
            )
        } else {
            ActivityTransferUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_transfer
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        binding.vm = TransferVM(this)


        if (!CheckUtil.isHaveWarehouseCode()) {
            AlertDialogUtil.showOnlyOkDialog(this, "未选择仓库", AlertDialogUtil.OnOkCallback {
                finish()
            })
            return
        }


        initViewPage()
    }


    private fun initViewPage() {
        adapter = MyAdapter(this, 2)
        binding.viewPager.adapter = adapter
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
            }
        })

        var tabLayoutMediator =
            TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
                tab.text = titles[position]
            }
        tabLayoutMediator.attach()


        DCUtils.initLot4dicts(this, null)//获取字典
        DCUtils.goodsStatue(this, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
            }
        })
    }

    class MyAdapter(activity: FragmentActivity, private val itemsCount: Int) :
        FragmentStateAdapter(activity) {

        val transGoodFragment = TransByGoodFragment.newInstance(0)
        val transLocFragment = TransByLocFragment.newInstance(1)

        override fun getItemCount(): Int {
            return itemsCount
        }

        override fun createFragment(position: Int): Fragment {
            if (position == 0) {
                return transGoodFragment
            } else {
                return transLocFragment
            }
            /*return when (position) {
                0 -> TransByGoodFragment.newInstance(0)
                else -> TransByLocFragment.newInstance(1)
            }*/
        }
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    // step 1  按商品移库  扫来源库位
    val STEP_TRANS_GOOD_FROM_LOC = 1

    // step 2  按商品移库  扫货品条码
    val STEP_TRANS_GOOD_ITEM_CODE = 2

    // step 3  按商品移库  扫目标库位
    val STEP_TRANS_GOOD_TO_LOC = 3

    // step 11 按库位移库  扫来源库位
    val STEP_TRANS_LOC_FROM_LOC = 11

    // step 12 按库位移库  扫目标库位
    val STEP_TRANS_LOC_TO_LOC = 12


    var currentStep = 0;

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == BaseActivity.QR_CODE_BACK && resultCode == RESULT_OK) {
            if (currentStep == STEP_TRANS_GOOD_FROM_LOC || currentStep == STEP_TRANS_GOOD_ITEM_CODE
                || currentStep == STEP_TRANS_GOOD_TO_LOC
            ) {
                adapter.transGoodFragment.binding.vm?.scanResult(
                    data?.extras?.getString(XQRCode.RESULT_DATA),
                    currentStep
                )

            } else if (currentStep == STEP_TRANS_LOC_FROM_LOC || currentStep == STEP_TRANS_LOC_TO_LOC) {

                adapter.transLocFragment.binding.vm?.scanResult(
                    data?.extras?.getString(XQRCode.RESULT_DATA),
                    currentStep
                )
            }

        }
    }


}