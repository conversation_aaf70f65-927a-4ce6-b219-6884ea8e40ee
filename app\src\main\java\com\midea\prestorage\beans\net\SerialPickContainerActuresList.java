package com.midea.prestorage.beans.net;

import androidx.annotation.Nullable;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.function.inv.response.PackageRelation;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class SerialPickContainerActuresList implements Serializable {

    @ShowAnnotation
    private int index;
    @ShowAnnotation
    private String itemName;
    @ShowAnnotation
    private String countInfo;
    @ShowAnnotation
    private String oqcInfo;
    @ShowAnnotation
    private String oqcWaitInfo;
    @ShowAnnotation
    private String bottomInfo;
    @ShowAnnotation
    private String relation;

    @ShowAnnotation
    @Nullable
    private String shippingContainerId;
    @ShowAnnotation
    @Nullable
    private String shipContainerCode;
    @ShowAnnotation
    @Nullable
    private String shipToCustomerName;
    @ShowAnnotation
    @Nullable
    private String custOrderNo;
    private String ownerCode;
    @ShowAnnotation
    @Nullable
    private String ownerName;
    @ShowAnnotation
    @Nullable
    private String waveNo;
    @ShowAnnotation
    @Nullable
    private String shipmentCode;
    @ShowAnnotation
    @Nullable
    private String closeContainerTime;
    @ShowAnnotation
    @Nullable
    private BigDecimal skuNum;
    @ShowAnnotation
    @Nullable
    private BigDecimal packedQty;
    @ShowAnnotation
    @Nullable
    private String packerName;
    @Nullable
    private String pickContainerId;

    private boolean isPrePack;

    private String custItemCode;
    private String whBarcode69;
    private String id;
    private String createTime;
    private String updateTime;
    private String tenantCode;
    private String createUserCode;
    private String createUserName;
    private String updateUserCode;
    private String updateUserName;
    private String remark;
    private String version;
    private String deleteFlag;
    private String pageNo;
    private String pageSize;
    private String offset;
    private String orderBy;
    private String orderByType;
    private String tenantCodes;
    private String count;
    private String startTime;
    private String endTime;
    private String pickContainerHeaderId;
    private String taskCode;
    private String csBarcode69;
    private String whMaxBarcode69;
    private String itemCode;
    private BigDecimal pickedQty;
    private BigDecimal pickedCsQty;
    private BigDecimal pickedEaQty;
    private BigDecimal packagePara;
    private BigDecimal oqcCsQty;
    private BigDecimal oqcEaQty;
    private String lotNum;
    private String fromLoc;
    private String traceId;
    private String whCode;
    private String oqcTime;
    private String csUnit;
    private String eaUnit;
    private BigDecimal waitCheckQty;
    private BigDecimal oqcQty;
    private boolean isWaitCheck;
    private String isNeedScan69;

    private List<String> ids;
    private List<String> detailIds;

    private List<PackageRelation> packageRelationList;

    private BigDecimal sumCsQty;
    private BigDecimal sumIpQty;
    private BigDecimal sumEaQty;

    private int isDecimal;

    private String whIpBarcode69;
    @ShowAnnotation
    private String cdpaFormat;

    @Nullable
    public String getPickContainerId() {
        return pickContainerId;
    }

    public void setPickContainerId(@Nullable String pickContainerId) {
        this.pickContainerId = pickContainerId;
    }

    @Nullable
    public String getShippingContainerId() {
        return shippingContainerId;
    }

    public void setShippingContainerId(@Nullable String shippingContainerId) {
        this.shippingContainerId = shippingContainerId;
    }

    public String getShipContainerCode() {
        return shipContainerCode;
    }

    public void setShipContainerCode(String shipContainerCode) {
        this.shipContainerCode = shipContainerCode;
    }

    @Nullable
    public String getShipToCustomerName() {
        return shipToCustomerName;
    }

    public void setShipToCustomerName(@Nullable String shipToCustomerName) {
        this.shipToCustomerName = shipToCustomerName;
    }

    @Nullable
    public String getCustOrderNo() {
        return custOrderNo;
    }

    public void setCustOrderNo(@Nullable String custOrderNo) {
        this.custOrderNo = custOrderNo;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    @Nullable
    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(@Nullable String ownerName) {
        this.ownerName = ownerName;
    }

    @Nullable
    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(@Nullable String waveNo) {
        this.waveNo = waveNo;
    }

    @Nullable
    public String getShipmentCode() {
        return shipmentCode;
    }

    public void setShipmentCode(@Nullable String shipmentCode) {
        this.shipmentCode = shipmentCode;
    }

    @Nullable
    public String getCloseContainerTime() {
        return closeContainerTime;
    }

    public void setCloseContainerTime(@Nullable String closeContainerTime) {
        this.closeContainerTime = closeContainerTime;
    }

    @Nullable
    public BigDecimal getSkuNum() {
        return skuNum;
    }

    public void setSkuNum(@Nullable BigDecimal skuNum) {
        this.skuNum = skuNum;
    }

    @Nullable
    public BigDecimal getPackedQty() {
        return packedQty;
    }

    public void setPackedQty(@Nullable BigDecimal packedQty) {
        this.packedQty = packedQty;
    }

    @Nullable
    public String getPackerName() {
        return packerName;
    }

    public void setPackerName(@Nullable String packerName) {
        this.packerName = packerName;
    }

    public boolean isPrePack() {
        return isPrePack;
    }

    public void setPrePack(boolean prePack) {
        isPrePack = prePack;
    }

    public String getCdpaFormat() {
        return cdpaFormat;
    }

    public void setCdpaFormat(String cdpaFormat) {
        this.cdpaFormat = cdpaFormat;
    }

    public String getWhIpBarcode69() {
        return whIpBarcode69;
    }

    public void setWhIpBarcode69(String whIpBarcode69) {
        this.whIpBarcode69 = whIpBarcode69;
    }

    public int getIsDecimal() {
        return isDecimal;
    }

    public void setIsDecimal(int isDecimal) {
        this.isDecimal = isDecimal;
    }

    public BigDecimal getSumCsQty() {
        return sumCsQty;
    }

    public void setSumCsQty(BigDecimal sumCsQty) {
        this.sumCsQty = sumCsQty;
    }

    public BigDecimal getSumIpQty() {
        return sumIpQty;
    }

    public void setSumIpQty(BigDecimal sumIpQty) {
        this.sumIpQty = sumIpQty;
    }

    public BigDecimal getSumEaQty() {
        return sumEaQty;
    }

    public void setSumEaQty(BigDecimal sumEaQty) {
        this.sumEaQty = sumEaQty;
    }

    public List<PackageRelation> getPackageRelationList() {
        return packageRelationList;
    }

    public void setPackageRelationList(List<PackageRelation> packageRelationList) {
        this.packageRelationList = packageRelationList;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getPageNo() {
        return pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getOffset() {
        return offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderByType() {
        return orderByType;
    }

    public void setOrderByType(String orderByType) {
        this.orderByType = orderByType;
    }

    public String getTenantCodes() {
        return tenantCodes;
    }

    public void setTenantCodes(String tenantCodes) {
        this.tenantCodes = tenantCodes;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getPickContainerHeaderId() {
        return pickContainerHeaderId;
    }

    public void setPickContainerHeaderId(String pickContainerHeaderId) {
        this.pickContainerHeaderId = pickContainerHeaderId;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getLotNum() {
        return lotNum;
    }

    public void setLotNum(String lotNum) {
        this.lotNum = lotNum;
    }

    public String getFromLoc() {
        return fromLoc;
    }

    public void setFromLoc(String fromLoc) {
        this.fromLoc = fromLoc;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getOqcTime() {
        return oqcTime;
    }

    public void setOqcTime(String oqcTime) {
        this.oqcTime = oqcTime;
    }

    public BigDecimal getPickedQty() {
        return pickedQty;
    }

    public void setPickedQty(BigDecimal pickedQty) {
        this.pickedQty = pickedQty;
    }

    public BigDecimal getPickedCsQty() {
        return pickedCsQty;
    }

    public void setPickedCsQty(BigDecimal pickedCsQty) {
        this.pickedCsQty = pickedCsQty;
    }

    public BigDecimal getPickedEaQty() {
        return pickedEaQty;
    }

    public void setPickedEaQty(BigDecimal pickedEaQty) {
        this.pickedEaQty = pickedEaQty;
    }

    public BigDecimal getPackagePara() {
        return packagePara;
    }

    public void setPackagePara(BigDecimal packagePara) {
        this.packagePara = packagePara;
    }

    public BigDecimal getOqcCsQty() {
        return oqcCsQty;
    }

    public void setOqcCsQty(BigDecimal oqcCsQty) {
        this.oqcCsQty = oqcCsQty;
    }

    public BigDecimal getOqcEaQty() {
        return oqcEaQty;
    }

    public void setOqcEaQty(BigDecimal oqcEaQty) {
        this.oqcEaQty = oqcEaQty;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SerialPickContainerActuresList that = (SerialPickContainerActuresList) o;
        return Objects.equals(taskCode, that.taskCode) &&
                Objects.equals(ownerCode, that.ownerCode) &&
                Objects.equals(itemCode, that.itemCode) &&
                Objects.equals(packagePara, that.packagePara) &&
                Objects.equals(fromLoc, that.fromLoc);
    }


    public String getCountInfo() {
        return countInfo;
    }

    public void setCountInfo(String countInfo) {
        this.countInfo = countInfo;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getOqcInfo() {
        return oqcInfo;
    }

    public void setOqcInfo(String oqcInfo) {
        this.oqcInfo = oqcInfo;
    }

    public String getOqcWaitInfo() {
        return oqcWaitInfo;
    }

    public void setOqcWaitInfo(String oqcWaitInfo) {
        this.oqcWaitInfo = oqcWaitInfo;
    }

    public String getBottomInfo() {
        return bottomInfo;
    }

    public void setBottomInfo(String bottomInfo) {
        this.bottomInfo = bottomInfo;
    }

    public String getWhBarcode69() {
        return whBarcode69;
    }

    public void setWhBarcode69(String whBarcode69) {
        this.whBarcode69 = whBarcode69;
    }

    public String getCsUnit() {
        return csUnit;
    }

    public void setCsUnit(String csUnit) {
        this.csUnit = csUnit;
    }

    public String getEaUnit() {
        return eaUnit;
    }

    public void setEaUnit(String eaUnit) {
        this.eaUnit = eaUnit;
    }

    public boolean isWaitCheck() {
        return isWaitCheck;
    }

    public void setWaitCheck(boolean waitCheck) {
        isWaitCheck = waitCheck;
    }

    public BigDecimal getWaitCheckQty() {
        return waitCheckQty;
    }

    public void setWaitCheckQty(BigDecimal waitCheckQty) {
        this.waitCheckQty = waitCheckQty;
    }

    public String getCsBarcode69() {
        return csBarcode69;
    }

    public void setCsBarcode69(String csBarcode69) {
        this.csBarcode69 = csBarcode69;
    }

    public String getWhMaxBarcode69() {
        return whMaxBarcode69;
    }

    public void setWhMaxBarcode69(String whMaxBarcode69) {
        this.whMaxBarcode69 = whMaxBarcode69;
    }

    public String getIsNeedScan69() {
        return isNeedScan69;
    }

    public void setIsNeedScan69(String isNeedScan69) {
        this.isNeedScan69 = isNeedScan69;
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    public List<String> getDetailIds() {
        return detailIds;
    }

    public void setDetailIds(List<String> detailIds) {
        this.detailIds = detailIds;
    }

    public BigDecimal getOqcQty() {
        return oqcQty;
    }

    public void setOqcQty(BigDecimal oqcQty) {
        this.oqcQty = oqcQty;
    }
}