package com.midea.prestorage.function.outstorage

import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.base.adapter.LoadMoreAdapter
import com.midea.prestorage.beans.net.OutStorageDeleteQuery
import com.midea.prestorage.beans.net.OutStorageDeleteQueryBean
import com.midea.prestoragesaas.databinding.ActivityOutStorageDeleteBinding
import com.midea.prestorage.function.outstorage.dialog.DeleteTipDialog
import com.midea.prestorage.utils.ToastUtils
import com.xuexiang.xqrcode.XQRCode

class OutStorageDeleteActivity : BaseActivity() {

    private lateinit var binding: ActivityOutStorageDeleteBinding
    private var adapter: CommonAdapter<OutStorageDeleteQuery.OutStorageDeleteList> =
        LoadMoreAdapter(R.layout.item_order_goods_outstorage_delete)
    private lateinit var deleteDialog: DeleteTipDialog

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_out_storage_delete)
        binding.vm = OutStorageDeleteVM(this)
        deleteDialog = DeleteTipDialog(this)
        deleteDialog.deleteBack = object : DeleteTipDialog.DeleteBack {
            override fun deleteOk() {
                ToastUtils.getInstance()
                    .showSuccessToastWithSound(this@OutStorageDeleteActivity, "删除成功!")
                binding!!.vm!!.onRefreshCommand.onRefresh()
                binding!!.vm!!.orderNo.set("")
            }

            override fun deleteFail(errorMsg: String) {
                ToastUtils.getInstance()
                    .showErrorToastWithSound(this@OutStorageDeleteActivity, errorMsg)
                binding!!.vm!!.orderNo.set("")
            }
        }

        binding!!.vm!!.init()
        initRecycle()
        initLoadMore()
    }

    /**
     * 初始化加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding!!.vm?.loadMore()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false

        adapter.setOnItemChildClickListener { adapter, view, position ->
            val bean = adapter.data[position] as OutStorageDeleteQuery.OutStorageDeleteList
            binding!!.vm!!.itemClick(bean)
        }
    }

    private fun initRecycle() {
        binding.srl.setOnRefreshListener(binding!!.vm?.onRefreshCommand)
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun showData(beans: MutableList<OutStorageDeleteQuery.OutStorageDeleteList>) {
        adapter.setNewInstance(beans)
        adapter.notifyDataSetChanged()
    }

    fun showDeleteTipDialog(data: OutStorageDeleteQueryBean) {
        deleteDialog.deleteInfo = data
        deleteDialog.queryType = binding!!.vm!!.queryType
        deleteDialog.show()
    }

    fun addNewData(beans: MutableList<OutStorageDeleteQuery.OutStorageDeleteList>) {
        adapter.addData(beans)
        adapter.notifyDataSetChanged()
    }

    fun stopLoad() {
        adapter.loadMoreModule.loadMoreComplete()
    }

    fun endLoad() {
        adapter.loadMoreModule.loadMoreEnd()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            binding!!.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }
}