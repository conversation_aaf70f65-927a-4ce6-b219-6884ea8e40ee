package com.midea.prestorage.function.main.dialog

import android.app.AlertDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter.OnCheckListener
import com.midea.prestoragesaas.databinding.DialogWhChooseBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.midea.prestorage.beans.setting.ImplWarehouse
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.SPUtils


/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/6/26$
 */
class WhChooseDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {

    private var binding: DialogWhChooseUnionBinding
    val adapter = WhChooseAdapter<ImplWarehouse>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.dialog_list_item_tenant_care else R.layout.dialog_list_item_click)

    init {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window?.setGravity(Gravity.CENTER)
            window?.attributes?.run {
                gravity = Gravity.CENTER
            }
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_wh_choose_care, null)
            setView(contentView)
            DialogWhChooseUnionBinding.V2(DataBindingUtil.bind(contentView)!!)
        } else {
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_wh_choose, null)
            setView(contentView)
            DialogWhChooseUnionBinding.V1(DataBindingUtil.bind(contentView)!!)
        }
        binding.vm = WhChooseDialogVM(this)

        setCanceledOnTouchOutside(false)
        // 加上这个 确保按回退键也不关闭选择框
        setCancelable(false)

        initRecycleView()
    }

    private fun initRecycleView() {
        binding.recycle.layoutManager = LinearLayoutManager(mContext)
        binding.recycle.adapter = adapter
    }

    fun setNewData(beans: MutableList<ImplWarehouse>) {
        binding.vm!!.allData.clear()
        binding.vm!!.allData.addAll(beans)
    }

    fun notifyDataChange (beans: MutableList<ImplWarehouse>) {
        adapter.setNewInstance(beans)
        adapter.notifyDataSetChanged()
    }

    fun setTitle(title: String) {
        if (!TextUtils.isEmpty(title)) {
            binding.vm!!.title.set(title)
        }
    }

    fun setOnCheckListener(onCheckListener: OnCheckListener<ImplWarehouse>) {
        adapter.setOnCheckListener(onCheckListener)
    }

    override fun dismiss() {
        binding.vm!!.cleanFilter()
        super.dismiss()
    }

    fun isAbleCancel(cancelAble: Boolean) {
        binding.vm!!.isCancelAble = cancelAble
    }

    interface ServerRefresh {
        fun refreshByServer()
    }
}
