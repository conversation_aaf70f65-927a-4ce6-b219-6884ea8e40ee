package com.midea.prestorage.function.inv.response;

import androidx.annotation.Nullable;

import com.midea.prestorage.beans.base.BaseItemForPopup;

import java.math.BigDecimal;
import java.util.List;

// 盘点明细表
public class InvStocktakeDetail extends BaseItemForPopup {


    private String id;

    /**
     * 仓库
     */
    private String whCode;
	
    /**
     * 盘点单号编码
     */
    private String stocktakeCode;
	
    /**
     * 货主
     */
    private String ownerCode;
    private String ownerName;

    /**
     * 盘点明细编码
     */
    private String detailCode;
	
    /**
     * 库区
     */
    private String zoneCode;
	
    /**
     * 库位
     */
    private String locCode;
	
    /**
     * 安得货品编码
     */
    private String itemCode;
	
    /**
     * 货品描述
     */
    private String itemName;
	
    /**
     * 客户货品编码
     */
    private String custItemCode;
	
    /**
     * 系统数量
     */
    private BigDecimal sysQty;
	
    /**
     * 第1次盘点数量
     */
    private BigDecimal firstQty;
	
    /**
     * 第2次盘点数量
     */
    private Double secondQty;
	
    /**
     * 差异数量
     */
    private Double diffQty;
	
    /**
     * 是否盲盘
     */
    private Integer isDarkCheck;
	
    /**
     * 盘点顺序号
     */
    private String stocktakeSeqNum;
	
    /**
     * 库存ID
     */
    private String inventoryId;
	
    /**
     * 批属性号
     */
    private String lotNum;
	
    /**
     * 批属性1
     */
    private String lotAtt01;
	
    /**
     * 批属性2
     */
    private String lotAtt02;
	
    /**
     * 批属性3
     */
    private String lotAtt03;
	
    /**
     * 库存状态
     */
    private String lotAtt04;
    private String lotAtt04Str;

    /**
     * 批次号
     */
    private String lotAtt05;
	
    /**
     * 指派用户
     */
    private String assignedBy;
	
    /**
     * 指派时间
     */
    private String assignedTime;
	
    /**
     * 第1次操作执行人
     */
    private String firstConfirmedBy;
	
    /**
     * 第1次操作执行时间
     */
    private String firstConfirmedTime;
	
    /**
     * 第2次操作执行人
     */
    private String secondConfirmedBy;
	
    /**
     * 第2次操作执行时间
     */
    private String secondConfirmedTime;
	
    /**
     * 状态
     */
    private String status;
	
    /**
     * 新增标识 1
     */
    private String isNew;

    private Long sortFlag;

    private int isDecimal;

    @Nullable
    private List<PackageRelation> packageRelationList;

    @Nullable
    private String whCsBarcode69;
    @Nullable
    private String whBarcode69;
    @Nullable
    private String whIpBarcode69;

    @Nullable
    public String getWhCsBarcode69() {
        return whCsBarcode69;
    }

    public void setWhCsBarcode69(@Nullable String whCsBarcode69) {
        this.whCsBarcode69 = whCsBarcode69;
    }

    @Nullable
    public String getWhBarcode69() {
        return whBarcode69;
    }

    public void setWhBarcode69(@Nullable String whBarcode69) {
        this.whBarcode69 = whBarcode69;
    }

    @Nullable
    public String getWhIpBarcode69() {
        return whIpBarcode69;
    }

    public void setWhIpBarcode69(@Nullable String whIpBarcode69) {
        this.whIpBarcode69 = whIpBarcode69;
    }

    @Nullable
    public List<PackageRelation> getPackageRelationList() {
        return packageRelationList;
    }

    public void setPackageRelationList(@Nullable List<PackageRelation> packageRelationList) {
        this.packageRelationList = packageRelationList;
    }

    private String cdpaFormat;

    public String getCdpaFormat() {
        return cdpaFormat;
    }

    public void setCdpaFormat(String cdpaFormat) {
        this.cdpaFormat = cdpaFormat;
    }

    public int getIsDecimal() {
        return isDecimal;
    }

    public void setIsDecimal(int isDecimal) {
        this.isDecimal = isDecimal;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getSortFlag() {
        return sortFlag;
    }

    public void setSortFlag(Long sortFlag) {
        this.sortFlag = sortFlag;
    }

    private boolean isFousing=false; // 后端没这个属性，这个是用来前端高亮


    public boolean isFousing() {
        return isFousing;
    }

    public void setFousing(boolean fousing) {
        isFousing = fousing;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getStocktakeCode() {
        return stocktakeCode;
    }

    public void setStocktakeCode(String stocktakeCode) {
        this.stocktakeCode = stocktakeCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getDetailCode() {
        return detailCode;
    }

    public void setDetailCode(String detailCode) {
        this.detailCode = detailCode;
    }

    public String getZoneCode() {
        return zoneCode;
    }

    public void setZoneCode(String zoneCode) {
        this.zoneCode = zoneCode;
    }

    public String getLocCode() {
        return locCode;
    }

    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public BigDecimal getSysQty() {
        return sysQty;
    }

    public void setSysQty(BigDecimal sysQty) {
        this.sysQty = sysQty;
    }

    public BigDecimal getFirstQty() {
        return firstQty;
    }

    public void setFirstQty(BigDecimal firstQty) {
        this.firstQty = firstQty;
    }

    public Double getSecondQty() {
        return secondQty;
    }

    public void setSecondQty(Double secondQty) {
        this.secondQty = secondQty;
    }

    public Double getDiffQty() {
        return diffQty;
    }

    public void setDiffQty(Double diffQty) {
        this.diffQty = diffQty;
    }

    public Integer getIsDarkCheck() {
        return isDarkCheck;
    }

    public void setIsDarkCheck(Integer isDarkCheck) {
        this.isDarkCheck = isDarkCheck;
    }

    public String getStocktakeSeqNum() {
        return stocktakeSeqNum;
    }

    public void setStocktakeSeqNum(String stocktakeSeqNum) {
        this.stocktakeSeqNum = stocktakeSeqNum;
    }

    public String getInventoryId() {
        return inventoryId;
    }

    public void setInventoryId(String inventoryId) {
        this.inventoryId = inventoryId;
    }

    public String getLotNum() {
        return lotNum;
    }

    public void setLotNum(String lotNum) {
        this.lotNum = lotNum;
    }

    public String getLotAtt01() {
        return lotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        this.lotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return lotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        this.lotAtt02 = lotAtt02;
    }

    public String getLotAtt03() {
        return lotAtt03;
    }

    public void setLotAtt03(String lotAtt03) {
        this.lotAtt03 = lotAtt03;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getLotAtt05() {
        return lotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        this.lotAtt05 = lotAtt05;
    }

    public String getAssignedBy() {
        return assignedBy;
    }

    public void setAssignedBy(String assignedBy) {
        this.assignedBy = assignedBy;
    }

    public String getAssignedTime() {
        return assignedTime;
    }

    public void setAssignedTime(String assignedTime) {
        this.assignedTime = assignedTime;
    }

    public String getFirstConfirmedBy() {
        return firstConfirmedBy;
    }

    public void setFirstConfirmedBy(String firstConfirmedBy) {
        this.firstConfirmedBy = firstConfirmedBy;
    }

    public String getFirstConfirmedTime() {
        return firstConfirmedTime;
    }

    public void setFirstConfirmedTime(String firstConfirmedTime) {
        this.firstConfirmedTime = firstConfirmedTime;
    }

    public String getSecondConfirmedBy() {
        return secondConfirmedBy;
    }

    public void setSecondConfirmedBy(String secondConfirmedBy) {
        this.secondConfirmedBy = secondConfirmedBy;
    }

    public String getSecondConfirmedTime() {
        return secondConfirmedTime;
    }

    public void setSecondConfirmedTime(String secondConfirmedTime) {
        this.secondConfirmedTime = secondConfirmedTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIsNew() {
        return isNew;
    }

    public void setIsNew(String isNew) {
        this.isNew = isNew;
    }

    public String getLotAtt04Str() {
        return lotAtt04Str;
    }

    public void setLotAtt04Str(String lotAtt04Str) {
        this.lotAtt04Str = lotAtt04Str;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }
}
