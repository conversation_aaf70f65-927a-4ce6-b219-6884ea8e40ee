package com.midea.prestorage.beans.setting;

import org.xutils.db.annotation.Column;
import org.xutils.db.annotation.Table;

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/9/23$
 */
@Table(name = "HandingInfoDb")
public class HandingInfoDb {

    @Column(name = "id", isId = true, autoGen = true, property = "NOT NULL")
    private int id;

    @Column(name = "userId")
    private Integer userId;

    @Column(name = "handlingCode")
    private String handlingCode;

    @Column(name = "handlingName")
    private String handlingName;

    @Column(name = "mode")
    private int mode; //1是入库，2是出库，3前置仓装卸组, 4是城配快销发运功能, 5是大物流模块

    @Column(name = "supplierCode")
    private String supplierCode;

    @Column(name = "supplierName")
    private String supplierName;

    public HandingInfoDb() {
    }

    public HandingInfoDb(Integer userId, String handlingName, String handlingCode, String supplierName, String supplierCode) {
        this.userId = userId;
        this.handlingName = handlingName;
        this.handlingCode = handlingCode;
        this.supplierName = supplierName;
        this.supplierCode = supplierCode;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getHandlingCode() {
        return handlingCode;
    }

    public void setHandlingCode(String handlingCode) {
        this.handlingCode = handlingCode;
    }

    public String getHandlingName() {
        return handlingName;
    }

    public void setHandlingName(String handlingName) {
        this.handlingName = handlingName;
    }

    public int getMode() {
        return mode;
    }

    public void setMode(int mode) {
        this.mode = mode;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }
}
