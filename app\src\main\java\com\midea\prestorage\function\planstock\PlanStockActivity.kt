package com.midea.prestorage.function.planstock

import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.FuShipmentStatue
import com.midea.prestorage.beans.net.PlanStockList
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.planstock.dialog.AddStockLocDialog
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.CareLoadMoreView
import com.midea.prestoragesaas.databinding.ActivityPlanStockBinding
import com.xuexiang.xqrcode.XQRCode

class PlanStockActivity : BaseActivity() {

    lateinit var binding: ActivityPlanStockUnionBinding
    private var vm = PlanStockVM(this)
    val adapter = OutPoolStorageAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityPlanStockUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_plan_stock_care
                )
            )
        } else {
            ActivityPlanStockUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_plan_stock
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        binding.vm = vm

        initData()
        initRecycle()
        initLoadMore()
        initSpinner()

        AppUtils.requestFocus(binding.etSearchOrderNo)
        binding?.etSearchOrderNo?.filters = arrayOf(vm.filter)
    }

    override fun onResume() {
        super.onResume()
        binding.etSearchOrderNo.post {
            binding.etSearchOrderNo.requestFocus()
            binding.etSearchOrderNo.selectAll()
        }
        binding.vm!!.onRefreshCommand.onRefresh()
    }

    fun initData() {
        vm.stocktakeCode = intent.getStringExtra("stocktakeCode")

        vm.title.value = vm.stocktakeCode
        vm.locNum.value = "总库位数：${intent.getStringExtra("locNum")}"
        vm.usedLocNum.value = "已盘库位：${intent.getStringExtra("usedLocNum")}"
        vm.stocktakeSchedule.value = "进度：${intent.getStringExtra("stocktakeSchedule")}"
    }

    private fun initRecycle() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter

        adapter.setOnItemClickListener { adapter, _, position ->
            val bean = adapter.data[position] as PlanStockList
            vm.onItemClick(bean)
        }
    }

    fun initSpinner() {
        var list = mutableListOf(
            FuShipmentStatue("All", "全部"),
            FuShipmentStatue("100", "新建"),
            FuShipmentStatue("200", "执行中")
        )
        val beans = mutableListOf<String>()
        list.forEach {
            beans.add(it.name)
        }
        binding.spinnerStatus.setItems(beans)
        binding.spinnerStatus.selectedIndex = 0

        binding.spinnerStatus.setOnItemSelectedListener { _, position, _, _ ->
            binding.vm!!.onChangeStatue(list[position])
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.startSearch()
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            adapter.loadMoreModule.loadMoreView = CareLoadMoreView()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }

    fun showData(data: MutableList<PlanStockList>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
    }

    fun showAddStockLocDialog() {
        //新增盘点库位dialog
        val addStockLocDialog = AddStockLocDialog()
        addStockLocDialog.setDeleteBack(object : AddStockLocDialog.AddBarcodeBack {
            override fun deleteBarcodeBack(locCode: String) {
                addStockLocDialog.dismiss()
                vm.createEmptyLoc(locCode)
            }
        })
        addStockLocDialog.showNow(supportFragmentManager, "addStockLocDialog")
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            vm.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    class OutPoolStorageAdapter :
        CommonAdapter<PlanStockList>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_plan_stock_care else R.layout.item_plan_stock),
        LoadMoreModule {

        override fun convert(holder: BaseViewHolder?, item: PlanStockList?) {
            super.convert(holder, item)

            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                if (item?.status == "新建") {
                    holder?.setTextColor(
                        R.id.tv_status,
                        ContextCompat.getColor(context, R.color.btn_blue_care)
                    )
                    holder?.setBackgroundResource(
                        R.id.tv_status,
                        R.drawable.bg_bt_litter_blue_care_02
                    )
                } else {
                    holder?.setTextColor(
                        R.id.tv_status,
                        ContextCompat.getColor(context, R.color.colorOrange_care)
                    )
                    holder?.setBackgroundResource(
                        R.id.tv_status,
                        R.drawable.bg_bt_litter_orange_care
                    )
                }
            }
        }

    }
}