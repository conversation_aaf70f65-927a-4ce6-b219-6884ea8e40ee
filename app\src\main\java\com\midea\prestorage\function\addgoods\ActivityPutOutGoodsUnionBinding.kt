package com.midea.prestorage.function.addgoods

import android.widget.*
import androidx.viewpager.widget.ViewPager
import com.midea.prestoragesaas.databinding.ActivityAddGoodsBinding
import com.midea.prestoragesaas.databinding.ActivityAddGoodsCareBinding
import com.midea.prestoragesaas.databinding.ActivityPutOutGoodsBinding
import com.midea.prestoragesaas.databinding.ActivityPutOutGoodsCareBinding

sealed class ActivityPutOutGoodsUnionBinding{
    abstract var vm: PutOutVM?
    abstract val llTitleBar: RelativeLayout
    abstract val edGoods: EditText
    abstract val llDataInfo: LinearLayout
    abstract val ivNoOrder: ImageView
    abstract val edQty: EditText
    abstract val tvNotification: TextView

    class V2(val binding: ActivityPutOutGoodsCareBinding) : ActivityPutOutGoodsUnionBinding() {
        override var vm: PutOutVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val edGoods = binding.edGoods
        override val llDataInfo = binding.llDataInfo
        override val ivNoOrder = binding.ivNoOrder
        override val edQty = binding.edQty
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityPutOutGoodsBinding) : ActivityPutOutGoodsUnionBinding() {
        override var vm: PutOutVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val edGoods = binding.edGoods
        override val llDataInfo = binding.llDataInfo
        override val ivNoOrder = binding.ivNoOrder
        override val edQty = binding.edQty
        override val tvNotification = binding.tvNotification
    }
}
