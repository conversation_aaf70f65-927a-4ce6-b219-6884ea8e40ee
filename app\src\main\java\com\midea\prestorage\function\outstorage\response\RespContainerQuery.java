package com.midea.prestorage.function.outstorage.response;

import java.io.Serializable;

public class RespContainerQuery implements Serializable {


    private String waveNo;
    private String shipmentCode;
    private String custOrderNo;
    private String ownerName;
    private String upperReceiverName;
    private String exportContainerNo;
    private String exportContainerLockNo;

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getShipmentCode() {
        return shipmentCode;
    }

    public void setShipmentCode(String shipmentCode) {
        this.shipmentCode = shipmentCode;
    }

    public String getCustOrderNo() {
        return custOrderNo;
    }

    public void setCustOrderNo(String custOrderNo) {
        this.custOrderNo = custOrderNo;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getUpperReceiverName() {
        return upperReceiverName;
    }

    public void setUpperReceiverName(String upperReceiverName) {
        this.upperReceiverName = upperReceiverName;
    }

    public String getExportContainerNo() {
        return exportContainerNo;
    }

    public void setExportContainerNo(String exportContainerNo) {
        this.exportContainerNo = exportContainerNo;
    }

    public String getExportContainerLockNo() {
        return exportContainerLockNo;
    }

    public void setExportContainerLockNo(String exportContainerLockNo) {
        this.exportContainerLockNo = exportContainerLockNo;
    }
}
