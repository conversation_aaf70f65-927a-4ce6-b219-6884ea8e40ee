package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.beans.base.BaseItemForPopup;

public class InvSetList extends BaseItemForPopup implements Comparable<InvSetList>{

    @ShowAnnotation
    private int index;
    @ShowAnnotation
    private String setCode;
    @ShowAnnotation
    private String status;
    @ShowAnnotation
    private String setArea;
    private String wholeNum;
    private String partNum;
    @ShowAnnotation
    private String setStartTime;
    @ShowAnnotation
    private String setEndTime;
    private String setUserCode;
    @ShowAnnotation
    private String setUserName;

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getSetCode() {
        return setCode;
    }

    public void setSetCode(String setCode) {
        this.setCode = setCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSetArea() {
        return setArea;
    }

    public void setSetArea(String setArea) {
        this.setArea = setArea;
    }

    public String getWholeNum() {
        return wholeNum;
    }

    public void setWholeNum(String wholeNum) {
        this.wholeNum = wholeNum;
    }

    public String getPartNum() {
        return partNum;
    }

    public void setPartNum(String partNum) {
        this.partNum = partNum;
    }

    public String getSetStartTime() {
        return setStartTime;
    }

    public void setSetStartTime(String setStartTime) {
        this.setStartTime = setStartTime;
    }

    public String getSetEndTime() {
        return setEndTime;
    }

    public void setSetEndTime(String setEndTime) {
        this.setEndTime = setEndTime;
    }

    public String getSetUserCode() {
        return setUserCode;
    }

    public void setSetUserCode(String setUserCode) {
        this.setUserCode = setUserCode;
    }

    public String getSetUserName() {
        return setUserName;
    }

    public void setSetUserName(String setUserName) {
        this.setUserName = setUserName;
    }

    @Override
    public int compareTo(InvSetList o) {
        if(Integer.parseInt(this.status)>Integer.parseInt(o.status)){
            return 1;
        }else if(Integer.parseInt(this.status)<Integer.parseInt(o.status)){
            return -1;
        }
        return 0;
    }
}
