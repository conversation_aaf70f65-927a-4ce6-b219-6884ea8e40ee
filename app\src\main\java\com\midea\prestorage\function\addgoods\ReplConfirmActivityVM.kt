package com.midea.prestorage.function.addgoods

import CheckUtil
import android.app.Application
import android.widget.Toast
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.DetailsItems
import com.midea.prestorage.beans.net.ReplenishmentBean
import com.midea.prestorage.beans.net.ReplenishmentBean.ReplenishmentListBean
import com.midea.prestorage.beans.net.ReqReplenishment
import com.midea.prestorage.event.LiveEvent
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.*
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal

class ReplConfirmActivityVM(application: Application) : BaseViewModel(application) {
    val title = MutableLiveData("快速补货")
    var content = MutableLiveData("")
    var unit = ObservableField("")
    var toLoc = ObservableField("")
    var whBarcode69 = ObservableField("")
    var count = ObservableField("")
    var itemName = ObservableField("")
    var cdpaFormat = ObservableField("")
    var sourceInfo = ObservableField("")
    val showTipDialog = LiveEvent<Unit>()
    var replenishmentListBean: ReplenishmentListBean? = null
    var barCode = ObservableField("")
    var orderByValue = ObservableField("")
    var orderByAttribute = ObservableField("")
    var availableQty = ObservableField("")
    var toZoneName = ObservableField("")
    val toActivityLiveEvent = LiveEvent<Unit>()
    val isShowOT = ObservableField(false)
    val isShowPL = ObservableField(false)
    val isShowCS = ObservableField(false)
    val isShowIP = ObservableField(false)
    val isShowEA = ObservableField(false)
    var otEditText = ObservableField("")
    var plEditText = ObservableField("")
    var csEditText = ObservableField("")
    var ipEditText = ObservableField("")
    var eaEditText = ObservableField("")
    var otUnitDesc = ObservableField("")
    var plUnitDesc = ObservableField("")
    var csUnitDesc = ObservableField("")
    var ipUnitDesc = ObservableField("")
    var eaUnitDesc = ObservableField("")
    var otQuantity = BigDecimal.ZERO
    var plQuantity = BigDecimal.ZERO
    var csQuantity = BigDecimal.ZERO
    var ipQuantity = BigDecimal.ZERO
    var eaQuantity = BigDecimal.ZERO
    val otRuquestLiveEvent = LiveEvent<Unit>()
    val plRuquestLiveEvent = LiveEvent<Unit>()
    val csRuquestLiveEvent = LiveEvent<Unit>()
    val ipRuquestLiveEvent = LiveEvent<Unit>()
    val eaRuquestLiveEvent = LiveEvent<Unit>()
    val selectAllLiveEvent = LiveEvent<Unit>()
    val isPoint = MutableLiveData(9999)

    override fun init() {
    }

    val locEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                when {
                    isShowCS.get() == true -> {
                        csRuquestLiveEvent.value = Unit
                    }
                    isShowEA.get() == true -> {
                        eaRuquestLiveEvent.value = Unit
                    }
                    isShowOT.get() == true -> {
                        otRuquestLiveEvent.value = Unit
                    }
                    isShowIP.get() == true -> {
                        ipRuquestLiveEvent.value = Unit
                    }
                }
            }
        }
    }

    fun onEnterOT() {
        if (CheckUtil.isFastDoubleClick()) {
            when {
                isShowPL.get() == true -> plRuquestLiveEvent.value = Unit
                isShowCS.get() == true -> csRuquestLiveEvent.value = Unit
                isShowIP.get() == true -> ipRuquestLiveEvent.value = Unit
                isShowEA.get() == true -> eaRuquestLiveEvent.value = Unit
            }
        }
    }

    fun onEnterPL() {
        if (CheckUtil.isFastDoubleClick()) {
            when {
                isShowCS.get() == true -> csRuquestLiveEvent.value = Unit
                isShowIP.get() == true -> ipRuquestLiveEvent.value = Unit
                isShowEA.get() == true -> eaRuquestLiveEvent.value = Unit
            }
        }
    }

    fun onEnterCS() {
        if (CheckUtil.isFastDoubleClick()) {
            when {
                isShowIP.get() == true -> ipRuquestLiveEvent.value = Unit
                isShowEA.get() == true -> eaRuquestLiveEvent.value = Unit
                else -> Unit
            }
        }
    }

    fun onEnterIP() {
        if (CheckUtil.isFastDoubleClick()) {
            when {
                isShowEA.get() == true -> eaRuquestLiveEvent.value = Unit
                else -> Unit
            }
        }
    }

    fun onEnterEA() {
        if (CheckUtil.isFastDoubleClick()) {
            confirmClick(false)
        }
    }

    fun initData(data: ReplenishmentListBean) {
        itemName.set(data.itemName)
        sourceInfo.set(data.fromLoc)
        orderByValue.set(data.orderByValue)
        orderByAttribute.set(data.orderByAttribute)
        toZoneName.set(data.toZoneName)
        isPoint.value = data.isDecimal
        when (data.unitValue) {
            "EA" -> barCode.set(data.barCode ?: "")
            "CS" -> {
                if (!data.whCsBarcode69.isNullOrEmpty()) {
                    barCode.set(data.whCsBarcode69 ?: "")
                } else {
                    barCode.set(data.barCode ?: "")
                }
            }
            "PL" -> {
                if (!data.whMaxBarcode69.isNullOrEmpty()) {
                    barCode.set(data.whMaxBarcode69 ?: "")
                } else {
                    barCode.set(data.barCode ?: "")
                }
            }
            else -> barCode.set(data.barCode ?: "")
        }

        //可以库存
        if (data.packageRelationList.isNullOrEmpty() || AppUtils.isZero(data.availableQty)) {
            availableQty.set(AppUtils.getBigDecimalValueStr(data.availableQty))
        } else {
            availableQty.set(
                AppUtils.getBigDecimalValueStr(data.availableQty) + "(" +
                        MathUtils.calculateUnits(
                            AppUtils.getBigDecimalValue(data.availableQty),
                            data.packageRelationList,
                            false
                        ) + ")"
            )
        }

        if (!data.packageRelationList.isNullOrEmpty()) {
            //商品规格
            //cdpaFormat.set(MathUtils.getRule(data.packageRelationList))
            cdpaFormat.set(data.cdpaFormat ?: "")
            if (!AppUtils.isZero(data.availableQty)) {
                data.packageRelationList = MathUtils.handlePackageRelationList(
                    AppUtils.getBigDecimalValue(data.totalQty),
                    data.packageRelationList
                )
            }
        }

        data.packageRelationList?.let { list ->
            for (item in list) {
                when (item.unit) {
                    "OT" -> {
                        isShowOT.set(true)
                        otUnitDesc.set(item.unitDesc ?: "")
                        if (!AppUtils.isZero(item.num)) {
                            otEditText.set(AppUtils.getBigDecimalValueStr(item.num))
                        }
                        otQuantity = item.quantity
                    }
                    "PL" -> {
                        isShowPL.set(true)
                        plUnitDesc.set(item.unitDesc ?: "")
                        if (!AppUtils.isZero(item.num)) {
                            plEditText.set(AppUtils.getBigDecimalValueStr(item.num))
                        }
                        plQuantity = item.quantity
                    }
                    "CS" -> {
                        isShowCS.set(true)
                        csUnitDesc.set(item.unitDesc ?: "")
                        if (!AppUtils.isZero(item.num)) {
                            csEditText.set(AppUtils.getBigDecimalValueStr(item.num))
                        }
                        csQuantity = item.quantity
                    }
                    "IP" -> {
                        isShowIP.set(true)
                        ipUnitDesc.set(item.unitDesc ?: "")
                        if (!AppUtils.isZero(item.num)) {
                            ipEditText.set(AppUtils.getBigDecimalValueStr(item.num))
                        }
                        ipQuantity = item.quantity
                    }
                    "EA" -> {
                        isShowEA.set(true)
                        eaUnitDesc.set(item.unitDesc ?: "")
                        if (!AppUtils.isZero(item.num)) {
                            eaEditText.set(AppUtils.getBigDecimalValueStr(item.num))
                        }
                        eaQuantity = item.quantity
                    }
                }
            }
        }

    }

    fun toInventorySearch() {
        if (CheckUtil.isFastDoubleClick()) {
            toActivityLiveEvent.value = Unit
        }
    }

    fun confirmClick(isClick: Boolean = true) {
        if (CheckUtil.isFastDoubleClick(isClick)) {

            if (toLoc.get().toString().isNullOrEmpty()) {
                showNotification("目标库位不能为空", false)
                return
            }

            if (otEditText.get().toString().isNullOrEmpty() && plEditText.get().toString()
                    .isNullOrEmpty() && csEditText.get().toString()
                    .isNullOrEmpty() && ipEditText.get().toString()
                    .isNullOrEmpty() && eaEditText.get().toString().isNullOrEmpty()
            ) {
                showNotification("补货数量不能为空", false)
                return
            }

            var allQty = getAllQty()
            if (allQty.compareTo(replenishmentListBean?.availableQty) == 1) {
                showNotification("补货数量不能大于可用数量", false)
                return
            }

            rfConfirm(allQty)
        }
    }

    fun showErrorNotification(msg: String, isSuccess: Boolean) {
        showNotification(msg, isSuccess)
    }

    //获取推荐库位
    fun getRecommendLoc(bean: ReplenishmentBean.ReplenishmentListBean) {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val map = mutableMapOf(
                "whCode" to Constants.whInfo?.whCode,
                "ownerCode" to bean.ownerCode,
                "scenario" to "replenishment",
                "orderType" to "",
                "qty" to bean.totalQty,
                "packageUnit" to bean.originalUnit,
                "lotAtt01" to bean.lotAtt01,
                "lotAtt02" to bean.lotAtt02,
                "lotAtt04" to bean.lotAtt04,
                "lotAtt05" to bean.lotAtt05,
                "lotAtt06" to bean.lotAtt06,
                "containerCode" to "",
                "itemCode" to bean.itemCode
            )
            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(map)
            )

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getAddGoodsService()
                    .getRecommendLocNew(requestBody)
            }

            if (result.code == 0L) {
                result.data?.locCode?.let {
                    if (!it.isNullOrEmpty()) {
                        toLoc.set(it)
                        selectAllLiveEvent.value = Unit
                    }
                }
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    private fun rfConfirm(qty: BigDecimal) {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val detail = DetailsItems(replenishmentListBean?.id, qty, toLoc.get().toString().trim())
            val list = mutableListOf<DetailsItems>()
            list.add(detail)

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getAddGoodsService()
                    .replenishmentConfirm(ReqReplenishment(details = list))
            }

            if (result.code == 0L) {
                ToastUtilsCare.toastBig(getApplication(), "补货成功", Toast.LENGTH_SHORT)
                back()
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    private fun getAllQty(): BigDecimal {
        var allQty = BigDecimal.ZERO

        if (!otEditText.get().isNullOrEmpty() && !AppUtils.isZero(otQuantity)) {
            allQty = allQty.add(
                AppUtils.getBigDecimalValue(otEditText.get()).multiply(otQuantity)
            )
        }

        if (!plEditText.get().isNullOrEmpty() && !AppUtils.isZero(plQuantity)) {
            allQty = allQty.add(
                AppUtils.getBigDecimalValue(plEditText.get()).multiply(plQuantity)
            )
        }

        if (!csEditText.get().isNullOrEmpty() && !AppUtils.isZero(csQuantity)) {
            allQty = allQty.add(
                AppUtils.getBigDecimalValue(csEditText.get()).multiply(csQuantity)
            )
        }

        if (!ipEditText.get().isNullOrEmpty() && !AppUtils.isZero(ipQuantity)) {
            allQty = allQty.add(
                AppUtils.getBigDecimalValue(ipEditText.get()).multiply(ipQuantity)
            )
        }

        if (!eaEditText.get().isNullOrEmpty() && !AppUtils.isZero(eaQuantity)) {
            allQty = allQty.add(
                AppUtils.getBigDecimalValue(eaEditText.get()).multiply(eaQuantity)
            )
        }

        return allQty
    }
}