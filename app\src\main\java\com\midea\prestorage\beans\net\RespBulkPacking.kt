package com.midea.prestorage.beans.net

import java.math.BigDecimal

data class RespBulkPacking(
    var index: Int,
    val pickContainerId: String?,
    val shipToCustomerName: String?,
    val custOrderNo: String?,
    val seqNum: BigDecimal?,
    val packNumber: BigDecimal?,
    val closeContainerTime: String?,
    val details: List<BulkPackingDetail>?,
    val shipContainerIdList: List<String>?,
    val custOrderNoNum: String?
)

data class BulkPackingDetail(
    val pickDetailId: String?,
    val packageId: String?,
    val pickContainerId: String?,
    val shipmentCode: String?,
    val custOrderNo: String?,
    val itemCode: String?,
    val custItemCode: String?,
    val itemName: String?,
    val pickedQty: BigDecimal?,
    val oqcQty: BigDecimal?,
    var packQty: BigDecimal?,
    val shipToCustomerName: String?,
    val unit: String?,
    val waitPackQty: BigDecimal?,
    val shipContainerCode: String?,
    val seqNum: BigDecimal?,
    val closeContainerTime: String?,
    val packerName: String?,
    val packedQty: BigDecimal?,
    val pickDetailIdList: List<String>?,
    val packageIdList: List<String>?
)
