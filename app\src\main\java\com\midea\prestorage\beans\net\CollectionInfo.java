package com.midea.prestorage.beans.net;

import com.midea.prestorage.utils.AppUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class CollectionInfo implements Serializable {
    //提示语
    private String tips;

    private String waveNo;
    private String status;
    //出库单号
    private String shipmentCode;
    //客户订单号
    private String custOrderNo;
    //已集货箱数
    private int num;
    //总数量
    private BigDecimal totalQty;
    //已集货
    private BigDecimal qty;
    //未集货
    private BigDecimal notQty;

    //销售店铺名称
    private String storeName;
    //联系人
    private String shipToAttentionTo;

    private List<DispatchInfoDot> despatchInfoDtos;

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getShipmentCode() {
        return shipmentCode;
    }

    public void setShipmentCode(String shipmentCode) {
        this.shipmentCode = shipmentCode;
    }

    public String getCustOrderNo() {
        return custOrderNo;
    }

    public void setCustOrderNo(String custOrderNo) {
        this.custOrderNo = custOrderNo;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getTotalQty() {
        return AppUtils.getBigDecimalValue(totalQty).toPlainString();
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public String getQty() {
        return AppUtils.getBigDecimalValue(qty).toPlainString();
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getNotQty() {
        return AppUtils.getBigDecimalValue(notQty).toPlainString();
    }

    public void setNotQty(BigDecimal notQty) {
        this.notQty = notQty;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getShipToAttentionTo() {
        return shipToAttentionTo;
    }

    public void setShipToAttentionTo(String shipToAttentionTo) {
        this.shipToAttentionTo = shipToAttentionTo;
    }

    public List<DispatchInfoDot> getDespatchInfoDtos() {
        return despatchInfoDtos;
    }

    public void setDespatchInfoDtos(List<DispatchInfoDot> despatchInfoDtos) {
        this.despatchInfoDtos = despatchInfoDtos;
    }
}
