package com.midea.prestorage.function.containerpick.provider

import com.chad.library.adapter.base.entity.node.BaseNode
import com.chad.library.adapter.base.provider.BaseNodeProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestorage.function.containerpick.adapter.BulkAlreadyPackedAdapter
import com.midea.prestorage.function.containerpick.fragment.BulkAlreadyPackedVM
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R

class BulkPickAlreadyDetailProvider(private val vm: BulkAlreadyPackedVM) :
    BaseNodeProvider() {
    override val itemViewType: Int
        get() = BulkAlreadyPackedAdapter.TYPE_DETAIL

    override val layoutId: Int
        get() = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_already_bulk_pick_detail_care else R.layout.item_already_bulk_pick_detail

    override fun convert(helper: BaseViewHolder, item: BaseNode) {
        val wrap = item as BulkPickToBeDetailWrap
        val task = wrap.task
        helper.setText(R.id.tv_item_name, task.itemName)
        helper.setText(R.id.tv_num, AppUtils.getBigDecimalValueStr(task.packedQty))
        helper.setText(R.id.tv_unit, task.unit)
    }

}