package com.midea.prestorage.function.addgoods.fragment

import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestorage.widgets.WarpLinearLayout
import com.midea.prestoragesaas.databinding.FragmentMyGoodsBinding
import com.midea.prestoragesaas.databinding.FragmentMyGoodsCareBinding

sealed class FragmentMyGoodsUnionBinding{
    abstract var vm: AddMyTaskVM?
    abstract val root: View
    abstract val srl: SwipeRefreshLayout
    abstract val rv: RecyclerView
    abstract val llContainer: LinearLayout
    abstract val ivNoOrder: ImageView
    abstract val tvTotalNumber: TextView
    abstract val warpLinear: WarpLinearLayout
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: FragmentMyGoodsCareBinding) : FragmentMyGoodsUnionBinding() {
        override var vm: AddMyTaskVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val srl = binding.srl
        override val rv = binding.rv
        override val llContainer = binding.llContainer
        override val ivNoOrder = binding.ivNoOrder
        override val tvTotalNumber = binding.tvTotalNumber
        override val warpLinear = binding.warpLinear
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: FragmentMyGoodsBinding) : FragmentMyGoodsUnionBinding() {
        override var vm: AddMyTaskVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val srl = binding.srl
        override val rv = binding.rv
        override val llContainer = binding.llContainer
        override val ivNoOrder = binding.ivNoOrder
        override val tvTotalNumber = binding.tvTotalNumber
        override val warpLinear = binding.warpLinear
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
