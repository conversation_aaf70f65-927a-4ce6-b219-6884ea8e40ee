package com.midea.prestorage.function.instorage

import android.app.Application
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.function.instorage.request.ReqContainerReceive
import com.midea.prestorage.function.instorage.response.RespNoReceived
import com.midea.prestorage.function.instorage.response.RespReceiptHeader
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody

class InStorageReceivingVM(application: Application) : BaseViewModel(application) {
    var finishActivity = MutableLiveData<String>("")
    var curOrderNo = MutableLiveData<String>("") // 单号 (入库单号或波次单号)
    //单号类型
    var curOrderReceiveType = ObservableField<String>("receipt")   // receipt 入库单  wave波次单
    var previousP = MutableLiveData(false) //上一条数据
    var nextP = MutableLiveData(false) //下一条数据
    var toReceivingRecord = MutableLiveData(false)
    var isFirstItem = MutableLiveData(false)
    var isLastItem = MutableLiveData(false)
    var isMidItem = MutableLiveData(false)
    var notReceivedDatas = MutableLiveData<MutableList<RespNoReceived>>()
    var loadMoreDatas = MutableLiveData<MutableList<RespNoReceived>>()
    var receiptCompleted = MutableLiveData(false)
    var closeOrder = MutableLiveData(false)
    val isNoData = MutableLiveData(false)
    var firstEnter = MutableLiveData(true)
    val loadMoreComplete = ObservableField(0)

    // 当前页码
    var pageNo = 1

    override fun init() {
        pageNo = 1
        loadMoreComplete.set(0)
        queryNotReceived()
    }

    /**
     * 待收明细查询
     */
    fun queryNotReceived(isLoadMore: Boolean = false) {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf<String, Any>()

            if(curOrderReceiveType.get() == "receipt") {
                param["receiptCode"] = curOrderNo.value.toString()
            }else {
                param["waveNo"] = curOrderNo.value.toString()
            }

            param["pageSize"] = 10
            param["pageNo"] = pageNo
            param["whCode"] = Constants.whInfo?.whCode.toString()

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().queryNotReceived(requestBody) }
                resp.await()
            }

            if(result.code == 0.toLong()) {
                if(result.data != null) {
                    if (isLoadMore) {
                        result.data!!.list.forEach { data ->
                            data.cdWhLotDetail?.sort()
                        }
                        loadMoreDatas.value = result.data!!.list
                        loadMoreComplete.set(0)
                    } else {
                        if(result.data!!.list.size == 0 && !firstEnter.value!! && pageNo == 1) {
                            finishActivity.value = "收货已完成"
                        }else {
                            result.data!!.list.forEach { data ->
                                data.cdWhLotDetail?.sort()
                            }
                            notReceivedDatas.value = result.data!!.list
                        }
                    }
                }
                if (pageNo >= result.data?.totalPage!!) {
                    loadMoreComplete.set(1)
                } else {
                    pageNo = result.data?.pageNo!! + 1
                }
            }else {
                showNotification(result.msg as String, false)
                val emptyList = mutableListOf<RespNoReceived>()
                notReceivedDatas.value = emptyList
            }
        }
    }

    /**
     * 收货
     */
    fun containerReceive(list: List<ReqContainerReceive>) {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(list)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().containerReceive(requestBody) }
                resp.await()
            }

            if(result.code == 0.toLong()) {
                firstEnter.value = false
                pageNo = 1
                loadMoreComplete.set(0)
                queryNotReceived()
            }else {
                showNotification(result.msg as String, false)
            }
        }
    }

    /**
     * 关闭订单
     */
    fun orderClose(list: List<String>) {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf<String, Any>()
            param["receiptHeaderIds"] = list

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().orderClose(requestBody) }
                resp.await()
            }

            if(result.code == 0.toLong()) {
                finishActivity.value = "关闭订单成功"
            }else {
                showNotification(result.msg as String, false)
            }
        }
    }

    fun showErrorNotification(msg: String, isSuccess: Boolean) {
        showNotification(msg, isSuccess)
    }

    fun receivingRecord() {
        toReceivingRecord.value = true
    }

    fun closeOrder() {
        closeOrder.value = true
    }

    fun receiptCompleted() {
        receiptCompleted.value = true
    }

    fun previousPage() {
        previousP.value = true
    }

    fun nextPage() {
        nextP.value = true
    }
}