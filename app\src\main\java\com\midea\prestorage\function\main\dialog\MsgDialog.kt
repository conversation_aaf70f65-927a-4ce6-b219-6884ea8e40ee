package com.midea.prestorage.function.main.dialog

import android.app.AlertDialog
import android.text.TextUtils
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter.OnCheckListener
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.net.OutStorageDeleteQueryBean
import com.midea.prestoragesaas.databinding.DialogMsgBinding
import com.midea.prestoragesaas.databinding.DialogOutStorageDeleteTipBinding
import com.midea.prestoragesaas.databinding.DialogTipBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/6/26$
 */
class MsgDialog(
    var mContext: BaseActivity
) : AlertDialog(mContext) {

    private var binding: DialogMsgBinding
    var tag: Any? = null

    init {
        val contentView =
            LayoutInflater.from(mContext).inflate(R.layout.dialog_msg, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = MsgDialogVM(this)

        setCanceledOnTouchOutside(false)
    }

    fun setTitle(title: String) {
        binding.vm!!.titleName.set(title)
    }

    fun setMsg(msg: String) {
        binding.vm!!.setMsg(msg)
    }
}
