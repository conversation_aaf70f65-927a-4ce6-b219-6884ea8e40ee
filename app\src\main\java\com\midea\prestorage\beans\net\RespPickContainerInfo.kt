package com.midea.prestorage.beans.net

import com.midea.prestorage.base.annotation.ShowAnnotation
import java.math.BigDecimal

data class RespPickContainerInfo(
    @ShowAnnotation
    var shippingLoc: String? = null,
    @ShowAnnotation
    var pickContainerCode: String? = null,
    @ShowAnnotation
    var containerCode: String? = null,
    @ShowAnnotation
    var skuCount: BigDecimal? = null,
    @ShowAnnotation
    var totalQty: BigDecimal? = null,
    @ShowAnnotation
    var totalCsQty: BigDecimal? = null,
    @ShowAnnotation
    var totalEaQty: BigDecimal? = null,
    @ShowAnnotation
    var pickUserName: String? = null,
    @ShowAnnotation
    var closeContainerTime: String? = null,
    @ShowAnnotation
    var custOrderNo: String? = null,
    @ShowAnnotation
    var shipToCustomerName: String? = null,
    @ShowAnnotation
    var status: String? = null
)
