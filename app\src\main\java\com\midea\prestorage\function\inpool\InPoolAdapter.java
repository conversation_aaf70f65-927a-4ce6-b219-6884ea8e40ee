package com.midea.prestorage.function.inpool;

import android.widget.ImageView;

import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.midea.prestoragesaas.R;
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter;
import com.midea.prestorage.beans.base.BaseItemForPopup;

public class InPoolAdapter<T extends BaseItemForPopup> extends ListCheckBoxAdapter<T> {

	public InPoolAdapter(int layoutResId) {
		super(layoutResId);
	}

	@Override
	protected void convert(BaseViewHolder helper, BaseItemForPopup item) {
		super.convert(helper,  item);
		ImageView check = helper.getView(R.id.img_select);

		if (item.isSelected()) {
			check.setImageResource(R.drawable.ic_check_selected);
		} else {
			check.setImageResource(R.drawable.ic_check_unselect);
		}
	}
}