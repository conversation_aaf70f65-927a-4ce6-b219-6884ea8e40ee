package com.midea.prestorage.function.inv

import android.view.View
import android.widget.Toast
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.ItemSerialAdjust
import com.midea.prestorage.beans.net.QueryBarcodeCollectRfVO
import com.midea.prestorage.beans.net.SerialScanDto
import com.midea.prestorage.beans.net.SkuBarcodeInfoDto
import com.midea.prestorage.beans.setting.SerialAdjustInfo
import com.midea.prestorage.function.inv.response.ItemRfVO
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.*
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import okhttp3.MediaType
import okhttp3.RequestBody
import org.xutils.db.sqlite.SqlInfo
import org.xutils.db.sqlite.WhereBuilder
import org.xutils.db.table.DbModel
import org.xutils.ex.DbException
import kotlin.collections.List
import kotlin.collections.MutableList
import kotlin.collections.forEach
import kotlin.collections.mutableListOf
import kotlin.collections.mutableMapOf
import kotlin.collections.set

// 条码采集
class SerialNoAdjustVM(val activity: SerialNoAdjustActivity) {

    // private var ownerDialog: FilterDialog  //货主选择对话框
    // 当前选择的调账类型
    var curSelectAdjustName = ""

    // 编辑框输入的条码
    var anyCode = ObservableField<String>("")

    // 采集单号 ，默认是空，进入界面后会查询列表，并得到一个单号 ，后面的请求都会带上这个单号
    var textCurAdjustCode = ObservableField<String>("")

    // 当前单号的条码总数
    var textTotalQty = ObservableField<String>("")

    // 采集超过多少条时 自动提交给后端暂存  需求是20  开发时为了测试方便可以调小一点
    val MAX_LIMIT = 20

    // rf本地db  用来加载本地缓存的 条码
    val db = DbUtils.db


    init {
    }


    // 从rf本地db缓存  加载某个采集单号对应的 条码列表
    fun loadCacheByAdjustCode(adjustCode: String) {

        val cacheKey = Constants.userInfo!!.name + "#" + adjustCode

        Observable.create<List<DbModel>> {
            //var listDbRecords = db.findDbModelAll(SqlInfo("select * from SerialAdjustInfo where cacheKey = '" + cacheKey + "'"))
            val listDbRecords = db.findDbModelAll(SqlInfo("select * from SerialAdjustInfo  where cacheKey = '" + cacheKey + "'"))
            it.onNext(listDbRecords!!)
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<List<DbModel>> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(listDbRecords: List<DbModel>) {
                    listDbRecords.forEach {
                        if (it.getString("jsonStrSerialNo") != null) {
                            val jsonStr = it.getString("jsonStrSerialNo")
                            val itemSerialAdjust = Gson().fromJson(jsonStr, ItemSerialAdjust::class.java)
                            activity.adapter.data.add(0, itemSerialAdjust)
                        }
                    }
                    activity.adapter.notifyDataSetChanged()
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    fun deleteCacheByAdjustCode(adjustCode: String) {
        val cacheKey = Constants.userInfo!!.name + "#" + adjustCode
        try {
            db.delete(SerialAdjustInfo::class.java, WhereBuilder.b("cacheKey", "=", cacheKey))
            //db.delete(SerialAdjustInfo.class, WhereBuilder.b("sex", "=", "woman").and("salary", "=", "5000"))
        } catch (e: DbException) {
            e.printStackTrace()
        }
    }


    private fun addCacheByAdjustCode(adjustCode: String, item: ItemSerialAdjust) {
        val cacheKey = Constants.userInfo!!.name + "#" + adjustCode
        try {
            val dbRecord = SerialAdjustInfo()
            dbRecord.cacheKey = cacheKey
            dbRecord.jsonStrSerialNo = Gson().toJson(item)
            db.save(dbRecord)
        } catch (e: DbException) {
            e.printStackTrace()
        }
    }


    // 先获取一个 采集单号adjustCode
    fun initAdjustCode() {


        val param = mutableMapOf(
            "adjustCode" to textCurAdjustCode.get(),
            "whCode" to activity.getWhCode()
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        // 货品条码校验
        RetrofitHelper.getAppAPI()
            .serialNoAdjustQuery(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<QueryBarcodeCollectRfVO>(activity) {
                override fun success(data: QueryBarcodeCollectRfVO?) {
                    activity.waitingDialogHelp.hidenDialog()

                    if (data != null) {
                        // 当前单号的条码数
                        textTotalQty.set(data.totalQty?.toString())

                        // 如果 nextAdjustCode 有值 就取nextAdjustCode 作为当前采集单，没有就取adjustCode 为当前采集单
                        if (!data.nextAdjustCode.isNullOrBlank()) {
                            textCurAdjustCode.set(data.nextAdjustCode)
                            loadCacheByAdjustCode(textCurAdjustCode.get()!!)
                        } else if (!data.adjustCode.isNullOrBlank()) {
                            textCurAdjustCode.set(data.adjustCode)
                            loadCacheByAdjustCode(textCurAdjustCode.get()!!)
                        } else {
                            ToastUtils.getInstance().showErrorToastWithSound(activity, "获取采集单号失败")
                        }
                    } else {
                        ToastUtils.getInstance().showErrorToastWithSound(activity, "获取采集单号失败")
                    }

                    AppUtils.requestFocus(activity.binding.etAnyCode)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                    AppUtils.clearAndFocus(activity.binding.etAnyCode)

                }
            })
    }


    fun onEnterAnyCode() {
        // 清除前后空格
        val snNo = activity.binding.etAnyCode.text.toString().trim()

        if (snNo.isBlank()) {
            //ToastUtils.getInstance().showErrorToastWithSound(activity, "货品条码为空")
            return
        }

        //先校验 当前输入的 货品编码 或sn码
        val param = mutableMapOf(
            "serialNo" to snNo,
            "whCode" to activity.getWhCode()
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        // 货品条码校验
        RetrofitHelper.getAppAPI()
            .scanCodeOnTransferGood(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<SerialScanDto>(activity) {
                override fun success(data: SerialScanDto?) {
                    activity.waitingDialogHelp.hidenDialog()
                    // success 表示识别成功
                    if (data != null) {
                        handleSerial(data)
                    } else {
                        ToastUtils.getInstance().showErrorToastWithSound(activity, "无法识别:" + anyCode.get())
                        AppUtils.clearAndFocus(activity.binding.etAnyCode)
                    }

                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                    AppUtils.clearAndFocus(activity.binding.etAnyCode)
                }
            })
    }

    fun handleSerial(serialScanDto: SerialScanDto) {

        // 20211129 和后端接口沟通结果:  判断一个字符串是不是sn码的 条件:   (barcodeType=0或2或5或6) 并且  serialScanDto.itemRfVOS 不为空  的才是sn码
        // 1-规则解析的条码，2-LMS接口解析的条码，3-69码 ，4-商品编码 5-条码库存 6-二级节点
        if (serialScanDto.barcodeType != 0 && serialScanDto.barcodeType != 2
            && serialScanDto.barcodeType != 5 && serialScanDto.barcodeType != 6
        ) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "请扫描SN码！")
            AppUtils.clearAndFocus(activity.binding.etAnyCode)
            return
        }

        if (serialScanDto.itemRfVOS == null || serialScanDto.itemRfVOS.size == 0) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "无法识别:" + anyCode.get())
            AppUtils.clearAndFocus(activity.binding.etAnyCode)
            return
        }

//        ToastUtils.getInstance().toastWithFinishSound(activity, "条码识别成功", Toast.LENGTH_SHORT)
        MySoundUtils.getInstance().finishSound()

        if (serialScanDto.itemRfVOS.size == 1) {
            addSerialNoToList(serialScanDto)
        } else {
            // 查询到多个客户商品编码 先弹框选择一个
            activity.popAdapterSelectCustItemCode.data.clear()
            serialScanDto.itemRfVOS.forEach {
                activity.popAdapterSelectCustItemCode.addData(it)
            }
            activity.popAdapterSelectCustItemCode.setOnItemClickListener { adapter, _, position ->
                val item = adapter.getItem(position) as ItemRfVO
                activity.binding.vm!!.onSelectCustItemCode(item, serialScanDto)
                activity.dlgSelectCustItemCode.dismiss()
            }
            activity.dlgSelectCustItemCode.show()
            activity.popAdapterSelectCustItemCode.notifyDataSetChanged()
        }
    }


    private fun onSelectCustItemCode(itemRfVO: ItemRfVO, serialScanDto: SerialScanDto) {

        if (serialScanDto.skuBarcodeInfoDtos == null || serialScanDto.skuBarcodeInfoDtos.size == 0) {
            return
        }
        // 选了 itemRfVO数组里的其中一个itemRfVO，那就让整个数组只有这一个被选的itemRfVO
        serialScanDto.itemRfVOS = mutableListOf<ItemRfVO>()
        serialScanDto.itemRfVOS.add(itemRfVO)
        addSerialNoToList(serialScanDto)
    }

    fun cacheSerialNo(serialNos: MutableList<String>, successCallback: HttpSuccessCallback?) {
        val param = mutableMapOf(
            "serialNos" to serialNos,
            "adjustCode" to textCurAdjustCode.get(),
            "whCode" to activity.getWhCode()
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        // 货品条码校验
        RetrofitHelper.getAppAPI()
            .serialNoAdjustCache(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    successCallback?.onAfterSuccess()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                    AppUtils.clearAndFocus(activity.binding.etAnyCode)

                    if (statusCode == 600024L) {
                        // 600024 采集单已关闭的时候（比如同个账号同个单号操作两台机，一台机已完成采集, 另一台机就会报600024）
                        // 自动获取新单号
                        textCurAdjustCode.set("")
                        initAdjustCode()
                        ToastUtils.getInstance().toastNoSound(activity, "已切换新的采集单，请继续采集", Toast.LENGTH_LONG)
                    }
                }
            })
    }

    private fun addSerialNoToList(serialScanDto: SerialScanDto) {

        if (serialScanDto.skuBarcodeInfoDtos == null || serialScanDto.skuBarcodeInfoDtos.size == 0) {
            if (serialScanDto.itemRfVOS != null && serialScanDto.itemRfVOS.size > 0) {

                // 如果后端返回数据 没有 skuBarcodeInfoDtos 但是又有 itemRfVOS  则构造一个小箱条码
                serialScanDto.skuBarcodeInfoDtos = mutableListOf<SkuBarcodeInfoDto>()

                val skuBarcodeInfoDto = SkuBarcodeInfoDto()
                skuBarcodeInfoDto.sn = serialScanDto.serialNo

                serialScanDto.skuBarcodeInfoDtos.add(skuBarcodeInfoDto)
            } else {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "无法识别:" + anyCode.get())
                return
            }

        }

        val serialCs = anyCode.get()
        val currentAdjustType = getCurrentAdjustTypeCode()

        val serialNos = mutableListOf<String>()

        // 拼接小箱条码 提交到后端缓存查重
        serialScanDto.skuBarcodeInfoDtos.forEach {
            serialNos.add(it.sn)
        }

        cacheSerialNo(serialNos, HttpSuccessCallback { // 把小箱条码 放入界面列表
            serialScanDto.skuBarcodeInfoDtos.forEach {
                val itemSerialAdjust = ItemSerialAdjust()
                itemSerialAdjust.serialCs = serialCs
                itemSerialAdjust.serialNo = it.sn
                itemSerialAdjust.custItemCode = serialScanDto.itemRfVOS.get(0).custItemCode
                itemSerialAdjust.itemName = serialScanDto.itemRfVOS.get(0).itemName
                itemSerialAdjust.adjustType = currentAdjustType

                activity.adapter.data.add(0, itemSerialAdjust)
                //缓存到本地db
                addCacheByAdjustCode(textCurAdjustCode.get()!!, itemSerialAdjust)
            }

            activity.adapter.notifyDataSetChanged()

            // 后端缓存成功了 单号已扫条码数+1
            val curTotalSize = textTotalQty.get()!!.toInt() + serialScanDto.skuBarcodeInfoDtos.size
            textTotalQty.set(curTotalSize.toString())

            // 超过最大数量时 提交给后端
            if (activity.adapter.data.size >= MAX_LIMIT) {
                submitList(false)
            }
        })

        AppUtils.clearAndFocus(activity.binding.etAnyCode)

    }


    private fun getCurrentAdjustTypeCode(): String {
        var adjustTypeCode = ""
        if (DCUtils.serialNoAdjustTypeN2C.get(curSelectAdjustName) != null) {
            adjustTypeCode = DCUtils.serialNoAdjustTypeN2C.get(curSelectAdjustName).toString()
        }
        return adjustTypeCode
    }


    //  isFinish 是否完成采集的标识
    private fun submitList(isFinish: Boolean) {


        val param = mutableMapOf<String, Any>()
        param["adjustCode"] = textCurAdjustCode.get()!!
        param["whCode"] = activity.getWhCode()
        param["whName"] = activity.getWhName()
        if (isFinish) {
            param["finish"] = "1"
        } else {
            param["finish"] = "0"
        }

        param["barcodeCollectSerialRfVOS"] = activity.adapter.data

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getAppAPI()
            .serialNoAdjustSubmit(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()

                    //提交成功后 清空本地缓存
                    if (textCurAdjustCode.get() != null) {
                        deleteCacheByAdjustCode(textCurAdjustCode.get()!!)
                    }

                    if (isFinish) {
                        // 如果是点了 完成按钮 的提交， 则清空当前 采集单号，然后获取新的采集单号
                        textCurAdjustCode.set("")
                        ToastUtils.getInstance().toastWithOkSound(activity, "操作成功，采集已完成", Toast.LENGTH_LONG)
                    } else {
                        ToastUtils.getInstance().toastWithOkSound(activity, "已提交" + activity.adapter.data.size + "条记录", Toast.LENGTH_SHORT)
                    }

                    activity.adapter.data.clear()
                    activity.adapter.notifyDataSetChanged()


                    //提交后 查询一次 最新的 采集单号adjustCode 和 单号条码数
                    initAdjustCode()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }


    // 点击了 采集按钮
    fun onEnterAdjust() {
        submitList(true)
    }

    fun scanAnyCode() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(code: String?) {
        code?.let {
            activity.binding.etAnyCode.setText(code)
            onEnterAnyCode()
            //AppUtils.requestFocus(activity.binding.etAnyCode)
        }
    }


    //后退键
    val back = View.OnClickListener {
        activity.finish()
    }
}