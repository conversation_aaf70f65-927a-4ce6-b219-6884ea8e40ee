package com.midea.prestorage.function.containerpick.provider

import android.widget.CheckBox
import android.widget.CompoundButton
import com.chad.library.adapter.base.entity.node.BaseNode
import com.chad.library.adapter.base.provider.BaseNodeProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestorage.function.containerpick.adapter.BulkAlreadyPackedAdapter
import com.midea.prestorage.function.containerpick.fragment.BulkAlreadyPackedVM
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R

class BulkPickAlreadyHeaderProvider(private val vm: BulkAlreadyPackedVM) : BaseNodeProvider() {
    override val itemViewType: Int
        get() = BulkAlreadyPackedAdapter.TYPE_HEADER

    override val layoutId: Int
        get() = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_already_bulk_pick_header_care else R.layout.item_already_bulk_pick_header

    private val checkBoxMap =
        mutableMapOf<BulkPickToBeWrap, CompoundButton.OnCheckedChangeListener>()

    override fun convert(helper: BaseViewHolder, item: BaseNode) {
        val wrap = item as BulkPickToBeWrap
        val task = wrap.respBulkPacking
        helper.setText(R.id.tv_ship_to_customer_name, task.shipToCustomerName)
        helper.setText(R.id.tv_serial_number, "第${AppUtils.getBigDecimalValueStr(task.seqNum)}箱")
        helper.setText(R.id.tv_time, task.closeContainerTime)
        helper.setText(R.id.tv_index, AppUtils.getBigDecimalValueStr(task.packNumber))

        val checkBox = helper.getView<CheckBox>(R.id.cb_select)
        checkBox.setOnCheckedChangeListener(null)
        checkBox.isChecked = wrap.selected
        val checkedChangeListener = if (!checkBoxMap.containsKey(wrap)) {
            CompoundButton.OnCheckedChangeListener { _, isChecked ->
                wrap.selected = isChecked
                vm.onItemCheckedChang(wrap)
            }.also {
                checkBoxMap[wrap] = it
            }
        } else {
            checkBoxMap[wrap]
        }
        checkBox.setOnCheckedChangeListener(checkedChangeListener)
    }

}