package com.midea.prestorage.function.instorage

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestorage.base.App
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickPositionAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestoragesaas.databinding.ActivityInStorageScanNewBinding
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.dialog.InStorageDialog
import com.midea.prestorage.dialog.TipDialog
import com.midea.prestorage.function.instorage.response.InOrderDataSort
import com.midea.prestorage.function.outstorage.dialog.DeleteBarcodeDialog
import com.midea.prestorage.function.outstorage.dialog.HandlingDialog
import com.midea.prestorage.utils.DCUtils
import com.xuexiang.xqrcode.XQRCode
import kotlinx.coroutines.launch

class InStorageScanNewActivity : BaseViewModelActivity<InStorageScanNewVM>() {
    private lateinit var binding: ActivityInStorageScanNewBinding
    private lateinit var statueDialog: FilterDialog
    val lot4Options = mutableListOf<String>()
    private lateinit var ccsDialog: InStorageDialog
    private lateinit var deleteDialog: DeleteBarcodeDialog
    private lateinit var handlingDialog: HandlingDialog
    private lateinit var tipDialog: TipDialog
    var adapter = InStorageScanGoodsAdapter()

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_in_storage_scan_new)
        vm = ViewModelProvider.AndroidViewModelFactory(application).create(InStorageScanNewVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        //返回
        vm.finishActivity.observe(this, Observer<Boolean> {
            if (it) {
                finish()
            }
        })

        //扫描
        vm.startScan.observe(this, Observer<Boolean> {
            if (it) {
                XQRCode.startScan(this, BaseActivity.QR_CODE_BACK)
            }
        })

        //弹出商品状态
        vm.isShowStatueDialog.observe(this, Observer<Boolean> {
            if (it) {
                statueDialog.show()
            }
        })

        //弹出自建板
        vm.isShowCcsDialog.observe(this, Observer<Boolean> {
            if (it) {
                lifecycleScope.launch {
                    ccsDialog.setCbStatus(vm.getCssInfo())
                    ccsDialog.show()
                }
            }
        })

        //弹出删除条码
        vm.isShowDeleteDialog.observe(this, Observer<Boolean> {
            if (it) {
                deleteDialog.show()
            }
        })

        //弹出装卸时间
        vm.isShowHandingDialog.observe(this, Observer<Boolean> {
            if (it) {
                handlingDialog.setStartTime(vm.handingStartTime.value.toString())
                handlingDialog.setEndTime(vm.handingEndTime.value.toString())
                handlingDialog.show()
            }
        })

        //进入收货页面
        vm.toReceivingActivity.observe(this, Observer<Boolean> {
            if (it) {
                val intent = Intent(this, InStorageReceivingActivity::class.java)
                // 这里塞一个 后端返回的波次号或入库单号
                intent.putExtra("orderNo", vm.curOrderNo.value)
                intent.putExtra("orderReceiveType", vm.curOrderReceiveType.get())
                startActivity(intent)
            }
        })

        //货品编码/SN码/69码 输入框获取光标
        vm.goodsRequest.observe(this, Observer<Boolean> {
            if (it) {
                goodsRequest()
            }
        })

        //提示更换托盘
        vm.containerChangeTip.observe(this, Observer<Boolean> {
            if (it) {
                containerChangeTip()
            }
        })

        vm.isShowCcs.observe(this, Observer<Boolean> {
            if (it) {
                if(!vm.isPalletEnter.value!!) {
                    binding.etScan.requestFocus()
                }else {
                    binding.etAnyCode.requestFocus()
                }
            }else {
                binding.etAnyCode.requestFocus()
            }
        })

        //设置装卸时间成功后，弹出自动消失
        vm.dismissDialog.observe(this, Observer<Boolean> {
            if (it) {
                handlingDialog.dismiss()
            }
        })

        if (App.tenantCode != null && App.tenantCode == "annto") {
            binding.tvTime.visibility = View.VISIBLE
        }else {
            binding.tvTime.visibility = View.GONE
        }

        initData()
        initDialog()
        initSpinner()
        initRecycleView()
    }

    fun initData() {
        val orderNo = intent.getStringExtra("orderNo")
        val orderReceiveType = intent.getStringExtra("orderReceiveType")
        val containerCode = intent.getStringExtra("containerCode")

        if (containerCode.isNullOrEmpty()) {
            AlertDialogUtil.showOnlyOkDialog(this, "容器号为空", AlertDialogUtil.OnOkCallback { })
        }else {
            vm.containerCode = containerCode
        }
        if (orderNo.isNullOrEmpty()) {
            AlertDialogUtil.showOnlyOkDialog(this, "单号为空", AlertDialogUtil.OnOkCallback { })
        } else {
            vm.curOrderNo.value = orderNo
        }
        if (!orderReceiveType.isNullOrEmpty()) {
            vm.curOrderReceiveType.set(orderReceiveType)  //单号类型
        }

        lifecycleScope.launch {
            vm.isShowCcs.value = vm.getCssInfo()
        }
    }

    private fun initDialog() {
        //商品状态dialog
        statueDialog = FilterDialog(this)
        statueDialog.setTitle("请选择商品状态")
        statueDialog.dismissEdit()
        statueDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            vm.statueStr.value = it.showInfo
            vm.curSelectLot4Name = it.showInfo
            statueDialog.dismiss()
        })

        //自建板dialog
        ccsDialog = InStorageDialog(this)
        ccsDialog.setTitle("设置")
        ccsDialog.displayCheckBox()
        ccsDialog.setBack(object : InStorageDialog.ConfirmFilterChooseBack {
            override fun confirmFilterChooseBack(isChecked: Boolean) {
                ccsDialog.dismiss()
                vm.saveCssInfo(isChecked)
                vm.isShowCcs.value = isChecked
                if(isChecked) {
                    containerRequest()
                }
            }
        })

        //删除条码dialog
        deleteDialog = DeleteBarcodeDialog(this)
        deleteDialog.setTitle("删除条码")
        deleteDialog.setDeleteBack(object : DeleteBarcodeDialog.DeleteBarcodeBack {
            override fun deleteBarcodeBack(barcode : String) {
                //deleteDialog.dismiss()
                vm.deleteSerial(getWhCode(), barcode)
            }
        })

        deleteDialog.setOnDismissListener {
            vm.loadInOrderDatas(getWhCode())
        }

        //装卸时间dialog
        handlingDialog = HandlingDialog(this)
        handlingDialog.setConfirmBack(object: HandlingDialog.ConfirmBack {
            override fun confirmBack(type: Int, time: String) {
                if(type == 1) {
                    vm.handingStartTime.value = time
                }else {
                    vm.handingEndTime.value = time
                }
                vm.handingTime()
            }

        })
    }

    // 商品库存状态 下拉框
    private fun initSpinner() {

        DCUtils.goodsStatue(this, object : DCUtils.DCBack {

            override fun dcBack(statusDC: MutableList<DCBean>) {
                statusDC.forEach {
                    lot4Options.add(it.key.toString())
                }

                //默认选第一个
                if (lot4Options.size > 0) {
                    binding.vm!!.curSelectLot4Name = lot4Options[0]
                    binding.vm!!.statueStr.value = lot4Options[0]
                }

                addStatueData(lot4Options)
            }
        })
    }

    fun addStatueData(data: MutableList<String>) {
        val beans = mutableListOf<BaseItemShowInfo>()
        data.forEach {
            beans.add(BaseItemShowInfo(it))
        }
        statueDialog.addAllData(beans)
    }

    private fun containerChangeTip() {
        tipDialog = TipDialog(this)
        tipDialog.setTitle("提示")
        tipDialog.setMsg("确定要更换托盘吗?")
        tipDialog.setOnTipBackListener(object : TipDialog.OnTipBack {
            override fun onConfirmClick() {
                if (CheckUtil.isFastDoubleClick()) {
                    containerRequest()
                }
            }

            override fun onDismissClick() {
            }
        })
        tipDialog.show()
    }

    fun containerRequest() {
        binding.etScan.setText("")
        binding.etAnyCode.setText("")
        binding.etScan.isEnabled = true
        binding.etScan.requestFocus()
        vm.isPalletEnter.value = false
    }

    private fun goodsRequest() {
        binding.etScan.isEnabled = false
        binding.etAnyCode.requestFocus()
    }

    override fun onResume() {
        super.onResume()
        binding.etScan.onFocusChangeListener = onFocusChangeListener
        vm.loadInOrderDatas(getWhCode())
    }

    private val onFocusChangeListener =
        View.OnFocusChangeListener { view: View, hasFocus: Boolean ->
            if (!hasFocus && vm.isShowCcs.value!!) {
                if (!vm.isPalletEnter.value!!) {
                    vm.showErrorNotification("请回车校验托盘码", false)
                    view.post {
                        binding.etScan.requestFocus()
                    }
                }
            }
        }

    override fun onPause() {
        super.onPause()
        binding.etScan.onFocusChangeListener = null
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == BaseActivity.QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果
            binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    fun initRecycleView() {
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        //binding.recyclerView.addItemDecoration(DividerItemDecoration(this, DividerItemDecoration.VERTICAL))
        binding.recyclerView.adapter = adapter

        vm.inOrderDatas.observe(this, Observer<MutableList<InOrderDataSort>> { result ->
            adapter.data.clear()
            result.sort()
            adapter.addData(result)
            adapter.notifyDataSetChanged()

            if(adapter.data.size > 0 && vm.isInitStatus.value!!) {
                DCUtils.goodsStatue?.forEach {
                    if(it.value == adapter.data[0].item.lotAtt04) {
                        binding.vm!!.curSelectLot4Name = it.key
                        binding.vm!!.statueStr.value = it.key
                        vm.isInitStatus.value = false
                    }
                }
            }
        })
    }

    class InStorageScanGoodsAdapter : ListChoiceClickPositionAdapter<InOrderDataSort>(R.layout.item_in_storage_scan_goods) {

        override fun convert(helper: BaseViewHolder, item: InOrderDataSort) {
            super.convert(helper, item)

            item.vs.sortFlag.get().let {
                when (it) {
                    0 -> {
                        helper.setBackgroundResource(R.id.rl_root, R.drawable.bg_round_rectangle_light_blue)
                        helper.setVisible(R.id.img_finish, false)
                    }   //进行中
                    1 -> {
                        helper.setBackgroundResource(R.id.rl_root, R.drawable.bg_round_rectangle_white)
                        helper.setVisible(R.id.img_finish, false)
                    }    //未完成
                    2 -> {
                        helper.setBackgroundResource(R.id.rl_root, R.drawable.bg_round_rectangle_green)
                        helper.setVisible(R.id.img_finish, true)
                    }    //已完成
                    else -> {
                        helper.setBackgroundResource(R.id.rl_root, R.drawable.bg_round_rectangle_white)
                        helper.setVisible(R.id.img_finish, false)
                    }
                }
            }

            if(!item.item.custItemCode.isNullOrBlank()) {
                helper.setText(R.id.tv_cust_item_code, item.item.custItemCode)
            }else {
                helper.setText(R.id.tv_cust_item_code, "")
            }

            if(!item.item.itemName.isNullOrBlank()) {
                helper.setText(R.id.tv_goodsName, item.item.itemName)
            }else {
                helper.setText(R.id.tv_goodsName, "")
            }

            helper.setText(R.id.tv_planNum, item.item.totalQty.toInt().toString())

            helper.setText(R.id.tv_scanNum, item.item.scanNum.toString())

            helper.setText(R.id.tv_recvNum, item.item.receiptQty.toInt().toString())

            if("1" == item.item.cdcmUnscanMark) {
                helper.setVisible(R.id.tv_no_scan, true)
            }else {
                helper.setVisible(R.id.tv_no_scan, false)
            }

//            DCUtils.goodsStatue?.forEach {
//                if(it.value == item.item.lotAtt04) {
//                    helper.setText(R.id.tv_inventoryStsStr, it.key.toString())
//                }
//            }

        }
    }
}