package com.midea.prestorage.function.planstock.dialog

import android.widget.GridView
import android.widget.ImageView
import android.widget.Switch
import android.widget.TextView
import com.midea.prestoragesaas.databinding.DialogInputNumBinding
import com.midea.prestoragesaas.databinding.DialogInputNumCareBinding

sealed class DialogInputNumUnionBinding{
    abstract var vm: InputNumDialogVM?
    abstract val gridNumber: GridView
    abstract val tvCarcode69: TextView
    abstract val addSwitch: Switch

    class V2(val binding: DialogInputNumCareBinding) : DialogInputNumUnionBinding() {
        override var vm: InputNumDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val gridNumber = binding.gridNumber
        override val tvCarcode69 = binding.tvCarcode69
        override val addSwitch = binding.addSwitch
    }

    class V1(val binding: DialogInputNumBinding) : DialogInputNumUnionBinding() {
        override var vm: InputNumDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val gridNumber = binding.gridNumber
        override val tvCarcode69 = binding.tvCarcode69
        override val addSwitch = binding.addSwitch
    }
}
