package com.midea.prestorage.beans.net;

import java.io.Serializable;

/**
 * Created by LIXK5 on 2019/5/16.
 */
public class ScanReciveResp implements Serializable {
    private String id;
    private String version;
    private String pageSize;
    private String warehouseCode;
    private String companyCode;
    private String code;
    private String receiptType;
    private String sourcePlatform;
    private String leadingSts;
    private String trailingSts;
    private String erpOrderCode;
    private String erpOrderId;
    private String shipFromCode;
    private String shipFromAddress1;
    private String shipFromCity;
    private String shipFromState;
    private String shipFromCountry;
    private String shipFromName;
    private String shipFromPhoneNum;
    private String scheduledArriveDate;
    private String closedAt;
    private String closedBy;
    private String totalQty;
    private String totalLines;
    private String toalCases;
    private String totalWeight;
    private String totalVolume;
    private String uploadRequired;
    private String locked;
    private String created;
    private String createdBy;
    private String lastUpdated;
    private String lastUpdatedBy;
    private String userDef1;
    private String userDef6;
    private String uploadBatch;
    private String expressNo;
    private String siteCode;
    private String orgOrderCode;
    private String businessType;
    private String collectionOrderNo;
    private String upperWhCode;
    private String orderCode;
    private String branchcode;
    private String branchName;
    private String siteName;
    private String orderDate;
    private String shipFromTown;
    private String upperWhName;
    private String rpFlag;
    private String elsProject;
    private String invoiceunitcode;
    private String invoiceunitName;
    private String handlingFeeSts;
    private String invAgeSts;
    private String upperCustomerCode;
    private String upSourcePlatform;
    private String contractCustomerCode;
    private String confirmCt;
    private String dispatchNo;
    private String printed;
    private String projectclassify;
    private String totalpackageqty;
    private String carryService;
    private String carryServiceName;
    private String extBuisType;
    private String waveId;
    private boolean isWave;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getReceiptType() {
        return receiptType;
    }

    public void setReceiptType(String receiptType) {
        this.receiptType = receiptType;
    }

    public String getSourcePlatform() {
        return sourcePlatform;
    }

    public void setSourcePlatform(String sourcePlatform) {
        this.sourcePlatform = sourcePlatform;
    }

    public String getLeadingSts() {
        return leadingSts;
    }

    public void setLeadingSts(String leadingSts) {
        this.leadingSts = leadingSts;
    }

    public String getTrailingSts() {
        return trailingSts;
    }

    public void setTrailingSts(String trailingSts) {
        this.trailingSts = trailingSts;
    }

    public String getErpOrderCode() {
        return erpOrderCode;
    }

    public void setErpOrderCode(String erpOrderCode) {
        this.erpOrderCode = erpOrderCode;
    }

    public String getErpOrderId() {
        return erpOrderId;
    }

    public void setErpOrderId(String erpOrderId) {
        this.erpOrderId = erpOrderId;
    }

    public String getShipFromCode() {
        return shipFromCode;
    }

    public void setShipFromCode(String shipFromCode) {
        this.shipFromCode = shipFromCode;
    }

    public String getShipFromAddress1() {
        return shipFromAddress1;
    }

    public void setShipFromAddress1(String shipFromAddress1) {
        this.shipFromAddress1 = shipFromAddress1;
    }

    public String getShipFromCity() {
        return shipFromCity;
    }

    public void setShipFromCity(String shipFromCity) {
        this.shipFromCity = shipFromCity;
    }

    public String getShipFromState() {
        return shipFromState;
    }

    public void setShipFromState(String shipFromState) {
        this.shipFromState = shipFromState;
    }

    public String getShipFromCountry() {
        return shipFromCountry;
    }

    public void setShipFromCountry(String shipFromCountry) {
        this.shipFromCountry = shipFromCountry;
    }

    public String getShipFromName() {
        return shipFromName;
    }

    public void setShipFromName(String shipFromName) {
        this.shipFromName = shipFromName;
    }

    public String getShipFromPhoneNum() {
        return shipFromPhoneNum;
    }

    public void setShipFromPhoneNum(String shipFromPhoneNum) {
        this.shipFromPhoneNum = shipFromPhoneNum;
    }

    public String getScheduledArriveDate() {
        return scheduledArriveDate;
    }

    public void setScheduledArriveDate(String scheduledArriveDate) {
        this.scheduledArriveDate = scheduledArriveDate;
    }

    public String getClosedAt() {
        return closedAt;
    }

    public void setClosedAt(String closedAt) {
        this.closedAt = closedAt;
    }

    public String getClosedBy() {
        return closedBy;
    }

    public void setClosedBy(String closedBy) {
        this.closedBy = closedBy;
    }

    public String getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(String totalQty) {
        this.totalQty = totalQty;
    }

    public String getTotalLines() {
        return totalLines;
    }

    public void setTotalLines(String totalLines) {
        this.totalLines = totalLines;
    }

    public String getToalCases() {
        return toalCases;
    }

    public void setToalCases(String toalCases) {
        this.toalCases = toalCases;
    }

    public String getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(String totalWeight) {
        this.totalWeight = totalWeight;
    }

    public String getTotalVolume() {
        return totalVolume;
    }

    public void setTotalVolume(String totalVolume) {
        this.totalVolume = totalVolume;
    }

    public String getUploadRequired() {
        return uploadRequired;
    }

    public void setUploadRequired(String uploadRequired) {
        this.uploadRequired = uploadRequired;
    }

    public String getLocked() {
        return locked;
    }

    public void setLocked(String locked) {
        this.locked = locked;
    }

    public String getCreated() {
        return created;
    }

    public void setCreated(String created) {
        this.created = created;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(String lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getUserDef1() {
        return userDef1;
    }

    public void setUserDef1(String userDef1) {
        this.userDef1 = userDef1;
    }

    public String getUserDef6() {
        return userDef6;
    }

    public void setUserDef6(String userDef6) {
        this.userDef6 = userDef6;
    }

    public String getUploadBatch() {
        return uploadBatch;
    }

    public void setUploadBatch(String uploadBatch) {
        this.uploadBatch = uploadBatch;
    }

    public String getExpressNo() {
        return expressNo;
    }

    public void setExpressNo(String expressNo) {
        this.expressNo = expressNo;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getOrgOrderCode() {
        return orgOrderCode;
    }

    public void setOrgOrderCode(String orgOrderCode) {
        this.orgOrderCode = orgOrderCode;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getCollectionOrderNo() {
        return collectionOrderNo;
    }

    public void setCollectionOrderNo(String collectionOrderNo) {
        this.collectionOrderNo = collectionOrderNo;
    }

    public String getUpperWhCode() {
        return upperWhCode;
    }

    public void setUpperWhCode(String upperWhCode) {
        this.upperWhCode = upperWhCode;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public String getBranchcode() {
        return branchcode;
    }

    public void setBranchcode(String branchcode) {
        this.branchcode = branchcode;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(String orderDate) {
        this.orderDate = orderDate;
    }

    public String getShipFromTown() {
        return shipFromTown;
    }

    public void setShipFromTown(String shipFromTown) {
        this.shipFromTown = shipFromTown;
    }

    public String getUpperWhName() {
        return upperWhName;
    }

    public void setUpperWhName(String upperWhName) {
        this.upperWhName = upperWhName;
    }

    public String getRpFlag() {
        return rpFlag;
    }

    public void setRpFlag(String rpFlag) {
        this.rpFlag = rpFlag;
    }

    public String getElsProject() {
        return elsProject;
    }

    public void setElsProject(String elsProject) {
        this.elsProject = elsProject;
    }

    public String getInvoiceunitcode() {
        return invoiceunitcode;
    }

    public void setInvoiceunitcode(String invoiceunitcode) {
        this.invoiceunitcode = invoiceunitcode;
    }

    public String getInvoiceunitName() {
        return invoiceunitName;
    }

    public void setInvoiceunitName(String invoiceunitName) {
        this.invoiceunitName = invoiceunitName;
    }

    public String getHandlingFeeSts() {
        return handlingFeeSts;
    }

    public void setHandlingFeeSts(String handlingFeeSts) {
        this.handlingFeeSts = handlingFeeSts;
    }

    public String getInvAgeSts() {
        return invAgeSts;
    }

    public void setInvAgeSts(String invAgeSts) {
        this.invAgeSts = invAgeSts;
    }

    public String getUpperCustomerCode() {
        return upperCustomerCode;
    }

    public void setUpperCustomerCode(String upperCustomerCode) {
        this.upperCustomerCode = upperCustomerCode;
    }

    public String getUpSourcePlatform() {
        return upSourcePlatform;
    }

    public void setUpSourcePlatform(String upSourcePlatform) {
        this.upSourcePlatform = upSourcePlatform;
    }

    public String getContractCustomerCode() {
        return contractCustomerCode;
    }

    public void setContractCustomerCode(String contractCustomerCode) {
        this.contractCustomerCode = contractCustomerCode;
    }

    public String getConfirmCt() {
        return confirmCt;
    }

    public void setConfirmCt(String confirmCt) {
        this.confirmCt = confirmCt;
    }

    public String getDispatchNo() {
        return dispatchNo;
    }

    public void setDispatchNo(String dispatchNo) {
        this.dispatchNo = dispatchNo;
    }

    public String getPrinted() {
        return printed;
    }

    public void setPrinted(String printed) {
        this.printed = printed;
    }

    public String getProjectclassify() {
        return projectclassify;
    }

    public void setProjectclassify(String projectclassify) {
        this.projectclassify = projectclassify;
    }

    public String getTotalpackageqty() {
        return totalpackageqty;
    }

    public void setTotalpackageqty(String totalpackageqty) {
        this.totalpackageqty = totalpackageqty;
    }

    public String getCarryService() {
        return carryService;
    }

    public void setCarryService(String carryService) {
        this.carryService = carryService;
    }

    public String getCarryServiceName() {
        return carryServiceName;
    }

    public void setCarryServiceName(String carryServiceName) {
        this.carryServiceName = carryServiceName;
    }

    public String getExtBuisType() {
        return extBuisType;
    }

    public void setExtBuisType(String extBuisType) {
        this.extBuisType = extBuisType;
    }

    public String getWaveId() {
        return waveId;
    }

    public void setWaveId(String waveId) {
        this.waveId = waveId;
    }

    public boolean isWave() {
        return isWave;
    }

    public void setWave(boolean wave) {
        isWave = wave;
    }
}
