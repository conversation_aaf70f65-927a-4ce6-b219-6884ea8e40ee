package com.midea.prestorage.function.pick

import android.content.Context
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R


class OutCancelDetailAdapter(val mContext: Context, data: MutableList<OutCancelDetailHelp>?) :
    BaseMultiItemQuickAdapter<OutCancelDetailHelp, BaseViewHolder>(data) {
    var originData: MutableList<OutCancelDetailHelp>? = null

    init {
        addItemType(0, R.layout.item_order_out_pool_cancel_title)
        addItemType(1, R.layout.item_order_out_pool_cancel_child)

        addChildClickViewIds(
            R.id.tv_cancel
        )
    }

    fun setOriginDate(data: MutableList<OutCancelDetailHelp>?) {
        originData = data
        closeAll()
    }

    override fun convert(holder: BaseViewHolder, item: OutCancelDetailHelp) {
        if (item.itemType == 0) {
            val title = item.title
            holder.setText(R.id.tv_tk_order, title.shipmentCode)
            holder.setText(R.id.tv_customer_order, title.custOrderCode)
            holder.setText(R.id.tv_order_type, title.shipmentTypeStr)
            holder.setText(R.id.tv_num, title.totalQty.toString())
            holder.setText(R.id.tv_customer_address, title.shipToAddress)
            holder.setText(R.id.tv_customer, title.ownerName)
            holder.setText(R.id.tv_status, title.statusStr)

            val tvStatue = holder.itemView.findViewById<TextView>(R.id.tv_status)
            if (title.status == "300") {
                tvStatue.setTextColor(ContextCompat.getColor(mContext, R.color.btn_orange))
            } else {
                tvStatue.setTextColor(ContextCompat.getColor(mContext, R.color.colorGreen))
            }

            holder.itemView.findViewById<View>(R.id.ll_bottom)?.setOnClickListener {
                title.isExpand = !title.isExpand

                val results =
                    originData?.filter { item -> item.child == null || item.child.parentBean.isExpand }
                setNewInstance(results?.toMutableList())

                notifyDataSetChanged()
            }

            val arrow = holder.itemView.findViewById<View>(R.id.img_arrow)
            if (title.isExpand) {
                arrow.rotation = 180F
                holder.setText(R.id.tv_child, "收起")
            } else {
                arrow.rotation = 0F
                holder.setText(R.id.tv_child, "展开")
            }
            if (title.isCancel) {
                holder.setBackgroundResource(R.id.tv_cancel, R.drawable.bg_bt_gray)
                holder.setEnabled(R.id.tv_cancel, false)
            } else {
                holder.setBackgroundResource(R.id.tv_cancel, R.drawable.bg_bt_orange)
                holder.setEnabled(R.id.tv_cancel, true)
            }
        } else if (item.itemType == 1) {
            val child = item.child
            holder.setText(R.id.tv_num, child.planQty.toString())
            holder.setText(R.id.tv_unit, child.unit)
            holder.setText(R.id.tv_goods_code, child.custItemCode)
            holder.setText(R.id.tv_goods, child.itemName)
            if (!child.lotAtt04.isNullOrBlank()) {
                holder.setText(R.id.tv_goods_status, "(" + child.lotAtt04Str + ")")
            }
        }
    }

    private fun closeAll() {
        val results =
            originData?.filter { item -> item.child == null || item.child.parentBean.isExpand }
        setNewInstance(results?.toMutableList())

        notifyDataSetChanged()
    }
}