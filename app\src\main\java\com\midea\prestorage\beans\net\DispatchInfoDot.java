package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.utils.AppUtils;

import java.math.BigDecimal;

public class DispatchInfoDot {
    @ShowAnnotation
    private int serialNo;
    //纸箱编号
    @ShowAnnotation
    private String shipContainerCode;
    //总数量
    @ShowAnnotation
    private BigDecimal totalQty;
    //状态
    @ShowAnnotation
    private String statusStr;
    //包装类型
    @ShowAnnotation
    private String packageType;

    private Integer status;

    public int getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(int serialNo) {
        this.serialNo = serialNo;
    }

    public String getTotalQty() {
        return AppUtils.getBigDecimalValue(totalQty).toPlainString();
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getPackageType() {
        if ("CS".equals(packageType)) {
            return "箱拣";
        } else if ("EA".equals(packageType)) {
            return "拆零";
        }
        return null;
    }

    public void setPackageType(String packageType) {
        this.packageType = packageType;
    }

    public String getShipContainerCode() {
        return shipContainerCode;
    }

    public void setShipContainerCode(String shipContainerCode) {
        this.shipContainerCode = shipContainerCode;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }
}