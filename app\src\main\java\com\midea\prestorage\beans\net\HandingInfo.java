package com.midea.prestorage.beans.net;

import com.midea.prestorage.beans.base.BaseItemShowInfo;

import java.io.Serializable;
import java.util.List;

public class HandingInfo implements Serializable {
    private List<HandingList> list;

    public List<HandingList> getList() {
        return list;
    }

    public void setList(List<HandingList> list) {
        this.list = list;
    }

    public static class HandingList extends BaseItemShowInfo {
        private String createTime;
        private String remark;
        private String pageSize;
        private String jobId;
        private String whCode;
        private String handlingCode;
        private String handlingName;
        private String handlingType;
        private String handlingLeader;
        private String handlingTel;
        private String handlingNumber;
        private String handlingStatus;
        private String recStatus;
        private String orgId;
        private String siteCode;
        private String companyCode;

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getPageSize() {
            return pageSize;
        }

        public void setPageSize(String pageSize) {
            this.pageSize = pageSize;
        }

        public String getJobId() {
            return jobId;
        }

        public void setJobId(String jobId) {
            this.jobId = jobId;
        }

        public String getWhCode() {
            return whCode;
        }

        public void setWhCode(String whCode) {
            this.whCode = whCode;
        }

        public String getHandlingCode() {
            return handlingCode;
        }

        public void setHandlingCode(String handlingCode) {
            this.handlingCode = handlingCode;
        }

        public String getHandlingName() {
            return handlingName;
        }

        public void setHandlingName(String handlingName) {
            this.handlingName = handlingName;
        }

        public String getHandlingType() {
            return handlingType;
        }

        public void setHandlingType(String handlingType) {
            this.handlingType = handlingType;
        }

        public String getHandlingLeader() {
            return handlingLeader;
        }

        public void setHandlingLeader(String handlingLeader) {
            this.handlingLeader = handlingLeader;
        }

        public String getHandlingTel() {
            return handlingTel;
        }

        public void setHandlingTel(String handlingTel) {
            this.handlingTel = handlingTel;
        }

        public String getHandlingNumber() {
            return handlingNumber;
        }

        public void setHandlingNumber(String handlingNumber) {
            this.handlingNumber = handlingNumber;
        }

        public String getHandlingStatus() {
            return handlingStatus;
        }

        public void setHandlingStatus(String handlingStatus) {
            this.handlingStatus = handlingStatus;
        }

        public String getRecStatus() {
            return recStatus;
        }

        public void setRecStatus(String recStatus) {
            this.recStatus = recStatus;
        }

        public String getOrgId() {
            return orgId;
        }

        public void setOrgId(String orgId) {
            this.orgId = orgId;
        }

        public String getSiteCode() {
            return siteCode;
        }

        public void setSiteCode(String siteCode) {
            this.siteCode = siteCode;
        }

        public String getCompanyCode() {
            return companyCode;
        }

        public void setCompanyCode(String companyCode) {
            this.companyCode = companyCode;
        }
    }
}
