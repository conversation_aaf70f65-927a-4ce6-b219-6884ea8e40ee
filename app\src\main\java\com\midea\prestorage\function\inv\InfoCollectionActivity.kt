package com.midea.prestorage.function.inv

import android.os.Bundle
import android.view.View
import android.widget.EditText
import androidx.core.widget.addTextChangedListener
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.function.inv.response.RespMaterial
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.outstorage.dialog.GoodsInfoChangesDialog
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.isNull
import com.midea.prestorage.widgets.FilterDigitTextWatcher
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityInfoCollectionBinding
import java.math.BigDecimal

class InfoCollectionActivity : BaseViewModelActivity<InfoCollectionVM>() {
    private lateinit var binding: ActivityInfoCollectionUnionBinding
    private lateinit var changesDialog: GoodsInfoChangesDialog
    var ipTextWatcher: FilterDigitTextWatcher? = null
    var eaTextWatcher: FilterDigitTextWatcher? = null

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityInfoCollectionUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_info_collection_care
                )
            )
        } else {
            ActivityInfoCollectionUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_info_collection
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        vm = ViewModelProvider.AndroidViewModelFactory(application)
            .create(InfoCollectionVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        vm.mSubmitNotification.observe(this, Observer<Boolean> {
            if (it) {
                changesDialog.setContent("是否确定要修改？")
                changesDialog.show()
            }
        })

        initWatcher()
        initSpinner()
        initListener()
        initDialog()
        initData()

        vm.initReceiptOrderType()
    }

    private fun initWatcher() {
        if (ipTextWatcher == null) {
            ipTextWatcher =
                FilterDigitTextWatcher(binding.etIpQty, 0, true) {
                    if ("只能输入正整数" == it) {
                        binding.etIpQty.setText(
                            binding.etIpQty.text.toString().replace(Regex("[^0-9]"), "")
                        )
                        if (!binding.etIpQty.text.isNullOrEmpty()) {
                            binding.etIpQty.setSelection(binding.etIpQty.text!!.length)
                        }
                    }
                    AppUtils.showToast(this, it)
                }
        }

        if (eaTextWatcher == null) {
            eaTextWatcher =
                FilterDigitTextWatcher(binding.etEaQty, 0, true) {
                    if ("只能输入正整数" == it) {
                        binding.etEaQty.setText(
                            binding.etEaQty.text.toString().replace(Regex("[^0-9]"), "")
                        )
                        if (!binding.etEaQty.text.isNullOrEmpty()) {
                            binding.etEaQty.setSelection(binding.etEaQty.text!!.length)
                        }
                    }
                    AppUtils.showToast(this, it)
                }
        }
    }

    private fun setWatcher(isPoint: Int) {
        binding.etIpQty.removeTextChangedListener(ipTextWatcher)
        if (isPoint != 0) {
            ipTextWatcher?.limitDecimalPlaces = 4
        }else {
            ipTextWatcher?.limitDecimalPlaces = 0
        }
        binding.etIpQty.addTextChangedListener(ipTextWatcher)

        binding.etEaQty.removeTextChangedListener(eaTextWatcher)
        if (isPoint != 0) {
            eaTextWatcher?.limitDecimalPlaces = 4
        }else {
            eaTextWatcher?.limitDecimalPlaces = 0
        }
        binding.etEaQty.addTextChangedListener(eaTextWatcher)
    }

    fun initData() {
        vm.respMaterial =
            intent.getSerializableExtra("RespFuInvLocationInventory") as RespMaterial

        vm.respMaterial?.let {
            vm.isPoint.value = it.cdcmIsDecimal
            vm.cdcmMaterialNo.set(it.cdcmMaterialNo ?: "")
            vm.custItemCode.set(it.cdcmCustMaterialNo ?: "")
            vm.cdcmCustomerName.set(it.cdcmCustomerName ?: "")
            vm.itemName.set(it.cdcmNameCn ?: "")
            vm.itemLength.set(AppUtils.getBigDecimalValueStrNull(it.cdcmLength))
            vm.itemWidth.set(AppUtils.getBigDecimalValueStrNull(it.cdcmWidth))
            vm.itemHeight.set(AppUtils.getBigDecimalValueStrNull(it.cdcmHeight))
            vm.itemVolume.set(AppUtils.getBigDecimalValueStrNull(it.cdcmCube))
            vm.itemRoughWeight.set(AppUtils.getBigDecimalValueStrNull(it.cdcmWeight))
            vm.item69Code.set(it.cdcmBarcode69 ?: "")
            vm.itemIp69Code.set(it.ipBarcode69 ?: "")
            vm.itemCs69Code.set(it.csBarcode69 ?: "")
            vm.itemMax69Code.set(it.otBarcode69 ?: "")
            vm.layerQty.set(AppUtils.getBigDecimalValueStrNull(it.layerQty))
            vm.stackLevel.set(AppUtils.getBigDecimalValueStrNull(it.stackLevel))
            if ("N" == it.cdcmIsValidity) {
                vm.validityControl.set("否")
                binding.spinnerExpiryDate.selectedIndex = 1
                binding.llExpiryDate.visibility = View.INVISIBLE
            } else {
                vm.validityControl.set("是")
                binding.spinnerExpiryDate.selectedIndex = 0
                binding.llExpiryDate.visibility = View.VISIBLE
            }
            vm.itemGoodsExpiryDate.set(AppUtils.getBigDecimalValueStrNull(it.cdcmPeriodOfValidity))
            when (it.cdcmValidityUnit) {
                "YEAR" -> {
                    binding.spinnerExpiryDateUnit.selectedIndex = 0
                    vm.validityUnit.set("年")
                    vm.goodsExpiryDate = 365
                }
                "MONTH" -> {
                    binding.spinnerExpiryDateUnit.selectedIndex = 1
                    vm.validityUnit.set("月")
                    vm.goodsExpiryDate = 30
                }
                "WEEK" -> {
                    binding.spinnerExpiryDateUnit.selectedIndex = 2
                    vm.validityUnit.set("周")
                    vm.goodsExpiryDate = 7
                }
                else -> {
                    binding.spinnerExpiryDateUnit.selectedIndex = 3
                    vm.validityUnit.set("天")
                    vm.goodsExpiryDate = 1
                }
            }
            vm.ipQty.set(AppUtils.getBigDecimalValueStrNull(it.ipUnitQty))
            vm.eaQty.set(AppUtils.getBigDecimalValueStrNull(it.eaUnitQty))
            vm.csUnitDesc.set(it.csUnitDesc ?: "")
            vm.ipUnitDesc.set(it.ipUnitDesc ?: "")
            vm.eaUnitDesc.set(it.eaUnitDesc ?: "")
            vm.cdpaFormat.set(it.cdpaFormat ?: "")
            binding.addSwitch.isChecked = !it.ipUnitQty.isNull()
            binding.rbA.isChecked = it.ipUnitQty.isNull()
            binding.rbB.isChecked = !it.ipUnitQty.isNull()
            if (binding.addSwitch.isChecked || binding.rbA.isChecked) {
                binding.etIpQty.visibility = View.VISIBLE
                binding.flIp.visibility = View.VISIBLE
                vm.isOpenIp = true
            } else {
                binding.etIpQty.visibility = View.GONE
                binding.flIp.visibility = View.GONE
                vm.isOpenIp = false
            }

        }
    }

    fun initSpinner() {

        val expiryDateBeans = mutableListOf<String>(
            "是",
            "否"
        )
        binding.spinnerExpiryDate.setItems(expiryDateBeans)
        binding.spinnerExpiryDate.selectedIndex = 0

        binding.spinnerExpiryDate.setOnItemSelectedListener { _, position, _, _ ->
            vm.validityControl.set(expiryDateBeans[position])
            if (position == 0) {
                binding.llExpiryDate.visibility = View.VISIBLE
            } else {
                binding.llExpiryDate.visibility = View.INVISIBLE
            }
        }

        val expiryDateUnitBeans = mutableListOf<String>(
            "年",
            "月",
            "周",
            "天"
        )
        binding.spinnerExpiryDateUnit.setItems(expiryDateUnitBeans)
        binding.spinnerExpiryDateUnit.selectedIndex = 3

        binding.spinnerExpiryDateUnit.setOnItemSelectedListener { _, position, _, _ ->
            vm.validityUnit.set(expiryDateUnitBeans[position])
            when (position) {//效期单位代表的天数,年：365天；月：30天；周：7天，日：1天
                0 -> {
                    vm.goodsExpiryDate = 365
                }
                1 -> {
                    vm.goodsExpiryDate = 30
                }
                2 -> {
                    vm.goodsExpiryDate = 7
                }
                3 -> {
                    vm.goodsExpiryDate = 1
                }
            }
        }
    }

    private fun initDialog() {
        //信息如有改动时弹出的提示
        changesDialog = GoodsInfoChangesDialog(this)
        changesDialog.setTitle("提示")
        changesDialog.setConfirmBack(object : GoodsInfoChangesDialog.ConfirmBack {
            override fun cancelBack() {
                changesDialog.dismiss()
            }

            override fun confirmBack() {
                changesDialog.dismiss()
                vm.materialUpdate()
            }

        })
    }

    private fun initListener() {
        createLWHWatcher(binding.edItemLength)
        createLWHWatcher(binding.edItemWidth)
        createLWHWatcher(binding.edItemHeight)

        createCdpaFormatWatcher(binding.etIpQty)
        createCdpaFormatWatcher(binding.etEaQty)

        vm.inOrderType.observe(this, Observer<MutableList<DCBean>> { result ->
            val beans = result.map { it.value.toString() }

            binding.spinnerCs.setItems(beans)
            binding.spinnerCs.apply {
                selectedIndex = beans.indexOf(vm.respMaterial?.csUnitDesc).takeIf { it != -1 } ?: 0
            }
            binding.spinnerCs.setOnItemSelectedListener { _, position, _, _ ->
                vm.csUnitDesc.set(beans[position])
                vm.cdpaFormat.set(vm.setCdpaFormat())
            }

            binding.spinnerIp.setItems(beans)
            binding.spinnerIp.apply {
                selectedIndex = beans.indexOf(vm.respMaterial?.ipUnitDesc).takeIf { it != -1 } ?: 0
            }
            binding.spinnerIp.setOnItemSelectedListener { _, position, _, _ ->
                vm.ipUnitDesc.set(beans[position])
                vm.cdpaFormat.set(vm.setCdpaFormat())
            }

            binding.spinnerEa.setItems(beans)
            binding.spinnerEa.apply {
                selectedIndex = beans.indexOf(vm.respMaterial?.eaUnitDesc).takeIf { it != -1 } ?: 0
            }
            binding.spinnerEa.setOnItemSelectedListener { _, position, _, _ ->
                vm.eaUnitDesc.set(beans[position])
                vm.cdpaFormat.set(vm.setCdpaFormat())
            }

            vm.csUnitDesc.set(vm.respMaterial?.csUnitDesc ?: "")
            vm.ipUnitDesc.set(vm.respMaterial?.ipUnitDesc ?: "")
            vm.eaUnitDesc.set(vm.respMaterial?.eaUnitDesc ?: "")

        })

        binding.addSwitch.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.etIpQty.visibility = View.VISIBLE
                binding.flIp.visibility = View.VISIBLE
                vm.isOpenIp = true
            } else {
                binding.etIpQty.visibility = View.GONE
                binding.flIp.visibility = View.GONE
                vm.isOpenIp = false
            }
            vm.cdpaFormat.set(vm.setCdpaFormat())
        }

        binding.rg.setOnCheckedChangeListener { group, checkedId ->
            if (checkedId == R.id.rb_a) {
                binding.etIpQty.visibility = View.GONE
                binding.flIp.visibility = View.GONE
                vm.isOpenIp = false
            } else {
                binding.etIpQty.visibility = View.VISIBLE
                binding.flIp.visibility = View.VISIBLE
                vm.isOpenIp = true
            }
            vm.cdpaFormat.set(vm.setCdpaFormat())
        }

        vm.isPoint.observe(this, {
            if (it != 9999) {
                setWatcher(it)
            }
        })
    }

    private fun createLWHWatcher(view: EditText) {
        view.addTextChangedListener {
            vm.calculateVolume()
        }
    }

    private fun createCdpaFormatWatcher(view: EditText) {
        view.addTextChangedListener {
            vm.cdpaFormat.set(vm.setCdpaFormat())
        }
    }

}