package com.midea.prestorage.function.inv

import android.app.Application
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.SerialPickContainerInfo
import com.midea.prestorage.function.inv.response.RespFuInvLocationInventory
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.RoundingMode

class GoodsInfoChangesVM(application: Application) : BaseViewModel(application) {

    var needScanCode = ObservableField("否")
    var validityControl = ObservableField("是")
    var validityUnit = ObservableField("")
    val itemCustItemCodeVM = GoodsInfoVM("客户货品编码:", false, "", 0)
    val item69CodeVM = GoodsInfoVM("69码:", false, "请输入69码", 0)
    val itemCs69CodeVM = GoodsInfoVM("箱69码:", false, "请输入箱69码", 0)
    val itemMax69CodeVM = GoodsInfoVM("最大包装69码:", false, "请输入最大包装69码", 0)
    val itemLengthVM = GoodsInfoVM("长(mm):", false, "请输入大于零的数字", 2)
    val itemWidthVM = GoodsInfoVM("宽(mm):", false, "请输入大于零的数字", 2)
    val itemHeightVM = GoodsInfoVM("高(mm):", false, "请输入大于零的数字", 2)
    val itemVolumeVM = GoodsInfoVM("体积(m³):", true, "请输入大于零的数字", 6)
    val itemRoughWeightVM = GoodsInfoVM("毛重(KG):", false, "请输入大于等于零的数字", 2)
    val itemNetWeightVM = GoodsInfoVM("净重(KG):", false, "请输入大于等于零的数字", 2)
    val itemGoodsExpiryDateVM = GoodsInfoVM("商品效期:", true, "请输入大于零的整数", 0)
    val itemInPercentageVM = GoodsInfoVM("入库效期百分比:", false, "请输入大于等于零的数字", 2)
    val itemOutPercentageVM = GoodsInfoVM("出库效期百分比:", false, "请输入大于等于零的数字", 2)
    val itemInDateVM = GoodsInfoVM("入库效期(天):", true, "请输入大于等于零的整数", 0)
    val itemOutDateVM = GoodsInfoVM("出库效期(天):", true, "请输入大于等于零的整数", 0)
    val itemWarningDateVM = GoodsInfoVM("预警天数(提前):", false, "请输入大于零的整数", 0)
    val itemSoldOutDateVM = GoodsInfoVM("下架天数(提前):", false, "请输入大于零的整数", 0)
    var goodsExpiryDate = 1
    var mBackNotification = MutableLiveData(false)
    var respFuInvLocationInventory: RespFuInvLocationInventory? = null
    var itemGoodsExpiryDate = ""
    var itemInPercentage = ""
    var itemOutPercentage = ""
    var itemInDate = ""
    var itemOutDate = ""
    var itemWarningDate = ""
    var itemSoldOutDate = ""

    override fun init() {
        itemCustItemCodeVM.editable.set(false)
    }

    companion object {
        const val YES_CN = "是"
        const val NO_CN = "否"
        const val DATE_YEAR = "年"
        const val DATE_MONTH = "月"
        const val DATE_WEEK = "周"
        const val DATE_DAY = "日"
    }

    /**
     * 当“是否效期管理”选择否时，该字段后的字段均不可编辑或不展示
     */
    fun setEditable(type: Boolean) {
        itemGoodsExpiryDateVM.editable.set(type)
        itemInPercentageVM.editable.set(type)
        itemOutPercentageVM.editable.set(type)
        if (!type) {
            setItemInDateEditable(type)
            setItemOutDateEditable(type)
        } else {
            if (itemInPercentageVM.content.get().isNullOrEmpty()) {
                setItemInDateEditable(type)
            } else {
                setItemInDateEditable(false)
            }
            if (itemOutPercentageVM.content.get().isNullOrEmpty()) {
                setItemOutDateEditable(type)
            } else {
                setItemOutDateEditable(false)
            }
        }
        itemWarningDateVM.editable.set(type)
        itemSoldOutDateVM.editable.set(type)
    }

    fun setItemInDateEditable(type: Boolean) {
        if (type) {
            if (checkValidity()) {
                itemInDateVM.editable.set(type)
            } else {
                itemInDateVM.editable.set(false)
            }
        } else {
            itemInDateVM.editable.set(type)
        }
    }

    fun setItemOutDateEditable(type: Boolean) {
        if (type) {
            if (checkValidity()) {
                itemOutDateVM.editable.set(type)
            } else {
                itemOutDateVM.editable.set(false)
            }
        } else {
            itemOutDateVM.editable.set(type)
        }
    }

    fun calculateVolume() {
        val length = itemLengthVM.content.get().toString().toDoubleOrNull()
        val width = itemWidthVM.content.get().toString().toDoubleOrNull()
        val height = itemHeightVM.content.get().toString().toDoubleOrNull()

        if (length != null && width != null && height != null) {
            val volume = length * width * height / 1000000000 // 将毫米转换为立方米
            itemVolumeVM.content.set(
                volume.toBigDecimal().setScale(6, RoundingMode.HALF_UP).toDouble().toString()
            ) // 保留6位小数
        } else {
            itemVolumeVM.content.set("")
        }
    }

    fun toCancel() {
        if (CheckUtil.isFastDoubleClick()) {
            back()
        }
    }

    fun toSave() {
        if (CheckUtil.isFastDoubleClick()) {
            startSave(true)
        }
    }

    fun startSave(needBack: Boolean = false) {

        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            if (checkValidity()) {
                if (itemGoodsExpiryDateVM.content.get().toString().trim().isBlank()) {
                    showNotification("商品效期不能为空", false)
                    return@launch
                }
                if (itemInDateVM.content.get().toString().trim().isBlank()) {
                    showNotification("入库效期不能为空", false)
                    return@launch
                }
                if (itemOutDateVM.content.get().toString().trim().isBlank()) {
                    showNotification("出库效期不能为空", false)
                    return@launch
                }
            }

            val param = mutableMapOf(
                "whCode" to Constants.whInfo?.whCode.toString(),
                "itemCode" to respFuInvLocationInventory?.itemCode,
                "custItemCode" to respFuInvLocationInventory?.custItemCode,
                "whBarcode69" to item69CodeVM.content.get().toString().trim(),
                "whCsBarcode69" to itemCs69CodeVM.content.get().toString().trim(),
                "whMaxBarcode69" to itemMax69CodeVM.content.get().toString().trim(),
                "isNeedScan69" to if (checkScan69()) 1 else 0,
                "isValidity" to if (checkValidity()) "Y" else "N",
                "periodOfValidity" to if (checkValidity()) itemGoodsExpiryDateVM.content.get()
                    .toString().trim() else "",
                "validityUnit" to if (checkValidity()) getValidityUnitDateStr() else "",
                "inLifePercentage" to if (checkValidity()) itemInPercentageVM.content.get()
                    .toString().trim() else "",
                "outLifePercentage" to if (checkValidity()) itemOutPercentageVM.content.get()
                    .toString().trim() else "",
                "inLifeDays" to if (checkValidity()) itemInDateVM.content.get().toString()
                    .trim() else "",
                "outLifeDays" to if (checkValidity()) itemOutDateVM.content.get().toString()
                    .trim() else "",
                "earlyWarning" to if (checkValidity()) itemWarningDateVM.content.get().toString()
                    .trim() else "",
                "earlyTakedown" to if (checkValidity()) itemSoldOutDateVM.content.get().toString()
                    .trim() else ""
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getInventoryAPI().whItemConfigUpdate(requestBody)
            }

            if (result.code == 0L) {
                if (needBack) {
                    back()
                } else {
                    showNotification("保存成功", true)
                    if (validityUnit.get().isNullOrEmpty()) {
                        validityUnit.set("日")
                    }
                    updateInventory()
                }
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    fun toBack() {
        mBackNotification.value = true
    }

    private fun updateInventory() {
        respFuInvLocationInventory?.let {
            it.whBarcode69 = item69CodeVM.content.get()
            it.whCsBarcode69 = itemCs69CodeVM.content.get()
            it.whMaxBarcode69 = itemMax69CodeVM.content.get()
            it.isNeedScan69 = if (NO_CN == needScanCode.get()) 0 else 1
            it.isValidity = if (NO_CN == validityControl.get()) "n" else "y"
            it.periodOfValidity =
                AppUtils.getBigDecimalValueNull(itemGoodsExpiryDateVM.content.get())
            it.validityUnit =
                if (DATE_YEAR == validityUnit.get()) "y" else if (DATE_MONTH == validityUnit.get()) "m" else if (DATE_WEEK == validityUnit.get()) "w" else "d"
            it.inLifePercentage = itemInPercentageVM.content.get()
            it.outLifePercentage = itemOutPercentageVM.content.get()
            it.inLifeDays = AppUtils.getBigDecimalValueNull(itemInDateVM.content.get())
            it.outLifeDays = AppUtils.getBigDecimalValueNull(itemOutDateVM.content.get())
            it.earlyWarning = AppUtils.getBigDecimalValueNull(itemWarningDateVM.content.get())
            it.earlyTakedown = AppUtils.getBigDecimalValueNull(itemSoldOutDateVM.content.get())
        }

    }

    // 用于记录界面信息是否被修改
    fun isModified(): Boolean {
        var changeCount =
            checkModified(respFuInvLocationInventory?.whBarcode69, item69CodeVM.content.get())
        changeCount = changeCount or checkModified(
            respFuInvLocationInventory?.whCsBarcode69,
            itemCs69CodeVM.content.get()
        )
        changeCount = changeCount or checkModified(
            respFuInvLocationInventory?.whMaxBarcode69,
            itemMax69CodeVM.content.get()
        )
        changeCount = changeCount or checkModified(
            respFuInvLocationInventory?.getNeedScan69(),
            needScanCode.get()
        )
        changeCount = changeCount or checkModified(
            respFuInvLocationInventory?.getValidity(),
            validityControl.get()
        )

        if (checkValidity()) {
            changeCount = changeCount or checkModified(
                AppUtils.getBigDecimalValueStrNull(respFuInvLocationInventory?.periodOfValidity),
                itemGoodsExpiryDateVM.content.get()
            )
            changeCount = changeCount or checkModified(
                respFuInvLocationInventory?.getUnit(),
                validityUnit.get()
            )
            changeCount = changeCount or checkModified(
                respFuInvLocationInventory?.inLifePercentage,
                itemInPercentageVM.content.get()
            )
            changeCount = changeCount or checkModified(
                respFuInvLocationInventory?.outLifePercentage,
                itemOutPercentageVM.content.get()
            )
            changeCount = changeCount or checkModified(
                AppUtils.getBigDecimalValueStrNull(respFuInvLocationInventory?.inLifeDays),
                itemInDateVM.content.get()
            )
            changeCount = changeCount or checkModified(
                AppUtils.getBigDecimalValueStrNull(respFuInvLocationInventory?.outLifeDays),
                itemOutDateVM.content.get()
            )
            changeCount = changeCount or checkModified(
                AppUtils.getBigDecimalValueStrNull(respFuInvLocationInventory?.earlyWarning),
                itemWarningDateVM.content.get()
            )
            changeCount = changeCount or checkModified(
                AppUtils.getBigDecimalValueStrNull(respFuInvLocationInventory?.earlyTakedown),
                itemSoldOutDateVM.content.get()
            )
        }

        return changeCount
    }

    private fun checkModified(oldValue: String?, newValue: String?): Boolean {

        if (oldValue.isNullOrEmpty() && newValue.isNullOrEmpty()) {
            return false
        }

        if (!oldValue.isNullOrEmpty()) {
            return oldValue != newValue
        }
        if (!newValue.isNullOrEmpty()) {
            return newValue != oldValue
        }

        return newValue != oldValue
    }

    private fun checkScan69(): Boolean {
        return YES_CN == needScanCode.get()
    }

    private fun checkValidity(): Boolean {
        return YES_CN == validityControl.get()
    }

    private fun getValidityUnitDateStr(): String {
        return if (DATE_YEAR == validityUnit.get()) "Y" else if (DATE_MONTH == validityUnit.get()) "M" else if (DATE_WEEK == validityUnit.get()) "W" else "D"
    }

    fun setExpiryDateData() {
        itemGoodsExpiryDateVM.content.set(itemGoodsExpiryDate)
        itemInPercentageVM.content.set(itemInPercentage)
        itemOutPercentageVM.content.set(itemOutPercentage)
        itemInDateVM.content.set(itemInDate)
        itemOutDateVM.content.set(itemOutDate)
        itemWarningDateVM.content.set(itemWarningDate)
        itemSoldOutDateVM.content.set(itemSoldOutDate)
    }

    fun getExpiryDateData() {
        itemGoodsExpiryDate = itemGoodsExpiryDateVM.content.get().toString()
        itemInPercentage = itemInPercentageVM.content.get().toString()
        itemOutPercentage = itemOutPercentageVM.content.get().toString()
        itemInDate = itemInDateVM.content.get().toString()
        itemOutDate = itemOutDateVM.content.get().toString()
        itemWarningDate = itemWarningDateVM.content.get().toString()
        itemSoldOutDate = itemSoldOutDateVM.content.get().toString()

        //保存完数据把输入框数据清空
        clearExpiryDateData()
    }

    fun clearExpiryDateData() {
        itemGoodsExpiryDateVM.content.set("")
        itemInPercentageVM.content.set("")
        itemOutPercentageVM.content.set("")
        itemInDateVM.content.set("")
        itemOutDateVM.content.set("")
        itemWarningDateVM.content.set("")
        itemSoldOutDateVM.content.set("")
    }
}