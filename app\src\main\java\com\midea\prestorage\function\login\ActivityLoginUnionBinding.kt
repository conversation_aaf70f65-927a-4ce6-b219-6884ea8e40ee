package com.midea.prestorage.function.login

import android.widget.TextView
import androidx.appcompat.widget.AppCompatCheckBox
import androidx.appcompat.widget.AppCompatImageView
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.databinding.ActivityLoginBinding
import com.midea.prestoragesaas.databinding.ActivityLoginCareBinding

sealed class ActivityLoginUnionBinding {
    abstract var vm: LoginVM?
    abstract val cbAgreement: AppCompatCheckBox
    abstract val spinnerTenantCode: MaterialSpinner
    abstract val spinnerServer: MaterialSpinner
    abstract val viewPager: ViewPager2
    abstract val tlLogin: TabLayout
    abstract val tvNotification: TextView
    abstract val ivLogo: AppCompatImageView

    class V2(val binding: ActivityLoginCareBinding) : ActivityLoginUnionBinding() {
        override var vm: LoginVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val cbAgreement = binding.cbAgreement
        override val spinnerTenantCode = binding.spinnerTenantCode
        override val spinnerServer = binding.spinnerServer
        override val viewPager = binding.viewPager
        override val tlLogin = binding.tlLogin
        override val tvNotification = binding.tvNotification
        override val ivLogo = binding.ivLogo
    }

    class V1(val binding: ActivityLoginBinding) : ActivityLoginUnionBinding() {
        override var vm: LoginVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val cbAgreement = binding.cbAgreement
        override val spinnerTenantCode = binding.spinnerTenantCode
        override val spinnerServer = binding.spinnerServer
        override val viewPager = binding.viewPager
        override val tlLogin = binding.tlLogin
        override val tvNotification = binding.tvNotification
        override val ivLogo = binding.ivLogo
    }
}
