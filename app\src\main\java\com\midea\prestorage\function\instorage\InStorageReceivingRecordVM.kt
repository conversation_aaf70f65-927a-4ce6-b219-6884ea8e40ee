package com.midea.prestorage.function.instorage

import android.app.Application
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.function.instorage.response.RespInReceiptRecord
import com.midea.prestorage.function.inv.response.InReceiptOrder
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody

class InStorageReceivingRecordVM(application: Application) : BaseViewModel(application) {
    var finishActivity = MutableLiveData(false)
    var curOrderNo = MutableLiveData<String>("") // 单号 (入库单号或波次单号)
    //单号类型
    var curOrderReceiveType = ObservableField<String>("receipt")   // receipt 入库单  wave波次单
    val isRefreshing = MutableLiveData(false)
    val isNoData = MutableLiveData(false)
    var showDatas = MutableLiveData<MutableList<RespInReceiptRecord>>()
    var loadMoreDatas = MutableLiveData<MutableList<RespInReceiptRecord>>()
    val loadMoreComplete = MutableLiveData(0)

    // 当前页码
    var pageNo = 1

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        isRefreshing.value = true
        pageNo = 1
        initOrderList()
    }

    override fun init() {
        onRefreshCommand.onRefresh()
    }

    fun loadMore() {
        initOrderList(true)
    }

    fun initOrderList(isLoadMore: Boolean = false) {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf<String, Any>()
            param["pageNo"] = pageNo
            param["pageSize"] = 10

            // 请求参数  波次号或入库单号
            if (curOrderReceiveType.get().equals("wave")) {
                param["waveNo"] = curOrderNo.value.toString().trim() // 传波次
            } else if (curOrderReceiveType.get().equals("receipt")) {
                param["receiptCode"] = curOrderNo.value.toString().trim() //  传 入库单号
            }

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().inReceiptRecordPage(requestBody) }
                resp.await()
            }

            if(result.code == 0L) {
                isRefreshing.value = false
                if (result.data != null) {
                    if (isLoadMore) {
                        loadMoreDatas.value = result.data!!.list
                        loadMoreComplete.value = 1
                    } else {
                        showDatas.value = result.data!!.list
                    }
                }
                if (pageNo >= result.data?.totalPage!!) {
                    loadMoreComplete.value = 2
                } else {
                    pageNo = result.data?.pageNo!! + 1
                }
            }else {
                isRefreshing.value = false
            }
        }
    }
}