package com.midea.prestorage.function.outstorage

import android.widget.EditText
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestoragesaas.databinding.ActivityOutStorageBackTaskBinding
import com.midea.prestoragesaas.databinding.ActivityOutStorageBackTaskCareBinding

sealed class ActivityOutStorageBackTaskUnionBinding{
    abstract var vm: OutStorageBackTaskViewModel?
    abstract val clTitleLayout: ConstraintLayout
    abstract val etCustItemCode: EditText
    abstract val srl: SwipeRefreshLayout
    abstract val rv: RecyclerView
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: ActivityOutStorageBackTaskCareBinding) : ActivityOutStorageBackTaskUnionBinding() {
        override var vm: OutStorageBackTaskViewModel?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val clTitleLayout = binding.clTitleLayout
        override val etCustItemCode = binding.etCustItemCode
        override val srl = binding.srl
        override val rv = binding.rv
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: ActivityOutStorageBackTaskBinding) : ActivityOutStorageBackTaskUnionBinding() {
        override var vm: OutStorageBackTaskViewModel?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val clTitleLayout = binding.clTitleLayout
        override val etCustItemCode = binding.etCustItemCode
        override val srl = binding.srl
        override val rv = binding.rv
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
