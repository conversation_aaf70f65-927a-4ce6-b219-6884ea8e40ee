package com.midea.prestorage.function.planstock

import android.widget.EditText
import android.widget.RelativeLayout
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestoragesaas.databinding.ActivityPlanStockListBinding
import com.midea.prestoragesaas.databinding.ActivityPlanStockListCareBinding

sealed class ActivityPlanStockListUnionBinding{
    abstract var vm: PlanStockListVM?
    abstract val llTitleBar: RelativeLayout
    abstract val etSearchOrderNo: EditText
    abstract val srl: SwipeRefreshLayout
    abstract val recycle: RecyclerView
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: ActivityPlanStockListCareBinding) : ActivityPlanStockListUnionBinding() {
        override var vm: PlanStockListVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etSearchOrderNo = binding.etSearchOrderNo
        override val srl = binding.srl
        override val recycle = binding.recycle
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: ActivityPlanStockListBinding) : ActivityPlanStockListUnionBinding() {
        override var vm: PlanStockListVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etSearchOrderNo = binding.etSearchOrderNo
        override val srl = binding.srl
        override val recycle = binding.recycle
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
