package com.midea.prestorage.function.put

import android.os.Bundle
import android.util.Log
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestoragesaas.databinding.ActivityInStorageSettingBinding
import com.midea.prestorage.http.constants.Constants
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

class InStorageScanSettingActivity : BaseActivity() {

    private lateinit var binding: ActivityInStorageSettingBinding
    private lateinit var inStorageVM: InStorageScanSettingVM

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_in_storage_setting)
        inStorageVM = InStorageScanSettingVM(this)
        initView()
        binding.vm = inStorageVM
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    private fun initView() {
        if (Constants.userInfo?.scanPutMode == 0) {
            binding.rbQueue.isChecked = true
        } else {
            binding.rbAll.isChecked = true
        }
        binding.rgReceiveMode.setOnCheckedChangeListener { _, checkedId ->
            if (checkedId == R.id.rb_queue) {
                saveUserInfo(0)
            } else if (checkedId == R.id.rb_all) {
                saveUserInfo(1)
            }
        }
    }

    private fun saveUserInfo(scanPutMode: Int) {
        Observable.create<String> {
            try {
                Constants.userInfo?.scanPutMode = scanPutMode
                db.saveOrUpdate(Constants.userInfo)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(this, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }
}