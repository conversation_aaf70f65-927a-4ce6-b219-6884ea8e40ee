package com.midea.prestorage.beans.net;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


public class OutStorageScan implements Serializable {
    private String shipmentCode;
    private String receiptCode;
    private String waveNo;
    private String whCode;
    private String serialNo;
    private String custItemCode;
    private String itemName;
    private String barcode;
    private int qty;
    private BigDecimal totalQty;
    private int barcodeType;//为3 或4的时候 弹窗输入数量
    private String userdefined15;
    private String serialType;
    private String containerCode;
    private BigDecimal scanQty;
    private String waveScanQty;
    private List<String> containerCodes;
    private List<String> unScanItemCodes;
    private BigDecimal unScanQty;

    public String getShipmentCode() {
        return shipmentCode;
    }

    public void setShipmentCode(String shipmentCode) {
        this.shipmentCode = shipmentCode;
    }

    public String getReceiptCode() {
        return receiptCode;
    }

    public void setReceiptCode(String receiptCode) {
        this.receiptCode = receiptCode;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getUserdefined15() {
        return userdefined15;
    }

    public void setUserdefined15(String userdefined15) {
        this.userdefined15 = userdefined15;
    }

    public String getSerialType() {
        return serialType;
    }

    public void setSerialType(String serialType) {
        this.serialType = serialType;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public String getWaveScanQty() {
        return waveScanQty;
    }

    public void setWaveScanQty(String waveScanQty) {
        this.waveScanQty = waveScanQty;
    }

    public int getBarcodeType() {
        return barcodeType;
    }

    public void setBarcodeType(int barcodeType) {
        this.barcodeType = barcodeType;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public BigDecimal getUnScanQty() {
        return unScanQty;
    }

    public void setUnScanQty(BigDecimal unScanQty) {
        this.unScanQty = unScanQty;
    }

    public BigDecimal getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public BigDecimal getScanQty() {
        return scanQty;
    }

    public void setScanQty(BigDecimal scanQty) {
        this.scanQty = scanQty;
    }

    public List<String> getContainerCodes() {
        return containerCodes;
    }

    public void setContainerCodes(List<String> containerCodes) {
        this.containerCodes = containerCodes;
    }

    public List<String> getUnScanItemCodes() {
        return unScanItemCodes;
    }

    public void setUnScanItemCodes(List<String> unScanItemCodes) {
        this.unScanItemCodes = unScanItemCodes;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public int getQty() {
        return qty;
    }

    public void setQty(int qty) {
        this.qty = qty;
    }
}
