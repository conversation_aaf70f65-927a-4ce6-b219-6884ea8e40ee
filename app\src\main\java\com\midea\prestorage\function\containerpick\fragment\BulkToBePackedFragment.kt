package com.midea.prestorage.function.containerpick.fragment

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import com.chad.library.adapter.base.entity.node.BaseExpandNode
import com.midea.prestorage.base.App
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.BaseViewModelFragment
import com.midea.prestorage.beans.net.PrintBean
import com.midea.prestorage.dialog.TipDialog
import com.midea.prestorage.function.addgoods.fragment.FragmentAddPoolUnionBinding
import com.midea.prestorage.function.containerpick.BulkPackingActivity
import com.midea.prestorage.function.containerpick.BulkPackingVM
import com.midea.prestorage.function.containerpick.adapter.BulkToBePackedAdapter
import com.midea.prestorage.function.containerpick.provider.BulkPickToBeDetailWrap
import com.midea.prestorage.function.inv.InfoCollectionActivity
import com.midea.prestorage.function.inv.response.RespMaterial
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.isNull
import com.midea.prestorage.widgets.NpaLinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.FragmentAddPoolBinding
import com.midea.prestoragesaas.databinding.FragmentAddPoolCareBinding
import com.midea.prestoragesaas.databinding.FragmentBulkToBePackedBinding
import java.math.BigDecimal

class BulkToBePackedFragment :
    BaseViewModelFragment<BulkToBePackedVM>() {

    companion object {
        const val TAG = "BulkToBePackedFragment"

        fun newInstance(): BulkToBePackedFragment {
            val bundle = Bundle()

            val fragment = BulkToBePackedFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    private var binding: FragmentBulkToBePackedUnionBinding? = null
    private var adapter: BulkToBePackedAdapter? = null

    override fun beforeOnCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        vm = ViewModelProvider(
            requireActivity(),
            ViewModelProvider.AndroidViewModelFactory(App.mInstance)
        ).get(
            BulkPackingVM::class.java
        ).toBePackedVM
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            FragmentBulkToBePackedUnionBinding.V2(DataBindingUtil.inflate(
                inflater,
                R.layout.fragment_bulk_to_be_packed_care,
                container,
                false
            ))
        } else {
            FragmentBulkToBePackedUnionBinding.V1(DataBindingUtil.inflate(
                inflater,
                R.layout.fragment_bulk_to_be_packed,
                container,
                false
            ))
        }
        binding?.vm = vm
        binding?.lifecycleOwner = this

        initView()

        return binding?.root
    }

    override fun onResume() {
        super.onResume()
        vm.taskCode.set((activity as BulkPackingActivity).binding.vm?.taskCode?.get())
        vm.containerCode.set((activity as BulkPackingActivity).binding.vm?.containerCode?.get())
        vm.pickingContainerList = (activity as BulkPackingActivity).binding.vm?.pickingContainerList
        vm.initList()
    }

    private fun initView() {
        adapter = BulkToBePackedAdapter(vm)
        adapter?.addChildClickViewIds(R.id.img_reduce, R.id.img_plus)
        adapter?.setOnItemChildClickListener() { adapter, view, position ->
            val node = adapter.data[position]
            when (view.id) {
                //减
                R.id.img_reduce -> {
                    (node as? BulkPickToBeDetailWrap)?.task?.let { data ->
                        if (data.packQty?.compareTo(BigDecimal.ONE) == 1)  {
                            data.packQty = data.packQty?.subtract(BigDecimal.ONE)
                        }
                        adapter.notifyDataSetChanged()
                    }
                }

                //加
                R.id.img_plus -> {
                    (node as? BulkPickToBeDetailWrap)?.task?.let { data ->
                        if (data.packQty.isNull()) {
                            data.packQty = BigDecimal.ONE
                        }else if (data.packQty?.compareTo(data.waitPackQty) == -1)  {
                            data.packQty = data.packQty?.add(BigDecimal.ONE)
                        }
                        adapter.notifyDataSetChanged()
                    }
                }
            }
        }
        binding?.recycle?.layoutManager = NpaLinearLayoutManager(requireContext())
        binding?.recycle?.adapter = adapter
        vm.taskListMutableLiveData.observe(this) {
            adapter?.setNewInstance(it)
            adapter?.notifyDataSetChanged()
        }
        vm.refreshEvent.observe(this) {
            adapter?.notifyDataSetChanged()
        }

        vm.packHandleEvent.observe(this) {
            if (vm.detailFromSelected().isEmpty()) {
                vm.errorToaster.showError("请选择需要打包的货品！")
            }else {
                showPackHandleDialog()
            }
        }

        vm.packAndPrintEvent.observe(this) {
            if (vm.detailFromSelected().isEmpty()) {
                vm.errorToaster.showError("请选择需要打包的货品！")
            }else if (!Printer.isPrintOk()) {
                vm.errorToaster.showError("请先连接蓝牙打印机！")
            }else {
                showPackAndPrintDialog()
            }
        }
    }

    private fun showPackHandleDialog() {
        var tipDialog = TipDialog(activity as BaseActivity)
        tipDialog.setTitle("询问")
        tipDialog.setMsg("确定对所选货品进行打包吗？")
        tipDialog.setOnTipBackListener(object : TipDialog.OnTipBack {
            override fun onConfirmClick() {
                vm.packHandle(vm.detailFromSelected(), false)
            }

            override fun onDismissClick() {
            }
        })
        tipDialog.show()
    }

    private fun showPackAndPrintDialog() {
        var tipDialog = TipDialog(activity as BaseActivity)
        tipDialog.setTitle("询问")
        tipDialog.setMsg("确定对所选货品打包并打印清单吗？")
        tipDialog.setOnTipBackListener(object : TipDialog.OnTipBack {
            override fun onConfirmClick() {
                vm.packHandle(vm.detailFromSelected(), true)
            }

            override fun onDismissClick() {
            }
        })
        tipDialog.show()
    }
}