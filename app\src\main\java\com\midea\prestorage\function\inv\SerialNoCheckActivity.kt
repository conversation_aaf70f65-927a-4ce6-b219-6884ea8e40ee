package com.midea.prestorage.function.inv

import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.SerialNoCheckBeans
import com.midea.prestoragesaas.databinding.ActivitySerialNoCheckBinding
import com.midea.prestorage.utils.AppUtils
import com.xuexiang.xqrcode.XQRCode

// 按单收货
class SerialNoCheckActivity : BaseActivity() {

    private lateinit var binding: ActivitySerialNoCheckBinding
    var adapter: CommonAdapter<SerialNoCheckBeans> = CommonAdapter(R.layout.item_serial_no_check)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_serial_no_check)
        binding.vm = SerialNoCheckVM(this)

        initRecycle()

        //光标默认定位
        AppUtils.requestFocus(binding.etSerialNo)
    }

    private fun initRecycle() {
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun showData(beans: MutableList<SerialNoCheckBeans>) {
        adapter.setNewInstance(beans)
        adapter.notifyDataSetChanged()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == BaseActivity.QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }
}