package com.midea.prestorage.function.containerpick.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import com.midea.prestorage.base.App
import com.midea.prestorage.base.BaseViewModelFragment
import com.midea.prestorage.function.containerpick.BulkPackingActivity
import com.midea.prestorage.function.containerpick.BulkPackingVM
import com.midea.prestorage.function.containerpick.adapter.BulkAlreadyPackedAdapter
import com.midea.prestorage.function.containerpick.adapter.BulkToBePackedAdapter
import com.midea.prestorage.function.containerpick.provider.BulkPickToBeDetailWrap
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.NpaLinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.FragmentBulkAlreadyPackedBinding

class BulkAlreadyPackedFragment :
    BaseViewModelFragment<BulkAlreadyPackedVM>() {

    companion object {
        const val TAG = "BulkAlreadyPackedFragment"

        fun newInstance(): BulkAlreadyPackedFragment {
            val bundle = Bundle()

            val fragment = BulkAlreadyPackedFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    private var binding: FragmentBulkAlreadyPackedUnionBinding? = null
    private var adapter: BulkAlreadyPackedAdapter? = null

    override fun beforeOnCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        vm = ViewModelProvider(
            requireActivity(),
            ViewModelProvider.AndroidViewModelFactory(App.mInstance)
        ).get(
            BulkPackingVM::class.java
        ).alreadyPackedVM
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            FragmentBulkAlreadyPackedUnionBinding.V2(DataBindingUtil.inflate(
                inflater,
                R.layout.fragment_bulk_already_packed_care,
                container,
                false
            ))
        } else {
            FragmentBulkAlreadyPackedUnionBinding.V1(DataBindingUtil.inflate(
                inflater,
                R.layout.fragment_bulk_already_packed,
                container,
                false
            ))
        }
        binding?.vm = vm
        binding?.lifecycleOwner = this

        initView()

        return binding?.root
    }

    override fun onResume() {
        super.onResume()
        vm.taskCode.set((activity as BulkPackingActivity).binding.vm?.taskCode?.get())
        vm.containerCode.set((activity as BulkPackingActivity).binding.vm?.containerCode?.get())
        vm.pickingContainerList = (activity as BulkPackingActivity).binding.vm?.pickingContainerList
        vm.initList()
    }

    private fun initView() {
        adapter = BulkAlreadyPackedAdapter(vm)
        adapter?.addChildClickViewIds(R.id.img_reduce, R.id.img_plus)
        adapter?.setOnItemChildClickListener() { adapter, _, position ->
            val node = adapter.data[position]
            (node as? BulkPickToBeDetailWrap)?.task?.let { data ->

            }
        }
        binding?.recycle?.layoutManager = NpaLinearLayoutManager(requireContext())
        binding?.recycle?.adapter = adapter
        vm.taskListMutableLiveData.observe(this) {
            adapter?.setNewInstance(it)
            adapter?.notifyDataSetChanged()
        }
        vm.refreshEvent.observe(this) {
            adapter?.notifyDataSetChanged()
        }
    }
}