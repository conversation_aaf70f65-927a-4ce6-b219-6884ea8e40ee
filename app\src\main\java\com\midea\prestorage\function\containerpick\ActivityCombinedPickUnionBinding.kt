package com.midea.prestorage.function.containerpick

import android.widget.EditText
import android.widget.ImageButton
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestoragesaas.databinding.ActivityCombinedPickBinding
import com.midea.prestoragesaas.databinding.ActivityCombinedPickCareBinding

sealed class ActivityCombinedPickUnionBinding {
    abstract var vm: CombinedPickVM?
    abstract val llTitleBar: RelativeLayout
    abstract val titleBtnMore: ImageButton
    abstract val etSearchOrderNo: EditText
    abstract val recycle: RecyclerView
    abstract val tvNotification: TextView

    class V2(val binding: ActivityCombinedPickCareBinding) : ActivityCombinedPickUnionBinding() {
        override var vm: CombinedPickVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val titleBtnMore = binding.titleBtnMore
        override val etSearchOrderNo = binding.etSearchOrderNo
        override val recycle = binding.recycle
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityCombinedPickBinding) : ActivityCombinedPickUnionBinding() {
        override var vm: CombinedPickVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val titleBtnMore = binding.titleBtnMore
        override val etSearchOrderNo = binding.etSearchOrderNo
        override val recycle = binding.recycle
        override val tvNotification = binding.tvNotification
    }
}
