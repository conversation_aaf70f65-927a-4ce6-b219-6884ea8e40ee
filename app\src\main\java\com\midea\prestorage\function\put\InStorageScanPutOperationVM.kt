package com.midea.prestorage.function.put

import CheckUtil
import android.text.TextUtils
import android.view.View
import android.widget.EditText
import androidx.core.view.isVisible
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.PutContainerInfo
import com.midea.prestorage.beans.net.ReceiptListInfo
import com.midea.prestorage.beans.net.RespRecommendLoc
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.dialog.TipDialog
import com.midea.prestorage.function.inv.response.BsLocation
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.MySoundUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.FilterDigitTextWatcher
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal
import java.util.*

class InStorageScanPutOperationVM(val activity: InStorageScanPutOperationActivity) {

    val orderNo = ObservableField("")

    val locCode = ObservableField("")
    val goodsCode = ObservableField("")
    val csUnit = ObservableField("箱")
    val eaUnit = ObservableField("个")
    val qty = ObservableField("1")
    var bean: PutContainerInfo? = null

    private lateinit var tipDialog: TipDialog

    var status: MutableList<String>? = null
    val goodsStatus = ObservableField("正品")

    var scanMode: Int? = 1

    private lateinit var statusDialog: FilterDialog
    var goodsStatusCode = "Y" //Y为正品，N为不良品, B为包装破损
    var goodsStatueBeans: MutableList<DCBean>? = null
    val chooseReceiptItemLiveData = MutableLiveData<List<Pair<String, String>>>()
    private var selectedItemCode = ""
    var curItem: ReceiptListInfo? = null
    val eaQtyInputVM = InStorageScanPutOperationQtyInputVM().also {
        it.nextFocus = this::nextFocusForQtyInputItem
    }
    val csQtyInputVM = InStorageScanPutOperationQtyInputVM().also {
        it.nextFocus = this::nextFocusForQtyInputItem
    }
    val ipQtyInputVM = InStorageScanPutOperationQtyInputVM().also {
        it.nextFocus = this::nextFocusForQtyInputItem
    }
    val otQtyInputVM = InStorageScanPutOperationQtyInputVM().also {
        it.nextFocus = this::nextFocusForQtyInputItem
    }
    private val qtyInputVMList = listOf(eaQtyInputVM, csQtyInputVM, ipQtyInputVM, otQtyInputVM)

    var isPoint = 0

    var initialData: MutableList<ReceiptListInfo>? = null

    companion object {
        private const val REGEXP_ITEM = "\\((.*?)\\)"
    }

    fun init() {
        bean = activity.intent.getSerializableExtra("bean") as PutContainerInfo
        orderNo.set(bean?.containerCode)

        tipDialog = TipDialog(activity)

        statusDialog = FilterDialog(activity)
        statusDialog.setTitle("请选择状态")
        statusDialog.dismissEdit()
        statusDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            goodsStatusCode = it.showInfo
            goodsStatus.set(goodsStatusCode)
            statusDialog.dismiss()
        })

        DCUtils.goodsStatue(activity as RxAppCompatActivity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                goodsStatueBeans = statusDC
            }
        })

        scanMode = Constants.userInfo?.scanPutMode ?: 1
        resume()
    }

    fun resume() {
        goodsCode.set("")
        activity.binding.edGoods.isEnabled = true
        if (activity.binding.etScan.isEnabled) {
            activity.binding.etScan.requestFocus()
        } else {
            activity.binding.edGoods.requestFocus()
        }
        if (scanMode == 0) {
            activity.binding.edQty.visibility = View.VISIBLE
            activity.binding.rgUnit.visibility = View.VISIBLE
            activity.binding.rbCs.isChecked = true
            activity.qtyEditUnable()
            qty.set("1")
            activity.binding.constraintLayout.visibility = View.GONE
            isPoint = 0
        } else {
            activity.binding.edQty.visibility = View.GONE
            activity.binding.rgUnit.visibility = View.GONE
            isPoint = 0
        }
        scanReceiptItemCode()
    }

    val enterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                if (TextUtils.isEmpty(locCode.get().toString().trim())) {
                    return
                }
                validateLocationCode(locCode.get().toString().trim())
            }
        }
    }

    val inputTextChange = object : ViewBindingAdapter.TextChangedListener {

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

        }
    }

    val goodsKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                if (filterByGoodsCode(true)) return
                if (scanMode == 0) {
                    confirmTask(-1)
                } else {
                    activity.qtyLock()
                }
            }
        }
    }

    private fun filterByGoodsCode(isEnter: Boolean): Boolean {
        if (!goodsCode.get().isNullOrEmpty()) {
            val results = initialData?.filter {
                it.custItemCode == goodsCode.get()
                        || it.whBarcode69 == goodsCode.get()
                        || it.whCsBarcode69 == goodsCode.get()
                        || it.whMaxBarcode69 == goodsCode.get()
                        || it.whIpBarcode69 == goodsCode.get()
            } ?: emptyList()
            if (results.isEmpty()) {
                ToastUtils.getInstance()
                    .showErrorToastWithSound(activity, "暂无该货品")
                return true
            } else if (!allSameItemCode(results)) {
                chooseReceiptItemLiveData.value = groupReceiptList(results)
            } else {
                selectedItemCode = results[0].itemCode
                if (isEnter) {
                    curItem = results[0]
                }
                updateFilterResult(results)
            }
        }
        return false
    }

    private fun updateFilterResult(results: List<ReceiptListInfo>) {
        results.getOrNull(0)?.let {
            if (scanMode == 1) {
                updateInputQtyForAllPut(it)
                //获取推荐库位
                curItem?.let { it1 -> getRecommendLoc(it1) }
            }
        }
        activity.adapter.setNewInstance(results as MutableList<ReceiptListInfo>)
        activity.adapter.notifyDataSetChanged()
    }

    fun onChooseItem(itemInfo: String) {
        extractItemCode(itemInfo)?.let { itemCode ->
            selectedItemCode = itemCode
            filterByItemCode(itemCode)
        }
    }

    private fun extractItemCode(str: String) =
        REGEXP_ITEM.toRegex().findAll(str).last().groupValues.getOrNull(1)

    private fun filterByItemCode(itemCode: String) {
        val results = initialData?.filter {
            it.itemCode == itemCode
        } ?: emptyList()
        if (results.isNotEmpty()) {
            curItem = results[0]
            updateFilterResult(results)
        }
    }

    val qtyKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                if (TextUtils.isEmpty(qty.get()) || AppUtils.getBigDecimalValue(qty.get())
                        .compareTo(
                            BigDecimal.ZERO
                        ) == 0
                ) {
                    return
                }
                val csResults = activity.adapter.data.filter {
                    it.unitOrigin == "CS"
                }
                val eaResults = activity.adapter.data.filter {
                    it.unitOrigin == "EA"
                }

                if (csResults.isNotEmpty() && eaResults.isNotEmpty()) {
                    if (activity.getPackageStr() == "EA") {
                        var bigDecimal = BigDecimal(0)
                        eaResults.forEach {
                            bigDecimal = bigDecimal.add(it.unitQty)
                        }
                        val subtract = bigDecimal.subtract(AppUtils.getBigDecimalValue(qty.get()))
                        if (subtract.compareTo(BigDecimal(0)) == -1) {
                            //showTipDialog(subtract)
                            confirmTask(-1)
                        } else {
                            confirmTask(-1)
                        }
                    } else {
                        confirmTask(-1)
                    }
                } else {
                    confirmTask(-1)
                }
            }
        }
    }

    private fun allSameItemCode(list: List<ReceiptListInfo>): Boolean {
        if (list.size <= 1) {
            return true
        }
        val itemCode = list[0].itemCode
        return list.all { it.itemCode == itemCode }
    }

    /**
     * @return list of pair, pair: first：itemCode second: itemName
     */
    private fun groupReceiptList(list: List<ReceiptListInfo>): List<Pair<String, String>> {
        return list.groupBy { it.itemCode }.map { entry ->
            entry.key to (entry.value.getOrNull(0)?.itemName ?: "")
        }
    }

    fun showTipDialog(bigDecimal: BigDecimal) {
        tipDialog.setTitle("提示")
        tipDialog.setMsg("拆零${AppUtils.getBigDecimalValue(bigDecimal).toPlainString()}个，是否执行上架?")
        tipDialog.setOnTipBackListener(object : TipDialog.OnTipBack {
            override fun onConfirmClick() {
                confirmTask(-1)
            }

            override fun onDismissClick() {
            }
        })
        tipDialog.show()
    }

    //获取推荐库位
    private fun getRecommendLoc(bean: ReceiptListInfo) {
        activity.waitingDialogHelp.showDialog()
        val map = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "ownerCode" to bean.ownerCode,
            "scenario" to "receipt",
            "orderType" to bean.receiptType,
            "qty" to bean.qty,
            "packageUnit" to bean.unit,
            "lotAtt01" to bean.lotAtt01,
            "lotAtt02" to bean.lotAtt02,
            "lotAtt04" to bean.lotAtt04,
            "lotAtt05" to bean.lotAtt05,
            "lotAtt06" to bean.lotAtt06,
            "containerCode" to bean.containerCode,
            "itemCode" to bean.itemCode
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        RetrofitHelper.getAddGoodsService()
            .getRecommendLoc(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<RespRecommendLoc>(activity) {
                override fun success(data: RespRecommendLoc?) {
                    activity.waitingDialogHelp.hidenDialog()
                    data?.locCode?.let {
                        if (!it.isNullOrEmpty()) {
                            activity.inputRequestFocus()
                            locCode.set(it)
                            activity.binding.etScan.post {
                                activity.binding.etScan.selectAll()
                            }
                            //validateLocationCode(locCode.get().toString().trim())
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    // 校验库位是否存在
    //type :  from 表示 要检查的是来源库位     to 表示 要检查的是目标库位
    fun validateLocationCode(locCodeStr: String) {
        activity.waitingDialogHelp.showDialog()

        // 检查 库位编码是否正确 (即检查仓库里是否有该库位 库位数量>0表示存在)
        RetrofitHelper.getBasicDataAPI()
            .getLocationDetail(activity.getWhCode(), locCodeStr)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<BsLocation>(activity as RxAppCompatActivity) {
                override fun success(data: BsLocation?) {
                    activity.waitingDialogHelp.hidenDialog()
                    activity.goodsRequestFocus()
                    MySoundUtils.getInstance(activity).dingSound()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    locCode.set("")
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, "目标库位[$locCodeStr]不存在")
                }
            })
    }

    fun changeClick() {
        activity.inputRequestFocus()
    }

    private fun scanReceiptItemCode() {
        activity.waitingDialogHelp.showDialog()
        if (bean == null) {
            return
        }
        val map = mutableMapOf(
            "containerCode" to bean?.containerCode,
            "whCode" to activity.getWhCode()
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        RetrofitHelper.getAddGoodsService()
            .containerDetail(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<ReceiptListInfo>>(activity) {
                override fun success(data: MutableList<ReceiptListInfo>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (!data.isNullOrEmpty()) {
                        data.forEach {
                            it.lotAtt04Str =
                                goodsStatueBeans?.find { item -> item.value == it.lotAtt04 }?.key
                        }

                        //这里使用了sortWith函数和compareBy函数来实现排序。
                        // 首先，使用it.locZoneAreaDto.locCode == null比较函数来将locCode为null的元素排在最后面。
                        // 然后，使用it.locZoneAreaDto.locCode?.let { locCode -> locCode.toLowerCase() }来将locCode转换为小写字母并忽略大小写进行比较。
                        data.sortWith(
                            compareBy(
                                { it.locZoneAreaDto.locCode == null },
                                { it.locZoneAreaDto.locCode?.let { locCode -> locCode.toLowerCase() } })
                        )

                        initialData = data

                        activity.adapter.setNewInstance(initialData)
                        activity.adapter.notifyDataSetChanged()
                    } else {
                        AppUtils.showToast(activity, "容器上架完成!")
                        activity.finish()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun confirmTaskClick(tag: Int) {
        if (CheckUtil.isFastDoubleClick()) {
            confirmTask(tag)
        }
    }

    fun confirmTask(tag: Int) {
        if (TextUtils.isEmpty(locCode.get())) {
            activity.inputRequestFocus()
            ToastUtils.getInstance().showErrorToastWithSound(activity, "请输入库位编码")
            return
        }

        if (TextUtils.isEmpty(goodsCode.get()) && tag != 0) {
            activity.goodsLock()
            ToastUtils.getInstance().showErrorToastWithSound(activity, "请输入货品编码或者69码")
            return
        }

        if (tag != 0) {
            confirmTaskOperator(tag)
        } else {
            tipDialog.setTitle("提示")
            tipDialog.setMsg("确定箱/托【${orderNo.get()}】上架吗?")
            tipDialog.setOnTipBackListener(object : TipDialog.OnTipBack {
                override fun onConfirmClick() {
                    if (CheckUtil.isFastDoubleClick()) {
                        confirmTaskOperator(tag)
                    }
                }

                override fun onDismissClick() {
                }
            })
            if (!tipDialog.isShowing) {
                tipDialog.show()
            }
        }
    }

    fun confirmTaskOperator(tag: Int) {
        activity.waitingDialogHelp.showDialogUnCancel()
        val list = if (scanMode == 1) {
            if (tag == -1) {
                qtyInputVMList.filter { it.valid() }?.let {
                    confirmParamNew(it as MutableList<InStorageScanPutOperationQtyInputVM>)
                }
            } else {
                //整箱/托盘上架
                confirmParam(tag, "", "")
            }
        } else {
            confirmParam(tag, activity.getPackageStr(), qty.get())
        }
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(list)
        )

        RetrofitHelper.getAddGoodsService()
            .containerSubmit(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (tag != -1) {
                        locCode.set("")
                        activity.finish()
                    }
                    if (scanMode == 1) {
                        qty.set("")
                        activity.inputRequestFocus()
                    }
                    goodsCode.set("")
                    selectedItemCode = ""

                    otQtyInputVM.reset()
                    csQtyInputVM.reset()
                    ipQtyInputVM.reset()
                    eaQtyInputVM.reset()

                    scanReceiptItemCode()
                    ToastUtils.getInstance().showSuccessToastWithSound(activity, "上架成功!")
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    if (statusCode == 602001L) {
                        activity.clean()
                    } else if (statusCode == 602115L) {
                        activity.qtySelectAll()
                        if (scanMode == 1) {
                            qty.set("")
                        }
                    } else {
                        activity.binding.edGoods.setText("")
                        activity.binding.edGoods.isEnabled = true
                        activity.binding.edGoods.requestFocus()
                    }
                }
            })
    }

    private fun confirmParam(tag: Int, unit: String?, qty: String?): MutableMap<String, Any?> {
        val map = mutableMapOf<String, Any?>()
        map["containerCode"] = bean?.containerCode
        map["whCode"] = activity.getWhCode()
        map["locCode"] = locCode.get().toString().trim().uppercase()
        map["userCode"] = Constants.userInfo?.name
        if (tag == -1) {
            map["submitMode"] = 0.toString()
            map["barcode"] = selectedItemCode.ifEmpty { goodsCode.get() }
            val utilMap = mutableMapOf<String, Any?>(
                "$unit" to AppUtils.getBigDecimalValue(qty)
            )
            map["utilMap"] = utilMap
        } else {
            map["submitMode"] = 1.toString()
        }
        return map
    }

    private fun confirmParamNew(list: MutableList<InStorageScanPutOperationQtyInputVM>): MutableMap<String, Any?> {
        val map = mutableMapOf<String, Any?>()
        map["containerCode"] = bean?.containerCode
        map["whCode"] = activity.getWhCode()
        map["locCode"] = locCode.get().toString().trim().uppercase()
        map["userCode"] = Constants.userInfo?.name
        map["submitMode"] = 0.toString()
        map["barcode"] = selectedItemCode.ifEmpty { goodsCode.get() }
        val utilMap = mutableMapOf<String, Any?>()
        list.forEach {
            utilMap["${it.unitValue()}"] = AppUtils.getBigDecimalValue(it.qtyValue())
        }
        map["utilMap"] = utilMap

        return map
    }

    fun onItemClick(bean: ReceiptListInfo) {
        if (bean.isNeedScan69 == "1") {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "请扫码货品69码或货品条码！")
            return
        }
        goodsCode.set(bean.custItemCode)
        selectedItemCode = bean.itemCode
        curItem = bean
        filterByGoodsCode(false)
    }

    //从大到小OT CS IP EA
    private fun updateInputQtyForAllPut(bean: ReceiptListInfo) {
        bean?.let {
            isPoint = it.isDecimal
        }
        val itemQtyInputList = listOf(
            activity.binding.itemQtyInput1,
            activity.binding.itemQtyInput2,
            activity.binding.itemQtyInput3,
            activity.binding.itemQtyInput4
        )
        otQtyInputVM.reset()
        csQtyInputVM.reset()
        ipQtyInputVM.reset()
        eaQtyInputVM.reset()
        val packageList = bean.packageRelationList.filter { bean ->
            bean.cdprUnit in listOf(
                "CS",
                "IP",
                "EA"
            )
        }
        itemQtyInputList.forEach { it.root.visibility = View.GONE }
        packageList?.forEachIndexed { index, packageRelation ->
            when (packageRelation.cdprUnit) {
                "CS" -> csQtyInputVM
                "IP" -> ipQtyInputVM
                "EA" -> eaQtyInputVM
                else -> null
            }?.let { itemInputVM ->
                itemInputVM.unitName.value = packageRelation.cdprDesc
                itemInputVM.unit.value = packageRelation.cdprUnit
                itemInputVM.index = index
                itemQtyInputList.getOrNull(index)?.let { itemQtyInputBinding ->
                    if (activity.binding.tvNumTitle.visibility != View.VISIBLE) {
                        activity.binding.tvNumTitle.visibility = View.VISIBLE
                    }
                    if (activity.binding.constraintLayout.visibility != View.VISIBLE) {
                        activity.binding.constraintLayout.visibility = View.VISIBLE
                    }
                    itemQtyInputBinding.root.visibility = View.VISIBLE
                    itemQtyInputBinding.vm = itemInputVM
                    setDecimalPlaces(
                        itemQtyInputBinding.edQty,
                        getTextWatcher(index),
                        "EA" == packageRelation.cdprUnit
                    )
                }
            }
        }
        itemQtyInputList.forEach { itemPackageRelationQtyInputBinding ->
            if (itemPackageRelationQtyInputBinding.vm?.unitValue() == bean.unit) {
                AppUtils.requestFocus(itemPackageRelationQtyInputBinding.edQty)
            }
        }
    }

    private fun nextFocusForQtyInputItem(index: Int) {
        val itemQtyInputList = listOf(
            activity.binding.itemQtyInput1,
            activity.binding.itemQtyInput2,
            activity.binding.itemQtyInput3,
            activity.binding.itemQtyInput4
        )
        if (index in 0..2) {
            val binding = itemQtyInputList[index + 1]
            if (binding.root.visibility == View.VISIBLE) {
                AppUtils.requestFocus(binding.edQty)
            }
        }
    }

    fun back() {
        activity.finish()
    }

    fun confirmPutAwayClick() {
        if (CheckUtil.isFastDoubleClick()) {
            confirmPutAway()
        }
    }

    private fun confirmPutAway() {
        if (scanMode == 1) {
            if (qtyInputVMList.all { !it.valid() }) {
                ToastUtils.getInstance().showErrorToast(activity, "请输入数量后点击上架确认")
            } else {
                confirmTask(-1)
            }
        } else {
            ToastUtils.getInstance().showErrorToast(activity, "本功能只支持批量上架")
        }
    }

    private fun setDecimalPlaces(
        view: EditText,
        textWatcher: FilterDigitTextWatcher,
        isEa: Boolean
    ) {
        view.removeTextChangedListener(textWatcher)
        textWatcher.limitDecimalPlaces = if (isEa && isPoint != 0) 4 else 0
        view.addTextChangedListener(textWatcher)
    }

    private fun getTextWatcher(index: Int): FilterDigitTextWatcher {
        return when (index) {
            0 -> {
                activity.textWatcher1!!
            }
            1 -> {
                activity.textWatcher2!!
            }
            2 -> {
                activity.textWatcher3!!
            }
            else -> {
                activity.textWatcher4!!
            }
        }
    }
}