package com.midea.prestorage.beans.net;

import android.text.TextUtils;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.beans.base.BaseItemForPopup;
import com.midea.prestorage.utils.AppUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * Created by LUCY6 on 2017-5-23.
 */

public class ScanReviewInfo implements Serializable {

    private String shipmentCode;
    private String containerId;
    private String containerCode;
    private String pickStatus;
    private String unit;
    private BigDecimal totalQty;
    private BigDecimal allocatedQty;

    private List<ScanReviewList> reviewScanBoxItemDtos;

    public String getShipmentCode() {
        return shipmentCode;
    }

    public void setShipmentCode(String shipmentCode) {
        this.shipmentCode = shipmentCode;
    }

    public String getContainerId() {
        return containerId;
    }

    public void setContainerId(String containerId) {
        this.containerId = containerId;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public String getPickStatus() {
        return pickStatus;
    }

    public void setPickStatus(String pickStatus) {
        this.pickStatus = pickStatus;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getTotalQty() {
        return AppUtils.getBigDecimalValue(totalQty);
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public BigDecimal getAllocatedQty() {
        return AppUtils.getBigDecimalValue(allocatedQty);
    }

    public void setAllocatedQty(BigDecimal allocatedQty) {
        this.allocatedQty = allocatedQty;
    }

    public List<ScanReviewList> getReviewScanBoxItemDtos() {
        return reviewScanBoxItemDtos;
    }

    public void setReviewScanBoxItemDtos(List<ScanReviewList> reviewScanBoxItemDtos) {
        this.reviewScanBoxItemDtos = reviewScanBoxItemDtos;
    }

    public class ScanReviewList implements Serializable {
        @ShowAnnotation
        private String custItemCode;
        @ShowAnnotation
        private String itemName;
        @ShowAnnotation
        private BigDecimal pickedQty;
        @ShowAnnotation
        private BigDecimal oqcQty;

        public String getCustItemCode() {
            return custItemCode;
        }

        public void setCustItemCode(String custItemCode) {
            this.custItemCode = custItemCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public BigDecimal getPickedQty() {
            return AppUtils.getBigDecimalValue(pickedQty);
        }

        public void setPickedQty(BigDecimal pickedQty) {
            this.pickedQty = pickedQty;
        }

        public BigDecimal getOqcQty() {
            return AppUtils.getBigDecimalValue(oqcQty);
        }

        public void setOqcQty(BigDecimal oqcQty) {
            this.oqcQty = oqcQty;
        }
    }
}
