package com.midea.prestorage.dialog

import android.view.View
import com.midea.prestorage.function.inv.InvReconciliationDetailVM
import com.midea.prestorage.function.inv.InvReconciliationDetailVM.Companion.MULTIPLE_UNIT_INPUT
import com.midea.prestorage.function.inv.InvReconciliationDetailVM.Companion.PIECE_SCAN
import com.midea.prestorage.function.inv.InvReconciliationDetailVM.Companion.RESERVED_LOTATT
import com.midea.prestorage.function.inv.InvReconciliationDetailVM.Companion.unitInputType
import com.midea.prestorage.function.inv.InvReconciliationDetailVM.Companion.lotType
import com.midea.prestorage.function.inv.InvReconciliationDetailVM.Companion.scanType
import com.midea.prestoragesaas.databinding.DialogInvReconciliationBinding

class InvReconciliationDialogVM(
    var dialog: InvReconciliationDialog,
    val binding: DialogInvReconciliationBinding
) {
    fun init() {
        binding.cbSelect01.isChecked = scanType == PIECE_SCAN
        binding.cbSelect02.isChecked = lotType == RESERVED_LOTATT
        binding.cbSelect03.isChecked = unitInputType == MULTIPLE_UNIT_INPUT
    }

    var listener: InvReconciliationDialog.SettingBack? = null

    init {
        binding.cbSelect01.isChecked = scanType == PIECE_SCAN
        binding.cbSelect02.isChecked = lotType == RESERVED_LOTATT
        binding.cbSelect03.isChecked = unitInputType == MULTIPLE_UNIT_INPUT
    }

    /**
     * 确认按钮
     */
    val confirmClick = View.OnClickListener {
        var sacnType = if (binding.cbSelect01.isChecked) {
            1
        } else {
            0
        }
        var lotType = if (binding.cbSelect02.isChecked) {
            1
        } else {
            0
        }
        var inputType = if (binding.cbSelect03.isChecked) {
            1
        } else {
            0
        }
        listener?.onConfirmClick(sacnType, lotType, inputType)
        dialog.dismiss()
    }

    /**
     * 取消按钮
     */
    var cancelClick = View.OnClickListener {
        dialog.dismiss()
    }
}