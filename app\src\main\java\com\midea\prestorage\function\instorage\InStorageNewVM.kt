package com.midea.prestorage.function.instorage

import android.app.Application
import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.function.instorage.response.RespReceiptHeader
import com.midea.prestorage.function.inv.response.InReceiptOrder
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody

class InStorageNewVM(application: Application) : BaseViewModel(application) {
    val title = MutableLiveData<String>("订单列表")
    var finishActivity = MutableLiveData(false)
    var curOrderNo = MutableLiveData<String>("") // 单号 (入库单号或波次单号)
    val isRefreshing = MutableLiveData(false)
    val isNoData = MutableLiveData(false)
    val showFilter = MutableLiveData(false)
    val isShowDriverDialog = MutableLiveData(false)
    var driverInfo = MutableLiveData<String>("")
    val isShowCarNoDialog = MutableLiveData(false)
    var carNoInfo = MutableLiveData<String>("")
    val isShowReceiptTypeDialog = MutableLiveData(false)
    var receiptType = MutableLiveData<String>("")
    val isShowOrderStatusDialog = MutableLiveData(false)
    var orderStatus = MutableLiveData<String>("待收货,收货中")
    var receiptTypeInfo: MutableList<DCBean>? = null
    var orderStatusInfo: MutableList<DCBean>? = null
    var resetFilterInfo = MutableLiveData<MutableList<String>>()
    var dayInfo = MutableLiveData<String>("3天")
    val toScanActivity = MutableLiveData<MutableList<String>>()
    var inOrderType = MutableLiveData<MutableList<DCBean>>()
    var inOrderStatus = MutableLiveData<MutableList<DCBean>>()
    var carNoBeans = MutableLiveData<MutableList<BaseItemShowInfo>>()
    var driverNoBeans = MutableLiveData<MutableList<BaseItemShowInfo>>()
    var showDatas = MutableLiveData<MutableList<RespReceiptHeader>>()
    var loadMoreDatas = MutableLiveData<MutableList<RespReceiptHeader>>()
    val loadMoreComplete = MutableLiveData(0)
    val statusList = mutableListOf<String>()
    val receiptTypeList = mutableListOf<String>()
    var isFirstEnter = MutableLiveData(true)
    val resetData = MutableLiveData(false)

    // 当前页码
    var pageNo = 1

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        isRefreshing.value = true
        pageNo = 1
        initOrderList()
    }

    override fun init() {
        resetFilterInfo.value = mutableListOf()
        inOrderType.value = mutableListOf()
        toScanActivity.value = mutableListOf()
        initFilterInfo()
    }

    fun loadMore() {
        initOrderList(true)
    }

    fun onEnterOrderNo() {
        if (CheckUtil.isFastDoubleClick()) {
            if (TextUtils.isEmpty(curOrderNo.value)) {
                showNotification("单号不能为空", false)
                return
            }
            enterOrderList(curOrderNo.value.toString().trim())
        }
    }

    fun reset() {
        resetData.value = true
    }

    fun initReceiptOrderType() {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {
            val result = withContext(Dispatchers.IO) {
                val direction = async { RetrofitHelper.getDirectionAPI().searchDictNew("CL_RECEIPT_TYPE") }
                direction.await()
            }

            if(result.code == 0.toLong()) {
                val datas = mutableListOf<DCBean>()
                result.data?.removeAll { it.enableFlag == 0 }
                result.data?.forEach {
                    datas.add(DCBean(it.code, it.name, DCBean.SHOW_VALUE))
                }
                inOrderType.value = datas
            }
        }
    }

    fun initReceiptOrderStatus() {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {
            val result = withContext(Dispatchers.IO) {
                val direction = async { RetrofitHelper.getDirectionAPI().searchDictNew("BL_RECEIPT_STATUS") }
                direction.await()
            }

            if(result.code == 0.toLong()) {
                val datas = mutableListOf<DCBean>()
                result.data?.removeAll { it.enableFlag == 0 }
                result.data?.forEach {
                    datas.add(DCBean(it.code, it.name, DCBean.SHOW_VALUE))
                }
                inOrderStatus.value = datas
            }
        }
    }

    fun initCarNo(currentDay: String, whCode: String) {

        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf<String, Any>()
            param["pageNo"] = 1
            param["pageSize"] = 30
            param["whCode"] = whCode
            param["days"] = currentDay.toInt()

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val carNoInfo = async { RetrofitHelper.getAppAPI().queryCarNoPage(requestBody) }
                carNoInfo.await()
            }

            if(result.code == 0.toLong()) {
                if (result.data != null) {
                    val beans = mutableListOf<BaseItemShowInfo>()
                    var hashSetCarNo = HashSet<String>() //车牌号排重
                    result.data?.list?.forEach {
                        if (!it.isNullOrBlank() && !hashSetCarNo.contains(it)) {
                            val bean = BaseItemShowInfo()
                            bean.showInfo = it
                            beans.add(bean)
                            hashSetCarNo.add(it)
                        }
                    }
                    carNoBeans.value = beans
                }
            }
        }
    }

    fun initDriver(currentDay: String, whCode: String) {

        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf<String, Any>()
            param["pageNo"] = 1
            param["pageSize"] = 30
            param["whCode"] = whCode
            param["days"] = currentDay.toInt()

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val carNoInfo = async { RetrofitHelper.getAppAPI().queryDriverPage(requestBody) }
                carNoInfo.await()
            }

            if(result.code == 0.toLong()) {
                if (result.data != null) {
                    val beans = mutableListOf<BaseItemShowInfo>()
                    var hashSetCarNo = HashSet<String>() //车牌号排重
                    result.data?.list?.forEach {
                        if (!it.isNullOrBlank() && !hashSetCarNo.contains(it)) {
                            val bean = BaseItemShowInfo()
                            bean.showInfo = it
                            beans.add(bean)
                            hashSetCarNo.add(it)
                        }
                    }
                    driverNoBeans.value = beans
                }
            }
        }
    }

    fun initOrderList(isLoadMore: Boolean = false) {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf<String, Any>()
            param["pageNo"] = pageNo
            param["pageSize"] = 10
            param["whCode"] = Constants.whInfo?.whCode.toString()
            param["days"] = dayInfo.value.toString().split("天")[0].toInt()

            param["statusList"] = statusList
            param["receiptTypeList"] = receiptTypeList
            param["orderNo"] = ""
            if("全部" == driverInfo.value.toString()) {
                param["driver"] = ""
            }else {
                param["driver"] = driverInfo.value.toString()
            }
            if("全部" == carNoInfo.value.toString()) {
                param["carNo"] = ""
            }else {
                param["carNo"] = carNoInfo.value.toString()
            }

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().queryReceiptHeader(requestBody) }
                resp.await()
            }

            if(result.code == 0L) {
                isFirstEnter.value = false
                isRefreshing.value = false
                if (result.data != null) {
                    if (isLoadMore) {
                        loadMoreDatas.value = result.data!!.list
                        loadMoreComplete.value = 1
                    } else {
                        showDatas.value = result.data!!.list
                    }
                }
                if (pageNo >= result.data?.totalPage!!) {
                    loadMoreComplete.value = 2
                } else {
                    pageNo = result.data?.pageNo!! + 1
                }
            }else {
                isFirstEnter.value = false
                isRefreshing.value = false
                result.msg?.let { showNotification(it, false) }
            }
        }
    }


    fun enterOrderList(orderNo: String) {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf<String, Any>()
            param["pageNo"] = 1
            param["pageSize"] = 10
            param["whCode"] = Constants.whInfo?.whCode.toString()
            param["days"] = dayInfo.value.toString().split("天")[0].toInt()

            param["statusList"] = statusList
            param["receiptTypeList"] = receiptTypeList
            param["orderNo"] = orderNo
            if("全部" == driverInfo.value.toString()) {
                param["driver"] = ""
            }else {
                param["driver"] = driverInfo.value.toString()
            }
            if("全部" == carNoInfo.value.toString()) {
                param["carNo"] = ""
            }else {
                param["carNo"] = carNoInfo.value.toString()
            }

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().queryReceiptHeader(requestBody) }
                resp.await()
            }

            if(result.code == 0L) {
                curOrderNo.value = ""
                if (result.data != null) {
                    if (result.data!!.list.size >0) { //这里是为了判断是否是按了ENTER键，如果是的话，并且有数据则跳转到扫码页面
                        val datas = mutableListOf<String>()
                        if(!result.data!!.list[0].receiptCode.isNullOrBlank()) {
                            if(result.data!!.list[0].receiptCode.endsWith(orderNo)) { //这里判断是根据单号来搜索出来的数据
                                datas.add(result.data!!.list[0].receiptCode)
                                datas.add("receipt")
                            }else if(!result.data!!.list[0].waveNo.isNullOrBlank() && result.data!!.list[0].waveNo.endsWith(orderNo)) {
                                datas.add(result.data!!.list[0].waveNo)
                                datas.add("wave")
                            }else {
                                datas.add(result.data!!.list[0].receiptCode)
                                datas.add("receipt")
                            }
                        }else {
                            if(!result.data!!.list[0].waveNo.isNullOrBlank()) {
                                datas.add(result.data!!.list[0].waveNo)
                                datas.add("wave")
                            }
                        }
                        toScanActivity.value = datas
                    }else {
                        result.msg?.let { showNotification(it, false) }
                    }
                }
            }else {
                curOrderNo.value = ""
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    /**
     * 弹出筛选框
     */
    fun showFilter() {
        showFilter.value = true
    }

    fun showDriverDialog() {
        isShowDriverDialog.value = true
    }

    fun showCarNoDialog() {
        isShowCarNoDialog.value = true
    }

    fun showReceiptTypeDialog() {
        isShowReceiptTypeDialog.value = true
    }

    fun showOrderStatusDialog() {
        isShowOrderStatusDialog.value = true
    }

    fun showReceiptTypeInfo() {
        val status = receiptTypeInfo?.joinToString(separator = ",") { it.value as String }
        receiptType.value = status
        receiptTypeList.clear()
        receiptTypeInfo?.forEach {
            receiptTypeList.add(it.key)
        }
    }

    fun showOrderStatusInfo() {
        val status = orderStatusInfo?.joinToString(separator = ",") { it.value as String }
        orderStatus.value = status
        statusList.clear()
        orderStatusInfo?.forEach {
            statusList.add(it.key)
        }
        if (statusList.size == 0) {
            inOrderStatus.value?.forEach {
                statusList.add(it.key)
            }
        }
    }

    fun initFilterInfo() {
        val filters = mutableListOf<String>()
        filters.add(dayInfo.value.toString())

        if (!TextUtils.isEmpty(orderStatus.value)) {
            filters.add(orderStatus.value.toString())
        }

        resetFilterInfo.value = filters
    }
}