package com.midea.prestorage.function.outstorage.dialog

import android.graphics.drawable.ColorDrawable
import androidx.core.content.ContextCompat
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.midea.prestoragesaas.R
import com.midea.prestorage.utils.MySoundUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*

class HandlingDialogVM(val dialog: HandlingDialog) : ViewModel() {

    var startTime = MutableLiveData<String>("装卸开始时间")
    var endTime = MutableLiveData<String>("装卸结束时间")
    var formatter: SimpleDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    var isShowNotification = MutableLiveData(false)

    fun close() {
        dialog.dismiss()
    }

    fun handlingStart() {
        if("装卸开始时间" != startTime.value.toString()) {
            return
        }
        val date = Date()
        dialog.backConfirm(1, formatter.format(date))
    }

    fun handlingEnd() {
        if("装卸结束时间" != endTime.value.toString()) {
            return
        }
        if("装卸开始时间" == startTime.value.toString()) { //请先设置装卸开始时间
            showNotification()
            return
        }
        val date = Date()
        dialog.backConfirm(2, formatter.format(date))
    }

    fun showNotification() {
        MySoundUtils.getInstance().errorSound()
        isShowNotification.value = true
        viewModelScope.launch {
            val result = withContext(Dispatchers.IO) {
                delay(1500)
                1
            }
            if (result == 1) {
                isShowNotification.value = false
            }
        }
    }
}