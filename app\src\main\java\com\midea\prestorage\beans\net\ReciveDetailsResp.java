package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.utils.AppUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class ReciveDetailsResp implements Serializable {

    private BigDecimal totalSkuQty;
    private BigDecimal receiptedSkuQty;
    private BigDecimal totalQty;
    private BigDecimal receiptedQty;
    private BigDecimal csPlQty;
    private String startReceiveDate;
    private List<DetailDtos> details;

    public BigDecimal getTotalSkuQty() {
        return totalSkuQty;
    }

    public void setTotalSkuQty(BigDecimal totalSkuQty) {
        this.totalSkuQty = totalSkuQty;
    }

    public BigDecimal getReceiptedSkuQty() {
        return receiptedSkuQty;
    }

    public void setReceiptedSkuQty(BigDecimal receiptedSkuQty) {
        this.receiptedSkuQty = receiptedSkuQty;
    }

    public BigDecimal getTotalQty() {
        return AppUtils.getBigDecimalValue(totalQty);
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public BigDecimal getReceiptedQty() {
        return AppUtils.getBigDecimalValue(receiptedQty);
    }

    public void setReceiptedQty(BigDecimal receiptedQty) {
        this.receiptedQty = receiptedQty;
    }

    public BigDecimal getCsPlQty() {
        return csPlQty;
    }

    public void setCsPlQty(BigDecimal csPlQty) {
        this.csPlQty = csPlQty;
    }

    public List<DetailDtos> getDetails() {
        return details;
    }

    public void setDetails(List<DetailDtos> details) {
        this.details = details;
    }

    public String getStartReceiveDate() {
        return startReceiveDate;
    }

    public void setStartReceiveDate(String startReceiveDate) {
        this.startReceiveDate = startReceiveDate;
    }

    public class DetailDtos implements Serializable {
        @ShowAnnotation
        private String custItemCode;
        @ShowAnnotation
        private String itemName;
        @ShowAnnotation
        private String lotAtt04Str;
        @ShowAnnotation
        private BigDecimal totalQty;
        @ShowAnnotation
        private BigDecimal receiptQty;
        private String lotAtt04;
        private String whBarcode69;
        @ShowAnnotation
        private BigDecimal eaqty;
        @ShowAnnotation
        private BigDecimal csqty;
        private BigDecimal ipQty;
        private BigDecimal otQty;
        private String otUnit;
        private String csUnit;
        private String ipUnit;
        private String eaUnit;
        private String whCsBarcode69;

        public String getWhCsBarcode69() {
            return whCsBarcode69;
        }

        public void setWhCsBarcode69(String whCsBarcode69) {
            this.whCsBarcode69 = whCsBarcode69;
        }

        public BigDecimal getIpQty() {
            return AppUtils.getBigDecimalValue(ipQty);
        }

        public void setIpQty(BigDecimal ipQty) {
            this.ipQty = ipQty;
        }

        public BigDecimal getOtQty() {
            return AppUtils.getBigDecimalValue(otQty);
        }

        public void setOtQty(BigDecimal otQty) {
            this.otQty = otQty;
        }

        public String getOtUnit() {
            return otUnit;
        }

        public void setOtUnit(String otUnit) {
            this.otUnit = otUnit;
        }

        public String getCsUnit() {
            return csUnit;
        }

        public void setCsUnit(String csUnit) {
            this.csUnit = csUnit;
        }

        public String getIpUnit() {
            return ipUnit;
        }

        public void setIpUnit(String ipUnit) {
            this.ipUnit = ipUnit;
        }

        public String getEaUnit() {
            return eaUnit;
        }

        public void setEaUnit(String eaUnit) {
            this.eaUnit = eaUnit;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public BigDecimal getTotalQty() {
            return totalQty;
        }

        public void setTotalQty(BigDecimal totalQty) {
            this.totalQty = totalQty;
        }

        public String getLotAtt04() {
            return lotAtt04;
        }

        public void setLotAtt04(String lotAtt04) {
            this.lotAtt04 = lotAtt04;
        }

        public BigDecimal getReceiptQty() {
            return receiptQty;
        }

        public void setReceiptQty(BigDecimal receiptQty) {
            this.receiptQty = receiptQty;
        }

        public String getCustItemCode() {
            return custItemCode;
        }

        public void setCustItemCode(String custItemCode) {
            this.custItemCode = custItemCode;
        }

        public String getLotAtt04Str() {
            return lotAtt04Str;
        }

        public void setLotAtt04Str(String lotAtt04Str) {
            this.lotAtt04Str = lotAtt04Str;
        }

        public String getWhBarcode69() {
            return whBarcode69;
        }

        public void setWhBarcode69(String whBarcode69) {
            this.whBarcode69 = whBarcode69;
        }

        public BigDecimal getEaqty() {
            return AppUtils.getBigDecimalValue(eaqty);
        }

        public void setEaqty(BigDecimal eaqty) {
            this.eaqty = eaqty;
        }

        public BigDecimal getCsqty() {
            return AppUtils.getBigDecimalValue(csqty);
        }

        public void setCsqty(BigDecimal csqty) {
            this.csqty = csqty;
        }
    }
}
