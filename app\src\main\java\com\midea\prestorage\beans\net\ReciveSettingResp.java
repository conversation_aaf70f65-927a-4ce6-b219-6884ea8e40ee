package com.midea.prestorage.beans.net;

import androidx.annotation.Nullable;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.function.inv.response.PackageRelation;
import com.midea.prestorage.utils.AppUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class ReciveSettingResp implements Serializable {

    private String whCode;
    private String receiptCode;
    private String containerCode;
    private String itemCode;
    private String custItemCode;
    private String itemName;
    private String lotAtt4;
    private String barcodeType;
    private String barcode;
    private String orderTotalQty;
    private BigDecimal waitForReceiptQty;
    private String receiptedQty;
    private Integer submitQty;
    private String submitUnit;
    private String csUnit;
    private String eaUnit;
    private BigDecimal csRuleQty;
    private BigDecimal submitEaQty;

    private CdWhLotDetailDto batch;
    private CdWhLotDetailDto lotAtt01; //生产日期
    private CdWhLotDetailDto lotAtt02; //失效日期
    private CdWhLotDetailDto lotAtt03; //入库日期

    private List<ListDetail> listDetail;

    private String tempBatch;
    private String tempMa;
    private String tempEx;
    private String tempAg;

    private int processFlag;

    @Nullable
    private String ownerCode;

    @Nullable
    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(@Nullable String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public int getProcessFlag() {
        return processFlag;
    }

    public void setProcessFlag(int processFlag) {
        this.processFlag = processFlag;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getLotAtt4() {
        return lotAtt4;
    }

    public void setLotAtt4(String lotAtt4) {
        this.lotAtt4 = lotAtt4;
    }

    public String getBarcodeType() {
        return barcodeType;
    }

    public void setBarcodeType(String barcodeType) {
        this.barcodeType = barcodeType;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getOrderTotalQty() {
        return orderTotalQty;
    }

    public void setOrderTotalQty(String orderTotalQty) {
        this.orderTotalQty = orderTotalQty;
    }

    public String getReceiptedQty() {
        return receiptedQty;
    }

    public void setReceiptedQty(String receiptedQty) {
        this.receiptedQty = receiptedQty;
    }

    public String getSubmitUnit() {
        return submitUnit;
    }

    public void setSubmitUnit(String submitUnit) {
        this.submitUnit = submitUnit;
    }

    public BigDecimal getSubmitEaQty() {
        if (submitEaQty == null) {
            return new BigDecimal(0);
        }
        return submitEaQty;
    }

    public void setSubmitEaQty(BigDecimal submitEaQty) {
        this.submitEaQty = submitEaQty;
    }

    public String getReceiptCode() {
        return receiptCode;
    }

    public void setReceiptCode(String receiptCode) {
        this.receiptCode = receiptCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public CdWhLotDetailDto getBatch() {
        return batch;
    }

    public void setBatch(CdWhLotDetailDto batch) {
        this.batch = batch;
    }

    public CdWhLotDetailDto getLotAtt01() {
        return lotAtt01;
    }

    public void setLotAtt01(CdWhLotDetailDto lotAtt01) {
        this.lotAtt01 = lotAtt01;
    }

    public CdWhLotDetailDto getLotAtt02() {
        return lotAtt02;
    }

    public void setLotAtt02(CdWhLotDetailDto lotAtt02) {
        this.lotAtt02 = lotAtt02;
    }

    public CdWhLotDetailDto getLotAtt03() {
        return lotAtt03;
    }

    public void setLotAtt03(CdWhLotDetailDto lotAtt03) {
        this.lotAtt03 = lotAtt03;
    }

    public Integer getSubmitQty() {
        return submitQty;
    }

    public void setSubmitQty(Integer submitQty) {
        this.submitQty = submitQty;
    }

    public BigDecimal getCsRuleQty() {
        return csRuleQty;
    }

    public void setCsRuleQty(BigDecimal csRuleQty) {
        this.csRuleQty = csRuleQty;
    }

    public BigDecimal getWaitForReceiptQty() {
        return waitForReceiptQty;
    }

    public void setWaitForReceiptQty(BigDecimal waitForReceiptQty) {
        this.waitForReceiptQty = waitForReceiptQty;
    }

    public List<ListDetail> getListDetail() {
        return listDetail;
    }

    public void setListDetail(List<ListDetail> listDetail) {
        this.listDetail = listDetail;
    }

    public String getCsUnit() {
        return csUnit;
    }

    public void setCsUnit(String csUnit) {
        this.csUnit = csUnit;
    }

    public String getEaUnit() {
        return eaUnit;
    }

    public void setEaUnit(String eaUnit) {
        this.eaUnit = eaUnit;
    }

    public String getTempBatch() {
        return tempBatch;
    }

    public void setTempBatch(String tempBatch) {
        this.tempBatch = tempBatch;
    }

    public String getTempMa() {
        return tempMa;
    }

    public void setTempMa(String tempMa) {
        this.tempMa = tempMa;
    }

    public String getTempEx() {
        return tempEx;
    }

    public void setTempEx(String tempEx) {
        this.tempEx = tempEx;
    }

    public String getTempAg() {
        return tempAg;
    }

    public void setTempAg(String tempAg) {
        this.tempAg = tempAg;
    }

    public class ListDetail implements Serializable {

        @ShowAnnotation
        private String custItemCode;
        @ShowAnnotation
        private String itemName;
        @ShowAnnotation
        private String lotAtt4Str;

        private String detailId;
        private String itemCode;
        private String lotAtt4;
        private String ruleName;
        private String isValidity; //y是n否
        private String outLifeDays;
        private String inLifeDays;
        private BigDecimal periodOfValidity;
        private String validityUnit;
        private BigDecimal orderTotalQty;
        private BigDecimal waitForReceiptQty;
        private BigDecimal receiptedQty;

        private String whBarcode69;

        private String packageCode;
        private String csQuantity;

        private BigDecimal waitForCsQty;
        private BigDecimal waitForEaQty;
        private BigDecimal waitForOtQty;
        private BigDecimal waitForIpQty;

        private List<PackageRelation> packageRelationList;

        private int isDecimal;

        private String priorityCheckLocation;

        private String cdpaFormat;

        public String getCdpaFormat() {
            return cdpaFormat;
        }

        public void setCdpaFormat(String cdpaFormat) {
            this.cdpaFormat = cdpaFormat;
        }

        public String getPriorityCheckLocation() {
            return priorityCheckLocation;
        }

        public void setPriorityCheckLocation(String priorityCheckLocation) {
            this.priorityCheckLocation = priorityCheckLocation;
        }

        public int getIsDecimal() {
            return isDecimal;
        }

        public void setIsDecimal(int isDecimal) {
            this.isDecimal = isDecimal;
        }

        public BigDecimal getWaitForIpQty() {
            return waitForIpQty;
        }

        public void setWaitForIpQty(BigDecimal waitForIpQty) {
            this.waitForIpQty = waitForIpQty;
        }

        public BigDecimal getWaitForOtQty() {
            return waitForOtQty;
        }

        public void setWaitForOtQty(BigDecimal waitForOtQty) {
            this.waitForOtQty = waitForOtQty;
        }

        public List<PackageRelation> getPackageRelationList() {
            return packageRelationList;
        }

        public void setPackageRelationList(List<PackageRelation> packageRelationList) {
            this.packageRelationList = packageRelationList;
        }

        public BigDecimal getWaitForCsQty() {
            return waitForCsQty;
        }

        public void setWaitForCsQty(BigDecimal waitForCsQty) {
            this.waitForCsQty = waitForCsQty;
        }

        public BigDecimal getWaitForEaQty() {
            return waitForEaQty;
        }

        public void setWaitForEaQty(BigDecimal waitForEaQty) {
            this.waitForEaQty = waitForEaQty;
        }

        public String getPackageCode() {
            return packageCode;
        }

        public void setPackageCode(String packageCode) {
            this.packageCode = packageCode;
        }

        public String getCsQuantity() {
            return csQuantity;
        }

        public void setCsQuantity(String csQuantity) {
            this.csQuantity = csQuantity;
        }

        public String getWhBarcode69() {
            return whBarcode69;
        }

        public void setWhBarcode69(String whBarcode69) {
            this.whBarcode69 = whBarcode69;
        }

        public String getDetailId() {
            return detailId;
        }

        public void setDetailId(String detailId) {
            this.detailId = detailId;
        }

        public String getItemCode() {
            return itemCode;
        }

        public void setItemCode(String itemCode) {
            this.itemCode = itemCode;
        }

        public String getCustItemCode() {
            return custItemCode;
        }

        public void setCustItemCode(String custItemCode) {
            this.custItemCode = custItemCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getLotAtt4() {
            return lotAtt4;
        }

        public void setLotAtt4(String lotAtt4) {
            this.lotAtt4 = lotAtt4;
        }

        public BigDecimal getOrderTotalQty() {
            return AppUtils.getBigDecimalValue(orderTotalQty);
        }

        public void setOrderTotalQty(BigDecimal orderTotalQty) {
            this.orderTotalQty = orderTotalQty;
        }

        public BigDecimal getWaitForReceiptQty() {
            return waitForReceiptQty;
        }

        public void setWaitForReceiptQty(BigDecimal waitForReceiptQty) {
            this.waitForReceiptQty = waitForReceiptQty;
        }

        public BigDecimal getReceiptedQty() {
            return AppUtils.getBigDecimalValue(receiptedQty);
        }

        public void setReceiptedQty(BigDecimal receiptedQty) {
            this.receiptedQty = receiptedQty;
        }

        public String getLotAtt4Str() {
            return lotAtt4Str;
        }

        public void setLotAtt4Str(String lotAtt4Str) {
            this.lotAtt4Str = lotAtt4Str;
        }

        public String getRuleName() {
            return ruleName;
        }

        public void setRuleName(String ruleName) {
            this.ruleName = ruleName;
        }

        public String getIsValidity() {
            return isValidity;
        }

        public void setIsValidity(String isValidity) {
            this.isValidity = isValidity;
        }

        public String getValidityUnit() {
            return validityUnit;
        }

        public void setValidityUnit(String validityUnit) {
            this.validityUnit = validityUnit;
        }

        public BigDecimal getPeriodOfValidity() {
            return periodOfValidity;
        }

        public void setPeriodOfValidity(BigDecimal periodOfValidity) {
            this.periodOfValidity = periodOfValidity;
        }

        public String getOutLifeDays() {
            return outLifeDays;
        }

        public void setOutLifeDays(String outLifeDays) {
            this.outLifeDays = outLifeDays;
        }

        public String getInLifeDays() {
            return inLifeDays;
        }

        public void setInLifeDays(String inLifeDays) {
            this.inLifeDays = inLifeDays;
        }
    }
}
