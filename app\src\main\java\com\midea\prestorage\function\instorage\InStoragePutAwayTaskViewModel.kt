package com.midea.prestorage.function.instorage

import CheckUtil
import android.app.Application
import android.view.View
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Transformations
import androidx.lifecycle.ViewModelProvider
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.SelectEntity
import com.midea.prestorage.beans.net.PutAwayTask
import com.midea.prestorage.beans.net.PutAwayTaskRequest
import com.midea.prestorage.dialog.EmbeddedFilterViewModel
import com.midea.prestorage.event.LiveEvent
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.withContext

class InStoragePutAwayTaskViewModel(application: Application) : BaseViewModel(application) {

    val custItemCode = MutableLiveData<String>()
    val canClearCustItemCode = Transformations.map(custItemCode) {
        it.isNotEmpty()
    }

    val embeddedFilterViewModel = ViewModelProvider.AndroidViewModelFactory.getInstance(application)
        .create(EmbeddedFilterViewModel::class.java).also {
            it.listener = object : EmbeddedFilterViewModel.EmbeddedFilterListener {
                override fun onDaySelect(selectEntity: SelectEntity) {
                    onRefreshCommand.onRefresh()
                }
            }
        }

    val isRefreshing = MutableLiveData<Boolean>()
    val showRecycleView = MutableLiveData<Boolean>()
    val taskListLiveData = MutableLiveData<MutableList<PutAwayTask>>()
    val showSettingDialogEvent = LiveEvent<Map<String, Boolean>>()
    val scanCodeResult = MutableLiveData<PutAwayTask>()

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        isRefreshing.value = true
        requestTaskList()
    }

    val settingViewModel = InStoragePutAwayTaskSettingViewModel(application)

    override fun init() {
    }

    fun initSetting() {
        settingViewModel.init()
    }

    fun onEnterCustItemCode() {
        if (CheckUtil.isFastDoubleClick()) {
            scanCode()
        }
    }

    fun clearCustItemCode() {
        custItemCode.value = ""
        onRefreshCommand.onRefresh()
    }

    fun popupDay(view: View) {
        embeddedFilterViewModel.popupDay(
            view, listOf(
                SelectEntity("1天", shipmentType = "1"),
                SelectEntity("7天", shipmentType = "7"),
                SelectEntity("30天", shipmentType = "30"),
            )
        )
    }

    private fun requestTaskList(): Job = launch(showDialog = true) {
        val result = withContext(Dispatchers.IO) {
            RetrofitHelper.getAppAPI().putAwayTaskList(
                PutAwayTaskRequest(
                    days = embeddedFilterViewModel.dayBean.shipmentType,
                    //接口分页，但是因为需要扫码过滤，所以需要拿全部。实际上每个仓库的上架任务都是有限的。跟产品和后端协商传个1000
                    pageSize = 1000,
                    groupByList = groupByListParam(),
                    custItemCode = custItemCode.value ?: ""
                )
            )
        }
        isRefreshing.value = false
        if (result.code == 0L) {
            taskListLiveData.value = result.data?.list ?: mutableListOf()
            showRecycleView.value = taskListLiveData.value?.isNotEmpty() == true
        } else {
            result.msg?.let { showNotification(it, false) }
        }
    }

    @VisibleForTesting
    fun groupByListParam() = settingViewModel.settingLiveData.value?.filter { it.value }
        ?.keys?.map {
            when (it) {
                InStoragePutAwayTaskSettingViewModel.SHOW_LOT_ATT_01 -> "irc.lot_att01"
                InStoragePutAwayTaskSettingViewModel.SHOW_LOT_ATT_03 -> "irc.lot_att03"
                InStoragePutAwayTaskSettingViewModel.SHOW_LOT_ATT_05 -> "irc.lot_att05"
                InStoragePutAwayTaskSettingViewModel.SHOW_TRACE_ID -> "irc.trace_id"
                else -> ""
            }
        }?.toList() ?: emptyList()

    @VisibleForTesting
    fun scanCode(): Job = launch(showDialog = true, error = {}) {
        checkScanCode()
        val result = withContext(Dispatchers.IO) {
            RetrofitHelper.getAppAPI().putAwayTaskList(
                PutAwayTaskRequest(
                    days = embeddedFilterViewModel.dayBean.shipmentType,
                    //接口分页，但是因为需要扫码过滤，所以需要拿全部。实际上每个仓库的上架任务都是有限的。跟产品和后端协商传个1000
                    pageSize = 1000,
                    groupByList = groupByListParam(),
                    custItemCode = custItemCode.value ?: ""
                )
            )
        }
        if (result.code == 0L) {
            if (result.data?.list.isNullOrEmpty()) {
                custItemCode.value = ""
                showNotification("商品不在待上架任务范围", false)
            } else if (result.data!!.list!!.size == 1) {
                scanCodeResult.value = result.data!!.list!![0]
            } else {
                taskListLiveData.value = result.data!!.list!!
            }
            showRecycleView.value = taskListLiveData.value?.isNotEmpty() == true
        } else {
            result.msg?.let { showNotification(it, false) }
        }
    }

    @VisibleForTesting
    @Throws(IllegalArgumentException::class)
    fun checkScanCode() {
        if (custItemCode.value.isNullOrBlank()) {
            throw IllegalArgumentException("商品编码/69码/跟踪号不能为空")
        }
    }

    fun showSettingDialog() {
        showSettingDialogEvent.value = settingViewModel.settingLiveData.value
    }

}