package com.midea.prestorage.beans.net;

import android.os.Parcel;
import android.os.Parcelable;

public class CdWhLotDetailDto implements Parcelable {

    /**
     * jobId
     */
    private Double jobId;

    /**
     * projectId
     */
    private String projectId;

    /**
     * lotCode
     */
    private String lotCode;

    /**
     * lotAtt
     */
    private String lotAtt;

    /**
     * title
     */
    private String title;

    /**
     * foreignTitle
     */
    private String foreignTitle;

    /**
     * inputControl
     */
    private String inputControl;

    /**
     * fieldType
     */
    private String fieldType;

    /**
     * key
     */
    private String key;

    /**
     * recVer
     */
    private Double recVer;

    /**
     * creator
     */
    private String creator;

    /**
     * modifier
     */
    private String modifier;

    /**
     * modifyTime
     */
    private String modifyTime;

    /**
     * recStatus
     */
    private Double recStatus;

    /**
     * timeZone
     */
    private String timeZone;

    /**
     * orgId
     */
    private String orgId;

    /**
     * siteCode
     */
    private String siteCode;

    /**
     * companyCode
     */
    private String companyCode;

    /**
     * whCode
     */
    private String whCode;

    /**
     * isPrint
     */
    private String isPrint;

    /**
     * cdcmMaterialNo
     */
    private String cdcmMaterialNo;

    /**
     * cdcmCustMaterialNo
     */
    private String cdcmCustMaterialNo;

    private boolean isOpen;

    private String value;

    protected CdWhLotDetailDto(Parcel in) {
        if (in.readByte() == 0) {
            jobId = null;
        } else {
            jobId = in.readDouble();
        }
        projectId = in.readString();
        lotCode = in.readString();
        lotAtt = in.readString();
        title = in.readString();
        foreignTitle = in.readString();
        inputControl = in.readString();
        fieldType = in.readString();
        key = in.readString();
        if (in.readByte() == 0) {
            recVer = null;
        } else {
            recVer = in.readDouble();
        }
        creator = in.readString();
        modifier = in.readString();
        modifyTime = in.readString();
        if (in.readByte() == 0) {
            recStatus = null;
        } else {
            recStatus = in.readDouble();
        }
        timeZone = in.readString();
        orgId = in.readString();
        siteCode = in.readString();
        companyCode = in.readString();
        whCode = in.readString();
        isPrint = in.readString();
        cdcmMaterialNo = in.readString();
        cdcmCustMaterialNo = in.readString();
        isOpen = in.readByte() != 0;
        value = in.readString();
    }

    public static final Creator<CdWhLotDetailDto> CREATOR = new Creator<CdWhLotDetailDto>() {
        @Override
        public CdWhLotDetailDto createFromParcel(Parcel in) {
            return new CdWhLotDetailDto(in);
        }

        @Override
        public CdWhLotDetailDto[] newArray(int size) {
            return new CdWhLotDetailDto[size];
        }
    };

    public Double getJobId() {
        return jobId;
    }

    public void setJobId(Double jobId) {
        this.jobId = jobId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getLotCode() {
        return lotCode;
    }

    public void setLotCode(String lotCode) {
        this.lotCode = lotCode;
    }

    public String getLotAtt() {
        return lotAtt;
    }

    public void setLotAtt(String lotAtt) {
        this.lotAtt = lotAtt;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getForeignTitle() {
        return foreignTitle;
    }

    public void setForeignTitle(String foreignTitle) {
        this.foreignTitle = foreignTitle;
    }

    public String getInputControl() {
        return inputControl;
    }

    public void setInputControl(String inputControl) {
        this.inputControl = inputControl;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Double getRecVer() {
        return recVer;
    }

    public void setRecVer(Double recVer) {
        this.recVer = recVer;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Double getRecStatus() {
        return recStatus;
    }

    public void setRecStatus(Double recStatus) {
        this.recStatus = recStatus;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getIsPrint() {
        return isPrint;
    }

    public void setIsPrint(String isPrint) {
        this.isPrint = isPrint;
    }

    public String getCdcmMaterialNo() {
        return cdcmMaterialNo;
    }

    public void setCdcmMaterialNo(String cdcmMaterialNo) {
        this.cdcmMaterialNo = cdcmMaterialNo;
    }

    public String getCdcmCustMaterialNo() {
        return cdcmCustMaterialNo;
    }

    public void setCdcmCustMaterialNo(String cdcmCustMaterialNo) {
        this.cdcmCustMaterialNo = cdcmCustMaterialNo;
    }

    public boolean isOpen() {
        return isOpen;
    }

    public void setOpen(boolean open) {
        isOpen = open;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        if (jobId == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeDouble(jobId);
        }
        dest.writeString(projectId);
        dest.writeString(lotCode);
        dest.writeString(lotAtt);
        dest.writeString(title);
        dest.writeString(foreignTitle);
        dest.writeString(inputControl);
        dest.writeString(fieldType);
        dest.writeString(key);
        if (recVer == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeDouble(recVer);
        }
        dest.writeString(creator);
        dest.writeString(modifier);
        dest.writeString(modifyTime);
        if (recStatus == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeDouble(recStatus);
        }
        dest.writeString(timeZone);
        dest.writeString(orgId);
        dest.writeString(siteCode);
        dest.writeString(companyCode);
        dest.writeString(whCode);
        dest.writeString(isPrint);
        dest.writeString(cdcmMaterialNo);
        dest.writeString(cdcmCustMaterialNo);
        dest.writeByte((byte) (isOpen ? 1 : 0));
        dest.writeString(value);
    }
}