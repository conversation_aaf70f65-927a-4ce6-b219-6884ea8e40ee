package com.midea.prestorage.function.agreement

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import com.midea.prestorage.beans.Agreement
import com.midea.prestoragesaas.databinding.ActivityAgreementBinding


class AgreementActivity : AppCompatActivity() {

    companion object {
        private const val AGREEMENT = "AGREEMENT"
        fun newIntent(context: Context, agreement: Agreement) =
            Intent(context, AgreementActivity::class.java).also {
                it.putExtra(AGREEMENT, agreement)
            }
    }

    private lateinit var binding: ActivityAgreementBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAgreementBinding.inflate(layoutInflater)
        setContentView(binding.root)
        val agreement = intent.getParcelableExtra<Agreement>(AGREEMENT)
        if (agreement?.htmlPath.isNullOrEmpty()) {
            finish()
            return
        }
        binding.vm = AgreementVM(this).also {
            it.titleBarVM.title.set(agreement?.name?.replace("《", "")?.replace("》", "") ?: "")
        }
        binding.webView.loadUrl("file:///android_asset/${agreement?.htmlPath}")
        binding.webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                url?.let {
                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                    startActivity(intent)
                    return true
                }
                return super.shouldOverrideUrlLoading(view, url)
            }

            @RequiresApi(Build.VERSION_CODES.LOLLIPOP)
            override fun shouldOverrideUrlLoading(
                view: WebView?,
                request: WebResourceRequest?
            ): Boolean {
                request?.url?.let {
                    val intent = Intent(Intent.ACTION_VIEW, it)
                    startActivity(intent)
                    return true
                }
                return super.shouldOverrideUrlLoading(view, request)
            }
        }
    }

}