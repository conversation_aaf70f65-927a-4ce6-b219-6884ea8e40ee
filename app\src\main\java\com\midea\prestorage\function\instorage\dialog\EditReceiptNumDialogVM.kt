package com.midea.prestorage.function.outstorage.dialog

import android.widget.Toast
import androidx.databinding.ObservableField
import com.midea.prestorage.beans.net.InReceiptSerialScanDto
import com.midea.prestorage.beans.net.SerialScanDto
import com.midea.prestorage.utils.ToastUtils

class EditReceiptNumDialogVM(val dialog: EditReceiptNumDialog) {

    val receiptNum = ObservableField<String>("")
    var serialScan = InReceiptSerialScanDto()

    fun show(strDefaultReceiptNum: String, inReceiptSerialScanDto: InReceiptSerialScanDto) {
        //每次展示 默认为空  有参数才设置
        receiptNum.set("")

        if (!strDefaultReceiptNum.isBlank()) {
            receiptNum.set(strDefaultReceiptNum)
        }
        serialScan = inReceiptSerialScanDto
        dialog.binding.etReceiptNum.requestFocus()
    }

    fun close() {
        dialog.dismiss()
    }

    fun confirm() {
        if (receiptNum.get() != null && !receiptNum.get().isNullOrEmpty() && !receiptNum.get().equals("0")) {
            try {
                serialScan.qty = receiptNum.get()!!.toInt()
            } catch (e: NumberFormatException) {
                ToastUtils.getInstance().taostWithErrorSound(dialog.activity, "数量格式错误或超过最大值", Toast.LENGTH_SHORT)
                return
            }
            // 要弹框编辑数量的是 客户商品编码 而不是sn码 所以第一个参数 isSerialNo = false
            dialog.activity.binding.vm!!.onConfirmScanOne(false, serialScan)
            dialog.dismiss()
        } else {
            ToastUtils.getInstance().taostWithErrorSound(dialog.activity, "数量不能为空或0", Toast.LENGTH_SHORT)
        }


    }
}