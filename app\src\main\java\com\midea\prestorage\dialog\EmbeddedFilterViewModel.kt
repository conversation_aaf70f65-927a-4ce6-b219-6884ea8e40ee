package com.midea.prestorage.dialog

import android.app.Application
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.PopupWindow
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.databinding.ObservableField
import androidx.databinding.ObservableInt
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import com.midea.prestorage.beans.SelectEntity
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ItemFilterTextBinding

class EmbeddedFilterViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        val DAY_OPTIONS = mutableListOf(
            SelectEntity("1天", choose = true, shipmentType = "1"),
            SelectEntity("3天", choose = false, shipmentType = "3"),
            SelectEntity("7天", choose = false, shipmentType = "7"),
            SelectEntity("15天", choose = false, shipmentType = "15")
        )
        val DEFAULT_DAY_OPTION = DAY_OPTIONS[0]
    }

    interface EmbeddedFilterListener {
        fun onDaySelect(selectEntity: SelectEntity)
    }

    var listener: EmbeddedFilterListener? = null

    var dayBean = DEFAULT_DAY_OPTION
        private set

    val dayObservable = ObservableField(DEFAULT_DAY_OPTION.name)

    val dayArrowSrc = ObservableInt(R.drawable.ic_filter_down_gray)

    private val openDayWindow = MutableLiveData<Boolean>()
    private val haveSelectedDay = MutableLiveData<Boolean>()

    val displayMaskLiveData = MutableLiveData<Boolean>()

    val daySelectedMediatorLiveData = MediatorLiveData<Boolean>()

    init {
        mergeMediator(daySelectedMediatorLiveData, openDayWindow)
        updateArrowSrc(false, haveValue = haveSelectedDay.value ?: false, src = dayArrowSrc)
    }

    private fun mergeMediator(
        mediatorLiveData: MediatorLiveData<Boolean>,
        vararg sources: LiveData<Boolean>
    ) {
        val onChanged: (Boolean?) -> Unit = {
            mediatorLiveData.value = sources.any { item -> item.value == true }
        }
        sources.forEach {
            mediatorLiveData.addSource(it, onChanged)
        }
    }

    fun popupDay(anchor: View, options: List<SelectEntity> = DAY_OPTIONS) {
        openDayWindow.value = true
        updateArrowSrc(select = true, haveValue = haveSelectedDay.value ?: false, src = dayArrowSrc)
        displayMaskLiveData.value = true
        val root = LinearLayout(anchor.context)
        root.orientation = LinearLayout.VERTICAL
        val popupWindow = PopupWindow(
            root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        val inflater = LayoutInflater.from(anchor.context)
        for (day in options) {
            val itemBinding = DataBindingUtil.inflate<ItemFilterTextBinding>(
                inflater,
                R.layout.item_filter_text,
                root,
                false
            )
            itemBinding.selected = dayObservable.get() == day.name
            itemBinding.tvName.text = day.name
            itemBinding.tvName.setOnClickListener {
                setDay(day)
                haveSelectedDay.value = true
                updateArrowSrc(select = true, haveValue = haveSelectedDay.value ?: false, src = dayArrowSrc)
                listener?.onDaySelect(day)
                popupWindow.dismiss()
            }
            root.addView(itemBinding.root)
        }
        popupWindow.setBackgroundDrawable(
            ColorDrawable(
                ContextCompat.getColor(
                    anchor.context,
                    R.color.bg_color
                )
            )
        )
        popupWindow.setOnDismissListener {
            updateArrowSrc(select = false, haveValue = haveSelectedDay.value ?: false, src = dayArrowSrc)
            openDayWindow.value = false
            displayMaskLiveData.value = false
        }
        popupWindow.isOutsideTouchable = true
        popupWindow.showAsDropDown(anchor)
    }

    fun setDay(day: SelectEntity) {
        dayBean = day
        dayObservable.set(day.name)
    }

    //打开蓝色向上 关闭灰色向下
    private fun updateArrowSrc(select: Boolean, haveValue: Boolean, src: ObservableInt) {
        val resId = when {
            select -> R.drawable.ic_filter_up_blue
            haveValue -> R.drawable.ic_filter_down_gray
            else -> R.drawable.ic_filter_down_gray
        }
        src.set(resId)
    }

    fun reset() {
        dayBean = DEFAULT_DAY_OPTION
        dayObservable.set(DEFAULT_DAY_OPTION.name)
    }

}