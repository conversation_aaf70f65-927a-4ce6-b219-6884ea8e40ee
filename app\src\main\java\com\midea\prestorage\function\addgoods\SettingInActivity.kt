package com.midea.prestorage.function.addgoods

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.send.ActivitySendDetailsUnionBinding
import com.midea.prestoragesaas.databinding.ActivityAddGoodsSettingBinding
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.SPUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

class SettingInActivity : BaseActivity() {

    companion object {

        fun newIntent(context: Context, toTitle: String): Intent {
            val intent = Intent(context, SettingInActivity::class.java)
            intent.putExtra("toTitle", toTitle)
            return intent
        }
    }

    private lateinit var binding: ActivityAddGoodsSettingUnionBinding
    private lateinit var inStorageVM: SettingInVM

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityAddGoodsSettingUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_add_goods_setting_care
                )
            )
        } else {
            ActivityAddGoodsSettingUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_add_goods_setting
                )
            )
        }
        inStorageVM = SettingInVM(this)
        initView()
        binding.vm = inStorageVM
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    private fun initView() {
        val scanMode = Constants.userInfo?.putMode
        if (scanMode == 0) {
            binding.cbDefault.isChecked = true
        } else if (scanMode == 1) {
            binding.cbRemember.isChecked = true
        }

        binding.llDefault.setOnClickListener {
            binding.cbDefault.isChecked = true
        }

        binding.llRemember.setOnClickListener {
            binding.cbRemember.isChecked = true
        }

        binding.cbDefault.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.cbRemember.isChecked = false
                saveUserInfo(0)
            } else {
                if (!binding.cbRemember.isChecked) {
                    saveUserInfo(null)
                }
            }
        }

        binding.cbRemember.setOnCheckedChangeListener { _, isChecked ->
            if (binding.cbRemember.isChecked) {
                binding.cbDefault.isChecked = false
                saveUserInfo(1)
            } else {
                if (!binding.cbDefault.isChecked) {
                    saveUserInfo(null)
                }
            }
        }
    }

    private fun saveUserInfo(putMode: Int?) {
        Observable.create<String> {
            try {
                Constants.userInfo?.putMode = putMode
                db.saveOrUpdate(Constants.userInfo)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(this, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }
}