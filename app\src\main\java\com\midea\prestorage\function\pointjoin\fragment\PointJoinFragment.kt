package com.midea.prestorage.function.pointjoin.fragment

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.PointDivideDetailBean
import com.midea.prestorage.beans.net.PointJoinList
import com.midea.prestoragesaas.databinding.FragmentPointJoinBinding
import com.midea.prestoragesaas.databinding.PopViewDivideBinding
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.pointjoin.PointJoinDetailActivity
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.components.support.RxFragment
import com.xuexiang.xqrcode.XQRCode

@Suppress("UNCHECKED_CAST")
class PointJoinFragment : RxFragment() {

    companion object {
        fun newInstance(position: Int): PointJoinFragment {
            val bundle = Bundle()
            bundle.putInt("position", position)
            val fragment = PointJoinFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    lateinit var binding: FragmentPointJoinBinding
    private var vm: PointJoinFragmentVM? = null

    private lateinit var popupWindow: PopupWindow
    private lateinit var popViewBinding: PopViewDivideBinding
    private lateinit var daysDialog: FilterDialog
    private lateinit var personDialog: FilterDialog
    lateinit var adapter: PointSelectAdapter<PointJoinList>

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = inflater.let { FragmentPointJoinBinding.inflate(it, container, false) }
        initView()
        return binding.root
    }

    private fun initView() {
        if (vm == null) {
            vm = PointJoinFragmentVM(this)
            binding.vm = vm
        }

        initRecycleView()
        initPopWindow()
        initPopData()
        refreshInfo()
        vm!!.init()
    }

    fun showPointDialog(data: MutableList<PointDivideDetailBean>) {
        val it = Intent(activity, PointJoinDetailActivity::class.java)
        it.putExtra("beans", ArrayList(data))
        it.putExtra("position", vm?.position!!)
        activity?.startActivity(it)
    }

    private fun initRecycleView() {
        binding.srl.setOnRefreshListener(vm!!.onRefreshCommand)
        adapter = PointSelectAdapter(R.layout.item_point_join)
        binding.recycle.layoutManager = LinearLayoutManager(activity)
        binding.recycle.adapter = adapter
        adapter.setItemClick {
            vm!!.itemClick(it.tag as PointJoinList)
        }

        adapter.setChangeSelectStatus {
            val results = adapter.data.filter { !it.isSelected }
            binding.cbSelectAll.isChecked = results.isEmpty()
        }
    }

    fun setCbSelectChecked(isChecked: Boolean) {
        binding.cbSelectAll.isChecked = isChecked
    }

    @SuppressLint("UseRequireInsteadOfGet")
    private fun initPopWindow() {
        popViewBinding =
            DataBindingUtil.inflate(layoutInflater, R.layout.pop_view_divide, null, false)
        popupWindow = PopupWindow(
            popViewBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        popupWindow.setBackgroundDrawable(
            ColorDrawable(
                ContextCompat.getColor(
                    activity!!,
                    R.color.bg_color
                )
            )
        )
        popupWindow.isOutsideTouchable = true
        popViewBinding.llFilterClose.setOnClickListener {
            popupWindow.dismiss()
        }
        popupWindow.setOnDismissListener {
            vm!!.orderNo = binding.edOrderNo.text.toString()
            vm!!.phone = popViewBinding.edPhone.text.toString()
            vm!!.person = popViewBinding.edPerson.text.toString()

            vm!!.refreshData()
            refreshInfo()
        }

        binding.llContainer.setOnClickListener(mClick)
        popViewBinding.llDayNum.setOnClickListener(mClick)
        binding.imgGoodsClean.setOnClickListener(mClick)
        popViewBinding.imgPersonClean.setOnClickListener(mClick)
        popViewBinding.imgPhoneClean.setOnClickListener(mClick)
        binding.imgStartScan.setOnClickListener(mClick)
        popViewBinding.tvDefault.setOnClickListener(mClick)
        popViewBinding.imgPersonChoose.setOnClickListener(mClick)

        binding.llAllChoose.setOnClickListener {
            binding.cbSelectAll.performClick()
        }

        binding.cbSelectAll.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                adapter.allSelect(true)
            }
        }

        binding.cbSelectAll.setOnClickListener {
            if (!binding.cbSelectAll.isChecked) {
                adapter.allSelect(false)
            }
        }

        binding.edOrderNo.setText(vm?.orderNo)
        /*binding.edOrderNo.setOnEditorActionListener { v, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEND
                || actionId == EditorInfo.IME_ACTION_DONE
                || (event != null && KeyEvent.KEYCODE_ENTER == event.keyCode && KeyEvent.ACTION_DOWN == event.action)
            ) {
                popupWindow.dismiss()
            }
            return@setOnEditorActionListener false
        }*/
    }

    fun setEdOrderNoSelectAll() {
        if (TextUtils.isEmpty(binding.edOrderNo.text)) {
            binding.edOrderNo.requestFocus()
        } else {
            binding.edOrderNo.selectAll()
        }
    }

    private var mClick: View.OnClickListener = View.OnClickListener {
        when (it.id) {
            R.id.ll_container -> {
                popupWindow.showAsDropDown(binding.llContainer)
            }
            R.id.ll_day_num -> {
                showDaysDialog(vm!!.days.toMutableList())
            }
            R.id.img_person_choose -> {
                popViewBinding.edPerson.setText("")
                if (vm?.creatorStatus!!.isNotEmpty()) {
                    showPersonDialog(vm?.creatorStatus!!)
                }
            }
            R.id.img_goods_clean -> {
                binding.edOrderNo.setText("")
            }
            R.id.img_person_clean -> {
                popViewBinding.edPerson.setText("")
            }
            R.id.img_phone_clean -> {
                popViewBinding.edPhone.setText("")
            }
            R.id.img_start_scan -> {
                vm!!.startScan()
            }
            R.id.tv_default -> {
                binding.edOrderNo.setText("")
                popViewBinding.edPhone.setText("")
                popViewBinding.edPerson.setText("")
                popViewBinding.tvDay.text = "3天"

                vm?.setDefault()
            }
        }
    }

    private fun showPersonDialog(days: MutableSet<DCBean>) {
        personDialog.addAllData(days.toMutableList() as MutableList<BaseItemShowInfo>)
        personDialog.show()
    }

    private fun initPopData() {
        daysDialog = FilterDialog(activity as RxAppCompatActivity)
        daysDialog.setTitle("请选择天数")
        daysDialog.dismissEdit()
        daysDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            popViewBinding.tvDay.text = it.showInfo
            vm?.dayInfo = it as DCBean
            vm?.dayChange()
            daysDialog.dismiss()
        })

        personDialog = FilterDialog(activity as RxAppCompatActivity)
        personDialog.setTitle("请选择提货人")
        personDialog.dismissEdit()
        personDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            popViewBinding.edPerson.setText(it.showInfo)
            vm?.person = it.showInfo.toString()
            personDialog.dismiss()
        })
    }

    private fun showDaysDialog(days: MutableList<BaseItemShowInfo>) {
        daysDialog.addAllData(days)
        daysDialog.show()
    }

    private fun refreshInfo() {
        binding.warpLinear.removeAllViews()
        if (vm!!.dayInfo != null) {
            binding.warpLinear.addView(getItemView(vm!!.dayInfo?.showInfo!!))
        }
        /* if (!TextUtils.isEmpty(vm!!.orderNo)) {
             binding.warpLinear.addView(getItemView(vm!!.orderNo!!))
         }
         if (!TextUtils.isEmpty(vm!!.phone)) {
             binding.warpLinear.addView(getItemView(vm!!.phone!!))
         }
         if (!TextUtils.isEmpty(vm!!.person)) {
             binding.warpLinear.addView(getItemView(vm!!.person!!))
         }*/
    }

    @SuppressLint("InflateParams")
    private fun getItemView(info: String): View {
        val view = LayoutInflater.from(context).inflate(R.layout.item_filter, null)
        val tvItem = view.findViewById<View>(R.id.tv_item) as TextView
        tvItem.text = info
        return view
    }

    fun showListData(data: MutableList<PointJoinList>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
    }

    fun batchOrSingle(mode: Boolean) {
        vm?.mode?.set(mode)
        adapter.isShowMode = mode
        adapter.notifyDataSetChanged()
    }

    fun notified(isResume: Boolean) {
        if (isResume && this::binding.isInitialized) {
            binding.edOrderNo.setText("")
        }
        vm?.onRefreshCommand?.onRefresh()
    }

    fun personDataChange(mutableList: MutableList<BaseItemShowInfo>) {
        personDialog.addAllData(mutableList)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == BaseActivity.QR_CODE_BACK && resultCode == RxAppCompatActivity.RESULT_OK) {
            //处理扫描结果（在界面上显示）
            binding.edOrderNo.setText(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    fun toastDismiss() {
        if (this::popupWindow.isInitialized) {
            popupWindow.update(
                popViewBinding.root,
                popViewBinding.root.width,
                popViewBinding.root.height
            )
        }
    }
}