package com.midea.prestorage.base

import android.app.Application
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.RetrofitHelperTenant
import com.midea.prestoragesaas.BuildConfig
import com.tencent.bugly.crashreport.CrashReport
import org.xutils.x

class App : Application() {

    companion object {
        // 当前租户  默认annto
        var tenantCode: String? = null

        lateinit var mInstance: App
        fun getInstance() = mInstance
    }

    override fun onCreate() {
        super.onCreate()
        mInstance = this

        x.Ext.init(this)
        x.Ext.setDebug(BuildConfig.DEBUG)
        RetrofitHelper.initOkHttpClient(this)
        RetrofitHelperTenant.initOkHttpClient(this)

        CrashReport.initCrashReport(
            applicationContext,
            BuildConfig.BUGLY_APP_ID,
            !BuildConfig.DEBUG
        )
    }
}