package com.midea.prestorage.beans.net;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class OutPickCancelBean {

    private OutTaskHeaderDto outTaskHeaderDto;
    private List<OutShipmentDetailResponses> outShipmentDetailResponses;

    public OutTaskHeaderDto getOutTaskHeaderDto() {
        return outTaskHeaderDto;
    }

    public void setOutTaskHeaderDto(OutTaskHeaderDto outTaskHeaderDto) {
        this.outTaskHeaderDto = outTaskHeaderDto;
    }

    public List<OutShipmentDetailResponses> getOutShipmentDetailResponses() {
        if (outShipmentDetailResponses == null) {
            return new ArrayList();
        }
        return outShipmentDetailResponses;
    }

    public void setOutShipmentDetailResponses(List<OutShipmentDetailResponses> outShipmentDetailResponses) {
        this.outShipmentDetailResponses = outShipmentDetailResponses;
    }

    public class OutTaskHeaderDto {
        private String taskCode;
        private String assignedBy;
        private String confirmedBy;
        private String createTime;
        private String taskStartTime;
        private String status;

        public String getTaskCode() {
            return taskCode;
        }

        public void setTaskCode(String taskCode) {
            this.taskCode = taskCode;
        }

        public String getAssignedBy() {
            return assignedBy;
        }

        public void setAssignedBy(String assignedBy) {
            this.assignedBy = assignedBy;
        }

        public String getConfirmedBy() {
            return confirmedBy;
        }

        public void setConfirmedBy(String confirmedBy) {
            this.confirmedBy = confirmedBy;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getTaskStartTime() {
            return taskStartTime;
        }

        public void setTaskStartTime(String taskStartTime) {
            this.taskStartTime = taskStartTime;
        }
    }

    public class OutShipmentDetailResponses {

        private OutShipmentHeader outShipmentHeader;
        private OutShipmentHeaderExt outShipmentHeaderExt;
        private List<OutShipmentDetails> outShipmentDetails;

        public OutShipmentHeader getOutShipmentHeader() {
            return outShipmentHeader;
        }

        public void setOutShipmentHeader(OutShipmentHeader outShipmentHeader) {
            this.outShipmentHeader = outShipmentHeader;
        }

        public OutShipmentHeaderExt getOutShipmentHeaderExt() {
            return outShipmentHeaderExt;
        }

        public void setOutShipmentHeaderExt(OutShipmentHeaderExt outShipmentHeaderExt) {
            this.outShipmentHeaderExt = outShipmentHeaderExt;
        }

        public List<OutShipmentDetails> getOutShipmentDetails() {
            if (outShipmentDetails == null) {
                return new ArrayList<>();
            }
            return outShipmentDetails;
        }

        public void setOutShipmentDetails(List<OutShipmentDetails> outShipmentDetails) {
            this.outShipmentDetails = outShipmentDetails;
        }
    }

    public class OutShipmentDetails {
        private String orderBy;
        private String orderByType;
        private String ids;
        private String tenantCodes;
        private String count;
        private String ownerCode;
        private String shipmentCode;
        private String customerOrderCode;
        private String customerOrderLineNum;
        private String custItemCode;
        private String itemCode;
        private String itemName;
        private BigDecimal planQty;
        private String allocatedQty;
        private String shipQty;
        private String unit;
        private String netWeight;
        private String weight;
        private String volume;
        private String itemSuiteCode;
        private String kitFlag;
        private String itemSuiteQty;
        private String lotAtt01;
        private String lotAtt02;
        private String lotAtt03;
        private String lotAtt04;
        private String lotAtt04Str;
        private String lotAtt05;

        private OutShipmentHeader parentBean;

        public String getOrderBy() {
            return orderBy;
        }

        public void setOrderBy(String orderBy) {
            this.orderBy = orderBy;
        }

        public String getOrderByType() {
            return orderByType;
        }

        public void setOrderByType(String orderByType) {
            this.orderByType = orderByType;
        }

        public String getIds() {
            return ids;
        }

        public void setIds(String ids) {
            this.ids = ids;
        }

        public String getTenantCodes() {
            return tenantCodes;
        }

        public void setTenantCodes(String tenantCodes) {
            this.tenantCodes = tenantCodes;
        }

        public String getCount() {
            return count;
        }

        public void setCount(String count) {
            this.count = count;
        }

        public String getOwnerCode() {
            return ownerCode;
        }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
        }

        public String getShipmentCode() {
            return shipmentCode;
        }

        public void setShipmentCode(String shipmentCode) {
            this.shipmentCode = shipmentCode;
        }

        public String getCustomerOrderCode() {
            return customerOrderCode;
        }

        public void setCustomerOrderCode(String customerOrderCode) {
            this.customerOrderCode = customerOrderCode;
        }

        public String getCustomerOrderLineNum() {
            return customerOrderLineNum;
        }

        public void setCustomerOrderLineNum(String customerOrderLineNum) {
            this.customerOrderLineNum = customerOrderLineNum;
        }

        public String getCustItemCode() {
            return custItemCode;
        }

        public void setCustItemCode(String custItemCode) {
            this.custItemCode = custItemCode;
        }

        public String getItemCode() {
            return itemCode;
        }

        public void setItemCode(String itemCode) {
            this.itemCode = itemCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getAllocatedQty() {
            return allocatedQty;
        }

        public void setAllocatedQty(String allocatedQty) {
            this.allocatedQty = allocatedQty;
        }

        public String getShipQty() {
            return shipQty;
        }

        public void setShipQty(String shipQty) {
            this.shipQty = shipQty;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public String getNetWeight() {
            return netWeight;
        }

        public void setNetWeight(String netWeight) {
            this.netWeight = netWeight;
        }

        public String getWeight() {
            return weight;
        }

        public void setWeight(String weight) {
            this.weight = weight;
        }

        public String getVolume() {
            return volume;
        }

        public void setVolume(String volume) {
            this.volume = volume;
        }

        public String getItemSuiteCode() {
            return itemSuiteCode;
        }

        public void setItemSuiteCode(String itemSuiteCode) {
            this.itemSuiteCode = itemSuiteCode;
        }

        public String getKitFlag() {
            return kitFlag;
        }

        public void setKitFlag(String kitFlag) {
            this.kitFlag = kitFlag;
        }

        public String getItemSuiteQty() {
            return itemSuiteQty;
        }

        public void setItemSuiteQty(String itemSuiteQty) {
            this.itemSuiteQty = itemSuiteQty;
        }

        public String getLotAtt01() {
            return lotAtt01;
        }

        public void setLotAtt01(String lotAtt01) {
            this.lotAtt01 = lotAtt01;
        }

        public String getLotAtt02() {
            return lotAtt02;
        }

        public void setLotAtt02(String lotAtt02) {
            this.lotAtt02 = lotAtt02;
        }

        public String getLotAtt03() {
            return lotAtt03;
        }

        public void setLotAtt03(String lotAtt03) {
            this.lotAtt03 = lotAtt03;
        }

        public String getLotAtt04() {
            return lotAtt04;
        }

        public void setLotAtt04(String lotAtt04) {
            this.lotAtt04 = lotAtt04;
        }

        public String getLotAtt05() {
            return lotAtt05;
        }

        public void setLotAtt05(String lotAtt05) {
            this.lotAtt05 = lotAtt05;
        }

        public OutShipmentHeader getParentBean() {
            return parentBean;
        }

        public void setParentBean(OutShipmentHeader parentBean) {
            this.parentBean = parentBean;
        }

        public int getPlanQty() {
            if (planQty == null) {
                return 0;
            }
            return planQty.intValue();
        }

        public void setPlanQty(BigDecimal planQty) {
            this.planQty = planQty;
        }

        public String getLotAtt04Str() {
            return lotAtt04Str;
        }

        public void setLotAtt04Str(String lotAtt04Str) {
            this.lotAtt04Str = lotAtt04Str;
        }
    }

    public class OutShipmentHeaderExt {
        private String orderBy;
        private String orderByType;
        private String count;
        private String startTime;
        private String endTime;
        private String shipmentCode;
        private String storeCode;
        private String storeName;
        private String customerCode;
        private String customerName;
        private String transhubOrderNo;
        private String transhubCode;
        private String transhubName;
        private String transhubAddress;
        private String transhubPhone;
        private String transhubContactPerson;
        private String unscanMark;
        private String unscanNote;
        private String parentOrderNo;
        private String orderNo;
        private String erpOrderLinkCode;
        private String relationOrderNo;
        private String uploadBatch;
        private String allocFailNumber;
        private String waybillNo;

        public String getOrderBy() {
            return orderBy;
        }

        public void setOrderBy(String orderBy) {
            this.orderBy = orderBy;
        }

        public String getOrderByType() {
            return orderByType;
        }

        public void setOrderByType(String orderByType) {
            this.orderByType = orderByType;
        }

        public String getCount() {
            return count;
        }

        public void setCount(String count) {
            this.count = count;
        }

        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public String getShipmentCode() {
            return shipmentCode;
        }

        public void setShipmentCode(String shipmentCode) {
            this.shipmentCode = shipmentCode;
        }

        public String getStoreCode() {
            return storeCode;
        }

        public void setStoreCode(String storeCode) {
            this.storeCode = storeCode;
        }

        public String getStoreName() {
            return storeName;
        }

        public void setStoreName(String storeName) {
            this.storeName = storeName;
        }

        public String getCustomerCode() {
            return customerCode;
        }

        public void setCustomerCode(String customerCode) {
            this.customerCode = customerCode;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public String getTranshubOrderNo() {
            return transhubOrderNo;
        }

        public void setTranshubOrderNo(String transhubOrderNo) {
            this.transhubOrderNo = transhubOrderNo;
        }

        public String getTranshubCode() {
            return transhubCode;
        }

        public void setTranshubCode(String transhubCode) {
            this.transhubCode = transhubCode;
        }

        public String getTranshubName() {
            return transhubName;
        }

        public void setTranshubName(String transhubName) {
            this.transhubName = transhubName;
        }

        public String getTranshubAddress() {
            return transhubAddress;
        }

        public void setTranshubAddress(String transhubAddress) {
            this.transhubAddress = transhubAddress;
        }

        public String getTranshubPhone() {
            return transhubPhone;
        }

        public void setTranshubPhone(String transhubPhone) {
            this.transhubPhone = transhubPhone;
        }

        public String getTranshubContactPerson() {
            return transhubContactPerson;
        }

        public void setTranshubContactPerson(String transhubContactPerson) {
            this.transhubContactPerson = transhubContactPerson;
        }

        public String getUnscanMark() {
            return unscanMark;
        }

        public void setUnscanMark(String unscanMark) {
            this.unscanMark = unscanMark;
        }

        public String getUnscanNote() {
            return unscanNote;
        }

        public void setUnscanNote(String unscanNote) {
            this.unscanNote = unscanNote;
        }

        public String getParentOrderNo() {
            return parentOrderNo;
        }

        public void setParentOrderNo(String parentOrderNo) {
            this.parentOrderNo = parentOrderNo;
        }

        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public String getErpOrderLinkCode() {
            return erpOrderLinkCode;
        }

        public void setErpOrderLinkCode(String erpOrderLinkCode) {
            this.erpOrderLinkCode = erpOrderLinkCode;
        }

        public String getRelationOrderNo() {
            return relationOrderNo;
        }

        public void setRelationOrderNo(String relationOrderNo) {
            this.relationOrderNo = relationOrderNo;
        }

        public String getUploadBatch() {
            return uploadBatch;
        }

        public void setUploadBatch(String uploadBatch) {
            this.uploadBatch = uploadBatch;
        }

        public String getAllocFailNumber() {
            return allocFailNumber;
        }

        public void setAllocFailNumber(String allocFailNumber) {
            this.allocFailNumber = allocFailNumber;
        }

        public String getWaybillNo() {
            return waybillNo;
        }

        public void setWaybillNo(String waybillNo) {
            this.waybillNo = waybillNo;
        }
    }


    public class OutShipmentHeader {
        private String id;
        private String orderBy;
        private String count;
        private String shipmentCode;
        private String shipmentTypeStr;
        private String shipmentType;
        private String status;
        private String statusStr;
        private String ownerCode;
        private String ownerName;
        private String custOrderCode;
        private String shipToName;
        private String shipToProvinceCode;
        private String shipToProvinceName;
        private String shipToCityCode;
        private String shipToCityName;
        private String shipToDistrictCode;
        private String shipToDistrictName;
        private String shipToTownCode;
        private String shipToTownName;
        private String shipToAddress;
        private String shipToAttentionTo;
        private String shipToPhoneNum;
        private String shipToMobile;
        private String shipToPostalCode;
        private String waveNo;
        private String totalWeight;
        private BigDecimal totalQty;
        private String totalVolume;
        private String totalLines;
        private String totalValue;
        private String upSystemNote;
        private String shipmentNote;
        private String businessType;
        private String companyCode;
        private String companyName;
        private String siteCode;
        private String siteName;
        private String shippingWay;

        private boolean isCancel;
        private boolean isExpand;

        public String getOrderBy() {
            return orderBy;
        }

        public void setOrderBy(String orderBy) {
            this.orderBy = orderBy;
        }

        public String getCount() {
            return count;
        }

        public void setCount(String count) {
            this.count = count;
        }

        public String getShipmentCode() {
            return shipmentCode;
        }

        public void setShipmentCode(String shipmentCode) {
            this.shipmentCode = shipmentCode;
        }

        public String getShipmentType() {
            return shipmentType;
        }

        public void setShipmentType(String shipmentType) {
            this.shipmentType = shipmentType;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getOwnerCode() {
            return ownerCode;
        }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
        }

        public String getOwnerName() {
            return ownerName;
        }

        public void setOwnerName(String ownerName) {
            this.ownerName = ownerName;
        }

        public String getCustOrderCode() {
            return custOrderCode;
        }

        public void setCustOrderCode(String custOrderCode) {
            this.custOrderCode = custOrderCode;
        }

        public String getShipToName() {
            return shipToName;
        }

        public void setShipToName(String shipToName) {
            this.shipToName = shipToName;
        }

        public String getShipToProvinceCode() {
            return shipToProvinceCode;
        }

        public void setShipToProvinceCode(String shipToProvinceCode) {
            this.shipToProvinceCode = shipToProvinceCode;
        }

        public String getShipToProvinceName() {
            return shipToProvinceName;
        }

        public void setShipToProvinceName(String shipToProvinceName) {
            this.shipToProvinceName = shipToProvinceName;
        }

        public String getShipToCityCode() {
            return shipToCityCode;
        }

        public void setShipToCityCode(String shipToCityCode) {
            this.shipToCityCode = shipToCityCode;
        }

        public String getShipToCityName() {
            return shipToCityName;
        }

        public void setShipToCityName(String shipToCityName) {
            this.shipToCityName = shipToCityName;
        }

        public String getShipToDistrictCode() {
            return shipToDistrictCode;
        }

        public void setShipToDistrictCode(String shipToDistrictCode) {
            this.shipToDistrictCode = shipToDistrictCode;
        }

        public String getShipToDistrictName() {
            return shipToDistrictName;
        }

        public void setShipToDistrictName(String shipToDistrictName) {
            this.shipToDistrictName = shipToDistrictName;
        }

        public String getShipToTownCode() {
            return shipToTownCode;
        }

        public void setShipToTownCode(String shipToTownCode) {
            this.shipToTownCode = shipToTownCode;
        }

        public String getShipToTownName() {
            return shipToTownName;
        }

        public void setShipToTownName(String shipToTownName) {
            this.shipToTownName = shipToTownName;
        }

        public String getShipToAddress() {
            return shipToAddress;
        }

        public void setShipToAddress(String shipToAddress) {
            this.shipToAddress = shipToAddress;
        }

        public String getShipToAttentionTo() {
            return shipToAttentionTo;
        }

        public void setShipToAttentionTo(String shipToAttentionTo) {
            this.shipToAttentionTo = shipToAttentionTo;
        }

        public String getShipToPhoneNum() {
            return shipToPhoneNum;
        }

        public void setShipToPhoneNum(String shipToPhoneNum) {
            this.shipToPhoneNum = shipToPhoneNum;
        }

        public String getShipToMobile() {
            return shipToMobile;
        }

        public void setShipToMobile(String shipToMobile) {
            this.shipToMobile = shipToMobile;
        }

        public String getShipToPostalCode() {
            return shipToPostalCode;
        }

        public void setShipToPostalCode(String shipToPostalCode) {
            this.shipToPostalCode = shipToPostalCode;
        }

        public String getWaveNo() {
            return waveNo;
        }

        public void setWaveNo(String waveNo) {
            this.waveNo = waveNo;
        }

        public String getTotalWeight() {
            return totalWeight;
        }

        public void setTotalWeight(String totalWeight) {
            this.totalWeight = totalWeight;
        }

        public String getTotalVolume() {
            return totalVolume;
        }

        public void setTotalVolume(String totalVolume) {
            this.totalVolume = totalVolume;
        }

        public String getTotalLines() {
            return totalLines;
        }

        public void setTotalLines(String totalLines) {
            this.totalLines = totalLines;
        }

        public String getTotalValue() {
            return totalValue;
        }

        public void setTotalValue(String totalValue) {
            this.totalValue = totalValue;
        }

        public String getUpSystemNote() {
            return upSystemNote;
        }

        public void setUpSystemNote(String upSystemNote) {
            this.upSystemNote = upSystemNote;
        }

        public String getShipmentNote() {
            return shipmentNote;
        }

        public void setShipmentNote(String shipmentNote) {
            this.shipmentNote = shipmentNote;
        }

        public String getBusinessType() {
            return businessType;
        }

        public void setBusinessType(String businessType) {
            this.businessType = businessType;
        }

        public String getCompanyCode() {
            return companyCode;
        }

        public void setCompanyCode(String companyCode) {
            this.companyCode = companyCode;
        }

        public String getCompanyName() {
            return companyName;
        }

        public void setCompanyName(String companyName) {
            this.companyName = companyName;
        }

        public String getSiteCode() {
            return siteCode;
        }

        public void setSiteCode(String siteCode) {
            this.siteCode = siteCode;
        }

        public String getSiteName() {
            return siteName;
        }

        public void setSiteName(String siteName) {
            this.siteName = siteName;
        }

        public String getShippingWay() {
            return shippingWay;
        }

        public void setShippingWay(String shippingWay) {
            this.shippingWay = shippingWay;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public boolean isExpand() {
            return isExpand;
        }

        public void setExpand(boolean expand) {
            isExpand = expand;
        }

        public int getTotalQty() {
            if (totalQty == null) {
                return 0;
            }
            return totalQty.intValue();
        }

        public void setTotalQty(BigDecimal totalQty) {
            this.totalQty = totalQty;
        }

        public String getShipmentTypeStr() {
            return shipmentTypeStr;
        }

        public void setShipmentTypeStr(String shipmentTypeStr) {
            this.shipmentTypeStr = shipmentTypeStr;
        }

        public String getStatusStr() {
            return statusStr;
        }

        public void setStatusStr(String statusStr) {
            this.statusStr = statusStr;
        }

        public boolean isCancel() {
            return isCancel;
        }

        public void setCancel(boolean cancel) {
            isCancel = cancel;
        }
    }
}