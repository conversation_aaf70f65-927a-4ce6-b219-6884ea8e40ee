package com.midea.prestorage.function.inv

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.beans.net.InvSetDetailList
import com.midea.prestorage.beans.net.InvSetList
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.outstorage.ActivityBindShippingLocUnionBinding
import com.midea.prestoragesaas.databinding.ActivitySetPackageBinding
import com.midea.prestorage.function.outstorage.dialog.SendTipDialog
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.SPUtils
import com.xuexiang.xqrcode.XQRCode

/**
 * 集托
 */
class SetPackageActivity : BaseActivity() {
    lateinit var binding: ActivitySetPackageUnionBinding
    private var vm = SetPackageVM(this)
    val adapter = OutStorageListAdapter()
    lateinit var sendTipDialog: SendTipDialog

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivitySetPackageUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_set_package_care
                )
            )
        } else {
            ActivitySetPackageUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_set_package
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        binding.vm = vm

        initView()
        initRecycle()
        initDialog()
        inputRequest()
    }

    override fun onResume() {
        super.onResume()
        vm.init()
    }

    private fun initView() {
        binding.llAllChoose.setOnClickListener {
            binding.cbSelect.performClick()
        }

        binding.cbSelect.setOnClickListener {
            if (!binding.cbSelect.isChecked) {
                val data: List<InvSetList> = adapter.data
                for (i in data.indices) {
                    if (data[i].status == "集托中") { //只把集托中的取消勾选，集托完成的默认勾选
                        data[i].isSelected = false
                    }
                }
                adapter.notifyDataSetChanged()
                getTotalNum()
            }
        }

        binding.cbSelect.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                adapter.allSelect(true)
                getTotalNum()
            }
        }
    }

    private fun initRecycle() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter

        adapter.setClickId(R.id.ll_checked)

        adapter.setOnItemClickListener { adapter, _, position ->
            val bean = adapter.data[position] as InvSetList
            vm.onItemClick(bean)
        }

        adapter.setChangeSelectStatus {
            val results = adapter.data.filter { !it.isSelected }
            binding.cbSelect.isChecked = results.isEmpty()
            getTotalNum()
        }

        adapter.setOnItemChildClickListener { adapter, view, position ->

        }
    }

    private fun initDialog() {
        //集托完成的弹窗dialog
        sendTipDialog = SendTipDialog(this)
        sendTipDialog.setTitle("提示")
        sendTipDialog.setConfirmBack(object : SendTipDialog.ConfirmBack {
            override fun confirmBack() {
                val results = adapter.data.filter { it.isSelected && it.status == "集托中"}
                val data = mutableListOf<String>()
                results.forEach {
                    data.add(it.setCode)
                }
                vm.finishSetPackage(data)
                sendTipDialog.dismiss()
            }

        })

    }

    fun inputRequest() {
        binding.etSearchOrderNo.requestFocus()
    }

    fun getTotalNum() {
        val totalNum = adapter.data.filter { it.isSelected && it.status == "集托中" } //这里筛选已勾选并且是集托中状态的条目
        vm.totalNum.set(totalNum.size.toString())
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun showData(data: MutableList<InvSetList>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
        vm.isRefreshing.set(false)
        adapter.allSelect(false)
        binding.cbSelect.isChecked = false
        getTotalNum()
    }

    override fun onDestroy() {
        super.onDestroy()

        Printer.closeBluetooth()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            vm.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    class OutStorageListAdapter :
        ListCheckBoxAdapter<InvSetList>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_set_package_care else R.layout.item_set_package) {

        init {
            addChildClickViewIds(R.id.img_finish)
        }

        @SuppressLint("SetTextI18n")
        override fun convert(helper: BaseViewHolder, item: BaseItemForPopup) {
            super.convert(helper, item as InvSetList)

            if (item.status == "集托中") {
                helper.setGone(R.id.img_select, false)
                helper.setGone(R.id.img_finish, true)
                helper.setGone(R.id.ll_checked, false)
            } else {
                helper.setGone(R.id.img_select, true)
                helper.setGone(R.id.img_finish, false)
                helper.setGone(R.id.ll_checked, true)
            }

            if(item.setEndTime.isNullOrEmpty()) {
                helper.setGone(R.id.tv_set_end_time, true)
            }else {
                helper.setGone(R.id.tv_set_end_time, false)
            }

            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                val textView = helper.getView<TextView>(R.id.tv_set_num)
                val text = "共${item.wholeNum}整件，${item.partNum}散包裹"
                val blueColor = ContextCompat.getColor(context, R.color.btn_blue_care)

                val spannable = SpannableString(text).apply {
                    setSpan(
                        ForegroundColorSpan(blueColor),
                        text.indexOf(item.wholeNum.toString()),
                        text.indexOf(item.wholeNum.toString()) + item.wholeNum.toString().length,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    setSpan(
                        ForegroundColorSpan(blueColor),
                        text.indexOf(item.partNum.toString()),
                        text.indexOf(item.partNum.toString()) + item.partNum.toString().length,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }

                textView.text = spannable
            } else {
                helper.setText(R.id.tv_set_num, "共${item.wholeNum}整件，${item.partNum}散包裹")
            }

            val check = helper.getView<ImageView>(R.id.img_select)
            if (item.isSelected()) {
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    check.setImageResource(R.drawable.select_selected_care)
                } else {
                    check.setImageResource(R.drawable.ic_check_sel)
                }
            } else {
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    check.setImageResource(R.drawable.select_normal_care)
                } else {
                    check.setImageResource(R.drawable.ic_check_unsel)
                }
            }

        }
    }
}