package com.midea.prestorage.function.inv

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.RespAdjustList
import com.midea.prestorage.beans.net.RespCustomerPage
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.inv.InvReconciliationVM.Companion.TYPE_ADD_ADJUST
import com.midea.prestorage.function.inv.InvReconciliationVM.Companion.TYPE_SELECT_CUSTOMER
import com.midea.prestorage.utils.AppUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityInvReconciliationBinding

class InvReconciliationActivity : BaseViewModelActivity<InvReconciliationVM>() {
    private lateinit var binding: ActivityInvReconciliationBinding
    private lateinit var adapter: InStorageOrderAdapter

    private lateinit var ownerDialog: FilterDialog  //客户选择对话框

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_inv_reconciliation)
        vm = ViewModelProvider.AndroidViewModelFactory(application)
            .create(InvReconciliationVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        vm.showCustomerDialog.observe(this, {
            if (it) {
                ownerDialog.show()
            }
        })

        vm.customerDataNotify.observe(this, {
            ownerDialog.changeDataNotify(it)
        })

        vm.loadMoreComplete.observe(this, {
            if (it == 1) {
                adapter.loadMoreModule.loadMoreComplete()
            } else if (it == 2) {
                adapter.loadMoreModule.loadMoreEnd()
            }
        })

        vm.showDatas.observe(this, {
            showData(it)
        })

        vm.loadMoreDatas.observe(this, {
            loadMoreData(it)
        })

        initRecycleView()
        initLoadMore()
        initDialog()

        AppUtils.requestFocus(binding.etSerialNo)
    }

    private fun initDialog() {
        ownerDialog = FilterDialog(this)
        ownerDialog.setTitle("请选择调账客户")
        //隐藏编辑框
        ownerDialog.dismissEdit()
        ownerDialog.setOnCheckListener {
            it as RespCustomerPage
            if (vm.dialogType == TYPE_SELECT_CUSTOMER) {
                vm.mCustomer.value = it.customerName
                vm.mCustomerCode.value = it.customerCode
                vm.onRefreshCommand.onRefresh()
            } else if (vm.dialogType == TYPE_ADD_ADJUST) {
                vm.addAdjust(it)
            }
            ownerDialog.dismiss()
        }
    }

    fun initRecycleView() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        adapter = InStorageOrderAdapter()
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.itemAnimator = null
        binding.recycle.adapter = adapter

        adapter.setOnItemClickListener { adapter, _, position ->
            val bean = adapter.data[position] as RespAdjustList
            vm.onItemClick(bean)
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.initOrderList()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    fun showData(data: MutableList<RespAdjustList>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()

        vm.isNoData.set(adapter.data.isNullOrEmpty())
        vm.isRefreshing.set(false)
    }

    fun loadMoreData(data: MutableList<RespAdjustList>?) {
        data?.let { adapter.addData(it) }
        adapter.notifyDataSetChanged()
    }

    override fun onResume() {
        super.onResume()
        vm.onRefreshCommand.onRefresh()
    }

    class InStorageOrderAdapter :
        CommonAdapter<RespAdjustList>(
            R.layout.item_inv_reconciliation
        ), LoadMoreModule

}