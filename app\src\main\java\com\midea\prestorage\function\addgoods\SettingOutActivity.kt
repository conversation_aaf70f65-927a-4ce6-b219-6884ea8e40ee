package com.midea.prestorage.function.addgoods

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestoragesaas.databinding.ActivityAddGoodsOutSettingBinding
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.SPUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

/**
 * Created by LIXK5 on 2019/4/9.
 */
class SettingOutActivity : BaseActivity() {

    companion object {
        val SCAN_NUM = "SCAN_NUM"
        val SCAN_JUMP = "SCAN_JUMP"

        fun newIntent(context: Context, toTitle: String): Intent {
            val intent = Intent(context, SettingOutActivity::class.java)
            intent.putExtra("toTitle", toTitle)
            return intent
        }
    }

    private lateinit var binding: ActivityAddGoodsOutSettingUnionBinding
    private lateinit var inStorageVM: SettingOutVM

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityAddGoodsOutSettingUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_add_goods_out_setting_care
                )
            )
        } else {
            ActivityAddGoodsOutSettingUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_add_goods_out_setting
                )
            )
        }
        inStorageVM = SettingOutVM(this)
        initView()
        binding.vm = inStorageVM
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    private fun initView() {
        val scanNum = Constants.userInfo?.scanNum
        val scanJump = Constants.userInfo?.scanJump
        binding.cbDefault.isChecked = scanNum ?: false
        binding.cbRemember.isChecked = scanJump ?: false

        binding.llDefault.setOnClickListener {
            binding.cbDefault.performClick()
        }

        binding.llRemember.setOnClickListener {
            binding.cbRemember.performClick()
        }

        binding.cbDefault.setOnCheckedChangeListener { _, isChecked ->
            Constants.userInfo?.scanNum = isChecked
            saveUserInfo()
        }

        binding.cbRemember.setOnCheckedChangeListener { _, isChecked ->
            Constants.userInfo?.scanJump = isChecked
            saveUserInfo()
        }
    }

    private fun saveUserInfo() {
        Observable.create<String> {
            try {
                db.saveOrUpdate(Constants.userInfo)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(this, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }
}