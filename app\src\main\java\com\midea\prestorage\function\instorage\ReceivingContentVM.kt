package com.midea.prestorage.function.instorage

import android.app.Application
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext

class ReceivingContentVM(application: Application) : BaseViewModel(application) {
    val itemCode = ObservableField<String>("")
    val totalQty = ObservableField<String>("")
    val itemName = ObservableField<String>("")
    var statueStr = MutableLiveData<String>("") // 商品状态
    var curSelectLot4Name = ""
    var lotAttStr = MutableLiveData<MutableList<String>>()

    override fun init() {

    }

    fun getLotAttStr(dictCode: String) {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {
            val result = withContext(Dispatchers.IO) {
                val direction = async { RetrofitHelper.getDirectionAPI().searchDictNew(dictCode) }
                direction.await()
            }

            if(result.code == 0.toLong()) {
                val datas = mutableListOf<String>()
                result.data?.removeAll { it.enableFlag == 0 }
                result.data?.forEach {
                    datas.add(it.name)
                }
                lotAttStr.value = datas
            }else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }




}