package com.midea.prestorage.function.arriving

import androidx.databinding.ObservableField
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.net.ArrivingBean
import com.midea.prestorage.beans.net.ArrivingDetailInfo
import com.midea.prestorage.beans.net.FuShipmentStatue
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent


class ArrivingDetailVM(val activity: ArrivingDetailActivity) {

    private var bean: ArrivingBean? = null

    val customerCode = ObservableField<String>("")
    val orderNo = ObservableField<String>("")
    val parentOrderNo = ObservableField<String>("")
    val taskTypeName = ObservableField<String>("")
    val taskStatus = ObservableField<String>("")
    val excuteStatusName = ObservableField<String>("")
    val customerOrderNo = ObservableField<String>("")
    val deliveryTypeName = ObservableField<String>("")
    val businessMode = ObservableField<String>("")
    val orderTime = ObservableField<String>("")
    val totalQty = ObservableField<String>("")
    val companyName = ObservableField<String>("")
    val totalVolume = ObservableField<String>("")
    val customerName = ObservableField<String>("")

    val finalName = ObservableField<String>("")
    val finalTel = ObservableField<String>("")
    val finalDetailAddr = ObservableField<String>("")

    val receiverName = ObservableField<String>("")
    val receiverTel = ObservableField<String>("")
    val receiverDetailAddr = ObservableField<String>("")

    val orderSiteCode = ObservableField<String>("")
    val orderSiteName = ObservableField<String>("")
    val remark = ObservableField<String>("")

    fun init() {
        bean = activity.intent.getSerializableExtra("bean") as ArrivingBean?
        initOrderDetailList()
    }

    private fun initOrderDetailList() {
        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getAppAPI()
            .searchTaskDetails(bean?.taskNo)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ArrivingDetailInfo>(activity) {
                override fun success(data: ArrivingDetailInfo?) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (data != null) {
                        activity.adapter.setNewInstance(data.details)

                        customerCode.set(data.custOrderNo)
                        orderNo.set(data.waybillNo)
                        parentOrderNo.set(data.relationOrderNo)

                        initTaskType(data)
                        initTaskStatus(data)
                        initShStatus(data)

                        customerOrderNo.set(data.orderNo)
                        deliveryTypeName.set(data.shippingWay)
                        businessMode.set(data.businessType)
                        orderTime.set(data.scheduledArriveDate)
                        totalQty.set(AppUtils.getBigDecimalValue(data.totalQty).toPlainString())
                        companyName.set(data.siteCode)
                        totalVolume.set(
                            AppUtils.getBigDecimalValue(data.totalVolume).toPlainString()
                        )
                        customerName.set(data.customerCode)

                        finalName.set(data.senderName)
                        finalTel.set(data.shipFromMobile)
                        finalDetailAddr.set(data.shipFromAddress)
//
                        receiverName.set(data.shipToAttentionTo)
                        receiverTel.set(data.shipToMobile)
                        receiverDetailAddr.set(data.shipToAddress)
//
                        orderSiteCode.set(data.transhubCode)
                        orderSiteName.set(data.transhubName)
                        remark.set(data.note)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun initTaskType(dataArriving: ArrivingDetailInfo) {
        RetrofitHelper.getDirectionAPI()
            .fuShipmentStatus("TASK_TYPE")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<FuShipmentStatue>>(activity) {
                override fun success(data: MutableList<FuShipmentStatue>?) {
                    if (data != null) {
                        data.removeAll { it.enableFlag == 0 }
                        val result = data.find { it.code == dataArriving.taskType }
                        taskTypeName.set(result?.name)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    AppUtils.showToast(activity, apiErrorModel.message)
                }
            })
    }

    private fun initTaskStatus(dataArriving: ArrivingDetailInfo) {
        RetrofitHelper.getDirectionAPI()
            .fuShipmentStatus("ORDER_STATUS")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<FuShipmentStatue>>(activity) {
                override fun success(data: MutableList<FuShipmentStatue>?) {
                    if (data != null) {
                        data.removeAll { it.enableFlag == 0 }
                        val result = data.find { it.code == dataArriving.taskStatus }
                        taskStatus.set(result?.name)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    AppUtils.showToast(activity, apiErrorModel.message)
                }
            })
    }

    private fun initShStatus(dataArriving: ArrivingDetailInfo) {
        RetrofitHelper.getDirectionAPI()
            .fuShipmentStatus("EXCUTE_STATUS")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<FuShipmentStatue>>(activity) {
                override fun success(data: MutableList<FuShipmentStatue>?) {
                    if (data != null) {
                        data.removeAll { it.enableFlag == 0 }
                        val result = data.find { it.code == dataArriving.excuteStatus }
                        excuteStatusName.set(result?.name)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun back() {
        activity.finish()
    }
}