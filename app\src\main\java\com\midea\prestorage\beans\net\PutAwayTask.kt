package com.midea.prestorage.beans.net

import android.os.Parcelable
import com.midea.prestorage.base.annotation.ShowAnnotation
import kotlinx.parcelize.Parcelize
import java.math.BigDecimal

@Parcelize
data class PutAwayTask(
    val checkBy: String? = null,
    val checkByName: String? = null,
    val checkTime: String? = null,
    val confirmQty: BigDecimal? = BigDecimal.ZERO,
    val containerCode: String? = null,
    val count: String? = null,
    val createTime: String? = null,
    val createUserCode: String? = null,
    val createUserName: String? = null,
    @ShowAnnotation
    val custItemCode: String? = null,
    @ShowAnnotation
    val custOrderNo: String? = null,
    val days: Int? = 0,
    val deleteFlag: Int? = 0,
    val endTime: String? = null,
    val fmContainer: String? = null,
    val fmLocCode: String? = null,
    val groupByList: List<String>? = null,
    val groupByStr: String? = null,
    val id: Long? = 0,
    val ids: List<Long>? = null,
    val itemCode: String? = null,
    @ShowAnnotation
    val itemName: String? = null,
    //生产日期
    val lotAtt01: String? = null,
    val lotAtt02: String? = null,
    //入库日期
    val lotAtt03: String? = null,
    //状态
    val lotAtt04: String? = null,
    //批次
    val lotAtt05: String? = null,
    val lotAtt06: String? = null,
    val lotNum: String? = null,
    val offset: Int? = 0,
    val opQty: BigDecimal? = BigDecimal.ZERO,
    val orderBy: String? = null,
    val orderByType: String? = null,
    val ownerCode: String? = null,
    val pageNo: Int? = 0,
    val pageSize: Int? = 0,
    val palletCode: String? = null,
    val receiptCode: String? = null,
    val receiptContainerId: Long = 0,
    val receiptHeaderId: Long? = 0,
    val receiptStatusList: List<String>? = null,
    val receiptType: String? = null,
    val recommendLocs: String? = null,
    val recordIds: String? = null,
    val remark: String? = null,
    val startTime: String? = null,
    val status: String? = null,
    val statusList: List<String>? = null,
    val tenantCode: String? = null,
    val tenantCodes: String? = null,
    val toContainer: String? = null,
    val toLocCode: String? = null,
    @ShowAnnotation
    val totalQty: BigDecimal? = BigDecimal.ZERO,
    val traceId: String? = null,
    val updateTime: String? = null,
    val updateUserCode: String? = null,
    val updateUserName: String? = null,
    val version: Int? = 0,
    val whBarcode69: String? = null,
    val whCode: String? = null,
    @ShowAnnotation
    val lotAtt04Str: String? = null
) : Parcelable