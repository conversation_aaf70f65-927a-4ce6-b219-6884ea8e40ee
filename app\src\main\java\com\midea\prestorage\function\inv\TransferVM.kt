package com.midea.prestorage.function.inv

import android.view.View

class TransferVM(val activity: TransferActivity) {

    init {

    }

    //后退键
    val back = View.OnClickListener {
        activity.finish()
    }


    val onReset = View.OnClickListener {
        if (activity.getBinding().viewPager.currentItem == 0) {
            activity.getAdapter().transGoodFragment.apply {
                getTransByGoodAdpter().data.clear()//清空数据
                getTransByGoodAdpter().notifyDataSetChanged()//通知改变
                getTransByGoodAdpter().calculatedTotalNub()
                getVm()?.resetDefault()//头部恢复默认设置
                selectedPositions.clear()
                getVm()?.firstSelectedPosition = -1

            }
        } else {
            activity.getAdapter().transLocFragment.getVm()?.resetDefault()
        }
    }
}