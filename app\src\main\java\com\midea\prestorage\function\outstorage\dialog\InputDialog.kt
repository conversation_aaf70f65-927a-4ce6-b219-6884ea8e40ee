package com.midea.prestorage.function.outstorage.dialog

import android.app.AlertDialog
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.OutStorageQuery
import com.midea.prestorage.beans.net.OutStorageScan
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogOutStorageInputBinding

class InputDialog(
    var mContext: BaseActivity
) : AlertDialog(mContext) {

    private var binding: DialogOutStorageInputBinding
    var deleteInfo: OutStorageScan? = null
    var bean: OutStorageQuery? = null
    var inputBack: InputBack? = null

    init {
        val contentView =
            LayoutInflater.from(mContext).inflate(R.layout.dialog_out_storage_input, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = InputDialogVM(this)

        setCanceledOnTouchOutside(true)

    }

    override fun show() {
        super.show()
        binding.etNum.post {
            binding.etNum.requestFocus()
        }
        binding.vm!!.goodsNo.set("")
        binding.vm!!.show()
    }

    interface InputBack {
        fun inputOk(data: OutStorageScan?)
        fun inputFail()
    }
}
