package com.midea.prestorage.beans.net;

import java.io.Serializable;

public class ContainerConfigBean implements Serializable {

    private int configValue;
    private int value;

    public int getConfigValue() {
        return configValue;
    }

    public void setConfigValue(int configValue) {
        this.configValue = configValue;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }
}