package com.midea.prestorage.function.planstockold

import android.content.Intent
import android.os.Handler
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.FuShipmentStatue
import com.midea.prestorage.beans.net.PlanStockList
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody


class PlanStockOldVM(val activity: PlanStockOldActivity) {

    val isRefreshing = ObservableBoolean(false)
    val isNoData = ObservableBoolean(false)

    // 搜索条件 单号
    var searchOrderNo = ObservableField<String>("")
    var checkStatue: FuShipmentStatue = FuShipmentStatue("100", "新建")

    var taskStatues: MutableList<FuShipmentStatue>? = null
    var taskRanges: MutableList<FuShipmentStatue>? = null
    var taskTypes: MutableList<FuShipmentStatue>? = null

    // 当前页码
    var pageNo = 1

    companion object {
        const val FU_INV_TASK_STATUS = "FU_INV_TASK_STATUS"//盘点任务状态字典名称
        const val STOCKTAKE_RANGE = "STOCKTAKE_RANGE"//盘点范围字典名称
        const val STOCKTAKE_STRATEGY_TYPE = "STOCKTAKE_STRATEGY_TYPE"//盘点类型字典名称
    }

    init {
        getDict(FU_INV_TASK_STATUS)
        getDict(STOCKTAKE_RANGE)
        getDict(STOCKTAKE_STRATEGY_TYPE)
    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        isRefreshing.set(true)
        initData(true)
    }

    fun initData(isRefresh: Boolean) {
        if (isRefresh) {
            pageNo = 1
        }

        val map = mutableMapOf(
            "locCode" to searchOrderNo.get(),
            "whCode" to activity.getWhCode(),
            "statusList" to mutableListOf(checkStatue.code),
            "pageSize" to "10",
            "pageNo" to pageNo.toString()
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        RetrofitHelper.getWareManageAPI()
            .taskPage(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PageResult<PlanStockList>>(activity) {
                override fun success(data: PageResult<PlanStockList>?) {
                    if (pageNo == 1) {
                        isRefreshing.set(false)
                    }
                    activity.adapter.loadMoreModule.loadMoreComplete()

                    data?.let {
                        if (pageNo < it.totalPage) {
                            pageNo++
                        } else {
                            Handler(activity.mainLooper).post{
                                activity.adapter.loadMoreModule.loadMoreEnd()
                            }
                        }
                        if (data.pageNo == 1) {
                            activity.adapter.setNewInstance(it.list)
                        } else {
                            activity.adapter.addData(it.list)
                        }
                        isNoData.set(it.totalCount == 0)
                        setDict()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    val serialKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                initData(true)
            }
        }
    }

    private fun getDict(dictName: String) {
        RetrofitHelper.getDirectionAPI()
            .fuShipmentStatus(dictName)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<FuShipmentStatue>>(activity) {
                override fun success(data: MutableList<FuShipmentStatue>?) {
                    data?.removeAll { it.enableFlag == 0 }
                    if (data != null) {
                        when (dictName) {
                            FU_INV_TASK_STATUS -> taskStatues = data
                            STOCKTAKE_RANGE -> taskRanges = data
                            STOCKTAKE_STRATEGY_TYPE -> taskTypes = data
                        }

                        setDict()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    AppUtils.showToast(activity, apiErrorModel.message)
                }
            })
    }

    fun setDict() {
        if (taskStatues == null || taskRanges == null || taskTypes == null) {
            return
        }

        activity.adapter.data.forEach {
            val resultStatue = taskStatues?.find { item -> it.status == item.code }
            if (resultStatue != null) {
                it.status = resultStatue.name
            }

            val resultRange = taskRanges?.find { item -> it.stocktakeRange == item.code }
            if (resultRange != null) {
                it.stocktakeRange = resultRange.name
            }

            val resultType = taskTypes?.find { item -> it.strategyType == item.code }
            if (resultType != null) {
                it.strategyType = resultType.name
            }
        }
        activity.adapter.notifyDataSetChanged()
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        result?.let {
            searchOrderNo.set(result)
        }
    }

    fun back() {
        activity.finish()
    }

    fun clearOrderNo() {
        searchOrderNo.set("")
        initData(true)
    }

    fun onItemClick(bean: PlanStockList) {
        val it = Intent(activity, PlanStockDetailActivity::class.java)
        it.putExtra("bean", bean)
        activity.startActivity(it)
    }

    fun startSearch() {
        if (pageNo > 1) {
            initData(false)
        }
    }

    fun onChangeStatue(statue: FuShipmentStatue) {
        checkStatue = statue
        initData(true)
    }
}