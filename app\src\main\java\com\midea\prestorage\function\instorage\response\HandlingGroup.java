package com.midea.prestorage.function.instorage.response;

import com.midea.prestorage.beans.base.BaseItemShowInfo;

// 装卸组
public class HandlingGroup extends BaseItemShowInfo {

    /**
     * jobId
     */
    private Double jobId;
	
    /**
     * whCode
     */
    private String whCode;
	
    /**
     * handlingCode
     */
    private String handlingCode;
	
    /**
     * handlingName
     */
    private String handlingName;
	
    /**
     * handlingType
     */
    private String handlingType;
	
    /**
     * handlingLeader
     */
    private String handlingLeader;
	
    /**
     * handlingTel
     */
    private String handlingTel;
	
    /**
     * handlingNumber
     */
    private Double handlingNumber;
	
    /**
     * handlingStatus
     */
    private String handlingStatus;
	
    /**
     * def1
     */
    private String def1;
	
    /**
     * def2
     */
    private String def2;
	
    /**
     * def3
     */
    private String def3;
	
    /**
     * def4
     */
    private String def4;
	
    /**
     * def5
     */
    private String def5;
	
    /**
     * recVer
     */
    private Double recVer;
	
    /**
     * recStatus
     */
    private Double recStatus;
	
    /**
     * timeZone
     */
    private String timeZone;
	
    /**
     * orgId
     */
    private String orgId;
	
    /**
     * modifyTime
     */
    private String modifyTime;
	
    /**
     * modifier
     */
    private String modifier;
	
    /**
     * creator
     */
    private String creator;
	
    /**
     * platformId
     */
    private String platformId;
	
    /**
     * platformName
     */
    private String platformName;
	
    /**
     * siteCode
     */
    private String siteCode;
	
    /**
     * companyCode
     */
    private String companyCode;
	
    /**
     * is2cType
     */
    private String is2cType;

    private String supplierName;

    private String supplierCode;

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public Double getJobId() {
        return jobId;
    }

    public void setJobId(Double jobId) {
        this.jobId = jobId;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getHandlingCode() {
        return handlingCode;
    }

    public void setHandlingCode(String handlingCode) {
        this.handlingCode = handlingCode;
    }

    public String getHandlingName() {
        return handlingName;
    }

    public void setHandlingName(String handlingName) {
        this.handlingName = handlingName;
    }

    public String getHandlingType() {
        return handlingType;
    }

    public void setHandlingType(String handlingType) {
        this.handlingType = handlingType;
    }

    public String getHandlingLeader() {
        return handlingLeader;
    }

    public void setHandlingLeader(String handlingLeader) {
        this.handlingLeader = handlingLeader;
    }

    public String getHandlingTel() {
        return handlingTel;
    }

    public void setHandlingTel(String handlingTel) {
        this.handlingTel = handlingTel;
    }

    public Double getHandlingNumber() {
        return handlingNumber;
    }

    public void setHandlingNumber(Double handlingNumber) {
        this.handlingNumber = handlingNumber;
    }

    public String getHandlingStatus() {
        return handlingStatus;
    }

    public void setHandlingStatus(String handlingStatus) {
        this.handlingStatus = handlingStatus;
    }

    public String getDef1() {
        return def1;
    }

    public void setDef1(String def1) {
        this.def1 = def1;
    }

    public String getDef2() {
        return def2;
    }

    public void setDef2(String def2) {
        this.def2 = def2;
    }

    public String getDef3() {
        return def3;
    }

    public void setDef3(String def3) {
        this.def3 = def3;
    }

    public String getDef4() {
        return def4;
    }

    public void setDef4(String def4) {
        this.def4 = def4;
    }

    public String getDef5() {
        return def5;
    }

    public void setDef5(String def5) {
        this.def5 = def5;
    }

    public Double getRecVer() {
        return recVer;
    }

    public void setRecVer(Double recVer) {
        this.recVer = recVer;
    }

    public Double getRecStatus() {
        return recStatus;
    }

    public void setRecStatus(Double recStatus) {
        this.recStatus = recStatus;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getIs2cType() {
        return is2cType;
    }

    public void setIs2cType(String is2cType) {
        this.is2cType = is2cType;
    }
}