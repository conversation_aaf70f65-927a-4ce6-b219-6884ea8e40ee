package com.midea.prestorage.base.adapter;

import android.view.View;
import android.widget.ImageView;

import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.midea.prestoragesaas.R;
import com.midea.prestorage.beans.base.BaseItemForPopup;

public class ListChoiceAdapterV2<T extends BaseItemForPopup> extends CommonAdapter<T> {
	//指定点击的控件ID,不指定默认整个
	private int clickId = -1;

	private OnDataChangeListener onDataChangeListener;

	public ListChoiceAdapterV2(int layoutResId) {
		super(layoutResId);
	}

	@Override
	protected void convert(BaseViewHolder helper, BaseItemForPopup item) {
		super.convert(helper, (T) item);
		View view;
		if (clickId != -1){
			view = helper.itemView.findViewById(clickId);
		} else {
			view = helper.itemView;
		}
		view.setTag(item);

		view.setOnClickListener(v -> {
			BaseItemForPopup bean = (BaseItemForPopup) v.getTag();
			if (bean.isSelected()) {
				return;
			}
			bean.setSelected(!bean.isSelected());

			for (int i = 0; i < getData().size(); i++) {
				if (getData().get(i) == bean) {
					continue;
				}
				getData().get(i).setSelected(false);
			}

			if (onDataChangeListener != null) {
				onDataChangeListener.dataChange(bean);
			}

			notifyDataSetChanged();
		});

		ImageView check = helper.getView(R.id.img_select);

		if (item.isSelected()) {
			check.setImageResource(R.mipmap.select_selected);
		} else {
			check.setImageResource(R.mipmap.select_normal);
		}
	}

	public void setClickId(int clickId) {
		this.clickId = clickId;
	}

	public T getReturnBean() {
		for (int i = 0; i < getData().size(); i++) {
			if (getData().get(i).isSelected()) {
				return getData().get(i);
			}
		}
		return null;
	}

	public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
		this.onDataChangeListener = onDataChangeListener;
	}

	/**
	 * description 数据改变后监听器
	 * author: wangqin
	 * date: 2020/6/29
	 * param
	 * return
	 */
	public interface OnDataChangeListener<T> {
		void dataChange(T t);
	}
}