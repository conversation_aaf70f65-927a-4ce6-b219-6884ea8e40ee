package com.midea.prestorage.function.outpool

import android.content.Intent
import android.text.TextUtils
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.OutPoolStorageDetail
import com.midea.prestorage.beans.net.OutPoolStorageList
import com.midea.prestorage.beans.setting.OutStorageInfo
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.function.waybill.WaybillSearchActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.DbUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import okhttp3.MediaType
import okhttp3.RequestBody


class OutPoolStorageDetailVM(val activity: OutPoolStorageDetailActivity) {

    private var bean: OutPoolStorageList? = null
    private var originData: OutPoolStorageDetail? = null
    private var outStorageInfo: OutStorageInfo? = null
    var goodsStatus: MutableList<DCBean>? = null
    var orderStatus: MutableList<DCBean>? = null

    val btnModeChange = ObservableField<String>("汇总")
    val isShowBtn = ObservableField(true)
    val isPrint = ObservableField(true)

    //记录订单详情是否汇总的字段  0为明细显示，1为汇总显示
    private var orderDetailShowMode = 0

    val waveNo = ObservableField("")
    val totalQty = ObservableField("")
    val totalVolume = ObservableField("")
    val totalWeight = ObservableField("")

    val db = DbUtils.db

    init {
        readProperty()
    }

    fun init() {
        DCUtils.fuShipmentStatus(activity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                orderStatus = statusDC
                combineDCInfo()
            }
        })

        DCUtils.getGoodsStatusDC(activity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                goodsStatus = statusDC
                combineDCInfo()
            }
        })

        bean = activity.intent.getSerializableExtra("bean") as OutPoolStorageList?
        initOrderDetailList()
    }


    /**
     * 融合数据字典
     */
    fun combineDCInfo() {
        activity.adapter.data.forEach {
            if (goodsStatus != null) {
                if (it.child != null) {
                    val results = goodsStatus?.filter { item ->
                        item.value.toString() == it.child.lotAtt04
                    }
                    if (results != null && results.isNotEmpty()) {
                        it.child.statusStr = results[0].key
                    }
                } else {
                    val results = orderStatus?.filter { item ->
                        item.value.toString() == it.title.status
                    }
                    if (results != null && results.isNotEmpty()) {
                        it.title.statusStr = results[0].key
                    }
                }
            }
        }
        activity.adapter.notifyDataSetChanged()
    }

    private fun readProperty() {
        Observable.create<OutStorageInfo> {
            outStorageInfo = db.findFirst(OutStorageInfo::class.java)
            if (outStorageInfo != null) {
                it.onNext(outStorageInfo!!)
            }
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<OutStorageInfo> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: OutStorageInfo) {
                    when (t.orderDetailShowMode) {
                        0 -> {
                            btnModeChange.set("汇总")
                        }
                        1 -> {
                            btnModeChange.set("明细")
                        }
                    }
                    orderDetailShowMode = t.orderDetailShowMode
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    private fun initOrderDetailList() {
        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getOutStorageAPI()
            .outShipmentHeaderDetail(bean?.id)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<OutPoolStorageDetail>(activity) {
                override fun success(data: OutPoolStorageDetail?) {
                    if (data != null) {
                        originData = data

                        sortByItemCode(data)
                        initWaveNoInfo()
                        initRecycleViewData()
                        combineDCInfo()
                    }
                    activity.waitingDialogHelp.hidenDialog()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun sortByItemCode(data: OutPoolStorageDetail) {
        data.outShipmentDetailResponses.forEach { item ->
            if (!item.outShipmentDetails.isNullOrEmpty()) {
                item.outShipmentDetails.sortBy {
                    it.custItemCode
                }
            }
        }
    }

    private fun initRecycleViewData() {
        val beans = mutableListOf<OutPoolStorageDetailHelp>()
        if (originData != null && originData!!.outShipmentDetailResponses != null) {
            val listData = originData!!.outShipmentDetailResponses
            listData.forEach { item ->
                if (item.outShipmentHeaderExt != null) {
                    item.outShipmentHeader.customerName = item.outShipmentHeaderExt.customerName
                    item.outShipmentHeader.unscanMark = item.outShipmentHeaderExt.unscanMark
                    item.outShipmentHeader.netEngineerName = item.outShipmentHeaderExt.netEngineerName
                    item.outShipmentHeader.netEngineerMobile = item.outShipmentHeaderExt.netEngineerMobile
                }
                //模式切换
                if (orderDetailShowMode == 0) {
                    beans.add(OutPoolStorageDetailHelp(item.outShipmentHeader))
                    if (item.outShipmentDetails != null) {
                        item.outShipmentDetails.forEach { secondItem ->
                            if (secondItem.planQtyOrigin != null) {
                                secondItem.planQty = secondItem.planQtyOrigin
                            }
                            beans.add(OutPoolStorageDetailHelp(secondItem))
                        }
                    }
                } else if (orderDetailShowMode == 1) {
                    if (item.outShipmentDetails != null) {
                        item.outShipmentDetails.forEach { secondItem ->
                            secondItem.planQtyOrigin = secondItem.planQty //保留原始数据
                            val result =
                                beans.filter { fItem ->
                                    fItem.child.custItemCode == secondItem.custItemCode
                                            && fItem.child.lotAtt04 == secondItem.lotAtt04
                                }
                            if (result.isNotEmpty()) {
                                result[0].getChild().planQty =
                                    result[0].child.planQty.add(secondItem.planQty)
                            } else {
                                beans.add(OutPoolStorageDetailHelp(secondItem))
                            }
                        }
                    }
                }
            }
        }
        activity.resetAdapterData(beans)
    }

    private fun initWaveNoInfo() {
        if (originData != null) {
            waveNo.set(originData!!.waveNo)
            totalQty.set(originData!!.totalQty.toString())
            totalVolume.set(originData!!.totalVolume.toString())
            totalWeight.set(originData!!.totalWeight.toString())

            if (TextUtils.isEmpty(originData!!.waveNo)) {
                isShowBtn.set(false)
            }
//            if (originData!!.outShipmentDetailResponses.find { it.outShipmentHeader.status.toInt() < 600 } == null) {
//                isPrint.set(true)
//            }
        }
    }

    fun toPrint() {
        val it = Intent(activity, WaybillSearchActivity::class.java)
        it.putExtra("waveNo", originData!!.waveNo)
        activity.startActivity(it)
    }

    fun back() {
        activity.finish()
    }

    fun modeChange() {
        orderDetailShowMode = when (orderDetailShowMode) {
            1 -> {
                btnModeChange.set("汇总")
                saveProperty()
                0
            }
            else -> {
                btnModeChange.set("明细")
                saveProperty()
                1
            }
        }
        initRecycleViewData()
    }

    private fun saveProperty() {
        Observable.create<String> {
            if (outStorageInfo == null) {
                outStorageInfo = OutStorageInfo()
            }
            outStorageInfo!!.orderDetailShowMode = orderDetailShowMode
            db.saveOrUpdate(outStorageInfo)
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }


    // 取消 申请不扫码
    fun onSubmitCancelUnScan(shipmentCode: String) {
        AlertDialogUtil.showOkAndCancelDialog(activity, "确定取消申请不扫码？",
            { dialogInterface, i ->  //点了确定
                cancelUnscan(shipmentCode)
            },
            { dialogInterface, i ->  //点了取消

            })
    }

    private fun cancelUnscan(shipmentCode: String) {
        val param = mutableMapOf<String, Any>()
        param["whCode"] = activity.getWhCode()
        param["orderType"] = "out"
        //出库单号
        param["referenceNo"] = shipmentCode.trim()

        // 三个必传参数:  ids 勾选的记录id列表 ,  status 要改成的状态， checkedBy 审核人
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getBarcodeAPI()
            .cancelApplyUnscan(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()

                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                }

                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    AlertDialogUtil.showOnlyOkDialog(
                        activity,
                        "操作成功",
                        AlertDialogUtil.OnOkCallback {
                            //操作成功后重新刷新列表
                            activity.finish()
                        })
                }
            })
    }

}