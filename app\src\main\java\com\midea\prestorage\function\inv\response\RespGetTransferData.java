package com.midea.prestorage.function.inv.response;

import java.util.List;

public class RespGetTransferData {
    Long id;  //例 =10000.0,

    String ownerCode; //例 =Gw1001,
    String locCode; //例 =Gx1001,
    String custItemCode; //例 =null,
    String itemCode; //例 =Gix1001,
    String itemName; //例 =电冰箱,
    String inDate; //例 =2021-10-09 13:59:29,
    String lotNum; //例 =Gl1001,
    Integer onHandQty; //例 =1000.0,
    Integer allocatedQty; //例 =100.0,
    Integer inTransitQty; //例 =100.0,
    Integer lockedQty; //例 =100.0,
    String traceId; //例 =null,
    String status; //例 =0.0

    String createTime; //例 =2021-10-09 14:00:05,
    String updateTime; //例 =2021-10-09 14:00:26,
    String tenantCode; //例 =null,
    String createUserCode; //例 =system,
    String createUserName; //例 =系统用户,
    String updateUserCode; //例 =system,
    String updateUserName; //例 =系统用户,
    String remark; //例 =null,
    String version; //例 =0.0,
    Integer deleteFlag; //例 =0.0,
    String pageNo; //例 =null,
    String pageSize; //例 =30.0,
    String offset; //例 =null,
    String orderBy; //例 =null,
    String orderByType; //例 =null,
    String ids; //例 =null,
    String tenantCodes; //例 =null,
    Long count; //例 =null,
    String startTime; //例 =null,
    String endTime; //例 =null,
    String whCode; //例 =Gz1001,

    List<FuInvLot> fuInvLotList;

    public List<FuInvLot> getFuInvLotList() {
        return fuInvLotList;
    }

    public void setFuInvLotList(List<FuInvLot> fuInvLotList) {
        this.fuInvLotList = fuInvLotList;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getLocCode() {
        return locCode;
    }

    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getInDate() {
        return inDate;
    }

    public void setInDate(String inDate) {
        this.inDate = inDate;
    }

    public String getLotNum() {
        return lotNum;
    }

    public void setLotNum(String lotNum) {
        this.lotNum = lotNum;
    }

    public Integer getOnHandQty() {
        return onHandQty;
    }

    public void setOnHandQty(Integer onHandQty) {
        this.onHandQty = onHandQty;
    }

    public Integer getAllocatedQty() {
        return allocatedQty;
    }

    public void setAllocatedQty(Integer allocatedQty) {
        this.allocatedQty = allocatedQty;
    }

    public Integer getInTransitQty() {
        return inTransitQty;
    }

    public void setInTransitQty(Integer inTransitQty) {
        this.inTransitQty = inTransitQty;
    }

    public Integer getLockedQty() {
        return lockedQty;
    }

    public void setLockedQty(Integer lockedQty) {
        this.lockedQty = lockedQty;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getPageNo() {
        return pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getOffset() {
        return offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderByType() {
        return orderByType;
    }

    public void setOrderByType(String orderByType) {
        this.orderByType = orderByType;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getTenantCodes() {
        return tenantCodes;
    }

    public void setTenantCodes(String tenantCodes) {
        this.tenantCodes = tenantCodes;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }
}
