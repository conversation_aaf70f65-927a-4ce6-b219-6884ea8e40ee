package com.midea.prestorage.function.planstock

import android.widget.EditText
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.databinding.ActivityPlanStockBinding
import com.midea.prestoragesaas.databinding.ActivityPlanStockCareBinding
import com.midea.prestoragesaas.databinding.ActivityPlanStockDetailBinding
import com.midea.prestoragesaas.databinding.ActivityPlanStockDetailCareBinding

sealed class ActivityPlanStockDetailUnionBinding{

    abstract var vm: PlanStockDetailVM?
    abstract val llTitleBar: RelativeLayout
    abstract val edGoods: EditText
    abstract val rv: RecyclerView
    abstract val spinnerStatus: MaterialSpinner
    abstract val tvNotification: TextView

    class V2(val binding: ActivityPlanStockDetailCareBinding) : ActivityPlanStockDetailUnionBinding() {
        override var vm: PlanStockDetailVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val edGoods = binding.edGoods
        override val rv = binding.rv
        override val spinnerStatus = binding.spinnerStatus
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityPlanStockDetailBinding) : ActivityPlanStockDetailUnionBinding() {
        override var vm: PlanStockDetailVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val edGoods = binding.edGoods
        override val rv = binding.rv
        override val spinnerStatus = binding.spinnerStatus
        override val tvNotification = binding.tvNotification
    }
}
