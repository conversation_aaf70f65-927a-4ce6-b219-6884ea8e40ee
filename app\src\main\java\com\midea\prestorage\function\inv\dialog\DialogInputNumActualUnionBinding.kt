package com.midea.prestorage.function.inv.dialog

import android.widget.EditText
import android.widget.GridView
import android.widget.TextView
import com.midea.prestoragesaas.databinding.DialogInputNumActualBinding
import com.midea.prestoragesaas.databinding.DialogInputNumActualCareBinding

sealed class DialogInputNumActualUnionBinding{
    abstract var vm: InputNumActualDialogVM?
    abstract val etNum: EditText
    abstract val gridNumber: GridView
    abstract val tvCarcode69: TextView

    class V2(val binding: DialogInputNumActualCareBinding) : DialogInputNumActualUnionBinding() {
        override var vm: InputNumActualDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val etNum = binding.etNum
        override val gridNumber = binding.gridNumber
        override val tvCarcode69 = binding.tvCarcode69
    }

    class V1(val binding: DialogInputNumActualBinding) : DialogInputNumActualUnionBinding() {
        override var vm: InputNumActualDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val etNum = binding.etNum
        override val gridNumber = binding.gridNumber
        override val tvCarcode69 = binding.tvCarcode69
    }
}
