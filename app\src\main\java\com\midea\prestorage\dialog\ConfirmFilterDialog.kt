package com.midea.prestorage.dialog

import android.app.AlertDialog
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.adapter.ListChoiceAdapterV2
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter.OnCheckListener
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestoragesaas.databinding.DialogListClickBinding
import com.midea.prestoragesaas.databinding.DialogListClickConfirmBinding
import com.midea.prestorage.utils.AppUtils
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/6/26$
 */
class ConfirmFilterDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {

    private var binding: DialogListClickConfirmBinding
    val adapter = ListChoiceAdapterV2<BaseItemShowInfo>(R.layout.dialog_list_multi_choose_item)
    private var back: ConfirmFilterChooseBack? = null

    init {
        val contentView =
            LayoutInflater.from(mContext).inflate(R.layout.dialog_list_click_confirm, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = ConfirmFilterDialogVM(this)

        setCanceledOnTouchOutside(true)

        initRecycleView()
    }

    private fun initRecycleView() {
        binding.recycle.layoutManager = LinearLayoutManager(mContext)
        binding.recycle.adapter = adapter
    }

    fun addAllData(beans: MutableList<BaseItemShowInfo>) {
        binding!!.vm!!.allData = beans
        changeDataNotify(beans)
    }

    fun addAllDataV2(beans: MutableList<BaseItemShowInfo>) {
        adapter.data.addAll(beans)
        adapter.notifyDataSetChanged()
    }

    fun addFirst(bean: BaseItemShowInfo) {
        adapter.data.add(0, bean)
        adapter.notifyDataSetChanged()
    }

    fun changeDataNotify(beans: List<BaseItemShowInfo>) {
        adapter.setNewInstance(beans.toMutableList())
        adapter.notifyDataSetChanged()
    }

    fun setTitle(title: String) {
        if (!TextUtils.isEmpty(title)) {
            binding!!.vm!!.title.set(title)
        }
    }

    fun getData(): MutableList<BaseItemShowInfo> {
        return adapter.data
    }

    fun setBack(backImpl: ConfirmFilterChooseBack) {
        back = backImpl
    }

    fun dismissEdit() {
        binding!!.vm!!.editUnable()
    }

    override fun dismiss() {
        binding!!.vm!!.cleanFilter()
        super.dismiss()
    }

    fun backConfirm(baseInfo: BaseItemShowInfo) {
        if (back != null) {
            back!!.confirmFilterChooseBack(baseInfo)
        }
    }

    fun setUnCancel() {
        binding.imgClose.visibility = View.INVISIBLE
        setCanceledOnTouchOutside(false)
    }

    fun setCancel() {
        binding.imgClose.visibility = View.VISIBLE
        setCanceledOnTouchOutside(true)
    }

    fun lockSize() {
        binding.recycle.layoutParams.height = AppUtils.dp2px(mContext, 200)
    }

    interface ConfirmFilterChooseBack {
        fun confirmFilterChooseBack(baseInfo: BaseItemShowInfo)
    }
}
