package com.midea.prestorage.function.barcode

import CheckUtil
import android.app.Application
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Transformations
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.*
import com.midea.prestorage.event.LiveEvent
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.MySoundUtils
import com.midea.prestorage.utils.launch
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.midea.prestoragesaas.BuildConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.math.BigDecimal

class BarcodeCollectionViewModel(application: Application) : BaseViewModel(application) {

    val cjNo = MutableLiveData<String>()
    val locCode = MutableLiveData<String>()
    val canClearLocCode = Transformations.map(locCode) {
        it.isNotEmpty()
    }
    val barcode = MutableLiveData<String>()
    val canClearBarcode = Transformations.map(barcode) {
        it.isNotEmpty()
    }
    val focusBarcodeEvent = LiveEvent<Unit>()
    val barcodeCollectionList = MutableLiveData<MutableList<BarcodeCollectionInfoResp>>()
    val deleteBarcodeEnable = Transformations.map(cjNo) {
        it.isNotEmpty()
    }
    val locChecked = MutableLiveData<Boolean>().also { it.value = true }
    val deleteBarcodeEvent = LiveEvent<Unit>()

    var lastScanCustItemCode = ""
    val sumTotal = MutableLiveData<String>("0")
    val scanBarcodeLiveEvent = LiveEvent<Unit>()

    override fun init() {
    }

    fun initList(list: MutableList<BarcodeCollectionInfoResp>) {
        cjNo.value = list.getOrNull(0)?.cjNo ?: "扫描条码后生成"
    }

    fun onEnterLocCode() {
        if (CheckUtil.isFastDoubleClick()) {
            val locCode = locCode.value?.trim() ?: ""
            checkLocCode(locCode)
        }
    }

    private fun checkLocCode(locCode: String) {
        launch(showDialog = true, isCancelEnable = false, error = {}, finish = {}) {
            if (locCode.isEmpty()) {
                return@launch
            }
            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getWareManageAPI().checkLocCode(ReqCheckLocCode(locCode = locCode))
            }
            if (result.code == 0L) {
                locChecked.value = true
                focusBarcodeEvent.value = Unit
            } else {
                this.locCode.value = ""
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    val locCodeTextChange = object : ViewBindingAdapter.TextChangedListener {

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            locChecked.value = s.isNullOrEmpty()
        }
    }

    fun onEnterBarcode() {
        if (!BuildConfig.DEBUG || CheckUtil.isFastDoubleClick()) {
            val barcode = barcode.value?.trim() ?: ""
            scanBarcode(barcode)
        }
    }

    @Throws(IllegalStateException::class)
    fun checkLocChecked() {
        if (locChecked.value != true) {
            throw IllegalStateException("请先回车校验库位")
        }
    }

    @Throws(IllegalStateException::class)
    fun checkBarcodeInput(barcode: String) {
        if (barcode.isEmpty()) {
            throw IllegalStateException("条码不能为空")
        }
    }

    private fun scanBarcode(barcode: String) {
        launch(showDialog = true, isCancelEnable = false, error = {}, finish = {}) {
            checkLocChecked()
            checkBarcodeInput(barcode)
            var cjNo = cjNo.value?.trim() ?: ""
            val locCode = locCode.value?.trim() ?: ""
            val resp = withContext(Dispatchers.IO) {
                RetrofitHelper.getBarcodeAPI().scanBarcodeCollection(
                    ScanBarcodeCollectionReq(
                        cjNo = cjNo, serialNo = barcode, locCode = locCode
                    )
                )
            }
            if (resp.code == 0L) {
                if (lastScanCustItemCode.isNotEmpty() && lastScanCustItemCode != resp.data?.custItemCode) {
                    showNotification("编码切换!", true, playSound = false)
                    MySoundUtils.getInstance().codeChangeSound()
                } else {
                    showNotification("扫码成功!", true)
                }

                lastScanCustItemCode = resp.data?.custItemCode ?: ""

                if (cjNo.isEmpty()) {
                    cjNo = resp.data?.cjNo ?: ""
                    this.cjNo.value = cjNo
                }
                queryBarcodeCollectionInfo(cjNo)
            } else {
                resp.msg?.let { showNotification(it, false) }
            }
            this.barcode.value = ""
        }
    }

    private suspend fun queryBarcodeCollectionInfo(cjNo: String) {
        val resp = withContext(Dispatchers.IO) {
            RetrofitHelper.getBarcodeAPI()
                .barcodeCollectionInfo(BarcodeCollectionInfoReq(cjNo = cjNo))
        }
        if (resp.code == 0L) {
            barcodeCollectionList.value = resp.data?.dataList?.toMutableList() ?: mutableListOf()
            sumTotal.value = AppUtils.getBigDecimalValueStr(resp.data?.countQty ?: BigDecimal.ZERO)
        } else {
            resp.msg?.let { showNotification(it, false) }
        }
    }

    fun deleteBarcodeClick() {
        if (CheckUtil.isFastDoubleClick()) {
            deleteBarcodeEvent.value = Unit
        }
    }

    fun deleteBarcode(barcode: String) {
        launch(showDialog = true, error = {}) {
            checkDeleteBarcodeInput()
            val cjNo = cjNo.value?.trim() ?: ""
            val resp = withContext(Dispatchers.IO) {
                RetrofitHelper.getBarcodeAPI().deleteBarcodeCollection(
                    DeleteBarcodeCollectionReq(
                        cjNo = cjNo,
                        serialNo = barcode,
                    )
                )
            }
            if (resp.code == 0L) {
                showNotification("删除成功！", true)
                queryBarcodeCollectionInfo(cjNo)
            } else {
                resp.msg?.let { showNotification(it, false) }
            }
        }
    }

    @Throws(IllegalStateException::class)
    fun checkDeleteBarcodeInput() {
        val cjNo = cjNo.value?.trim() ?: ""
        if (cjNo.isEmpty()) {
            throw IllegalStateException("采集单号不能为空")
        }
    }

    fun clearLocCode() {
        locCode.value = ""
    }

    fun clearBarcode() {
        barcode.value = ""
    }

    fun scanBarcodeClick() {
        scanBarcodeLiveEvent.value = Unit
    }

    fun scanBarcodeResult(result: String?) {
        result?.let {
            barcode.value = it
            onEnterBarcode()
        }
    }
}