package com.midea.prestorage.function.instorage

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.ListChoiceClickPositionAdapter
import com.midea.prestoragesaas.databinding.ActivityInStorageReceivingRecordBinding
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.function.instorage.response.ReceivingDetailsData
import com.midea.prestorage.function.instorage.response.RespInReceiptRecord
import com.midea.prestorage.function.inv.response.InReceiptOrder
import com.midea.prestorage.utils.DCUtils

class InStorageReceivingRecordActivity: BaseViewModelActivity<InStorageReceivingRecordVM>() {
    private lateinit var binding: ActivityInStorageReceivingRecordBinding
    var adapter = ReceivingRecordAdapter()

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_in_storage_receiving_record)
        vm = ViewModelProvider.AndroidViewModelFactory(application).create(InStorageReceivingRecordVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        //返回
        vm.finishActivity.observe(this, Observer<Boolean> {
            if (it) {
                finish()
            }
        })

        vm.showDatas.observe(this, Observer<MutableList<RespInReceiptRecord>> {
            showData(it)
        })

        vm.loadMoreDatas.observe(this, Observer<MutableList<RespInReceiptRecord>> {
            loadMoreData(it)
        })

        vm.loadMoreComplete.observe(this, Observer<Int> {
            if (it == 1) {
                adapter.loadMoreModule.loadMoreComplete()
            } else if (it == 2) {
                adapter.loadMoreModule.loadMoreEnd()
            }
        })

        initData()
        initRecycleView()
        initLoadMore()
    }

    fun initData() {
        val orderNo = intent.getStringExtra("orderNo")
        val orderReceiveType = intent.getStringExtra("orderReceiveType")
        if (orderNo.isNullOrEmpty()) {
            AlertDialogUtil.showOnlyOkDialog(this, "单号为空", AlertDialogUtil.OnOkCallback { })
        } else {
            vm.curOrderNo.value = orderNo
        }
        if (!orderReceiveType.isNullOrEmpty()) {
            vm.curOrderReceiveType.set(orderReceiveType)  //单号类型
        }

    }

    fun initRecycleView() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        binding.recycle.layoutManager = LinearLayoutManager(this)
//        binding.recycle.addItemDecoration(DividerItemDecoration(this, DividerItemDecoration.VERTICAL))
        binding.recycle.adapter = adapter
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.loadMore()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    fun showData(data: MutableList<RespInReceiptRecord>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()

        vm.isNoData.value = adapter.data.isNullOrEmpty()
        vm.isRefreshing.value = false
    }

    fun loadMoreData(data: MutableList<RespInReceiptRecord>?) {
        data?.let { adapter.addData(it) }
        adapter.notifyDataSetChanged()
    }

    class ReceivingRecordAdapter : ListChoiceClickPositionAdapter<RespInReceiptRecord>(R.layout.item_receiving_record),
        LoadMoreModule {

        override fun convert(helper: BaseViewHolder, item: RespInReceiptRecord) {
            super.convert(helper, item)

            if(!item.custItemCode.isNullOrBlank()) {
                helper.setText(R.id.tv_goods_Code, item.custItemCode)
            }else {
                helper.setText(R.id.tv_goods_Code, "")
            }

            if(!item.itemName.isNullOrBlank()) {
                helper.setText(R.id.tv_goods_name, item.itemName)
            }else {
                helper.setText(R.id.tv_goods_name, "")
            }

            if(item.qty != null) {
                helper.setText(R.id.tv_totalQty, item.qty.toString())
            }else {
                helper.setText(R.id.tv_totalQty, "")
            }

            if(!item.checkLoc.isNullOrBlank()) {
                helper.setText(R.id.tv_in_storage, item.checkLoc)
            }else {
                helper.setText(R.id.tv_in_storage, "")
            }

            if(!item.lotAtt01.isNullOrBlank()) {
                helper.setText(R.id.tv_produced_date, item.lotAtt01)
            }else {
                helper.setText(R.id.tv_produced_date, "")
            }

            if(!item.lotAtt02.isNullOrBlank()) {
                helper.setText(R.id.tv_expiration_date, item.lotAtt02)
            }else {
                helper.setText(R.id.tv_expiration_date, "")
            }

            if(!item.lotAtt05.isNullOrBlank()) {
                helper.setText(R.id.tv_lot, item.lotAtt05)
            }else {
                helper.setText(R.id.tv_lot, "")
            }

            DCUtils.goodsStatue?.forEach {
                if(it.value == item.lotAtt04) {
                    helper.setText(R.id.tv_goods_status, it.key.toString())
                }
            }
        }
    }

}