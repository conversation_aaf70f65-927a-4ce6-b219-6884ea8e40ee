package com.midea.prestorage.beans.net

import android.os.Parcelable
import com.midea.prestorage.base.annotation.ShowAnnotation
import kotlinx.parcelize.Parcelize

@Parcelize
data class RespAdjustList(
    var id: Long? = null,
    @ShowAnnotation
    var adNo: String? = null,
    @ShowAnnotation
    var customerName: String? = null,
    @ShowAnnotation
    var createTimeCn: String? = null,
    @ShowAnnotation
    var statusCn: String? = null,
    @ShowAnnotation
    var adTypeCn: String? = null,
    var customerCode: String? = null,
    var ownerCode: String? = null,
    var ownerName: String? = null,
) : Parcelable