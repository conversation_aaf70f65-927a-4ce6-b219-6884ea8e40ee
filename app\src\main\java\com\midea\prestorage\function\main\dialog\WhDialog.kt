package com.midea.prestorage.function.main.dialog

import android.app.AlertDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.setting.ImplWarehouse
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogWarehouseChooseBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

class WhDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {

    private var binding: DialogWarehouseChooseUnionBinding
    val adapter = WhChooseAdapter<ImplWarehouse>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.dialog_list_item_tenant_care else R.layout.dialog_list_item_click)

    init {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window?.setGravity(Gravity.CENTER)
            window?.attributes?.run {
                gravity = Gravity.CENTER
            }
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_warehouse_choose_care, null)
            setView(contentView)
            DialogWarehouseChooseUnionBinding.V2(DataBindingUtil.bind(contentView)!!)
        } else {
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_warehouse_choose, null)
            setView(contentView)
            DialogWarehouseChooseUnionBinding.V1(DataBindingUtil.bind(contentView)!!)
        }
        binding.vm = WhDialogVM(this)

//        setCanceledOnTouchOutside(false)
//        // 加上这个 确保按回退键也不关闭选择框
//        setCancelable(false)

        initRecycleView()
    }

    private fun initRecycleView() {
        binding.recycle.layoutManager = LinearLayoutManager(mContext)
        binding.recycle.adapter = adapter
    }

    fun setNewData(beans: MutableList<ImplWarehouse>?) {
        binding.vm!!.allData.clear()
        if (beans != null) {
            binding.vm!!.allData.addAll(beans)
        }
    }

    fun notifyDataChange (beans: MutableList<ImplWarehouse>?) {
        adapter.setNewInstance(beans)
        adapter.notifyDataSetChanged()
    }

    fun setTitle(title: String) {
        if (!TextUtils.isEmpty(title)) {
            binding.vm!!.title.set(title)
        }
    }

    fun setOnCheckListener(onCheckListener: ListChoiceClickAdapter.OnCheckListener<ImplWarehouse>) {
        adapter.setOnCheckListener(onCheckListener)
    }

    override fun dismiss() {
        binding.vm!!.cleanFilter()
        super.dismiss()
    }

}