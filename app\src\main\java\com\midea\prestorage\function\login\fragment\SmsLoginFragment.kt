package com.midea.prestorage.function.login.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.CountDownTimeUtil
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.databinding.FragmentSmsLoginBinding
import com.midea.prestoragesaas.databinding.FragmentSmsLoginCareBinding
import com.trello.rxlifecycle2.components.support.RxFragment

class SmsLoginFragment : RxFragment() {
    companion object {
        const val TAG = "SmsLoginFragment"

        fun newInstance(position: Int): SmsLoginFragment {
            val bundle = Bundle()
            bundle.putInt("position", position)
            val fragment = SmsLoginFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    lateinit var binding: FragmentSmsLoginUnionBinding
    private var vm: SmsLoginVM? = null
    var mCountDownTimeUtil: CountDownTimeUtil? = null

    fun getVm(): SmsLoginVM? {
        return vm
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            FragmentSmsLoginUnionBinding.V2(inflater.let {
                FragmentSmsLoginCareBinding.inflate(
                    it,
                    container,
                    false
                )
            })
        } else {
            FragmentSmsLoginUnionBinding.V1(inflater.let {
                FragmentSmsLoginBinding.inflate(
                    it,
                    container,
                    false
                )
            })
        }

        initView()

        return binding.root
    }

    //初始化
    private fun initView() {
        if (vm == null) {
            vm = SmsLoginVM(this)
            binding.vm = vm
        }
    }

    /**
     * 密码获取焦点
     */
    fun pwdFocus() {
        binding.etPassword.requestFocus()
    }

    override fun onDestroy() {
        super.onDestroy()

        mCountDownTimeUtil?.cancel()
    }
}