package com.midea.prestorage.function.inv

import android.os.Bundle
import android.widget.ArrayAdapter
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.FuShipmentStatue
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestoragesaas.databinding.ActivityLotEditBinding
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.FilterDigitTextWatcher


class LotEditActivity : BaseActivity() {

    lateinit var binding: ActivityLotEditUnionBinding
    var textWatcher: FilterDigitTextWatcher? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityLotEditUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_lot_edit_care
                )
            )
        } else {
            ActivityLotEditUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_lot_edit
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        binding.vm = LotEditVM(this)


        // 初始化 调整原因  (下拉选项和字典)
        initDropEditText()
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }


    fun initDropEditText() {
        val options = mutableListOf<String>()
        var adapter = ArrayAdapter(
            this,
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.simple_list_item_care else android.R.layout.simple_list_item_1,
            options
        )
        // 初始化  批属性调整原因  的字典 key-val
        DCUtils.initUpdateLotReason(this, object : DCUtils.OnInitFinish {
            override fun finish() {
                DCUtils.dictUpdateLotReasonsCode2Name.values.forEach {
                    adapter.add(it)
                }
                binding.etUpdateReason.setAdapter(adapter);
            }
        });

    }

}