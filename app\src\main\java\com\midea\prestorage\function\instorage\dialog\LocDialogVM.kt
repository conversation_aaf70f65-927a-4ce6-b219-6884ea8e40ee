package com.midea.prestorage.function.instorage.dialog

import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.function.instorage.response.RespLocationPage
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.function.receivecpkx.ContainerListActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody

class LocDialogVM(val dialog: LocDialog) {

    val title = ObservableField<String>("请选择上架库位")

    var pageNo = 1
    var totalPage = 10

    val filterInfo = ObservableField("")
    val inputTextChange = object : ViewBindingAdapter.TextChangedListener {

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            initPalletInfo(true, s.toString())
        }
    }

    fun initPalletInfo(isSearch: Boolean, searchInfo: String? = null) {
        if (isSearch) {
            dialog.adapter.setNewInstance(mutableListOf())
            pageNo = 1
        }
        if (pageNo > totalPage) {
            return
        }

        RetrofitHelper.getAppAPI()
            .getLocationPage(pageNo, 10, searchInfo, (dialog.mContext as BaseActivity).getWhCode())
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(dialog.mContext, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PageResult<RespLocationPage>>(dialog.mContext) {
                override fun success(data: PageResult<RespLocationPage>?) {
                    data?.let {
                        totalPage = if (it.totalCount == 0) 1 else it.totalPage
                        pageNo = it.pageNo + 1

                        val info = mutableListOf<BaseItemShowInfo>()
                        it.list?.forEach { item ->
                            info.add(BaseItemShowInfo(item.locCode))
                        }

                        dialog.addData(info)

                        if (pageNo > it.totalPage) {
                            dialog.endLoad()
                        } else {
                            dialog.stopLoad()
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    AppUtils.showToast(dialog.mContext, apiErrorModel.message)
                }
            })
    }

    fun close() {
        dialog.dismiss()
    }
}