package com.midea.prestorage.function.arriving.dialog

import android.graphics.Color
import android.text.TextUtils
import androidx.core.content.ContextCompat
import androidx.databinding.ObservableField
import com.bigkoo.pickerview.TimePickerView
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.net.ArrivingPhoneBean
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody
import java.text.SimpleDateFormat
import java.util.*

class ArrivingDialogVM(val dialog: ArrivingDialog) {

    var format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    val phoneInfoMode1 = ObservableField<String>("")
    val carNoMode1 = ObservableField<String>("")
    val dateTimeMode1 = ObservableField<String>("")

    val phoneInfoMode2 = ObservableField<String>("")
    val carNoMode2 = ObservableField<String>("")
    val dateTimeMode2 = ObservableField<String>("")

    val noteMode = ObservableField(1)// 1手工登记 2到车登记

    private var phoneChooseDialog: FilterDialog = FilterDialog(dialog.mContext)
    var bean: ArrivingPhoneBean? = null

    init {
        phoneChooseDialog.setTitle("请选择车牌")
        phoneChooseDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            bean = it as ArrivingPhoneBean
            phoneInfoMode2.set(bean!!.driverMobile)
            carNoMode2.set(bean!!.carNo)
            dateTimeMode2.set(bean!!.updateTime)
            phoneChooseDialog.dismiss()
        })
    }

    fun showTimePick() {
        val pvTime = TimePickerView.Builder(
            dialog.mContext,
            TimePickerView.OnTimeSelectListener { date2, _ ->
                dateTimeMode1.set(format.format(date2))
            })
        pvTime.setType(TimePickerView.Type.ALL)//默认全部显示
            .setLabel("-", "-", " ", ":", ":", "")
            .setCancelText("取消")//取消按钮文字
            .setSubmitText("确定")//确认按钮文字
            .setContentSize(20)//滚轮文字大小
            .setTitleSize(20)//标题文字大小
            .setOutSideCancelable(true)//点击屏幕，点在控件外部范围时，是否取消显示
            .setTitleText("到车时间")
            .setTextColorCenter(Color.BLACK)//设置选中项的颜色
            .setTitleColor(Color.BLACK)//标题文字颜色
            .setSubmitColor(ContextCompat.getColor(dialog.mContext, R.color.colorBlue))//确定按钮文字颜色
            .setCancelColor(ContextCompat.getColor(dialog.mContext, R.color.colorOrange))//取消按钮文字颜色
            .isDialog(true)
            .setDate(Calendar.getInstance())
            .isCenterLabel(false) //是否只显示中间选中项的label文字，false则每项item全部都带有label。
            .build()
        pvTime.build().show()
    }

    fun phoneClick() {
        phoneChooseDialog.show()
    }

    fun close() {
        dialog.dismiss()
    }

    fun confirm() {
        if (noteMode.get() == 1) {
            if (TextUtils.isEmpty(carNoMode1.get())) {
                AppUtils.showToast(dialog.mContext, "请输入车牌!")
                return
            }

            if (TextUtils.isEmpty(phoneInfoMode1.get())) {
                AppUtils.showToast(dialog.mContext, "请输入司机收手机号!")
                return
            }

            if (TextUtils.isEmpty(dateTimeMode1.get())) {
                AppUtils.showToast(dialog.mContext, "请选择到车时间!")
                return
            }
        } else {
            if (TextUtils.isEmpty(carNoMode2.get())) {
                AppUtils.showToast(dialog.mContext, "请选择车牌!")
                return
            }

            if (TextUtils.isEmpty(dateTimeMode2.get())) {
                AppUtils.showToast(dialog.mContext, "请选择到车时间!")
                return
            }
        }

        val taskNos = mutableListOf<String>()
        dialog.tag?.forEach {
            taskNos.add(it.taskNo)
        }
        (dialog.mContext as BaseActivity).waitingDialogHelp.showDialog()
        val param = mutableMapOf<String, Any?>(
            "driverQueueCode" to bean?.code,
//            "code" to bean?.carNo,
//            "phone" to bean?.driverMobile,
//            "arrivedTime" to dateTimeMode1.get(),
            "queueType" to 10,
            "taskNos" to taskNos
        )

        if (noteMode.get() == 1) {
            param["code"] = carNoMode1.get()
            param["phone"] = phoneInfoMode1.get()
            param["driverQueueCode"] = bean?.code
            param["arrivedTime"] = dateTimeMode1.get()
            param["type"] = 1
        } else {
            param["code"] = carNoMode2.get()
            param["phone"] = phoneInfoMode2.get()
            param["arrivedTime"] = dateTimeMode1.get()
            param["type"] = 2
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getAppAPI()
            .carArrivedInfo(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(dialog.mContext, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(dialog.mContext) {
                override fun success(data: Any?) {
                    (dialog.mContext as BaseActivity).waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showSuccessToastWithSound(dialog.mContext, "登记成功!")
                    dialog.dismiss()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    (dialog.mContext as BaseActivity).waitingDialogHelp.hidenDialog()
                    AppUtils.showToast(dialog.mContext, apiErrorModel.message)
                }
            })
    }

    fun addAllData(phoneBeans: MutableList<BaseItemShowInfo>) {
        phoneChooseDialog.addAllData(phoneBeans)
    }

    fun dismiss() {
        phoneInfoMode1.set("")
        carNoMode1.set("")
        dateTimeMode1.set("")

        phoneInfoMode2.set("")
        carNoMode2.set("")
        dateTimeMode2.set("")
    }
}
