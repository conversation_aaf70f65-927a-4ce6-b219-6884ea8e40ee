package com.midea.prestorage.function.main.dialog

import com.midea.prestoragesaas.databinding.DialogSaasWhChooseBinding
import com.midea.prestoragesaas.databinding.DialogSaasWhChooseCareBinding

sealed class DialogSaasWhChooseUnionBinding{
    abstract var vm: SaasWhChooseDialogVM?

    class V2(val binding: DialogSaasWhChooseCareBinding) : DialogSaasWhChooseUnionBinding() {
        override var vm: SaasWhChooseDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
    }

    class V1(val binding: DialogSaasWhChooseBinding) : DialogSaasWhChooseUnionBinding() {
        override var vm: SaasWhChooseDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
    }
}
