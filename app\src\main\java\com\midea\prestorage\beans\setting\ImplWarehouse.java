package com.midea.prestorage.beans.setting;

import com.midea.prestorage.beans.base.BaseItemShowInfo;

import org.xutils.db.annotation.Column;
import org.xutils.db.annotation.Table;

@Table(name = "ImplWarehouse")
public class ImplWarehouse extends BaseItemShowInfo {

    @Column(name = "id", isId = true, autoGen = true, property = "NOT NULL")
    private String id;

    @Column(name = "whCode")
    public String whCode;

    @Column(name = "cdwhName")
    public String cdwhName;

    @Column(name = "whSystem")
    public String whSystem;

    @Column(name = "isChecked")
    public boolean isChecked;

    @Column(name = "bearingSystem")
    public String bearingSystem;

    public int cdwhIsStop;

    public ImplWarehouse() {
    }

    public ImplWarehouse(String whName, String whCode) {
        this.cdwhName = whName;
        this.whCode = whCode;
    }

    public int getCdwhIsStop() {
        return cdwhIsStop;
    }

    public void setCdwhIsStop(int cdwhIsStop) {
        this.cdwhIsStop = cdwhIsStop;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getCdwhName() {
        return cdwhName;
    }

    public void setCdwhName(String cdwhName) {
        this.cdwhName = cdwhName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setChecked(boolean checked) {
        isChecked = checked;
    }

    public boolean isChecked() {
        return isChecked;
    }

    public void setWhSystem(String whSystem) {
        this.whSystem = whSystem;
    }

    public String getWhSystem() {
        return whSystem;
    }

    public void setBearingSystem(String bearingSystem) {
        this.bearingSystem = bearingSystem;
    }

    public String getBearingSystem() {
        return bearingSystem;
    }


    public String getBearingSystemStr() {
        if("1".equals(bearingSystem)) {
            return "AWMS";
        }else if("2".equals(bearingSystem)) {
            return "EWMS";
        }else if("3".equals(bearingSystem)) {
            return "WMS2B";
        }else {
            return "";
        }
    }
 }
