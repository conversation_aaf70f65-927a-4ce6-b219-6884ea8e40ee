package com.midea.prestorage.function.containerpick

import CheckUtil
import android.content.Intent
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.net.PickDetail
import com.midea.prestorage.beans.net.RespTaskInfo
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody

class CombinedPickDeatilVM(val activity: CombinedPickDeatilActivity) {

    var taskQty = ObservableField("")
    var storeTotalQty = ObservableField("")
    var skuTotalQty = ObservableField("")
    var totalQty = ObservableField("")
    var totalCsQty = ObservableField("")
    var totalEaQty = ObservableField("")
    var respTaskInfo: RespTaskInfo? = null

    fun init() {
        respTaskInfo =
            activity.intent.getParcelableExtra("RespTaskInfo")

        respTaskInfo?.let {
            taskQty.set(AppUtils.getBigDecimalValueStr(it.taskQty))
            storeTotalQty.set(AppUtils.getBigDecimalValueStr(it.storeTotalQty))
            skuTotalQty.set(AppUtils.getBigDecimalValueStr(it.skuTotalQty))
            totalQty.set(AppUtils.getBigDecimalValueStr(it.totalQty))
            totalCsQty.set(AppUtils.getBigDecimalValueStr(it.totalCsQty))
            totalEaQty.set(AppUtils.getBigDecimalValueStr(it.totalEaQty))

            activity.adapterNumber.addData(it.pickDetailList as MutableList<PickDetail>?)
            activity.adapterNumber.notifyDataSetChanged()
        }
    }

    fun bottomClick() {
        if (CheckUtil.isFastDoubleClick()) {
            comPickStartPick()
        }
    }

    private fun comPickStartPick() {
        activity.waitingDialogHelp.showDialogUnCancel()
        val param = mutableMapOf(
            "whCode" to Constants.whInfo?.whCode,
            "pickDetailList" to respTaskInfo?.pickDetailList
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .comPickStartPick(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<String>(activity) {
                override fun success(data: String?) {
                    activity.waitingDialogHelp.hidenDialog()

                    CombinedPickVM.refreshEvent = true
                    val it = Intent(activity, CombinedPickDeatilListActivity::class.java)
                    it.putExtra("comPickNo", data)
                    activity.startActivity(it)
                    activity.finish()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun back() {
        activity.finish()
    }
}