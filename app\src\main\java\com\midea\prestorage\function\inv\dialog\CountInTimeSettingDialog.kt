package com.midea.prestorage.function.inv.dialog

import android.app.AlertDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.function.inv.CountInTimeSettingVM
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogCountInTimeSettingBinding

class CountInTimeSettingDialog(
    var mContext: BaseActivity
) : AlertDialog(mContext) {

    var binding: DialogCountInTimeSettingBinding

    init {
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        window?.setGravity(Gravity.CENTER)
        window?.attributes?.run {
            gravity = Gravity.CENTER
        }
        val contentView =
            LayoutInflater.from(mContext).inflate(R.layout.dialog_count_in_time_setting, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = CountInTimeSettingDialogVM(this)

        setCanceledOnTouchOutside(true)
    }

    override fun show() {
        super.show()

        binding.cbSelectCover.isChecked = CountInTimeSettingVM.countingMethod == CountInTimeSettingVM.COVER
        binding.cbSelectAccumulation.isChecked = CountInTimeSettingVM.countingMethod == CountInTimeSettingVM.ADD
        binding.cbSelectOne.isChecked = CountInTimeSettingVM.countingMethod == CountInTimeSettingVM.ONE

        binding.cbSelectMinUnits.isChecked = CountInTimeSettingVM.quantityEntryMode == CountInTimeSettingVM.MIN_UNITS
        binding.cbSelectByNum.isChecked = CountInTimeSettingVM.quantityEntryMode == CountInTimeSettingVM.BOXES_NUM

        binding.cbSelectByNum.isClickable = CountInTimeSettingVM.countingMethod != CountInTimeSettingVM.ONE

        binding.cbSelectCover.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.cbSelectAccumulation.isChecked = false
                binding.cbSelectOne.isChecked = false
            } else if (!binding.cbSelectAccumulation.isChecked && !binding.cbSelectOne.isChecked) {
                binding.cbSelectCover.isChecked = true
            }
            CountInTimeSettingVM.countingMethod = CountInTimeSettingVM.COVER
            binding.cbSelectByNum.isClickable = true
        }

        binding.cbSelectAccumulation.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.cbSelectCover.isChecked = false
                binding.cbSelectOne.isChecked = false
            } else if (!binding.cbSelectCover.isChecked && !binding.cbSelectOne.isChecked) {
                binding.cbSelectAccumulation.isChecked = true
            }
            CountInTimeSettingVM.countingMethod = CountInTimeSettingVM.ADD
            binding.cbSelectByNum.isClickable = true
        }

        binding.cbSelectOne.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.cbSelectCover.isChecked = false
                binding.cbSelectAccumulation.isChecked = false
            } else if (!binding.cbSelectCover.isChecked && !binding.cbSelectAccumulation.isChecked) {
                binding.cbSelectOne.isChecked = true
            }
            CountInTimeSettingVM.countingMethod = CountInTimeSettingVM.ONE
            binding.cbSelectByNum.isClickable = false
            binding.cbSelectMinUnits.isChecked = true
            CountInTimeSettingVM.quantityEntryMode = CountInTimeSettingVM.MIN_UNITS
        }

        binding.cbSelectMinUnits.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.cbSelectByNum.isChecked = false
            } else if (!binding.cbSelectByNum.isChecked) {
                binding.cbSelectMinUnits.isChecked = true
            }
            CountInTimeSettingVM.quantityEntryMode = CountInTimeSettingVM.MIN_UNITS
        }

        binding.cbSelectByNum.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.cbSelectMinUnits.isChecked = false
            } else if (!binding.cbSelectMinUnits.isChecked) {
                binding.cbSelectByNum.isChecked = true
            }
            CountInTimeSettingVM.quantityEntryMode = CountInTimeSettingVM.BOXES_NUM
        }
    }
}