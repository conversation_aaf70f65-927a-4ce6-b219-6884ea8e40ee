package com.midea.prestorage.function.outpool.dialog

import android.app.AlertDialog
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogCopyTextBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

class CopyDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {

    private var binding: DialogCopyTextBinding

    init {
        val contentView = LayoutInflater.from(mContext).inflate(R.layout.dialog_copy_text, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = CopyDialogVM(this)

        setCanceledOnTouchOutside(false)
        // 加上这个 确保按回退键也不关闭选择框
        setCancelable(false)

        binding.tvWaveNo.setOnClickListener {
            binding.vm!!.onTextClick(binding.tvWaveNo.text.toString())
        }

        binding.tvOrderNo.setOnClickListener {
            binding.vm!!.onTextClick(binding.tvOrderNo.text.toString())
        }
    }

    fun setCopyText(waveNo:String, custOrderCode:String) {
        binding.tvWaveNo.text = waveNo
        binding.tvOrderNo.text = custOrderCode
    }
}
