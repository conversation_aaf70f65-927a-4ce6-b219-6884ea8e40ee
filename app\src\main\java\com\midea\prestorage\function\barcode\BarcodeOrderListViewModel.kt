package com.midea.prestorage.function.barcode

import CheckUtil
import android.annotation.SuppressLint
import android.app.Application
import android.view.View
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Transformations
import androidx.lifecycle.ViewModelProvider
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.SelectEntity
import com.midea.prestorage.beans.net.BarcodeCollectionInfoReq
import com.midea.prestorage.beans.net.BarcodeCollectionInfoResp
import com.midea.prestorage.beans.net.BarcodeOrderListReq
import com.midea.prestorage.beans.net.BarcodeOrderListResp
import com.midea.prestorage.dialog.EmbeddedFilterViewModel
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date

class BarcodeOrderListViewModel(application: Application) : BaseViewModel(application) {

    companion object {
        @SuppressLint("SimpleDateFormat")
        private val dateFormat = SimpleDateFormat("yyyy-MM-dd")
    }

    val isRefreshing = MutableLiveData<Boolean>()
    val showRecycleView = MutableLiveData<Boolean>()
    val orderListLiveData = MutableLiveData<MutableList<BarcodeOrderListResp>>()
    val cjNo = MutableLiveData<String>()
    val canClearCode = Transformations.map(cjNo) {
        it.isNotEmpty()
    }
    val toBarcodeCollectionPage = MutableLiveData<MutableList<BarcodeCollectionInfoResp>>()

    val sumTotal = MutableLiveData<String>("0")

    val embeddedFilterViewModel = ViewModelProvider.AndroidViewModelFactory.getInstance(application)
        .create(EmbeddedFilterViewModel::class.java).also {
            it.listener = object : EmbeddedFilterViewModel.EmbeddedFilterListener {
                override fun onDaySelect(selectEntity: SelectEntity) {
                    onRefreshCommand.onRefresh()
                }
            }
        }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        refresh()
    }

    override fun init() {
        refresh()
    }

    fun refresh() {
        isRefreshing.value = true
        queryBarcodeOrderList()
    }

    @VisibleForTesting
    fun queryDate(range: Int): Pair<String, String> {
        val cal = Calendar.getInstance()
        cal.add(Calendar.DATE, -range + 1)
        val startDate = "${dateFormat.format(cal.time)} 00:00:00"
        val endDate = "${dateFormat.format(Date())} 23:59:59"
        return startDate to endDate
    }

    private fun queryBarcodeOrderList() {
        launch(showDialog = false, error = {
            isRefreshing.value = false
        }) {
            val (start, end) = queryDate(
                embeddedFilterViewModel.dayBean.shipmentType.toIntOrNull() ?: 1
            )
            val resp = withContext(Dispatchers.IO) {
                RetrofitHelper.getBarcodeAPI().barcodeOrderList(
                    BarcodeOrderListReq(
                        startTime = start, endTime = end
                    )
                )
            }
            isRefreshing.value = false
            if (resp.code == 0L) {
                orderListLiveData.value = resp.data?.list ?: mutableListOf()
                showRecycleView.value = orderListLiveData.value?.isNotEmpty() == true
            } else {
                resp.msg?.let { showNotification(it, false) }
            }
        }
    }

    fun onEnterCode() {
        if (CheckUtil.isFastDoubleClick()) {
            val cjNo = cjNo.value?.trim() ?: ""
            queryBarcodeCollectionInfo(cjNo)
        }
    }

    fun clearCode() {
        cjNo.value = ""
    }

    @Throws(IllegalStateException::class)
    fun checkCjNoInput(cjNo: String) {
        if (cjNo.isEmpty()) {
            throw IllegalStateException("采集单不能为空")
        }
    }

    fun queryBarcodeCollectionInfo(cjNo: String) {
        launch(showDialog = true, error = {}) {
            checkCjNoInput(cjNo)
            val resp = withContext(Dispatchers.IO) {
                RetrofitHelper.getBarcodeAPI()
                    .barcodeCollectionInfo(BarcodeCollectionInfoReq(cjNo = cjNo))
            }
            if (resp.code == 0L) {
                if (resp.data?.dataList.isNullOrEmpty()) {
                    showNotification("采集单号不存在", false)
                } else {
                    sumTotal.value = AppUtils.getBigDecimalValueStr(resp.data?.countQty ?: BigDecimal.ZERO)
                    toBarcodeCollectionPage.value = resp.data?.dataList?.toMutableList() ?: mutableListOf()
                }
            } else {
                resp.msg?.let { showNotification(it, false) }
            }
        }
    }

    fun popupDay(view: View) {
        embeddedFilterViewModel.popupDay(view)
    }

    fun createCollectionOrder() {
        sumTotal.value = "0"
        toBarcodeCollectionPage.value = mutableListOf()
    }

}