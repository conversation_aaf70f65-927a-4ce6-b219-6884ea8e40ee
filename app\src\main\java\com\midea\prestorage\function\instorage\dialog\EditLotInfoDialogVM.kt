package com.midea.prestorage.function.outstorage.dialog

import android.annotation.SuppressLint
import android.widget.Toast
import androidx.databinding.ObservableField
import com.midea.prestorage.beans.net.CdWhLotDetailDto
import com.midea.prestorage.beans.net.InReceiptSerialScanDto
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.function.inv.response.BarcodeLotDto
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.ToastUtils
import java.text.SimpleDateFormat
import java.util.*

class EditLotInfoDialogVM(val dialog: EditLotInfoDialog) {

    // 标题  货品属性采集
    val title = ObservableField<String>("")

    //属性模板，包含了一个属性是否必填，该用什么控件编辑 (rf统一用editText编辑)
    var listLotTpl = mutableListOf<CdWhLotDetailDto>()
    var serialScan = InReceiptSerialScanDto()

    val lot1title = ObservableField("")
    val lot1value = ObservableField("")
    val showEditLot1 = ObservableField(false)
    val lot2title = ObservableField("")
    val lot2value = ObservableField("")
    val showEditLot2 = ObservableField(false)
    val lot3title = ObservableField("")
    val lot3value = ObservableField("")
    val showEditLot3 = ObservableField(false)
    val lot4title = ObservableField("")
    val lot4value = ObservableField("")
    val showEditLot4 = ObservableField(false)
    val lot5title = ObservableField("")
    val lot5value = ObservableField("")
    val showEditLot5 = ObservableField(false)
    val lot6title = ObservableField("")
    val lot6value = ObservableField("")
    val showEditLot6 = ObservableField(false)
    val lot7title = ObservableField("")
    val lot7value = ObservableField("")
    val showEditLot7 = ObservableField(false)
    val lot8title = ObservableField("")
    val lot8value = ObservableField("")
    val showEditLot8 = ObservableField(false)
    val lot9title = ObservableField("")
    val lot9value = ObservableField("")
    val showEditLot9 = ObservableField(false)
    val lot10title = ObservableField("")
    val lot10value = ObservableField("")
    val showEditLot10 = ObservableField(false)
    val lot11title = ObservableField("")
    val lot11value = ObservableField("")
    val showEditLot11 = ObservableField(false)
    val lot12title = ObservableField("")
    val lot12value = ObservableField("")
    val showEditLot12 = ObservableField(false)


    fun show(
        strTile: String,
        inReceiptSerialScanDto: InReceiptSerialScanDto,
        listLot: MutableList<CdWhLotDetailDto>
    ) {
        listLotTpl = listLot
        serialScan = inReceiptSerialScanDto
        // 逻辑 ： 如果是商品编码 并且批次模板有必填项，就弹框  ，其他必填属性为空也要显示编辑
        // 有三个属性如果为空就设定默认值
        // 只有是商品编码才会进入这里  (sn码不会弹出编辑)
        listLotTpl.forEach {
            if (it.lotAtt.equals("LOT_ATT01")) {
                showEditLot1.set(true)
                lot1title.set(it.title)
                // 有值设值
                if (inReceiptSerialScanDto.barcodeLotDto != null && !inReceiptSerialScanDto.barcodeLotDto.lotAtt01.isNullOrEmpty()) {
                    lot1value.set(inReceiptSerialScanDto.barcodeLotDto.lotAtt01)
                }
            } else if (it.lotAtt.equals("LOT_ATT02")) {
                showEditLot2.set(true)
                lot2title.set(it.title)
                // 有值设值
                if (inReceiptSerialScanDto.barcodeLotDto != null && !inReceiptSerialScanDto.barcodeLotDto.lotAtt02.isNullOrEmpty()) {
                    lot2value.set(inReceiptSerialScanDto.barcodeLotDto.lotAtt02)
                }
            } else if (it.lotAtt.equals("LOT_ATT03")) {  //入库日期
                showEditLot3.set(true)
                lot3title.set(it.title)
                // 有值设值 没值生成默认值
                if (inReceiptSerialScanDto.barcodeLotDto != null && !inReceiptSerialScanDto.barcodeLotDto.lotAtt03.isNullOrEmpty()) {
                    lot3value.set(inReceiptSerialScanDto.barcodeLotDto.lotAtt03)
                } else {
                    val date = Date()
                    lot3value.set(SimpleDateFormat("yyyyMMdd").format(date))
                }
            } else if (it.lotAtt.equals("LOT_ATT04")) {  //状态 ： 良品不良品
                showEditLot4.set(true)
                lot4title.set(it.title)

                // 有值设值 没值生成默认值
                if (inReceiptSerialScanDto.barcodeLotDto != null && !inReceiptSerialScanDto.barcodeLotDto.lotAtt04.isNullOrEmpty()) {
                    lot4value.set(
                        DCUtils.lot4TypeC2N.get(inReceiptSerialScanDto.barcodeLotDto.lotAtt04)
                            .toString()
                    )
                } else {
                    val t = dialog.activity.binding.vm!!.curSelectLot4Name
                    if (!t.isNullOrBlank()) {
                        lot4value.set(t)
                    }
                }
            } else if (it.lotAtt.equals("LOT_ATT05")) {
                showEditLot5.set(true)
                lot5title.set(it.title)
                // 有值设值 没值生成默认值
                if (inReceiptSerialScanDto.barcodeLotDto != null && !inReceiptSerialScanDto.barcodeLotDto.lotAtt05.isNullOrEmpty()) {
                    lot5value.set(inReceiptSerialScanDto.barcodeLotDto.lotAtt05)
                    dialog.binding.etLot5Value.setText(inReceiptSerialScanDto.barcodeLotDto.lotAtt05)
                } else {
                    val strDate = SimpleDateFormat("yyMM").format(Date())
                    lot5value.set(strDate)
                    dialog.binding.etLot5Value.setText(strDate)
                }
                //AppUtils.requestFocus(dialog.binding.etLot5Value)
            } else if (it.lotAtt.equals("LOT_ATT06")) {
                showEditLot6.set(true)
                lot6title.set(it.title)
                // 有值设值
                if (inReceiptSerialScanDto.barcodeLotDto != null && !inReceiptSerialScanDto.barcodeLotDto.lotAtt06.isNullOrEmpty()) {
                    lot6value.set(inReceiptSerialScanDto.barcodeLotDto.lotAtt06)
                }
            } else if (it.lotAtt.equals("LOT_ATT07")) {
                showEditLot7.set(true)
                lot7title.set(it.title)
                // 有值设值
                if (inReceiptSerialScanDto.barcodeLotDto != null && !inReceiptSerialScanDto.barcodeLotDto.lotAtt07.isNullOrEmpty()) {
                    lot7value.set(inReceiptSerialScanDto.barcodeLotDto.lotAtt07)
                }
            } else if (it.lotAtt.equals("LOT_ATT08")) {
                showEditLot8.set(true)
                lot8title.set(it.title)
                // 有值设值
                if (inReceiptSerialScanDto.barcodeLotDto != null && !inReceiptSerialScanDto.barcodeLotDto.lotAtt08.isNullOrEmpty()) {
                    lot8value.set(inReceiptSerialScanDto.barcodeLotDto.lotAtt08)
                }
            } else if (it.lotAtt.equals("LOT_ATT09")) {
                showEditLot9.set(true)
                lot9title.set(it.title)
                // 有值设值
                if (inReceiptSerialScanDto.barcodeLotDto != null && !inReceiptSerialScanDto.barcodeLotDto.lotAtt09.isNullOrEmpty()) {
                    lot9value.set(inReceiptSerialScanDto.barcodeLotDto.lotAtt09)
                }
            } else if (it.lotAtt.equals("LOT_ATT10")) {
                showEditLot10.set(true)
                lot10title.set(it.title)
                // 有值设值
                if (inReceiptSerialScanDto.barcodeLotDto != null && !inReceiptSerialScanDto.barcodeLotDto.lotAtt10.isNullOrEmpty()) {
                    lot10value.set(inReceiptSerialScanDto.barcodeLotDto.lotAtt10)
                }
            } else if (it.lotAtt.equals("LOT_ATT11")) {
                showEditLot11.set(true)
                lot11title.set(it.title)
                // 有值设值
                if (inReceiptSerialScanDto.barcodeLotDto != null && !inReceiptSerialScanDto.barcodeLotDto.lotAtt11.isNullOrEmpty()) {
                    lot11value.set(inReceiptSerialScanDto.barcodeLotDto.lotAtt11)
                }
            } else if (it.lotAtt.equals("LOT_ATT12")) {
                showEditLot12.set(true)
                lot12title.set(it.title)
                // 有值设值
                if (inReceiptSerialScanDto.barcodeLotDto != null && !inReceiptSerialScanDto.barcodeLotDto.lotAtt12.isNullOrEmpty()) {
                    lot12value.set(inReceiptSerialScanDto.barcodeLotDto.lotAtt12)
                }
            }
        }
    }

    fun onSelectLot4() {
        AlertDialogUtil.showSelectListDialog(dialog.activity, "选择库存状态", DCUtils.lot4TypeN2C,
            AlertDialogUtil.OnSelectKey { key, value ->
                lot4value.set(key.toString())
            })
    }

    fun close() {
        dialog.dismiss()
    }

    fun confirm() {
        val barcodeLotDto = BarcodeLotDto()

        if (!lot1value.get().isNullOrEmpty()) {
            if (!vailDateTimeWithLongFormat(lot1value.get().toString())) {
                ToastUtils.getInstance().taostWithErrorSound(
                    dialog.activity,
                    "生产日期格式错误，正确格式为yyyyMMdd",
                    Toast.LENGTH_SHORT
                )
                return
            }
            barcodeLotDto.lotAtt01 = lot1value.get()
        }

        if (!lot2value.get().isNullOrEmpty()) {
            if (!vailDateTimeWithLongFormat(lot2value.get().toString())) {
                ToastUtils.getInstance().taostWithErrorSound(
                    dialog.activity,
                    "失效日期格式错误，正确格式为yyyyMMdd",
                    Toast.LENGTH_SHORT
                )
                return
            }
            barcodeLotDto.lotAtt02 = lot2value.get()
        }

        if (!lot3value.get().isNullOrEmpty()) {
            if (!vailDateTimeWithLongFormat(lot3value.get().toString())) {
                ToastUtils.getInstance().taostWithErrorSound(
                    dialog.activity,
                    "入库日期格式错误，正确格式为yyyyMMdd",
                    Toast.LENGTH_SHORT
                )
                return
            }
            barcodeLotDto.lotAtt03 = lot3value.get()
        }

        if (!lot2value.get().isNullOrEmpty() && !lot3value.get().isNullOrEmpty()) {
            val df = SimpleDateFormat("yyyyMMdd")
            val date2 = df.parse(lot2value.get())
            val date3 = df.parse(lot3value.get())
            if (date3.after(date2)) {
                ToastUtils.getInstance().taostWithErrorSound(
                    dialog.activity,
                    "失效日期应晚于入库日期" + lot3value.get(),
                    Toast.LENGTH_SHORT
                )
                return
            }
        }

        //校验生产日期lot1要早于入库日期lot3
        if (!lot2value.get().isNullOrEmpty() && !lot3value.get().isNullOrEmpty()) {
            val df = SimpleDateFormat("yyyyMMdd")
            val date1 = df.parse(lot1value.get()) //生产日期
            val date3 = df.parse(lot3value.get()) //入库日期
            if (date1.after(date3)) {
                ToastUtils.getInstance().taostWithErrorSound(
                    dialog.activity,
                    "生产日期应早于入库日期" + lot3value.get(),
                    Toast.LENGTH_SHORT
                )
                return
            }
        }

        if (!lot4value.get().isNullOrEmpty()) {
            barcodeLotDto.lotAtt04 = lot4value.get()
        }
        if (!lot5value.get().isNullOrEmpty()) {
            barcodeLotDto.lotAtt05 = lot5value.get()
        }
        if (!lot6value.get().isNullOrEmpty()) {
            barcodeLotDto.lotAtt06 = lot6value.get()
        }
        if (!lot7value.get().isNullOrEmpty()) {
            barcodeLotDto.lotAtt07 = lot7value.get()
        }
        if (!lot8value.get().isNullOrEmpty()) {
            barcodeLotDto.lotAtt08 = lot8value.get()
        }
        if (!lot9value.get().isNullOrEmpty()) {
            barcodeLotDto.lotAtt09 = lot9value.get()
        }
        if (!lot10value.get().isNullOrEmpty()) {
            barcodeLotDto.lotAtt10 = lot10value.get()
        }
        if (!lot11value.get().isNullOrEmpty()) {
            barcodeLotDto.lotAtt11 = lot11value.get()
        }
        if (!lot12value.get().isNullOrEmpty()) {
            barcodeLotDto.lotAtt12 = lot12value.get()
        }

        serialScan.barcodeLotDto = barcodeLotDto
        serialScan.barcodeLotDto.whCode = serialScan.whCode

        // 要弹框编辑批属性的都是商品编码而不是sn码，所以第一个参数为false
        dialog.activity.binding.vm!!.onConfirmWriteLotInfo(false, serialScan)

    }

    //验证字符串是不是 yyyyMMdd
    @SuppressLint("SimpleDateFormat")
    private fun vailDateTimeWithLongFormat(timeStr: String): Boolean {
        //判断结果 默认为true
        var judgeresult = true
        //1、首先使用SimpleDateFormat初步进行判断，过滤掉注入 yyyy0132 或yyyy000x等格式
        //此处可根据实际需求进行调整，如需判断yyyy/MM/dd格式将参数改掉即可
        val format = SimpleDateFormat("yyyyMMdd")
        try {
            //增加强判断条件，否则 诸如2022-02-29也可判断出去
            format.isLenient = false
            format.parse(timeStr)
        } catch (e: Exception) {
            judgeresult = false
        }
        //由于上述方法只能验证正常的日期格式，像诸如 0001-01-01、11-01-01，10001-01-01等无法校验，此处再添加校验年费是否合法
        val yearStr: String = timeStr.substring(0, 2)
        if (yearStr != "20") {
            judgeresult = false
        }
        return judgeresult
    }
}