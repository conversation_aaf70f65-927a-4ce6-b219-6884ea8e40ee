package com.midea.prestorage.function.containerpick

import CheckUtil
import android.annotation.SuppressLint
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.view.View
import android.widget.Toast
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.App
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.ContainerPickSecondList
import com.midea.prestorage.beans.net.ContainerPickTaskType
import com.midea.prestorage.beans.net.RespPickConfirm
import com.midea.prestorage.function.containerpick.dialog.ErrorTipDialog
import com.midea.prestorage.function.inv.response.FuInvLocationInventory
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.function.receivecpkx.ContainerListActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.*
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.util.*

class ContainerPickThirdVM(val activity: ContainerPickThirdActivity) {

    var title = ObservableField<String>()

    var itemName = ObservableField<String>()
    var item69Code = ObservableField<String>()
    var itemCode = ObservableField<String>()
    var pickLoc = ObservableField<String>()
    var taskCode = ObservableField<String>()
    var pickStatus = ObservableField<String>()
    var manufacturing = ObservableField<String>()
    var batch = ObservableField<String>()
    var handQty = ObservableField<String>("0")
    var handQtyStr = ObservableField<String>("")

    var handTotalQty = ObservableField<String>("0")

    var sumQty = ObservableField<String>()
    var packagePara = ObservableField<String>()
    var qtyInfo = ObservableField<String>()
    var csUnit = ObservableField<String>()
    var eaUnit = ObservableField<String>()
    var ipUnit = ObservableField<String>()

    var inputCs = ObservableField<String>()
    var inputEa = ObservableField<String>()
    var inputIp = ObservableField<String>()

    var isEnableClick = ObservableField(true)
    val isShowUpError = ObservableBoolean(false)

    var bean: ContainerPickSecondList? = null

    var taskType = ObservableField<String>()

    private var containerPickTaskType: ContainerPickTaskType? = null

    private var tipDialog: ErrorTipDialog? = null

    private var isActivityActive = true

    fun init() {
        bean =
            activity.intent.getSerializableExtra("ContainerPickSecondList") as ContainerPickSecondList

        containerPickTaskType =
            activity.intent.getSerializableExtra("containerPickTaskType") as ContainerPickTaskType

        taskType.set(activity.intent.getStringExtra("taskType"))

        if ("SORTPICK" == taskType.get()) { //若任务类型taskType=SORTPICK,则需展示分拣信息，若任务类型taskType不是SORTPICK，则不显示分拣信息
            activity.adapterNumber.addData(containerPickTaskType!!.list)
            activity.adapterNumber.notifyDataSetChanged()
        } else {
            activity.hideGv()
        }

        title.set(activity.intent.getStringExtra("containerCode"))

        bean?.let {
            activity.binding.edEa.removeTextChangedListener(activity.textWatcher)
            if (it.isDecimal != 0) {
                activity.textWatcher?.limitDecimalPlaces = 4
            } else {
                activity.textWatcher?.limitDecimalPlaces = 0
            }
            activity.binding.edEa.addTextChangedListener(activity.textWatcher)

            activity.binding.edEaSecond.removeTextChangedListener(activity.textWatcher02)
            if (it.isDecimal != 0) {
                activity.textWatcher02?.limitDecimalPlaces = 4
            } else {
                activity.textWatcher02?.limitDecimalPlaces = 0
            }
            activity.binding.edEaSecond.addTextChangedListener(activity.textWatcher02)
        }

        taskCode.set(bean?.taskCode)
        itemName.set(bean?.itemName)
        item69Code.set(bean?.custItemCode)
        if (bean?.csBarcode69.isNullOrEmpty() && bean?.whBarcode69.isNullOrEmpty()) {
            activity.binding.tvCarcode69.visibility = View.GONE
        } else {
            activity.binding.tvCarcode69.visibility = View.VISIBLE
            itemCode.set(LotAttUnit.formatWhBarcode69(bean?.csBarcode69, bean?.whBarcode69))
        }
        pickLoc.set(bean?.fromLoc)
        pickStatus.set(bean?.lotAtt04)
        manufacturing.set(bean?.lotAtt01?.split(" ")?.get(0))
        batch.set(bean?.lotAtt05)
        csUnit.set("${bean?.csUnit}")
        eaUnit.set("${bean?.eaUnit}")
        ipUnit.set("${bean?.ipUnit}")

        if (bean?.csUnit.isNullOrEmpty() || bean?.packagePara == null || AppUtils.isZero(bean?.packagePara)) {
            activity.binding.llCs.visibility = View.GONE
        } else {
            activity.binding.llCs.visibility = View.VISIBLE
        }

        if (bean?.ipUnit.isNullOrEmpty() || bean?.packageParaIp == null || AppUtils.isZero(bean?.packageParaIp)) {
            activity.binding.llIp.visibility = View.GONE
        } else {
            activity.binding.llIp.visibility = View.VISIBLE
        }

        if (bean?.eaUnit.isNullOrEmpty()) {
            activity.binding.llEa.visibility = View.GONE
            activity.binding.llSecondPackage.visibility = View.GONE
            activity.binding.llEaSecond.visibility = View.GONE
        } else {
            if (activity.binding.llCs.visibility == View.VISIBLE && activity.binding.llIp.visibility == View.VISIBLE) {
                activity.binding.llEa.visibility = View.GONE
                activity.binding.llSecondPackage.visibility = View.VISIBLE
                activity.binding.llEaSecond.visibility = View.VISIBLE
            } else {
                activity.binding.llEa.visibility = View.VISIBLE
                activity.binding.llSecondPackage.visibility = View.GONE
                activity.binding.llEaSecond.visibility = View.GONE
            }
        }

        val sumCsQtyStr = AppUtils.getBigDecimalValueStr(bean?.sumCsQty)
        val sumEaQtyStr = AppUtils.getBigDecimalValueStr(bean?.sumEaQty)
        val sumIpQtyStr = AppUtils.getBigDecimalValueStr(bean?.sumIpQty)

        var focusCsQty = 0
        var qtyInfoStr = ""

        if (!AppUtils.isZero(bean?.sumCsQty)) {
            qtyInfoStr = "${sumCsQtyStr}${bean?.csUnit}"
            focusCsQty = 1
        }


        if (!AppUtils.isZero(bean?.sumIpQty)) {
            if (qtyInfoStr.isNullOrEmpty()) {
                qtyInfoStr = "${sumIpQtyStr}${bean?.ipUnit}"
            } else {
                qtyInfoStr = "${qtyInfoStr}${sumIpQtyStr}${bean?.ipUnit}"
            }
            if (focusCsQty == 0) {
                focusCsQty = 2
            }
        }

        if (!AppUtils.isZero(bean?.sumEaQty)) {
            if (qtyInfoStr.isNullOrEmpty()) {
                qtyInfoStr = "${sumEaQtyStr}${bean?.eaUnit}"
            } else {
                qtyInfoStr = "${qtyInfoStr}${sumEaQtyStr}${bean?.eaUnit}"
            }
            if (focusCsQty == 0) {
                focusCsQty = 3
            }
        }

        qtyInfo.set(qtyInfoStr)

        sumQty.set("${AppUtils.getBigDecimalValueStr(bean?.sumQty)}${bean?.eaUnit}")
//        if (bean?.packagePara == null) {
//            packagePara.set("")
//        } else {
//            packagePara.set("${AppUtils.getBigDecimalValueStr(bean?.packagePara)}${bean?.eaUnit}/${bean?.csUnit}")
//        }
//
//        if ((bean?.packagePara == null || AppUtils.isZero(bean?.packagePara)) && (bean?.packageParaIp == null || AppUtils.isZero(
//                bean?.packageParaIp
//            ))
//        ) {
//            packagePara.set("")
//        } else if (bean?.packageParaIp == null || AppUtils.isZero(bean?.packageParaIp)) {
//            packagePara.set("${AppUtils.getBigDecimalValueStr(bean?.packagePara)}${bean?.eaUnit}/${bean?.csUnit}")
//        } else if (bean?.packagePara == null || AppUtils.isZero(bean?.packagePara)) {
//            packagePara.set("${AppUtils.getBigDecimalValueStr(bean?.packageParaIp)}${bean?.eaUnit}/${bean?.ipUnit}")
//        } else {
//            packagePara.set(
//                "${AppUtils.getBigDecimalValueStr(bean?.packageParaIp)}${bean?.eaUnit}/${bean?.ipUnit},${
//                    AppUtils.getBigDecimalValueStr(
//                        bean?.packagePara
//                    )
//                }${bean?.eaUnit}/${bean?.csUnit}"
//            )
//
//        }
        if (bean?.cdpaFormat.isNullOrEmpty()) {
            packagePara.set("")
        } else {
            packagePara.set(bean?.cdpaFormat)
        }

        if (AppUtils.isZero(bean?.packagePara)) {
            activity.csEnable(false)
        } else {
            activity.csEnable(true)
        }

        if (AppUtils.isZero(bean?.packageParaIp)) {
            activity.ipEnable(false)
        } else {
            activity.ipEnable(true)
        }

        when (focusCsQty) {
            1 -> {
                activity.csRequestFocus()
            }
            2 -> {
                activity.eaRequestFocus()
            }
            else -> {
                activity.ipRequestFocus()
            }
        }

        if (AppUtils.isZero(bean?.sumCsQty)) {
            inputCs.set("")
        } else {
            inputCs.set(AppUtils.getBigDecimalValueStr(bean?.sumCsQty))
        }

        if (AppUtils.isZero(bean?.sumIpQty)) {
            inputIp.set("")
        } else {
            inputIp.set(AppUtils.getBigDecimalValueStr(bean?.sumIpQty))
        }

        if (AppUtils.isZero(bean?.sumEaQty)) {
            inputEa.set("")
        } else {
            inputEa.set(AppUtils.getBigDecimalValueStr(bean?.sumEaQty))
        }

        tipDialog = ErrorTipDialog(activity)
        tipDialog?.setOnErrorTipBackListener(object : ErrorTipDialog.OnErrorTipBack {
            override fun onConfirmClick(reasonStr: String) {
                confirm(true, reasonStr)
            }
        })

        isShowUpError.set(Constants.isShowUpError)

        startSearch()
    }

    fun onErrorClick() {
        if (CheckUtil.isFastDoubleClick()) {
            tipDialog?.show()
        }
    }

    val inputCsTextChange = object : ViewBindingAdapter.TextChangedListener {
        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            val inputNumStr = s?.toString()?.toIntOrNull() ?: 0
            val inputIpInt = inputIp.get()?.toIntOrNull() ?: 0
            val inputEaInt = AppUtils.getBigDecimalValue(inputEa.get())
            val packagePara = AppUtils.getBigDecimalValue(bean?.packagePara)
            val packageParaIp = AppUtils.getBigDecimalValue(bean?.packageParaIp)

            fun calculateNum(): BigDecimal {
                return when {
                    inputIp.get().isNullOrBlank() && inputEa.get().isNullOrBlank() -> {
                        if (packagePara.compareTo(BigDecimal(0)) == 1) {
                            BigDecimal(inputNumStr).multiply(packagePara)
                        } else {
                            BigDecimal(inputNumStr)
                        }
                    }
                    inputIp.get().isNullOrBlank() -> {
                        if (packagePara.compareTo(BigDecimal(0)) == 1) {
                            if (packageParaIp.compareTo(BigDecimal(0)) == 1) {
                                BigDecimal(inputNumStr).multiply(packagePara) +
                                        inputEaInt +
                                        BigDecimal(inputIpInt).multiply(packageParaIp)
                            } else {
                                BigDecimal(inputNumStr).multiply(packagePara) +
                                        inputEaInt +
                                        BigDecimal(inputIpInt)
                            }
                        } else {
                            if (packageParaIp.compareTo(BigDecimal(0)) == 1) {
                                BigDecimal(inputNumStr) +
                                        BigDecimal(inputIpInt).multiply(packageParaIp) +
                                        inputEaInt
                            } else {
                                BigDecimal(inputNumStr) +
                                        BigDecimal(inputIpInt) +
                                        inputEaInt
                            }
                        }
                    }
                    inputEa.get().isNullOrBlank() -> {
                        if (packagePara.compareTo(BigDecimal(0)) == 1) {
                            if (packageParaIp.compareTo(BigDecimal(0)) == 1) {
                                BigDecimal(inputNumStr).multiply(packagePara) +
                                        BigDecimal(inputIpInt).multiply(packageParaIp)
                            } else {
                                BigDecimal(inputNumStr).multiply(packagePara) +
                                        BigDecimal(inputIpInt)
                            }
                        } else {
                            if (packageParaIp.compareTo(BigDecimal(0)) == 1) {
                                BigDecimal(inputNumStr).multiply(packageParaIp) +
                                        BigDecimal(inputIpInt)
                            } else {
                                BigDecimal(inputNumStr) +
                                        BigDecimal(inputIpInt)
                            }
                        }
                    }
                    else -> {
                        if (packagePara.compareTo(BigDecimal(0)) == 1) {
                            if (packageParaIp.compareTo(BigDecimal(0)) == 1) {
                                BigDecimal(inputNumStr).multiply(packagePara) +
                                        BigDecimal(inputIpInt).multiply(packageParaIp) +
                                        inputEaInt
                            } else {
                                BigDecimal(inputNumStr).multiply(packagePara) +
                                        BigDecimal(inputIpInt) +
                                        inputEaInt
                            }
                        } else {
                            if (packageParaIp.compareTo(BigDecimal(0)) == 1) {
                                BigDecimal(inputNumStr).multiply(packageParaIp) +
                                        BigDecimal(inputIpInt) +
                                        inputEaInt
                            } else {
                                BigDecimal(inputNumStr) +
                                        BigDecimal(inputIpInt) +
                                        inputEaInt
                            }
                        }
                    }
                }
            }

            setNum(calculateNum())
        }
    }

    val inputIpTextChange = object : ViewBindingAdapter.TextChangedListener {

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            val inputNumStr = AppUtils.getBigDecimalValue(s?.toString())
            val inputCsInt = AppUtils.getBigDecimalValue(inputCs.get())
            val inputEaInt = AppUtils.getBigDecimalValue(inputEa.get())
            val packagePara = AppUtils.getBigDecimalValue(bean?.packagePara)
            val packageParaIp = AppUtils.getBigDecimalValue(bean?.packageParaIp)
            val num = when {
                AppUtils.isZero(inputCsInt) && AppUtils.isZero(inputEaInt) -> {
                    if (packageParaIp > BigDecimal.ZERO) inputNumStr * packageParaIp
                    else inputNumStr
                }
                AppUtils.isZero(inputCsInt) -> {
                    if (packageParaIp > BigDecimal.ZERO) inputNumStr * packageParaIp + inputEaInt
                    else inputNumStr + inputEaInt
                }
                AppUtils.isZero(inputEaInt) -> {
                    if (packagePara > BigDecimal.ZERO) inputCsInt * packagePara + inputNumStr
                    else inputCsInt + inputNumStr
                }
                else -> {
                    if (packagePara > BigDecimal.ZERO && packageParaIp > BigDecimal.ZERO) {
                        inputCsInt * packagePara + inputNumStr * packageParaIp + inputEaInt
                    } else {
                        inputCsInt + inputNumStr + inputEaInt
                    }
                }
            }
            setNum(num)
        }
    }

    val inputEaTextChange = object : ViewBindingAdapter.TextChangedListener {

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            if (s.isNullOrEmpty()) {
                setNum(
                    getNumFromInput(
                        inputCs.get(),
                        inputIp.get(),
                        bean?.packagePara,
                        bean?.packageParaIp
                    )
                )
            } else {
                val inputNumStr = AppUtils.getBigDecimalValue(s.toString())
                setNum(
                    getNumFromInput(
                        inputCs.get(),
                        inputIp.get(),
                        bean?.packagePara,
                        bean?.packageParaIp
                    ) + inputNumStr
                )
            }
        }

        private fun getNumFromInput(
            cs: String?,
            ip: String?,
            para: BigDecimal?,
            paraIp: BigDecimal?
        ): BigDecimal {
            val csNum = cs?.toIntOrNull() ?: 0
            val ipNum = ip?.toIntOrNull() ?: 0
            val packagePara = para ?: BigDecimal.ZERO
            val packageParaIp = paraIp ?: BigDecimal.ZERO
            return when {
                cs.isNullOrBlank() && ip.isNullOrBlank() -> BigDecimal.ZERO
                cs.isNullOrBlank() -> if (packageParaIp > BigDecimal.ZERO) BigDecimal(ipNum).multiply(
                    packageParaIp
                ) else BigDecimal(ipNum)
                ip.isNullOrBlank() -> if (packagePara > BigDecimal.ZERO) BigDecimal(csNum).multiply(
                    packagePara
                ) else BigDecimal(csNum)
                else -> {
                    val csValue =
                        if (packagePara > BigDecimal.ZERO) BigDecimal(csNum).multiply(packagePara) else BigDecimal(
                            csNum
                        )
                    val ipValue = if (packageParaIp > BigDecimal.ZERO) BigDecimal(ipNum).multiply(
                        packageParaIp
                    ) else BigDecimal(ipNum)
                    csValue + ipValue
                }
            }
        }
    }

    fun startSearch() {

        activity.waitingDialogHelp.showDialog()
        val param = mutableMapOf(
            "ownerCode" to bean?.ownerCode,
            "locCode" to bean?.fromLoc,
            "custItemCode" to bean?.custItemCode,
            "checkQty" to true,
            "whCode" to activity.getWhCode(),
            "pageNo" to 1,
            "pageSize" to 10
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getInventoryAPI()
            .fuInvLocationInventoryList(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PageResult<FuInvLocationInventory>>(activity) {
                override fun success(data: PageResult<FuInvLocationInventory>?) {
                    activity.waitingDialogHelp.hidenDialog()

                    if (data != null && data.list != null) {
                        if (data.list.isNotEmpty()) {
                            bean?.sumQty.let {
                                handTotalQty.set(AppUtils.getBigDecimalValueStr(data.list[0].onHandQtyTotal))
                                if (it != null) {
                                    setNum(it)
                                }
                            }
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    /**
     * 计算剩余库存数，并换算为CS和EA数
     * it:输入的拣货数
     */
    fun setNum(it: BigDecimal) {
        if (AppUtils.getBigDecimalValue(handTotalQty.get()).compareTo(it) != 1) {
            handQty.set("0${eaUnit.get()}")
            handQtyStr.set("")
            return
        }
        handQty.set(
            AppUtils.getBigDecimalValueStr(
                AppUtils.getBigDecimalValue(handTotalQty.get()).subtract(it)
            ) + eaUnit.get()
        )

        //////
        val packagePara = AppUtils.getBigDecimalValue(bean?.packagePara)
        val packageParaIp = AppUtils.getBigDecimalValue(bean?.packageParaIp)
        val sumQtyBig =
            AppUtils.getBigDecimalValue(AppUtils.getBigDecimalValue(handTotalQty.get()) - it!!)
        var sumCsQty: BigDecimal
        var sumIpQty: BigDecimal
        var sumEaQty: BigDecimal
        var qtyInfo = ""
        if (packagePara.compareTo(BigDecimal(0)) == 1) {
            val divide =
                sumQtyBig.divide(packagePara, BigDecimal.ROUND_DOWN)
            if (divide.compareTo(BigDecimal(1)) == -1) {
                sumCsQty = BigDecimal(0)
                if (packageParaIp.compareTo(BigDecimal(0)) == 1) {
                    val divideIp =
                        sumQtyBig.divide(packageParaIp, BigDecimal.ROUND_DOWN)
                    if (divideIp.compareTo(BigDecimal(1)) == -1) {
                        sumIpQty = BigDecimal(0)
                        sumEaQty = sumQtyBig
                    } else {
                        sumIpQty = divideIp.setScale(0, BigDecimal.ROUND_DOWN)
                        sumEaQty =
                            sumQtyBig.subtract(sumIpQty.multiply(packageParaIp))
                    }
                } else {
                    sumIpQty = BigDecimal(0)
                    sumEaQty = sumQtyBig
                }
            } else {
                sumCsQty = divide.setScale(0, BigDecimal.ROUND_DOWN)
                var sumQty =
                    sumQtyBig.subtract(sumCsQty.multiply(packagePara))
                if (packageParaIp.compareTo(BigDecimal(0)) == 1) {
                    val divideIp =
                        sumQty.divide(packageParaIp, BigDecimal.ROUND_DOWN)
                    if (divideIp.compareTo(BigDecimal(1)) == -1) {
                        sumIpQty = BigDecimal(0)
                        sumEaQty = sumQty
                    } else {
                        sumIpQty = divideIp.setScale(0, BigDecimal.ROUND_DOWN)
                        sumEaQty =
                            sumQty.subtract(sumIpQty.multiply(packageParaIp))
                    }
                } else {
                    sumIpQty = BigDecimal(0)
                    sumEaQty = sumQty
                }
            }
        } else {
            sumCsQty = BigDecimal(0)
            if (packageParaIp.compareTo(BigDecimal(0)) == 1) {
                val divideIp =
                    sumQtyBig.divide(packageParaIp, BigDecimal.ROUND_DOWN)
                if (divideIp.compareTo(BigDecimal(1)) == -1) {
                    sumIpQty = BigDecimal(0)
                    sumEaQty = sumQtyBig
                } else {
                    sumIpQty = divideIp.setScale(0, BigDecimal.ROUND_DOWN)
                    sumEaQty =
                        sumQtyBig.subtract(sumIpQty.multiply(packageParaIp))
                }
            } else {
                sumIpQty = BigDecimal(0)
                sumEaQty = sumQtyBig
            }
        }

        val sumCsQtyStr = AppUtils.getBigDecimalValueStr(sumCsQty)
        val sumEaQtyStr = AppUtils.getBigDecimalValueStr(sumEaQty)
        val sumIpQtyStr = AppUtils.getBigDecimalValueStr(sumIpQty)

        if (!AppUtils.isZero(sumCsQty)) {
            qtyInfo = "${sumCsQtyStr}${csUnit.get()}"
        }


        if (!AppUtils.isZero(sumIpQty)) {
            if (qtyInfo.isNullOrEmpty()) {
                qtyInfo = "${sumIpQtyStr}${ipUnit.get()}"
            } else {
                qtyInfo = "${qtyInfo}${sumIpQtyStr}${ipUnit.get()}"
            }
        }

        if (!AppUtils.isZero(sumEaQty)) {
            if (qtyInfo.isNullOrEmpty()) {
                qtyInfo = "${sumEaQtyStr}${eaUnit.get()}"
            } else {
                qtyInfo = "${qtyInfo}${sumEaQtyStr}${eaUnit.get()}"
            }
        }

        if (qtyInfo.isNullOrEmpty()) {
            handQtyStr.set("")
        } else {
            handQtyStr.set("($qtyInfo)")
        }
    }

    fun confirmClick() {
        confirm(false, "")
    }

    @SuppressLint("SimpleDateFormat")
    fun confirm(isErrorEnter: Boolean = false, reasonStr: String) {
        if (CheckUtil.isFastDoubleClick()) {
            if (!bean?.confirmedBy.isNullOrEmpty() && Constants.userInfo?.name != bean?.confirmedBy) {
                ToastUtils.getInstance()
                    .showErrorToastWithSound(
                        activity,
                        "当前任务【${bean?.taskCode}】执行人为【${bean?.confirmedBy}】!"
                    )
                return
            }

            val csRule = AppUtils.getBigDecimalValue(bean?.packagePara)
            val ipRule = AppUtils.getBigDecimalValue(bean?.packageParaIp)
            val csCount = AppUtils.getBigDecimalValue(inputCs.get())
            val ipCount = AppUtils.getBigDecimalValue(inputIp.get()).multiply(ipRule)
            val eaCount = AppUtils.getBigDecimalValue(inputEa.get()).add(ipCount)
            val allQty = csRule.multiply(csCount).add(eaCount)

            if (AppUtils.isZero(allQty)) {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "必须输入数量!")
                inputCs.set("")
                inputIp.set("")
                inputEa.set("")
                activity.csRequestFocus()
                return
            }

            val containerCode = activity.intent.getStringExtra("containerCode")
            val autoGetContainerCode = activity.intent.getStringExtra("autoGetContainerCode")
            val waveNo = activity.intent.getStringExtra("waveNo")

            val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

            val param = mutableMapOf(
                "ids" to bean?.ids,
                "taskCode" to bean?.taskCode,
                "waveNo" to waveNo,
                "containerCode" to containerCode,
                "pickerTime" to format.format(Date()),
                "specifications" to bean?.packagePara,
                "picker" to Constants.userInfo?.name,
                "operateChanel" to "RF",
                "whCode" to Constants.whInfo?.whCode,
                "autoGetContainerCode" to ("1" == autoGetContainerCode)
            )

            if (isErrorEnter) {
                param["sumQty"] = 0
                param["sumCsQty"] = 0
                param["sumEaQty"] = 0
                param["shortPick"] = if (reasonStr.startsWith("C")) 2 else 1
                param["shortPickReason"] = reasonStr
            } else {
                param["sumQty"] = allQty
                param["sumCsQty"] = csCount
                param["sumEaQty"] = eaCount
                param["shortPick"] = 0
            }

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            isEnableClick.set(false)
            activity.waitingDialogHelp.showDialogUnCancel()

            RetrofitHelper.getOutStorageAPI()
                .pickConfirm(requestBody)
                .compose(NetworkScheduler.compose())
                .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
                .subscribe(object : RequestCallback<RespPickConfirm>(activity) {
                    override fun success(data: RespPickConfirm?) {
                        activity.waitingDialogHelp.hidenDialog()
                        isEnableClick.set(true)

                        MySoundUtils.getInstance().dingSound()
                        AppUtils.showToast(activity, "操作成功!")

                        val it = Intent()
                        it.putExtra("result", 1)
                        data?.let { data ->
                            data.containerCode?.let { result ->
                                it.putExtra("containerCode", result)
                            }
                        }
                        activity.setResult(RxAppCompatActivity.RESULT_OK, it)
                        activity.finish()
                    }

                    override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                        activity.waitingDialogHelp.hidenDialog()
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, apiErrorModel.message)
                        if (statusCode == 606015L) {
                            Handler(Looper.getMainLooper()).postDelayed({
                                if (isActivityActive) {
                                    val it = Intent(activity, ContainerPickActivity::class.java)
                                    activity.startActivity(it)
                                }
                            }, 1000)
                        } else {
                            isEnableClick.set(true)
                        }
                    }
                })
        }
    }

    fun onEnterOrderNo() {
        if (CheckUtil.isFastDoubleClick()) {
            activity.eaRequestFocus()
        }
    }

    fun onEnterIp() {
        if (CheckUtil.isFastDoubleClick()) {
            activity.ipRequestFocus()
        }
    }

    fun back() {
        isActivityActive = false
        activity.finish()
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }
}