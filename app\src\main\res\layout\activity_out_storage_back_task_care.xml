<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="vm"
            type="com.midea.prestorage.function.outstorage.OutStorageBackTaskViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/day_night_common_status_bar_blue"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_title_layout"
            android:layout_width="match_parent"
            android:layout_height="48dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="40dp"
                android:layout_height="20dp"
                android:clickable="true"
                android:focusable="true"
                android:onClick="@{() -> vm.back()}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_white_back" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="返库任务"
                android:textColor="@color/day_night_common_text_white"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@{vm.statusColor}"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:paddingLeft="10dp"
            android:paddingTop="5dp"
            android:paddingRight="10dp"
            android:paddingBottom="5dp"
            android:text="@{vm.notificationInfo}"
            android:textColor="@color/colorWhite"
            android:textSize="18sp"
            android:visibility="@{vm.isShowNotification? View.VISIBLE:View.GONE}"
            app:drawableLeftCompat="@mipmap/setting_prompt_care"
            tools:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginHorizontal="12dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/common_et_bg_4dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/et_cust_item_code"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:background="@color/transparent"
                android:digits="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_*"
                android:hint="请输入或扫描商品编码/条码"
                android:paddingHorizontal="12dp"
                android:singleLine="true"
                android:text="@={vm.barcode}"
                android:textColor="@color/black"
                android:textColorHint="#999999"
                android:textSize="19sp"
                app:onEnterKeyPress="@{()->vm.onEnterCustItemCode()}"
                tools:ignore="NestedWeights" />

            <ImageView
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_gravity="center_vertical|end"
                android:layout_marginEnd="8dp"
                android:onClick="@{()->vm.delete()}"
                android:padding="2dp"
                android:src="@mipmap/close_grey" />

            <ImageView
                android:id="@+id/iv_scan"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginRight="@dimen/dp_10"
                android:onClick="@{()->vm.scan()}"
                android:src="@mipmap/ic_scan_code" />

        </LinearLayout>

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/srl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:refreshing="@{vm.isRefreshing}">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/colorBg">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="@{vm.showRecycleView ? View.VISIBLE : View.GONE}"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:orientation="vertical"
                    android:visibility="@{vm.showRecycleView ? View.GONE : View.VISIBLE}">

                    <ImageView
                        android:id="@+id/iv_no_order"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/no_order" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="-15dp"
                        android:background="@color/colorBg"
                        android:gravity="center"
                        android:text="请重新选择条件"
                        android:textSize="@dimen/sp_12" />

                </LinearLayout>

                <View
                    android:id="@+id/v_mask"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/half_transport"
                    android:visibility="gone" />

            </FrameLayout>

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </LinearLayout>

</layout>