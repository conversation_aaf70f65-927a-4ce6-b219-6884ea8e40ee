package com.midea.prestorage.function.inv

import android.graphics.Color
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.databinding.ObservableField
import com.bigkoo.pickerview.TimePickerView
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.net.CdWhLotDetailDto
import com.midea.prestorage.beans.net.RespCdWhLotDetail
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.function.inv.response.FuInvLocationInventory
import com.midea.prestorage.function.inv.response.FuInvLot
import com.midea.prestorage.function.inv.response.SubmitInvLocationInventory
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.*
import com.midea.prestorage.widgets.FilterDigitTextWatcher
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.midea.prestoragesaas.R
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.util.*
import java.util.regex.Pattern

class LotEditVM(val activity: LotEditActivity) {

    var textLocCode = ObservableField<String>("")  //源货位
    var textOwnerCode = ObservableField<String>("")  //货主
    var textItemName = ObservableField<String>("")  //货主
    var textCanUseNum = ObservableField<String>("")  //可用库存
    var textEaUnit = ObservableField<String>("包")  //最小单位
    var textUnitAmount = ObservableField<String>("")
    var textAmount = ObservableField<String>("0")  //调整数
    var textEditReason = ObservableField<String>("")  //调整原因
    var textItemCode = ObservableField<String>("")  //条码
    var text69Code = ObservableField<String>("")  //条码

    //原属性
    var textLot1 = ObservableField<String>("")  //生产日期
    var textLot2 = ObservableField<String>("")  //失效日期
    var textLot3 = ObservableField<String>("")  //入库如期
    var textLot4 = ObservableField<String>("")  // 状态：正品良品
    var textLot5 = ObservableField<String>("")  //批次
    var textLot6 = ObservableField<String>("")  //其他自定义属性6
    var textLot7 = ObservableField<String>("")  //其他自定义属性7
    var textLot8 = ObservableField<String>("")  //其他自定义属性8
    var textLot9 = ObservableField<String>("")  //其他自定义属性9
    var textLot10 = ObservableField<String>("")  //其他自定义属性10
    var textLot11 = ObservableField<String>("")  //其他自定义属性11
    var textLot12 = ObservableField<String>("")  //其他自定义属性12

    //新属性
    var newLot1 = ObservableField<String>("")  //生产日期
    var newLot2 = ObservableField<String>("")  //失效日期
    var newLot3 = ObservableField<String>("")  //入库如期

    //var newLot4 = ObservableField<String>("")  // 状态：正品良品
    var newLot5 = ObservableField<String>("")  //批次
    var newLot6 = ObservableField<String>("")  //其他自定义属性6
    var newLot7 = ObservableField<String>("")  //其他自定义属性7
    var newLot8 = ObservableField<String>("")  //其他自定义属性8
    var newLot9 = ObservableField<String>("")  //其他自定义属性9
    var newLot10 = ObservableField<String>("")  //其他自定义属性10
    var newLot11 = ObservableField<String>("")  //其他自定义属性11
    var newLot12 = ObservableField<String>("")  //其他自定义属性12

    var showEditLot1 = ObservableField<Boolean>(false)
    var showEditLot2 = ObservableField<Boolean>(false)
    var showEditLot3 = ObservableField<Boolean>(false)

    //属性4不现实  不编辑
    //var showEditLot4 = ObservableField<Boolean>(true)
    var showEditLot5 = ObservableField<Boolean>(false)
    var showEditLot6 = ObservableField<Boolean>(false)
    var showEditLot7 = ObservableField<Boolean>(false)
    var showEditLot8 = ObservableField<Boolean>(false)
    var showEditLot9 = ObservableField<Boolean>(false)
    var showEditLot10 = ObservableField<Boolean>(false)
    var showEditLot11 = ObservableField<Boolean>(false)
    var showEditLot12 = ObservableField<Boolean>(false)

    var showTextLot1 = ObservableField<Boolean>(false)
    var showTextLot2 = ObservableField<Boolean>(false)
    var showTextLot3 = ObservableField<Boolean>(false)
    var showTextLot5 = ObservableField<Boolean>(false)
    var showTextLot6 = ObservableField<Boolean>(false)
    var showTextLot7 = ObservableField<Boolean>(false)
    var showTextLot8 = ObservableField<Boolean>(false)
    var showTextLot9 = ObservableField<Boolean>(false)
    var showTextLot10 = ObservableField<Boolean>(false)
    var showTextLot11 = ObservableField<Boolean>(false)
    var showTextLot12 = ObservableField<Boolean>(false)

    var inputControl1 = ObservableField<Boolean>(false)
    var inputControl2 = ObservableField<Boolean>(false)
    var inputControl3 = ObservableField<Boolean>(false)
    var inputControl5 = ObservableField<Boolean>(false)
    var inputControl6 = ObservableField<Boolean>(false)
    var inputControl7 = ObservableField<Boolean>(false)
    var inputControl8 = ObservableField<Boolean>(false)
    var inputControl9 = ObservableField<Boolean>(false)
    var inputControl10 = ObservableField<Boolean>(false)
    var inputControl11 = ObservableField<Boolean>(false)
    var inputControl12 = ObservableField<Boolean>(false)

    var isEnableClick = ObservableField(true)
    var curEditLocationInv = FuInvLocationInventory()
    var cdprQuantity = BigDecimal.ONE
    var cdprUnit: String? = null

    var format = SimpleDateFormat("yyyy-MM-dd")

    init {


        if (activity.intent.extras != null && activity.intent.extras!!.get("locationInv") != null) {
            curEditLocationInv =
                activity.intent.extras!!.get("locationInv") as FuInvLocationInventory
            textLocCode.set(curEditLocationInv.locCode)
            textOwnerCode.set(curEditLocationInv.ownerCode)
            textItemName.set(curEditLocationInv.itemName)
            textItemCode.set(curEditLocationInv.custItemCode)

            text69Code.set(
                LotAttUnit.formatWhBarcode69(
                    curEditLocationInv.whCsBarcode69,
                    curEditLocationInv.whBarcode69
                )
            )

            // 可用库存数 =  对应库存表的在库数-分配数-在途数-冻结数
            val canUserNum =
                curEditLocationInv.onHandQty - curEditLocationInv.allocatedQty -
                        curEditLocationInv.inTransitQty - curEditLocationInv.lockedQty

            textCanUseNum.set(AppUtils.getBigDecimalValueStr(canUserNum))

            if (activity.textWatcher == null) {
                activity.textWatcher =
                    FilterDigitTextWatcher(activity.binding.etUpdateNum, 0, true) {
                        if ("只能输入正整数" == it) {
                            activity.binding.etUpdateNum.setText(
                                activity.binding.etUpdateNum.text.toString().replace(".", "")
                            )
                            if (activity.binding.etUpdateNum.text!!.isNotEmpty()) {
                                activity.binding.etUpdateNum.setSelection(activity.binding.etUpdateNum.text!!.length)
                            }
                        }
                        AppUtils.showToast(activity, it)
                    }
            }

            val beans = curEditLocationInv.packageRelationList.map { it.cdprDesc }
            activity.binding.spinnerStatus.setItems(beans)
            if (beans.isNotEmpty()) {
                activity.binding.spinnerStatus.selectedIndex = 0
                cdprQuantity =
                    curEditLocationInv.packageRelationList.getOrNull(0)?.cdprQuantity ?: BigDecimal.ONE
                cdprUnit = curEditLocationInv.packageRelationList.getOrNull(0)?.cdprUnit ?: ""
            }

            activity.binding.spinnerStatus.setOnItemSelectedListener { _, position, _, _ ->
                cdprQuantity = curEditLocationInv.packageRelationList[position].cdprQuantity
                cdprUnit = curEditLocationInv.packageRelationList[position].cdprUnit
                textUnitAmount.set("")
                textAmount.set("0")
                setWatcher()//设置是否可输入小数点
            }

            setWatcher()//设置是否可输入小数点

            textEaUnit.set(curEditLocationInv.packageRelationList.find { it.cdprIsMain == "Y" }?.cdprDesc)

            if (!curEditLocationInv.lotAtt01.isNullOrBlank()) {
                //showEditLot1.set(true)
                showTextLot1.set(true)
                textLot1.set(formatDate(curEditLocationInv.lotAtt01))   // 只显示年月日并且不要横杠
                newLot1.set(formatDate(curEditLocationInv.lotAtt01))
            }

            if (!curEditLocationInv.lotAtt02.isNullOrBlank()) {
                //showEditLot2.set(true)
                showTextLot2.set(true)
                textLot2.set(formatDate(curEditLocationInv.lotAtt02))
                newLot2.set(formatDate(curEditLocationInv.lotAtt02))
            }

            if (!curEditLocationInv.lotAtt03.isNullOrBlank()) {
                //showEditLot3.set(true)
                showTextLot3.set(true)
                textLot3.set(formatDate(curEditLocationInv.lotAtt03))
                newLot3.set(formatDate(curEditLocationInv.lotAtt03))
            }

            // 状态 (正品良品)
            if (!curEditLocationInv.lotAtt04.isNullOrBlank()) {
                if (DCUtils.lot4TypeC2N.get(curEditLocationInv.lotAtt04) != null) {
                    textLot4.set(DCUtils.lot4TypeC2N.get(curEditLocationInv.lotAtt04).toString())
                }
            }

            if (!curEditLocationInv.lotAtt05.isNullOrBlank()) {
                //showEditLot5.set(true)
                showTextLot5.set(true)
                textLot5.set(curEditLocationInv.lotAtt05)
                newLot5.set(curEditLocationInv.lotAtt05)
            }

            if (!curEditLocationInv.lotAtt06.isNullOrBlank()) {
                //showEditLot6.set(true)
                showTextLot6.set(true)
                textLot6.set(curEditLocationInv.lotAtt06)
                newLot6.set(curEditLocationInv.lotAtt06)
            }

            if (!curEditLocationInv.lotAtt07.isNullOrBlank()) {
                //showEditLot7.set(true)
                showTextLot7.set(true)
                textLot7.set(curEditLocationInv.lotAtt07)
                newLot7.set(curEditLocationInv.lotAtt07)
            }

            if (!curEditLocationInv.lotAtt08.isNullOrBlank()) {
                //showEditLot8.set(true)
                showTextLot8.set(true)
                textLot8.set(curEditLocationInv.lotAtt08)
                newLot8.set(curEditLocationInv.lotAtt08)
            }

            if (!curEditLocationInv.lotAtt09.isNullOrBlank()) {
                //showEditLot9.set(true)
                showTextLot9.set(true)
                textLot9.set(curEditLocationInv.lotAtt09)
                newLot9.set(curEditLocationInv.lotAtt09)
            }

            if (!curEditLocationInv.lotAtt10.isNullOrBlank()) {
                //showEditLot10.set(true)
                showTextLot10.set(true)
                textLot10.set(curEditLocationInv.lotAtt10)
                newLot10.set(curEditLocationInv.lotAtt10)
            }

            if (!curEditLocationInv.lotAtt11.isNullOrBlank()) {
                //showEditLot11.set(true)
                showTextLot11.set(true)
                textLot11.set(curEditLocationInv.lotAtt11)
                newLot11.set(curEditLocationInv.lotAtt11)
            }

            if (!curEditLocationInv.lotAtt12.isNullOrBlank()) {
                //showEditLot12.set(true)
                showTextLot12.set(true)
                textLot12.set(curEditLocationInv.lotAtt12)
                newLot12.set(curEditLocationInv.lotAtt12)
            }

            if (!curEditLocationInv.itemCode.isNullOrBlank()) {
                loadServerLot(curEditLocationInv.itemCode)
            }

        }

    }

    val inputTextChange = object : ViewBindingAdapter.TextChangedListener {

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            if (!s.isNullOrEmpty()) {
                try {
                    val inputNumStr = AppUtils.getBigDecimalValue(s.toString())
                    if ((inputNumStr.multiply(cdprQuantity)).compareTo(
                            AppUtils.getBigDecimalValue(
                                textCanUseNum.get()
                            )
                        ) == 1
                    ) {
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, "调整数量不能大于当前可用库存数量")
                        textUnitAmount.set("")
                        textAmount.set("0")
                    } else {
                        textAmount.set(
                            AppUtils.getBigDecimalValueStr(
                                inputNumStr.multiply(
                                    cdprQuantity
                                )
                            )
                        )
                    }
                } catch (e: NumberFormatException) {
                    textUnitAmount.set("")
                    textAmount.set("0")
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "数量格式错误")
                }
            } else {
                textAmount.set("0")
            }
        }
    }

    // 把 类似 2021-10-27 15:58:28 的字符串 转成   20211027
    private fun formatYymmdd(str: String): String {
        var result = ""
        if (str.split(" ").isNotEmpty()) {
            result = str.split(" ")[0]
        }
        return result.replace("-", "")
    }

    private fun formatDate(str: String): String {
        var result = ""
        if (str.split(" ").isNotEmpty()) {
            result = str.split(" ")[0]
        }
        return result
    }

    // 把 类似 20211027 的字符串 转成   2021-10-27 00:00:00 (方便传给后端)
    private fun formatYymmddHhmmss(str: String): String {
        val df = SimpleDateFormat("yyyyMMdd")
        val date = df.parse(str)
        val df2 = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        return df2.format(date)
    }

    // 检查调整数
    fun onCheckUpdateNum(): Boolean {

        try {

            if (textAmount.get().isNullOrBlank()) {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "未填写调整数")
                return false
            } else if (AppUtils.getBigDecimalValue(textAmount.get()!!)
                    .compareTo(curEditLocationInv.usableQty) == 1
            ) {
                textAmount.set(AppUtils.getBigDecimalValueStr(curEditLocationInv.usableQty))
                ToastUtils.getInstance().showErrorToastWithSound(activity, "调整数量不能大于当前可用库存数量")
                return false
            } else if (AppUtils.getBigDecimalValue(textAmount.get()!!) <= BigDecimal.ZERO) {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "调整数量应大于0")
                return false
            }

        } catch (e: NumberFormatException) {
            textAmount.set("")
            // 避免恶意用户或测试输入稀奇古怪的数值
            ToastUtils.getInstance().showErrorToastWithSound(activity, "调整数填写错误,请重新填写")
            return false
        }



        return true
    }


    /*
    fun selectDateTime(type: Int) {

          //弹出日期时间选择
          var picker = buildDlgSelectDateTime(activity, OnTimeSelectListener { date, v ->
              val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
              var strDate = sdf.format(date)
              var timestamp = date.time


              if (type == 1) {
                  newLot1.set(strDate)
              } else if (type == 2) {
                  newLot2.set(strDate)
              } else if (type == 3) {
                  newLot3.set(strDate)
              }

          })
          picker.show()


      }*/


    fun submitEdit() {
        isEnableClick.set(false)
        //检查调整数
        if (!onCheckUpdateNum()) {
            isEnableClick.set(true)
            return
        }

        if (textEditReason.get().isNullOrBlank()) {
            isEnableClick.set(true)
            ToastUtils.getInstance().showErrorToastWithSound(activity, "未填写调整原因")
            return
        }

        if (textEditReason.get()!!.length > 100) {
            isEnableClick.set(true)
            ToastUtils.getInstance().showErrorToastWithSound(activity, "调整原因超过最大长度100")
            return
        }


        val editReason = textEditReason.get()  //修改原因
        val ammount = AppUtils.getBigDecimalValue(textAmount.get())
        val submitLocationInv = SubmitInvLocationInventory()

        submitLocationInv.amount = ammount
        submitLocationInv.lotNum = curEditLocationInv.lotNum
        submitLocationInv.reason = editReason
        submitLocationInv.whCode = Constants.whInfo?.whCode
        submitLocationInv.whName = Constants.whInfo?.cdwhName
        submitLocationInv.transferType = "RF"
        submitLocationInv.ownerCode = curEditLocationInv.ownerCode
        submitLocationInv.ownerName = curEditLocationInv.ownerName
        submitLocationInv.locCode = curEditLocationInv.locCode
        submitLocationInv.traceId = curEditLocationInv.traceId
        submitLocationInv.toTraceId = curEditLocationInv.traceId
        submitLocationInv.itemCode = curEditLocationInv.itemCode

        submitLocationInv.fmInvLot = SubmitInvLocationInventory.FmInvLotDTO()
        submitLocationInv.fmInvLot.lotNum = curEditLocationInv.lotNum
        submitLocationInv.fmInvLot.lotAtt01 = curEditLocationInv.lotAtt01
        submitLocationInv.fmInvLot.lotAtt02 = curEditLocationInv.lotAtt02
        submitLocationInv.fmInvLot.lotAtt03 = curEditLocationInv.lotAtt03
        submitLocationInv.fmInvLot.lotAtt04 = curEditLocationInv.lotAtt04
        submitLocationInv.fmInvLot.lotAtt05 = curEditLocationInv.lotAtt05
        submitLocationInv.fmInvLot.lotAtt06 = curEditLocationInv.lotAtt06
        submitLocationInv.fmInvLot.lotAtt07 = curEditLocationInv.lotAtt07
        submitLocationInv.fmInvLot.lotAtt08 = curEditLocationInv.lotAtt08
        submitLocationInv.fmInvLot.lotAtt09 = curEditLocationInv.lotAtt09
        submitLocationInv.fmInvLot.lotAtt10 = curEditLocationInv.lotAtt10
        submitLocationInv.fmInvLot.lotAtt11 = curEditLocationInv.lotAtt11
        submitLocationInv.fmInvLot.lotAtt12 = curEditLocationInv.lotAtt12

        submitLocationInv.toInvLot = SubmitInvLocationInventory.ToInvLotDTO()

        if (showEditLot1.get() == true) {
            if (!newLot1.get().isNullOrBlank()) { // 生产日期
                if (!valiDateTimeWithLongFormat(formatYymmdd(newLot1.get().toString()))) {
                    isEnableClick.set(true)
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "生产日期格式错误")
                    return
                } else {
                    val newLot1Value = formatYymmddHhmmss(formatYymmdd(newLot1.get().toString()))
                    submitLocationInv.toInvLot.lotAtt01 = newLot1Value
                }
            } else {
                isEnableClick.set(true)
                if (inputControl1.get() == true) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "目标属性[生产日期]不能为空")
                    return
                }
            }
        }

        if (showEditLot2.get() == true) {
            if (!newLot2.get().isNullOrBlank()) { // 失效日期
                if (!valiDateTimeWithLongFormat(formatYymmdd(newLot2.get().toString()))) {
                    isEnableClick.set(true)
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "失效日期格式错误")
                    return
                } else {
                    val newLot2Value = formatYymmddHhmmss(formatYymmdd(newLot2.get().toString()))
                    submitLocationInv.toInvLot.lotAtt02 = newLot2Value
                }
            } else {
                isEnableClick.set(true)
                if (inputControl2.get() == true) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "目标属性[失效日期]不能为空")
                    return
                }
            }
        }


        if (showEditLot3.get() == true) {
            if (!newLot3.get().isNullOrBlank()) { // 入库时间
                if (!valiDateTimeWithLongFormat(formatYymmdd(newLot3.get().toString()))) {
                    isEnableClick.set(true)
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "入库时间日期格式错误")
                    return
                } else {
                    // 当前日期
                    val df = SimpleDateFormat("yyyyMMdd")
                    val curDateInt = df.format(Date()).toString().toInt()
                    val lot3Int = formatYymmdd(newLot3.get().toString()).toInt()
                    if (lot3Int > curDateInt) {
                        isEnableClick.set(true)
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, "入库日期不能大于当前日期(" + curDateInt + ")")
                        return
                    }
                    val newLot3Value = formatYymmddHhmmss(formatYymmdd(newLot3.get().toString()))
                    submitLocationInv.toInvLot.lotAtt03 = newLot3Value
                }
            } else {
                isEnableClick.set(true)
                if (inputControl3.get() == true) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "目标属性[入库时间]不能为空")
                    return
                }
            }
        }


        //校验生产日期lot1 应早于 失效日期lot2
        if (showEditLot2.get() == true && showEditLot1.get() == true && !newLot2.get()
                .isNullOrBlank() && !newLot1.get().isNullOrBlank()
        ) {
            val df = SimpleDateFormat("yyyyMMdd")
            val date1 = df.parse(formatYymmdd(newLot1.get().toString()))
            val date2 = df.parse(formatYymmdd(newLot2.get().toString()))
            if (date1.after(date2)) {
                isEnableClick.set(true)
                ToastUtils.getInstance().showErrorToastWithSound(activity, "生产日期不能大于失效日期")
                return
            }
        }

        //校验生产日期lot1要早于入库日期lot3
        if (showEditLot1.get() == true && showEditLot3.get() == true && !newLot1.get()
                .isNullOrBlank() && !newLot3.get().isNullOrBlank()
        ) {
            val df = SimpleDateFormat("yyyyMMdd")
            val date1 = df.parse(formatYymmdd(newLot1.get().toString())) //生产日期
            val date3 = df.parse(formatYymmdd(newLot3.get().toString())) //入库日期
            if (date1.after(date3)) {
                isEnableClick.set(true)
                ToastUtils.getInstance().showErrorToastWithSound(activity, "生产日期不能大于入库日期")
                return
            }
        }


        //属性4不能修改，直接按原值上传
        if (!curEditLocationInv.lotAtt04.isNullOrBlank()) {
            submitLocationInv.toInvLot.lotAtt04 = curEditLocationInv.lotAtt04.trim()
        }


        if (showEditLot5.get() == true) {
            if (newLot5.get().isNullOrBlank()) {
                isEnableClick.set(true)
                if (inputControl5.get() == true) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "目标属性[自定义属性5]不能为空")
                    return
                }
            }
            submitLocationInv.toInvLot.lotAtt05 = newLot5.get()?.trim()  // 批次
        }


        if (showEditLot6.get() == true) {
            if (newLot6.get().isNullOrBlank()) {
                isEnableClick.set(true)
                if (inputControl6.get() == true) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "目标属性[自定义属性6]不能为空")
                    return
                }
            }
            submitLocationInv.toInvLot.lotAtt06 = newLot6.get()?.trim()  // 自定义属性
        }


        if (showEditLot7.get() == true) {
            if (newLot7.get().isNullOrBlank()) {
                isEnableClick.set(true)
                if (inputControl7.get() == true) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "目标属性[自定义属性7]不能为空")
                    return
                }
            }
            submitLocationInv.toInvLot.lotAtt07 = newLot7.get()?.trim()  // 自定义属性
        }


        if (showEditLot8.get() == true) {
            if (newLot8.get().isNullOrBlank()) {
                isEnableClick.set(true)
                if (inputControl8.get() == true) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "目标属性[自定义属性8]不能为空")
                    return
                }
            }
            submitLocationInv.toInvLot.lotAtt08 = newLot8.get()?.trim()  // 自定义属性
        }


        if (showEditLot9.get() == true) {
            if (newLot9.get().isNullOrBlank()) {
                isEnableClick.set(true)
                if (inputControl9.get() == true) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "目标属性[自定义属性9]不能为空")
                    return
                }
            }
            submitLocationInv.toInvLot.lotAtt09 = newLot9.get()?.trim()  // 自定义属性
        }


        if (showEditLot10.get() == true) {
            if (newLot10.get().isNullOrBlank()) {
                isEnableClick.set(true)
                if (inputControl10.get() == true) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "目标属性[自定义属性10]不能为空")
                    return
                }
            }
            submitLocationInv.toInvLot.lotAtt10 = newLot10.get()?.trim()  // 自定义属性
        }


        if (showEditLot11.get() == true) {
            if (newLot11.get().isNullOrBlank()) {
                isEnableClick.set(true)
                if (inputControl11.get() == true) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "目标属性[自定义属性11]不能为空")
                    return
                }
            }
            submitLocationInv.toInvLot.lotAtt11 = newLot11.get()?.trim()  // 自定义属性
        }


        if (showEditLot12.get() == true) {
            if (newLot12.get().isNullOrBlank()) {
                isEnableClick.set(true)
                if (inputControl12.get() == true) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "目标属性[自定义属性12]不能为空")
                    return
                }
            }
            submitLocationInv.toInvLot.lotAtt12 = newLot12.get()?.trim()  // 自定义属性
        }


        if (checkChangeCount() == 0) {
            isEnableClick.set(true)
            ToastUtils.getInstance().showErrorToastWithSound(activity, "属性未作修改，请修改")
            return
        }

        //不能取消，直到超时或成功
        activity.waitingDialogHelp.showDialogWithMsg(activity, "调整中...", false)

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(submitLocationInv)
        )

        RetrofitHelper.getWareManageAPI()
            .submitUpdateLocationInventory(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    isEnableClick.set(true)
                    activity.waitingDialogHelp.hidenDialog()
                    if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                        ToastUtilsCare.toastBigCenter(activity, "调整成功", Toast.LENGTH_SHORT)

                        activity.finish()
                    } else {
                        AlertDialogUtil.showOnlyOkDialog(
                        activity, "调整成功", {
                            activity.finish()
                        }, "确定"
                    )
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isEnableClick.set(true)
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })


    }


    //根据 itemCode 查询商品批次属性信息
    private fun loadServerLot(itemCode: String) {

        val param = mutableListOf<String>()
        param.add(itemCode)

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialogWithMsg(activity, "加载中...", false)

        RetrofitHelper.getAppAPI()
            .getCdcmLotDetails(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<RespCdWhLotDetail>>(activity) {
                override fun success(lotList: MutableList<RespCdWhLotDetail>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    lotList?.get(0)?.details?.forEach {
                        when (it.lotAtt) {
                            "LOT_ATT01" -> {
                                showEditLot1.set(true)
                                if (it.inputControl.toUpperCase() == "R") {
                                    inputControl1.set(true)
                                }
                            }
                            "LOT_ATT02" -> {
                                showEditLot2.set(true)
                                if (it.inputControl.toUpperCase() == "R") {
                                    inputControl2.set(true)
                                }
                            }
                            "LOT_ATT03" -> {
                                showEditLot3.set(true)
                                if (it.inputControl.toUpperCase() == "R") {
                                    inputControl3.set(true)
                                }
                            }
                            "LOT_ATT05" -> {
                                showEditLot5.set(true)
                                if (it.inputControl.toUpperCase() == "R") {
                                    inputControl5.set(true)
                                }
                            }
                            "LOT_ATT06" -> {
                                showEditLot6.set(true)
                                if (it.inputControl.toUpperCase() == "R") {
                                    inputControl6.set(true)
                                }
                            }
                            "LOT_ATT07" -> {
                                showEditLot7.set(true)
                                if (it.inputControl.toUpperCase() == "R") {
                                    inputControl7.set(true)
                                }
                            }
                            "LOT_ATT08" -> {
                                showEditLot8.set(true)
                                if (it.inputControl.toUpperCase() == "R") {
                                    inputControl8.set(true)
                                }
                            }
                            "LOT_ATT09" -> {
                                showEditLot9.set(true)
                                if (it.inputControl.toUpperCase() == "R") {
                                    inputControl9.set(true)
                                }
                            }
                            "LOT_ATT10" -> {
                                showEditLot10.set(true)
                                if (it.inputControl.toUpperCase() == "R") {
                                    inputControl10.set(true)
                                }
                            }
                            "LOT_ATT11" -> {
                                showEditLot11.set(true)
                                if (it.inputControl.toUpperCase() == "R") {
                                    inputControl11.set(true)
                                }
                            }
                            "LOT_ATT12" -> {
                                showEditLot12.set(true)
                                if (it.inputControl.toUpperCase() == "R") {
                                    inputControl12.set(true)
                                }
                            }
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    // 检查是不是所有的批属性都没有被修改
    fun checkChangeCount(): Int {
        var changeCount = 0
        if (showEditLot12.get() == true && !textLot12.get().equals(newLot12.get())) {
            changeCount++
        }
        if (showEditLot11.get() == true && !textLot11.get().equals(newLot11.get())) {
            changeCount++
        }
        if (showEditLot10.get() == true && !textLot10.get().equals(newLot10.get())) {
            changeCount++
        }
        if (showEditLot9.get() == true && !textLot9.get().equals(newLot9.get())) {
            changeCount++
        }
        if (showEditLot8.get() == true && !textLot8.get().equals(newLot8.get())) {
            changeCount++
        }
        if (showEditLot7.get() == true && !textLot7.get().equals(newLot7.get())) {
            changeCount++
        }
        if (showEditLot6.get() == true && !textLot6.get().equals(newLot6.get())) {
            changeCount++
        }
        if (showEditLot5.get() == true && !textLot5.get().equals(newLot5.get())) {
            changeCount++
        }
        if (showEditLot3.get() == true && !textLot3.get().equals(newLot3.get())) {
            changeCount++
        }
        if (showEditLot2.get() == true && !textLot2.get().equals(newLot2.get())) {
            changeCount++
        }
        if (showEditLot1.get() == true && !textLot1.get().equals(newLot1.get())) {
            changeCount++
        }

        return changeCount
    }

    //后退键
    val back = View.OnClickListener {
        activity.finish()
    }

    //验证字符串是不是 yyyyMMdd
    private fun valiDateTimeWithLongFormat(dateStr: String): Boolean {
        /*
            2022年4月20日 星期三 下面这个正则有问题  换另外一种方法验证
            val format =
                "([\\d]{4}(((0[13578]|1[02])((0[1-9])|([12][0-9])|(3[01])))|(((0[469])|11)((0[1-9])|([12][1-9])|30))|(02((0[1-9])|(1[0-9])|(2[1-8])))))|((((([02468][048])|([13579][26]))00)|([0-9]{2}(([02468][048])|([13579][26]))))(((0[13578]|1[02])((0[1-9])|([12][0-9])|(3[01])))|(((0[469])|11)((0[1-9])|([12][1-9])|30))|(02((0[1-9])|(1[0-9])|(2[1-9])))))"
            val pattern = Pattern.compile(format)
            val matcher = pattern.matcher(timeStr)
            if (matcher.matches()) {
                return true
            }
            return false
        */
        return AppUtils.checkDateStr(dateStr)
    }

    private fun setWatcher() {
        activity.binding.etUpdateNum.removeTextChangedListener(activity.textWatcher)
        when (curEditLocationInv.isDecimal) {
            0 -> {
                activity.textWatcher?.limitDecimalPlaces = 0
            }
            else -> {
                if ("EA" == cdprUnit) {
                    activity.textWatcher?.limitDecimalPlaces = 4
                } else {
                    activity.textWatcher?.limitDecimalPlaces = 0
                }
            }
        }
        activity.binding.etUpdateNum.addTextChangedListener(activity.textWatcher)
    }

    fun showTimePick(tag: Int) {
        val pvTime = TimePickerView.Builder(
            activity,
            TimePickerView.OnTimeSelectListener { date2, _ ->
                when (tag) {
                    1 -> {
                        newLot1.set(formatDate(format.format(date2)))
                        calculateDate(newLot1.get())
                    }
                    2 -> {
                        newLot2.set(formatDate(format.format(date2)))
                        calculateDateAfter(newLot2.get())
                    }
                    3 -> newLot3.set(formatDate(format.format(date2)))
                }
            })
        var title: String? = null
        when (tag) {
            1 -> title = "生产日期"
            2 -> title = "失效日期"
            3 -> title = "入库日期"
        }
        pvTime.setType(TimePickerView.Type.YEAR_MONTH_DAY)//默认全部显示
            .setCancelText("取消")//取消按钮文字
            .setSubmitText("确定")//确认按钮文字
            .setContentSize(20)//滚轮文字大小
            .setTitleSize(20)//标题文字大小
            .setOutSideCancelable(true)//点击屏幕，点在控件外部范围时，是否取消显示
            .setTitleText(title)
            .setTextColorCenter(Color.BLACK)//设置选中项的颜色
            .setTitleColor(Color.BLACK)//标题文字颜色
            .setSubmitColor(ContextCompat.getColor(activity, R.color.colorBlue))//确定按钮文字颜色
            .setCancelColor(ContextCompat.getColor(activity, R.color.colorOrange))//取消按钮文字颜色
            .isDialog(true)
            .setDate(Calendar.getInstance())
            .isCenterLabel(false) //是否只显示中间选中项的label文字，false则每项item全部都带有label。
            .build()

        val startDate = Calendar.getInstance()
        startDate.set(1900, 10, 30)
        val endDate = Calendar.getInstance()
        endDate.add(Calendar.YEAR, 100)
        pvTime.setRangDate(startDate, endDate)

        pvTime.build().show()
    }

    private fun calculateDate(lotAtt01: String?) {
        if (curEditLocationInv?.periodOfValidity.isNull()) {
            return
        }
        if (curEditLocationInv?.isValidity?.toLowerCase(Locale.ROOT) == "y") {
            if (!curEditLocationInv.validityUnit.isNullOrEmpty()) {
                if (!lotAtt01.isNullOrEmpty()) {
                    val cale = Calendar.getInstance()
                    cale.time = format.parse(lotAtt01)
                    when {
                        curEditLocationInv.validityUnit?.toLowerCase(Locale.ROOT) == "y" -> {
                            cale.add(Calendar.YEAR, curEditLocationInv.periodOfValidity.toInt())
                        }
                        curEditLocationInv.validityUnit?.toLowerCase(Locale.ROOT) == "m" -> {
                            cale.add(
                                Calendar.MONTH,
                                AppUtils.getBigDecimalValue(curEditLocationInv.periodOfValidity)
                                    .toInt()
                            )
                        }
                        curEditLocationInv.validityUnit?.toLowerCase(Locale.ROOT) == "w" -> {
                            cale.add(
                                Calendar.WEEK_OF_YEAR,
                                curEditLocationInv.periodOfValidity.toInt()
                            )
                        }
                        curEditLocationInv.validityUnit?.toLowerCase(Locale.ROOT) == "d" -> {
                            cale.add(Calendar.DATE, curEditLocationInv.periodOfValidity.toInt())
                        }
                    }
                    newLot2.set(format.format(cale.time))
                }
            }
        }
    }

    private fun calculateDateAfter(lotAtt01: String?) {
        if (curEditLocationInv?.periodOfValidity.isNull()) {
            return
        }
        if (curEditLocationInv?.isValidity?.toLowerCase(Locale.ROOT) == "y") {
            if (!curEditLocationInv.validityUnit.isNullOrEmpty()) {
                if (!lotAtt01.isNullOrEmpty()) {
                    val cale = Calendar.getInstance()
                    cale.time = format.parse(lotAtt01)
                    when {
                        curEditLocationInv.validityUnit?.toLowerCase(Locale.ROOT) == "y" -> {
                            cale.add(Calendar.YEAR, -curEditLocationInv.periodOfValidity.toInt())
                        }
                        curEditLocationInv.validityUnit?.toLowerCase(Locale.ROOT) == "m" -> {
                            cale.add(
                                Calendar.MONTH,
                                -AppUtils.getBigDecimalValue(curEditLocationInv.periodOfValidity)
                                    .toInt()
                            )
                        }
                        curEditLocationInv.validityUnit?.toLowerCase(Locale.ROOT) == "w" -> {
                            cale.add(
                                Calendar.WEEK_OF_YEAR,
                                -curEditLocationInv.periodOfValidity.toInt()
                            )
                        }
                        curEditLocationInv.validityUnit?.toLowerCase(Locale.ROOT) == "d" -> {
                            cale.add(Calendar.DATE, -curEditLocationInv.periodOfValidity.toInt())
                        }
                    }
                    newLot1.set(format.format(cale.time))
                }
            }
        }
    }

}