package com.midea.prestorage.function.inv

import CheckUtil
import android.app.Application
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.ReqQuerySort
import com.midea.prestorage.beans.net.ReqSortConfirm
import com.midea.prestorage.beans.net.ReqSortItemList
import com.midea.prestorage.beans.net.SortInfoItem
import com.midea.prestorage.event.LiveEvent
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.util.*

class SortGoodsVM(application: Application) : BaseViewModel(application) {

    val isNoData = ObservableBoolean(true)
    var orderNo = ObservableField("")
    var isStartScan = MutableLiveData(false)
    var showDatas = MutableLiveData<MutableList<SortInfoItem>>()
    val shippingLoc = ObservableField<String>("")
    val gridNumber = ObservableField<String>("")
    val itemQty = ObservableField<String>("")
    val totalQtyConvert = ObservableField<String>("")
    val shipToAttentionTo = ObservableField<String>("")
    var shippingLocList = MutableLiveData<MutableList<String>>()
    val isNoBottom = ObservableBoolean(true)
    val toDetailEvent = LiveEvent<Unit>()
    val nextEvent = LiveEvent<Unit>()
    val comfirmSortEvent = LiveEvent<Unit>()
    var curShippingLoc = 0 //当前的集货位

    override fun init() {
    }

    fun onEnterOrderNo() {
        if (CheckUtil.isFastDoubleClick()) {
            if (orderNo.get().toString().trim().isNullOrEmpty()) {
                showNotification("拣货箱号不能为空", false)
                return
            }
            initList(orderNo.get().toString().trim(), "")
        }
    }

    fun initList(setCode: String, loc: String) {

        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getOutStorageAPI()
                    .querySortList(ReqQuerySort(containerCode = setCode, shippingLoc = loc))
            }

            if (result.code == 0L) {
                if (result.data == null || result.data!!.shippingLocList.isNullOrEmpty()) {
                    isNoData.set(true)
                    isNoBottom.set(true)
                } else {
                    isNoData.set(false)
                    isNoBottom.set(false)
                    if (loc.isNullOrEmpty()) { //只有输入框回车的才需要去更新集货位显示
                        result.data?.shippingLocList?.let {
                            shippingLocList.value = it.toMutableList()
                        }
                    }
                    shippingLoc.set(result.data?.shippingLoc ?: "")
                    gridNumber.set(result.data?.gridNumber ?: "")
                    itemQty.set(AppUtils.getBigDecimalValueStrNull(result.data?.itemQty))
                    totalQtyConvert.set(result.data?.totalQtyConvert ?: "")
                    shipToAttentionTo.set(result.data?.shipToAttentionTo ?: "")
                    result.data!!.itemList?.forEachIndexed { index, it ->
                        it.index = index + 1
                        it.isSelected = true
                        if(AppUtils.isZero(it.packagePara)) {
                            it.isShowCs = false
                            it.csNum = BigDecimal.ZERO
                            it.eaNum = it.waitSortQty
                        }else {
                            it.isShowCs = true
                            val quotient = (it.waitSortQty ?: BigDecimal.ZERO).divideToIntegralValue(
                                it.packagePara ?: BigDecimal.ONE
                            )
                            if (quotient > BigDecimal.ZERO) {
                                it.csNum = quotient
                                it.eaNum = it.waitSortQty?.remainder(it.packagePara ?: BigDecimal.ONE)
                                    ?: BigDecimal.ZERO
                            } else {
                                it.csNum = BigDecimal.ZERO
                                it.eaNum = it.waitSortQty
                            }
                        }
                    }
                    result.data?.itemList?.let {
                        showDatas.value = it.toMutableList()
                    }
                }
            } else {
                val emptyLocList = mutableListOf<String>()
                shippingLocList.value = emptyLocList
                val sortInfoItem = mutableListOf<SortInfoItem>()
                showDatas.value = sortInfoItem
                isNoData.set(true)
                isNoBottom.set(true)
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    fun sortConfirm(results: MutableList<SortInfoItem>) {

        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

            var list = mutableListOf<ReqSortItemList>()
            results.forEach {
                var item = ReqSortItemList()
                item.itemCode = it.itemCode
                item.custItemCode = it.custItemCode
                item.packagePara = it.packagePara
                item.csSortQty = it.csNum
                item.eaSortQty = it.eaNum
                list.add(item)
            }
            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getOutStorageAPI().sortConfirm(
                    ReqSortConfirm(
                        containerCode = orderNo.get().toString().trim(),
                        shippingLoc = shippingLoc.get(),
                        operatorCode = Constants.userInfo?.name,
                        operator = Constants.userInfo?.userName,
                        operateTime = format.format(Date()),
                        itemList = list
                    )
                )
            }

            if (result.code == 0L) {
                initList(orderNo.get().toString().trim(), shippingLoc.get().toString())
            } else {
                result.msg?.let { showNotification(it, false) }
                if (result.code == 606015L) {
                    initList(orderNo.get().toString().trim(), shippingLoc.get().toString())
                }
            }
        }
    }

    fun startScan() {
        isStartScan.value = true
    }

    //点击删除后清空容器号/拣货箱号输入框，同时清空列表内容
    fun clearOrderNo() {
        orderNo.set("")
        val emptyLocList = mutableListOf<String>()
        shippingLocList.value = emptyLocList
        val sortInfoItem = mutableListOf<SortInfoItem>()
        showDatas.value = sortInfoItem
        isNoData.set(true)
        isNoBottom.set(true)
    }

    fun scanResult() {
        onEnterOrderNo()
    }

    //下一个集货位
    fun nextShippingLoc() {
        if (CheckUtil.isFastDoubleClick()) {
            nextEvent.value = Unit
        }
    }

    //容器剩余数量
    fun containerSurplus() {
        if (CheckUtil.isFastDoubleClick()) {
            toDetailEvent.value = Unit
        }
    }

    //确认分货
    fun comfirmSort() {
        if (CheckUtil.isFastDoubleClick()) {
            comfirmSortEvent.value = Unit
        }
    }
}