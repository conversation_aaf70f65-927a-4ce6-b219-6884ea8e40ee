package com.midea.prestorage.function.containerpick

import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import com.midea.prestorage.widgets.MyGridView
import com.midea.prestoragesaas.databinding.ActivityCombinedPickConfirmBinding
import com.midea.prestoragesaas.databinding.ActivityCombinedPickConfirmCareBinding

sealed class ActivityCombinedPickConfirmUnionBinding {
    abstract var vm: CombinedPickConfirmVM?
    abstract val gridNumber: MyGridView
    abstract val edEa: EditText
    abstract val edEaSecond: EditText
    abstract val edCs: EditText
    abstract val edIp: EditText
    abstract val llIp: LinearLayout
    abstract val llEa: LinearLayout
    abstract val llEaSecond: LinearLayout
    abstract val llCs: LinearLayout
    abstract val llSecondPackage: LinearLayout
    abstract val tvCarcode69: TextView
    abstract val tvNotification: TextView

    class V2(val binding: ActivityCombinedPickConfirmCareBinding) :
        ActivityCombinedPickConfirmUnionBinding() {
        override var vm: CombinedPickConfirmVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val gridNumber = binding.gridNumber
        override val edEa = binding.edEa
        override val edEaSecond = binding.edEaSecond
        override val edCs = binding.edCs
        override val edIp = binding.edIp
        override val llIp = binding.llIp
        override val llEa = binding.llEa
        override val llEaSecond = binding.llEaSecond
        override val llCs = binding.llCs
        override val llSecondPackage = binding.llSecondPackage
        override val tvCarcode69 = binding.tvCarcode69
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityCombinedPickConfirmBinding) :
        ActivityCombinedPickConfirmUnionBinding() {
        override var vm: CombinedPickConfirmVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val gridNumber = binding.gridNumber
        override val edEa = binding.edEa
        override val edEaSecond = binding.edEaSecond
        override val edCs = binding.edCs
        override val edIp = binding.edIp
        override val llIp = binding.llIp
        override val llEa = binding.llEa
        override val llEaSecond = binding.llEaSecond
        override val llCs = binding.llCs
        override val llSecondPackage = binding.llSecondPackage
        override val tvCarcode69 = binding.tvCarcode69
        override val tvNotification = binding.tvNotification
    }
}
