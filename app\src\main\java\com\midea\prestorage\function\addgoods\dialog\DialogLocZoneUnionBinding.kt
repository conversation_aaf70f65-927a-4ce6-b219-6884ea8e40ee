package com.midea.prestorage.function.addgoods.dialog

import androidx.recyclerview.widget.RecyclerView
import com.midea.prestoragesaas.databinding.DialogLocZoneBinding
import com.midea.prestoragesaas.databinding.DialogLocZoneCareBinding

sealed class DialogLocZoneUnionBinding{
    abstract var vm: LocZoneDialogVM?
    abstract val recycle: RecyclerView

    class V2(val binding: DialogLocZoneCareBinding) : DialogLocZoneUnionBinding() {
        override var vm: LocZoneDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val recycle = binding.recycle
    }

    class V1(val binding: DialogLocZoneBinding) : DialogLocZoneUnionBinding() {
        override var vm: LocZoneDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val recycle = binding.recycle
    }
}
