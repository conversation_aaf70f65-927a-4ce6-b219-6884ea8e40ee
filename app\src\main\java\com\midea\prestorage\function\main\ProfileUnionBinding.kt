package com.midea.prestorage.function.main

import android.widget.Switch
import android.widget.TextView
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.databinding.ActivityProfileBinding
import com.midea.prestoragesaas.databinding.ActivityProfileGhBinding

sealed class ProfileUnionBinding {

    abstract var vm: ProfileVM?
    abstract val mSwitch: Switch
    abstract val spinnerStartHour: MaterialSpinner
    abstract val spinnerEndHour: MaterialSpinner
    abstract val spinnerStartDay: MaterialSpinner
    abstract val spinnerEndDay: MaterialSpinner
    abstract val spinnerPrinter: MaterialSpinner
    abstract val spinnerIsAutoConnect: MaterialSpinner
    abstract val spinnerPickMode: MaterialSpinner
    abstract val tvNotification: TextView

    class V2(val binding: ActivityProfileGhBinding) : ProfileUnionBinding() {
        override var vm: ProfileVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val mSwitch = binding.mSwitch
        override val spinnerStartHour = binding.spinnerStartHour
        override val spinnerEndHour = binding.spinnerEndHour
        override val spinnerStartDay = binding.spinnerStartDay
        override val spinnerEndDay = binding.spinnerEndDay
        override val spinnerPrinter = binding.spinnerPrinter
        override val spinnerIsAutoConnect = binding.spinnerIsAutoConnect
        override val spinnerPickMode = binding.spinnerPickMode
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityProfileBinding) : ProfileUnionBinding() {
        override var vm: ProfileVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val mSwitch = binding.mSwitch
        override val spinnerStartHour = binding.spinnerStartHour
        override val spinnerEndHour = binding.spinnerEndHour
        override val spinnerStartDay = binding.spinnerStartDay
        override val spinnerEndDay = binding.spinnerEndDay
        override val spinnerPrinter = binding.spinnerPrinter
        override val spinnerIsAutoConnect = binding.spinnerIsAutoConnect
        override val spinnerPickMode = binding.spinnerPickMode
        override val tvNotification = binding.tvNotification
    }

}
