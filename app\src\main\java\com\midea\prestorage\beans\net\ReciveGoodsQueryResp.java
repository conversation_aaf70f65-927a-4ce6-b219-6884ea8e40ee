package com.midea.prestorage.beans.net;

import java.io.Serializable;
import java.util.List;

/**
 * Created by LIXK5 on 2019/5/16.
 */

public class ReciveGoodsQueryResp implements Serializable {

    private List<ReciveGoodsQueryListResp> list;

    public List<ReciveGoodsQueryListResp> getList() {
        return list;
    }

    public void setList(List<ReciveGoodsQueryListResp> list) {
        this.list = list;
    }

    public class ReciveGoodsQueryListResp implements Serializable {
        private String pageSize;
        private String itemCode;
        private String itemName;
        private int totalQty;
        private String companyCode;
        private String inventorySts;
        private int receivedQty;
        private String receiptCode;
        private String startCheckinDatetime;
        private int containerQty;
        private int plQty;
        private boolean isFull;

        public String getPageSize() {
            return pageSize;
        }

        public void setPageSize(String pageSize) {
            this.pageSize = pageSize;
        }

        public String getItemCode() {
            return itemCode;
        }

        public void setItemCode(String itemCode) {
            this.itemCode = itemCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getCompanyCode() {
            return companyCode;
        }

        public void setCompanyCode(String companyCode) {
            this.companyCode = companyCode;
        }

        public String getInventorySts() {
            return inventorySts;
        }

        public void setInventorySts(String inventorySts) {
            this.inventorySts = inventorySts;
        }

        public String getReceiptCode() {
            return receiptCode;
        }

        public void setReceiptCode(String receiptCode) {
            this.receiptCode = receiptCode;
        }

        public String getStartCheckinDatetime() {
            return startCheckinDatetime;
        }

        public void setStartCheckinDatetime(String startCheckinDatetime) {
            this.startCheckinDatetime = startCheckinDatetime;
        }

        public int getReceivedQty() {
            return receivedQty;
        }

        public void setReceivedQty(int receivedQty) {
            this.receivedQty = receivedQty;
        }

        public int getTotalQty() {
            return totalQty;
        }

        public void setTotalQty(int totalQty) {
            this.totalQty = totalQty;
        }

        public int getContainerQty() {
            return containerQty;
        }

        public void setContainerQty(int containerQty) {
            this.containerQty = containerQty;
        }

        public int getPlQty() {
            return plQty;
        }

        public void setPlQty(int plQty) {
            this.plQty = plQty;
        }

        public boolean isFull() {
            return isFull;
        }

        public void setFull(boolean full) {
            isFull = full;
        }
    }
}
