package com.midea.prestorage.function.inv.dialog

import android.app.AlertDialog
import android.text.TextUtils
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.beans.net.ContainerPickList
import com.midea.prestorage.beans.net.InvSetDetailList
import com.midea.prestoragesaas.databinding.DialogDeleteNumBinding
import com.midea.prestorage.utils.AppUtils
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

class DeleteNumDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {
    var binding: DialogDeleteNumBinding
    private var deleteback: DeleteBarcodeBack? = null

    init {
        val contentView = LayoutInflater.from(mContext).inflate(R.layout.dialog_delete_num, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = DeleteNumDialogVM(this)

        setCanceledOnTouchOutside(false)
    }

    fun setTitle(title: String) {
        if (!TextUtils.isEmpty(title)) {
            binding!!.vm!!.title.set(title)
        }
    }

    fun setDeleteBack(backImpl: DeleteBarcodeBack) {
        deleteback = backImpl
    }

    fun setData(data: InvSetDetailList) {
        binding!!.vm!!.defaultNum.set(AppUtils.getBigDecimalValueStr(data.num)) //该货品的集托数量
        binding!!.vm!!.bean = data
    }

    fun backDeleteBarcode(num : String) {
        if (deleteback != null) {
            deleteback!!.deleteBarcodeBack(num, binding!!.vm!!.bean!!.ids)
            binding!!.vm!!.etInfo.set("")
        }
    }

    interface DeleteBarcodeBack {
        fun deleteBarcodeBack(num : String, ids : String)
    }
}