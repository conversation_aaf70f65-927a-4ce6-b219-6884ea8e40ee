package com.midea.prestorage.beans.net;

import java.io.Serializable;
import java.math.BigDecimal;

public class RuleDataList implements Serializable {
    private String id;
    private String remark;
    private String statusType;
    private String modifier;
    private String modifyTime;
    private String creator;
    private String createTime;
    private String version;
    private String cdcmMaterialNo;
    private String cdcmCustMaterialNo;
    private String cdcmOwnerGroupCode;
    private String cdcmNameCn;
    private String cdcmBarcode69;
    private String ruleGroupCode;
    private String ruleCode;
    private String codeNum;
    private String paraNum;
    private String paraNumTo;
    private String paragraphDefine;
    private String cdcmBarcode;
    private String cdcmBarcode1;
    private String cdcmBarcode2;
    private String cdcmBarcode3;
    private String cdcmBarcode4;
    private String cdcmBarcode5;
    private String cdcmBarcode6;
    private String cdcmBarcode7;
    private String cdcmBarcode8;
    private String cdcmBarcode9;
    private String cdcmBarcode10;
    private String userdefined15;

    public RuleDataList(String cdcmMaterialNo, String cdcmCustMaterialNo, String codeNum, String paraNum, String paraNumTo, String paragraphDefine, String cdcmBarcode, String cdcmBarcode1
            , String cdcmBarcode2, String cdcmBarcode3, String cdcmBarcode4, String cdcmBarcode5, String cdcmBarcode6, String cdcmBarcode7, String cdcmBarcode8, String cdcmBarcode9, String cdcmBarcode10) {
        this.cdcmMaterialNo = cdcmMaterialNo;
        this.cdcmCustMaterialNo = cdcmCustMaterialNo;
        this.codeNum = codeNum;
        this.paraNum = paraNum;
        this.paraNumTo = paraNumTo;
        this.paragraphDefine = paragraphDefine;
        this.cdcmBarcode = cdcmBarcode;
        this.cdcmBarcode1 = cdcmBarcode1;
        this.cdcmBarcode2 = cdcmBarcode2;
        this.cdcmBarcode3 = cdcmBarcode3;
        this.cdcmBarcode4 = cdcmBarcode4;
        this.cdcmBarcode5 = cdcmBarcode5;
        this.cdcmBarcode6 = cdcmBarcode6;
        this.cdcmBarcode7 = cdcmBarcode7;
        this.cdcmBarcode8 = cdcmBarcode8;
        this.cdcmBarcode9 = cdcmBarcode9;
        this.cdcmBarcode10 = cdcmBarcode10;

    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStatusType() {
        return statusType;
    }

    public void setStatusType(String statusType) {
        this.statusType = statusType;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCdcmMaterialNo() {
        return cdcmMaterialNo;
    }

    public void setCdcmMaterialNo(String cdcmMaterialNo) {
        this.cdcmMaterialNo = cdcmMaterialNo;
    }

    public String getCdcmCustMaterialNo() {
        return cdcmCustMaterialNo;
    }

    public void setCdcmCustMaterialNo(String cdcmCustMaterialNo) {
        this.cdcmCustMaterialNo = cdcmCustMaterialNo;
    }

    public String getCdcmOwnerGroupCode() {
        return cdcmOwnerGroupCode;
    }

    public void setCdcmOwnerGroupCode(String cdcmOwnerGroupCode) {
        this.cdcmOwnerGroupCode = cdcmOwnerGroupCode;
    }

    public String getCdcmNameCn() {
        return cdcmNameCn;
    }

    public void setCdcmNameCn(String cdcmNameCn) {
        this.cdcmNameCn = cdcmNameCn;
    }

    public String getCdcmBarcode69() {
        return cdcmBarcode69;
    }

    public void setCdcmBarcode69(String cdcmBarcode69) {
        this.cdcmBarcode69 = cdcmBarcode69;
    }

    public String getRuleGroupCode() {
        return ruleGroupCode;
    }

    public void setRuleGroupCode(String ruleGroupCode) {
        this.ruleGroupCode = ruleGroupCode;
    }

    public String getRuleCode() {
        return ruleCode;
    }

    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }

    public String getCodeNum() {
        return codeNum;
    }

    public void setCodeNum(String codeNum) {
        this.codeNum = codeNum;
    }

    public String getParaNum() {
        return paraNum;
    }

    public void setParaNum(String paraNum) {
        this.paraNum = paraNum;
    }

    public String getParaNumTo() {
        return paraNumTo;
    }

    public void setParaNumTo(String paraNumTo) {
        this.paraNumTo = paraNumTo;
    }

    public String getParagraphDefine() {
        return paragraphDefine;
    }

    public void setParagraphDefine(String paragraphDefine) {
        this.paragraphDefine = paragraphDefine;
    }

    public String getCdcmBarcode() {
        return cdcmBarcode;
    }

    public void setCdcmBarcode(String cdcmBarcode) {
        this.cdcmBarcode = cdcmBarcode;
    }

    public String getCdcmBarcode1() {
        return cdcmBarcode1;
    }

    public void setCdcmBarcode1(String cdcmBarcode1) {
        this.cdcmBarcode1 = cdcmBarcode1;
    }

    public String getCdcmBarcode2() {
        return cdcmBarcode2;
    }

    public void setCdcmBarcode2(String cdcmBarcode2) {
        this.cdcmBarcode2 = cdcmBarcode2;
    }

    public String getCdcmBarcode3() {
        return cdcmBarcode3;
    }

    public void setCdcmBarcode3(String cdcmBarcode3) {
        this.cdcmBarcode3 = cdcmBarcode3;
    }

    public String getCdcmBarcode4() {
        return cdcmBarcode4;
    }

    public void setCdcmBarcode4(String cdcmBarcode4) {
        this.cdcmBarcode4 = cdcmBarcode4;
    }

    public String getCdcmBarcode5() {
        return cdcmBarcode5;
    }

    public void setCdcmBarcode5(String cdcmBarcode5) {
        this.cdcmBarcode5 = cdcmBarcode5;
    }

    public String getCdcmBarcode6() {
        return cdcmBarcode6;
    }

    public void setCdcmBarcode6(String cdcmBarcode6) {
        this.cdcmBarcode6 = cdcmBarcode6;
    }

    public String getCdcmBarcode7() {
        return cdcmBarcode7;
    }

    public void setCdcmBarcode7(String cdcmBarcode7) {
        this.cdcmBarcode7 = cdcmBarcode7;
    }

    public String getCdcmBarcode8() {
        return cdcmBarcode8;
    }

    public void setCdcmBarcode8(String cdcmBarcode8) {
        this.cdcmBarcode8 = cdcmBarcode8;
    }

    public String getCdcmBarcode9() {
        return cdcmBarcode9;
    }

    public void setCdcmBarcode9(String cdcmBarcode9) {
        this.cdcmBarcode9 = cdcmBarcode9;
    }

    public String getCdcmBarcode10() {
        return cdcmBarcode10;
    }

    public void setCdcmBarcode10(String cdcmBarcode10) {
        this.cdcmBarcode10 = cdcmBarcode10;
    }

    public String getUserdefined15() {
        return userdefined15;
    }

    public void setUserdefined15(String userdefined15) {
        this.userdefined15 = userdefined15;
    }
}