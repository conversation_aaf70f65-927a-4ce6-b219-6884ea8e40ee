package com.midea.prestorage.function.main.dialog

import android.widget.Toast
import androidx.databinding.ObservableField
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.utils.ToastUtilsCare

class SaasWhChooseDialogVM(val dialog: SaasWhChooseDialog) {

    val tenant = ObservableField<String>("")
    val warehouse = ObservableField<String>("")
    val tenantCode = ObservableField<String>("")
    val warehouseCode = ObservableField<String>("")

    fun close() {
        dialog.dismiss()
    }

    fun confirm() {
        if (CheckUtil.isFastDoubleClick()) {
            if(tenant.get().toString().isNullOrBlank() || warehouse.get().toString().isNullOrBlank()) {
                ToastUtilsCare.toastBig(dialog.mContext, "请先选择租户和仓库", Toast.LENGTH_SHORT)
                return
            }
            dialog.backConfirm(warehouseCode.get().toString(), warehouse.get().toString())
        }
    }

    //选择租户
    fun showTenantDialog() {
        dialog.backChoose("1")
    }

    //选择仓库
    fun showWarehouseDialog() {
        if(tenant.get().toString().isNullOrBlank()) {
            ToastUtilsCare.toastBig(dialog.mContext, "请先选择租户", Toast.LENGTH_SHORT)
            return
        }
        dialog.backChoose("2")
    }
}