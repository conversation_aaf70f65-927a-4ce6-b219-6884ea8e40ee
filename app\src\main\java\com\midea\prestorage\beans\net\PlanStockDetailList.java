package com.midea.prestorage.beans.net;

import androidx.annotation.Nullable;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.function.inv.response.PackageRelation;
import com.midea.prestorage.utils.AppUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class PlanStockDetailList implements Serializable {

    @ShowAnnotation
    private String custItemCode;
    @ShowAnnotation
    private String lotAtt04Str;
    @ShowAnnotation
    private BigDecimal firstQty;
    @ShowAnnotation
    private String itemName;
    @ShowAnnotation
    private String whBarcode69;
    @ShowAnnotation
    private String ownerName;

    private String sysQty;
    private String ownerCode;
    private String lotAtt04;

    private int isLot05Check;
    private int isLot01Check;
    private int isLot02Check;

    @ShowAnnotation
    private String lotAtt05;
    @ShowAnnotation
    private String lotAtt01;
    @ShowAnnotation
    private String lotAtt02;

    private int isDarkCheck;

    private String itemCode;

    private int isDecimal;

    private String isValidity;
    private BigDecimal periodOfValidity;
    private String validityUnit;
    private String cdpaFormat;
    @Nullable
    private String whCsBarcode69;
    @Nullable
    private String whIpBarcode69;

    @Nullable
    public String getWhCsBarcode69() {
        return whCsBarcode69;
    }

    public void setWhCsBarcode69(@Nullable String whCsBarcode69) {
        this.whCsBarcode69 = whCsBarcode69;
    }

    @Nullable
    public String getWhIpBarcode69() {
        return whIpBarcode69;
    }

    public void setWhIpBarcode69(@Nullable String whIpBarcode69) {
        this.whIpBarcode69 = whIpBarcode69;
    }

    public String getCdpaFormat() {
        return cdpaFormat;
    }

    public void setCdpaFormat(String cdpaFormat) {
        this.cdpaFormat = cdpaFormat;
    }

    public String getIsValidity() {
        return isValidity;
    }

    public void setIsValidity(String isValidity) {
        this.isValidity = isValidity;
    }

    public BigDecimal getPeriodOfValidity() {
        return periodOfValidity;
    }

    public void setPeriodOfValidity(BigDecimal periodOfValidity) {
        this.periodOfValidity = periodOfValidity;
    }

    public String getValidityUnit() {
        return validityUnit;
    }

    public void setValidityUnit(String validityUnit) {
        this.validityUnit = validityUnit;
    }

    public int getIsDecimal() {
        return isDecimal;
    }

    public void setIsDecimal(int isDecimal) {
        this.isDecimal = isDecimal;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    private List<PackageRelation> packageRelationList;

    public List<PackageRelation> getPackageRelationList() {
        return packageRelationList;
    }

    public void setPackageRelationList(List<PackageRelation> packageRelationList) {
        this.packageRelationList = packageRelationList;
    }

    public String getLotAtt05() {
        return lotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        this.lotAtt05 = lotAtt05;
    }

    public String getLotAtt01() {
        return lotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        this.lotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return lotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        this.lotAtt02 = lotAtt02;
    }

    public int getIsLot05Check() {
        return isLot05Check;
    }

    public void setIsLot05Check(int isLot05Check) {
        this.isLot05Check = isLot05Check;
    }

    public int getIsLot01Check() {
        return isLot01Check;
    }

    public void setIsLot01Check(int isLot01Check) {
        this.isLot01Check = isLot01Check;
    }

    public int getIsLot02Check() {
        return isLot02Check;
    }

    public void setIsLot02Check(int isLot02Check) {
        this.isLot02Check = isLot02Check;
    }

    public int getIsDarkCheck() {
        return isDarkCheck;
    }

    public void setIsDarkCheck(int isDarkCheck) {
        this.isDarkCheck = isDarkCheck;
    }

    public String getWhBarcode69() {
        return whBarcode69;
    }

    public void setWhBarcode69(String whBarcode69) {
        this.whBarcode69 = whBarcode69;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getFirstQty() {
        if (firstQty == null) {
            return "";
        }
        return AppUtils.getBigDecimalValueStr(firstQty);
    }

    public void setFirstQty(BigDecimal firstQty) {
        this.firstQty = firstQty;
    }

    public String getSysQty() {
        return sysQty;
    }

    public void setSysQty(String sysQty) {
        this.sysQty = sysQty;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getLotAtt04Str() {
        return lotAtt04Str;
    }

    public void setLotAtt04Str(String lotAtt04Str) {
        this.lotAtt04Str = lotAtt04Str;
    }
}
