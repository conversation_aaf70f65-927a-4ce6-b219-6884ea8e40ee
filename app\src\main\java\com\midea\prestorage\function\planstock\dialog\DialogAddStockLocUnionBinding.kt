package com.midea.prestorage.function.planstock.dialog

import android.view.View
import android.widget.EditText
import androidx.lifecycle.LifecycleOwner
import com.midea.prestoragesaas.databinding.DialogAddStockLocBinding
import com.midea.prestoragesaas.databinding.DialogAddStockLocCareBinding

sealed class DialogAddStockLocUnionBinding {
    abstract var vm: AddStockLocDialogVM?
    abstract val etNum: EditText
    abstract val root: View
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: DialogAddStockLocCareBinding) : DialogAddStockLocUnionBinding() {
        override var vm: AddStockLocDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val etNum = binding.etNum
        override val root = binding.root
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: DialogAddStockLocBinding) : DialogAddStockLocUnionBinding() {
        override var vm: AddStockLocDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val etNum = binding.etNum
        override val root = binding.root
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
