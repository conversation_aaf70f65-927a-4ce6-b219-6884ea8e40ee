package com.midea.prestorage.function.barcode

import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.BarcodeCollectionInfoResp
import com.midea.prestoragesaas.R

class BarcodeCollectionAdapter : CommonAdapter<BarcodeCollectionInfoResp>(R.layout.item_barcode_collection) {

    override fun convert(holder: BaseViewHolder?, item: BarcodeCollectionInfoResp?) {
        super.convert(holder, item)
        holder?.setGone(R.id.tv_cust_item_code, item?.custItemCode.isNullOrEmpty())
        holder?.setGone(R.id.tv_item_name, item?.itemName.isNullOrEmpty())
    }

}