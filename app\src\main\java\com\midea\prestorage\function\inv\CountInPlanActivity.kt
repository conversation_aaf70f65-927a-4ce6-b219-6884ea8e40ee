package com.midea.prestorage.function.inv

import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestoragesaas.databinding.ActivityCountInPlanBinding

// 计划盘点  项目一期 不做，代码留着先
class CountInPlanActivity : BaseActivity() {

    private lateinit var binding: ActivityCountInPlanBinding
   // private var adapter = ListPlanCountTaskAdapter();


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //setContentView(R.layout.activity_count_in_plan)

        binding = DataBindingUtil.setContentView(this, R.layout.activity_count_in_plan)
        binding.vm = CountInPlanVM(this)

        initRecycleView()
        // todo 测试数据
        addTestData()
    }


    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }


    fun initRecycleView(){
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        /*
        binding.recyclerView.adapter = adapter

        adapter.setOnItemClickListener { adapter, view, position ->
        }
        */
    }


    fun addTestData(){
        /*
        for(i in 1..20) {
            var countTask = CountTask()
            countTask.test1 = "31022010007129" // 货品编码
            countTask.test2 = "xxxx" // 系统库存
            countTask.test3 = "0"  //实盘数量
            adapter.addData(countTask)
        }
        */
        //adapter.notifyDataSetChanged()
    }

/*

    class CountTask : BaseItemForPopup() {
        var test1:String = ""  //货品编码
        var test2:String = ""  //系统库存
        var test3:String = ""  //实盘数量
    }



    class ListPlanCountTaskAdapter : ListCheckBoxAdapter<CountTask>(R.layout.item_count_task) {
        override fun convert(helper: BaseViewHolder, item: BaseItemForPopup) {
            super.convert(helper, item as CountTask)
            helper.setText(R.id.test1,item.test1)  //申请单号
                .setText(R.id.test2,item.test2) //服务平台
                .setText(R.id.test3,item.test3)  //仓库
        }
    }


*/

}