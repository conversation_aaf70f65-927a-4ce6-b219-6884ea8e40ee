package com.midea.prestorage.function.containerpick

import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.net.ContainerPickDetailInfo
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent

class ContainerPickFourVM(val activity: ContainerPickFourActivity) {

    val isNoData = ObservableBoolean(true)

    var totalQty = ObservableField("")
    var totalCsQty = ObservableField("")
    var totalEaQty = ObservableField("")

    var title = ObservableField("")
    var qtyInfo = ObservableField("")
    var picker = ObservableField("")
    var time = ObservableField("")

    fun init() {
        val containerCode = activity.intent.getStringExtra("containerCode")
        title.set(containerCode)

        activity.waitingDialogHelp.showDialog()

        RetrofitHelper.getOutStorageAPI()
            .queryContainer(containerCode)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ContainerPickDetailInfo>(activity) {
                override fun success(data: ContainerPickDetailInfo?) {
                    activity.waitingDialogHelp.hidenDialog()

                    if (data == null || data.outPickContainerList.isNullOrEmpty()) {
                        isNoData.set(true)
                    } else {
                        val totalQtyStr = AppUtils.getBigDecimalValueStr(data.sumQty)
                        val totalQtyCsStr = AppUtils.getBigDecimalValueStr(data.sumCsQty)
                        val totalQtyEaStr = AppUtils.getBigDecimalValueStr(data.sumEaQty)

                        totalQty.set(totalQtyStr)
                        totalCsQty.set(totalQtyCsStr)
                        totalEaQty.set(totalQtyEaStr)
                        picker.set(data.pickUserName)
                        time.set(data.pickStartTime)

                        isNoData.set(false)

                        data.outPickContainerList.forEachIndexed { index, it ->
                            it.index = index + 1

                            val pickQtyStr = AppUtils.getBigDecimalValueStr(it.pickedQty)
                            val pickedCsQtyStr = AppUtils.getBigDecimalValueStr(it.pickedCsQty)
                            val pickedEaQtyStr = AppUtils.getBigDecimalValueStr(it.pickedEaQty)
                            it.pickNumInfo =
                                "$pickQtyStr${it.eaUnit}，${pickedCsQtyStr}整${it.csUnit}，${pickedEaQtyStr}${it.eaUnit}   ${statusDC[it.lotAtt04] ?: ""}"

                            it.lotAtt01 = it.lotAtt01?.split(" ")?.get(0)
                            it.createTime = it.createTime?.split(" ")?.get(0)
                        }
                        activity.showData(data.outPickContainerList)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun back() {
        activity.finish()
    }

    val statusDC = mutableMapOf(
        "N" to "不良品",
        "B" to "包装破损",
        "Z" to "优惠品",
        "X" to "寄存品",
        "W" to "物料",
        "Q" to "待检",
        "F" to "样机",
        "E" to "优惠品",
        "D1" to "残次",
        "C" to "待返厂",
        "A" to "实物赔偿",
        "201" to "售后待鉴定",
        "109" to "上门退货残次",
        "108" to "箱损物料责任",
        "107" to "机损物流责任",
        "106" to "机损商家责任",
        "105" to "箱损待鉴定",
        "104" to "机损待鉴定",
        "101" to "菜鸟残次",
        "Y" to "正品",
        "D10" to "成品A",
        "D11" to "降级销售"
    )
}