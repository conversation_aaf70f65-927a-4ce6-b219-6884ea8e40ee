package com.midea.prestorage.function.pointjoin.fragment

import androidx.databinding.ObservableField
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.widgets.ViewBindingAdapter

class PointJoinDialogVM(val dialog: PointJoinDialog) {

    val title = ObservableField<String>("提示")
    val filterInfo = ObservableField("")
    val isShowEdit = ObservableField(true)

    init {

    }

    fun close() {
    }

    fun cleanFilter(){
        filterInfo.set("")
    }

    fun editUnable() {
        isShowEdit.set(false)
    }
}