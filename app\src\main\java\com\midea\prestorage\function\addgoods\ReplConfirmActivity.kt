package com.midea.prestorage.function.addgoods

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.beans.net.ReplenishmentBean.ReplenishmentListBean
import com.midea.prestorage.dialog.TipDialog
import com.midea.prestorage.function.inv.InventorySearchActivity
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.widgets.FilterDigitTextWatcher
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityReplConfirmBinding

class ReplConfirmActivity : BaseViewModelActivity<ReplConfirmActivityVM>() {
    private lateinit var binding: ActivityReplConfirmUnionBinding

    var textWatcher: FilterDigitTextWatcher? = null

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityReplConfirmUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_repl_confirm_care
                )
            )
        } else {
            ActivityReplConfirmUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_repl_confirm
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        vm = ViewModelProvider.AndroidViewModelFactory(application)
            .create(ReplConfirmActivityVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        initWatcher()
        initData()
        initListen()

        binding.edToLoc.post {
            binding.edToLoc.requestFocus()
            if (binding.edToLoc.isEnabled) {
                binding.edToLoc.selectAll()
            }
        }

    }

    private fun initWatcher() {
        if (textWatcher == null) {
            textWatcher =
                FilterDigitTextWatcher(binding.etEa, 0, true) {
                    if ("只能输入正整数" == it) {
                        binding.etEa.setText(
                            binding.etEa.text.toString().replace(Regex("[^0-9]"), "")
                        )
                        if (!binding.etEa.text.isNullOrEmpty()) {
                            binding.etEa.setSelection(binding.etEa.text!!.length)
                        }
                    }
                    AppUtils.showToast(this, it)
                }
        }
    }

    private fun setWatcher(isPoint: Int) {
        binding.etEa.removeTextChangedListener(textWatcher)
        if (isPoint != 0) {
            textWatcher?.limitDecimalPlaces = 4
        }else {
            textWatcher?.limitDecimalPlaces = 0
        }
        binding.etEa.addTextChangedListener(textWatcher)
    }

    fun initData() {
        vm.replenishmentListBean =
            intent.getSerializableExtra("ReplenishmentListBean") as ReplenishmentListBean
        vm.replenishmentListBean?.let {
            vm.initData(it)
            vm.toLoc.set(it.toLoc)
            if (it.isEditable == 0) {
                binding.edToLoc.isEnabled = false
                binding.etOt.isEnabled = false
                binding.etPl.isEnabled = false
                binding.etCs.isEnabled = false
                binding.etIp.isEnabled = false
                binding.etEa.isEnabled = false
            }else {
                vm.getRecommendLoc(it)
            }
        }
    }

    private fun initListen() {
        vm.showTipDialog.observe(this, Observer {
            showTipDialog("")
        })

        vm.toActivityLiveEvent.observe(this, Observer {
            //跳转到库存查询
            val intent = InventorySearchActivity.newIntent(
                this@ReplConfirmActivity,
                vm.barCode.get().toString()
            )
            startActivity(intent)
        })

        vm.otRuquestLiveEvent.observe(this, Observer {
            binding.etOt.post {
                binding.etOt.requestFocus()
                binding.etOt.selectAll()
            }
        })

        vm.plRuquestLiveEvent.observe(this, Observer {
            binding.etPl.post {
                binding.etPl.requestFocus()
                binding.etPl.selectAll()
            }
        })

        vm.csRuquestLiveEvent.observe(this, Observer {
            binding.etCs.post {
                binding.etCs.requestFocus()
                binding.etCs.selectAll()
            }
        })

        vm.ipRuquestLiveEvent.observe(this, Observer {
            binding.etIp.post {
                binding.etIp.requestFocus()
                binding.etIp.selectAll()
            }
        })

        vm.eaRuquestLiveEvent.observe(this, Observer {
            binding.etEa.post {
                binding.etEa.requestFocus()
                binding.etEa.selectAll()
            }
        })

        vm.selectAllLiveEvent.observe(this, Observer {
            binding.edToLoc.post {
                binding.edToLoc.requestFocus()
                if (binding.edToLoc.isEnabled) {
                    binding.edToLoc.selectAll()
                }
            }
        })

        vm.isPoint.observe(this, {
            if (it != 9999) {
                setWatcher(it)
            }
        })
    }

    private fun showTipDialog(str: String) {
        var tipDialog = TipDialog(this)
        tipDialog.setTitle("确认")
        tipDialog.setConfirmTitle("确定")
        tipDialog.setMsg("确定要把货补到【$str】库位吗？")
        tipDialog.setOnTipBackListener(object : TipDialog.OnTipBack {
            override fun onConfirmClick() {
                if (CheckUtil.isFastDoubleClick()) {

                }
            }

            override fun onDismissClick() {
            }
        })
        tipDialog.show()
    }

}