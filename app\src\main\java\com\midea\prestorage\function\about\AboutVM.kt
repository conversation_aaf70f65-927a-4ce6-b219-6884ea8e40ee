package com.midea.prestorage.function.about

import android.graphics.Color
import com.midea.prestorage.function.main.ProfileItemVM
import com.midea.prestorage.widgets.titlebar.TitleBarVm
import com.midea.prestorage.worker.ApkDownloadWorker

class AboutVM(activity: AboutActivity) {

    val titleBarVM = TitleBarVm(onActionBack = {
        activity.finish()
    })
    val itemVersionVM = ProfileItemVM("版本更新")
    val itemUserAgreementVM = ProfileItemVM("用户服务协议")
    val itemPrivacyVM = ProfileItemVM("隐私协议")
    val itemDataProtectionVM = ProfileItemVM("数据保护承诺书")

    init {
        titleBarVM.title.set("关于")
        titleBarVM.titleColor.set(Color.BLACK)
        updateVersionText()
    }

    fun updateVersionText() {
        itemVersionVM.content.set(if (ApkDownloadWorker.canUpdate) "发现新版本" else "已是最新版本")
    }

}