package com.midea.prestorage.beans.setting;

import org.xutils.db.annotation.Column;
import org.xutils.db.annotation.Table;

@Table(name = "SerialAdjustInfo")
public class SerialAdjustInfo {

    @Column(name = "id", isId = true, autoGen = true, property = "NOT NULL")
    private int id;

    // 缓存 key =  imip账号 # 采集单号
    @Column(name = "cacheKey")
    private String cacheKey = "";

    // 采集的条码记录的json string  对应 ItemSerialAdjust 类
    @Column(name = "jsonStrSerialNo")
    private String jsonStrSerialNo = "";



    public SerialAdjustInfo() {

    }


    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCacheKey() {
        return cacheKey;
    }

    public void setCacheKey(String cacheKey) {
        this.cacheKey = cacheKey;
    }

    public String getJsonStrSerialNo() {
        return jsonStrSerialNo;
    }

    public void setJsonStrSerialNo(String jsonStrSerialNo) {
        this.jsonStrSerialNo = jsonStrSerialNo;
    }
}
