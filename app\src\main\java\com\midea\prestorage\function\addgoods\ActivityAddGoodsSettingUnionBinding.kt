package com.midea.prestorage.function.addgoods

import android.widget.CheckBox
import android.widget.LinearLayout
import android.widget.TextView
import com.midea.prestoragesaas.databinding.ActivityAddGoodsSettingBinding
import com.midea.prestoragesaas.databinding.ActivityAddGoodsSettingCareBinding

sealed class ActivityAddGoodsSettingUnionBinding{
    abstract var vm: SettingInVM?
    abstract val cbDefault: CheckBox
    abstract val cbRemember: CheckBox
    abstract val llDefault: LinearLayout
    abstract val llRemember: LinearLayout
    abstract val tvNotification: TextView

    class V2(val binding: ActivityAddGoodsSettingCareBinding) : ActivityAddGoodsSettingUnionBinding() {
        override var vm: SettingInVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val cbDefault = binding.cbDefault
        override val cbRemember = binding.cbRemember
        override val llDefault = binding.llDefault
        override val llRemember = binding.llRemember
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityAddGoodsSettingBinding) : ActivityAddGoodsSettingUnionBinding() {
        override var vm: SettingInVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val cbDefault = binding.cbDefault
        override val cbRemember = binding.cbRemember
        override val llDefault = binding.llDefault
        override val llRemember = binding.llRemember
        override val tvNotification = binding.tvNotification
    }
}
