package com.midea.prestorage.function.inv

import android.widget.*
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestorage.function.outstorage.ActivityBindShippingLocUnionBinding
import com.midea.prestorage.function.outstorage.BindShippingLocVM
import com.midea.prestorage.widgets.spinner.MaterialSpinner
import com.midea.prestoragesaas.databinding.ActivityBindShippingLocBinding
import com.midea.prestoragesaas.databinding.ActivityBindShippingLocCareBinding
import com.midea.prestoragesaas.databinding.ActivitySetPackageBinding
import com.midea.prestoragesaas.databinding.ActivitySetPackageCareBinding

sealed class ActivitySetPackageUnionBinding{
    abstract var vm: SetPackageVM?
    abstract val llTitleBar: RelativeLayout
    abstract val llAllChoose: LinearLayout
    abstract val cbSelect: CheckBox
    abstract val srl: SwipeRefreshLayout
    abstract val recycle: RecyclerView
    abstract val etSearchOrderNo: EditText
    abstract val tvNotification: TextView

    class V2(val binding: ActivitySetPackageCareBinding) : ActivitySetPackageUnionBinding() {
        override var vm: SetPackageVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val llAllChoose = binding.llAllChoose
        override val cbSelect = binding.cbSelect
        override val srl = binding.srl
        override val recycle = binding.recycle
        override val etSearchOrderNo = binding.etSearchOrderNo
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivitySetPackageBinding) : ActivitySetPackageUnionBinding() {
        override var vm: SetPackageVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val llAllChoose = binding.llAllChoose
        override val cbSelect = binding.cbSelect
        override val srl = binding.srl
        override val recycle = binding.recycle
        override val etSearchOrderNo = binding.etSearchOrderNo
        override val tvNotification = binding.tvNotification
    }
}
