package com.midea.prestorage.function.planstock.dialog

import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.midea.prestorage.base.BaseViewModelDialog
import com.midea.prestorage.beans.net.RespExportContainer
import com.midea.prestorage.function.inv.dialog.DialogInputNumActualUnionBinding
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.planstock.dialog.AddStockLocDialogVM.Companion.QRCODE_BACK
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogAddStockLocBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.xuexiang.xqrcode.XQRCode
import java.math.BigDecimal

class AddStockLocDialog : BaseViewModelDialog<AddStockLocDialogVM>() {
    private var binding: DialogAddStockLocUnionBinding? = null
    private var addback: AddBarcodeBack? = null
    private var bean: RespExportContainer? = null
    private var isSerialNo: Boolean = false
    private var mSerialNo: String? = null
    private var mSerialQty: BigDecimal? = null

    override fun beforeOnCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        vm = ViewModelProvider.AndroidViewModelFactory(requireActivity().application)
            .create(AddStockLocDialogVM::class.java)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            dialog?.window?.apply {
                // 背景透明
                setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
                // 窗口居中
                setGravity(Gravity.CENTER)
            }
            DialogAddStockLocUnionBinding.V2(DataBindingUtil.inflate(
                inflater,
                R.layout.dialog_add_stock_loc_care,
                container,
                false
            ))
        } else {
            DialogAddStockLocUnionBinding.V1(DataBindingUtil.inflate(
                inflater,
                R.layout.dialog_add_stock_loc,
                container,
                false
            ))
        }
        binding!!.vm = vm
        binding!!.lifecycleOwner = this

        initView()
        return binding!!.root
    }

    private fun initView() {
        //关闭对话框
        binding?.vm?.isDissmiss?.observe(this, Observer<Boolean> {
            if (it) {
                this.dismiss()
            }
        })

        vm.content.observe(this, Observer<String> { data ->
            if (data.isNotEmpty()) {
                backAddBarcode(data)
            }
        })
        binding?.vm?.startScan?.observe(this, Observer<Boolean> {
            if (it) {
                XQRCode.startScan(this, QRCODE_BACK)
            }
        })


    }

    fun setDeleteBack(backImpl: AddBarcodeBack) {
        addback = backImpl
    }

    fun setData(
        data: RespExportContainer,
        isSerialNo: Boolean,
        serialNo: String,
        serialQty: BigDecimal
    ) {
        bean = data
        this.isSerialNo = isSerialNo
        this.mSerialNo = serialNo
        this.mSerialQty = serialQty
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QRCODE_BACK && resultCode == RxAppCompatActivity.RESULT_OK) {
            //处理扫描结果（在界面上显示）
            binding?.vm?.locCode?.set(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    private fun backAddBarcode(locCode: String) {
        if (addback != null) {
            addback!!.deleteBarcodeBack(locCode)
        }
    }

    interface AddBarcodeBack {
        fun deleteBarcodeBack(locCode: String)
    }

}