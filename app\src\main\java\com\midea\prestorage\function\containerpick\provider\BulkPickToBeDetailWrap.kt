package com.midea.prestorage.function.containerpick.provider

import com.chad.library.adapter.base.entity.node.BaseNode
import com.midea.prestorage.beans.net.BulkPackingDetail
import com.midea.prestorage.beans.net.RespBulkPacking

class BulkPickToBeDetailWrap(
    val parent: RespBulkPacking,
    val task: BulkPackingDetail,
) : BaseNode() {
    override val childNode: MutableList<BaseNode>?
        get() = null

    var selected = false

}