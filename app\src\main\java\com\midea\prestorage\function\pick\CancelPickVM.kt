package com.midea.prestorage.function.pick

import android.annotation.SuppressLint
import android.text.TextUtils
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.ObservableField
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestoragesaas.R
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.OutPickCancelBean
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.functions.Consumer


class CancelPickVM(val activity: CancelPickActivity) {

    val taskNo = ObservableField("") //任务号
    val confirmedBy = ObservableField("") //执行人
    val assignedBy = ObservableField("") //分配给
    val statueStr = ObservableField("") //分配给
    val timeInfo = ObservableField("") //分配给

    var typeStatus: MutableList<DCBean>? = null
    var orderStatus: MutableList<DCBean>? = null
    var goodsStatus: MutableList<DCBean>? = null
    var taskStatus: MutableList<DCBean>? = null

    var mBeans: OutPickCancelBean? = null

    fun init() {
        initData()

        DCUtils.fuShipmentType(activity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                typeStatus = statusDC
                combineDCInfo()
            }
        })

        DCUtils.fuShipmentStatus(activity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                orderStatus = statusDC
                combineDCInfo()
            }
        })

        DCUtils.getGoodsStatusDC(activity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                goodsStatus = statusDC
                combineDCInfo()
            }
        })

        DCUtils.fuOutTaskStatus(activity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                taskStatus = statusDC
                setConfirmedBy()
            }
        })
    }

    private fun initData() {
        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getOutStorageAPI()
            .outTaskHeaderCancelDetail(activity.intent.getStringExtra("taskId"))
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<OutPickCancelBean>(activity) {
                override fun success(data: OutPickCancelBean?) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (data != null) {
                        mBeans = data
                        taskNo.set(data.outTaskHeaderDto.taskCode)
                        assignedBy.set(data.outTaskHeaderDto.assignedBy)
                        confirmedBy.set(data.outTaskHeaderDto.confirmedBy)
                        setConfirmedBy()
                        timeInfo.set(data.outTaskHeaderDto.taskStartTime)

                        showListData(data)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun setConfirmedBy() {
        if (taskStatus != null && mBeans != null) {
            val key =
                taskStatus!!.filter { it.value.toString() == mBeans!!.outTaskHeaderDto.status }[0].key
            statueStr.set(key)
        }
    }

    private fun showListData(data: OutPickCancelBean) {
        val beans = mutableListOf<OutCancelDetailHelp>()
        data.outShipmentDetailResponses.forEach {
            beans.add(OutCancelDetailHelp(it.outShipmentHeader))
            it.outShipmentDetails.forEach { item ->
                item.parentBean = it.outShipmentHeader
                beans.add(OutCancelDetailHelp(item))
            }
        }
        activity.setNewData(beans)
        combineDCInfo()
    }

    /**
     * 融合数据字典
     */
    fun combineDCInfo() {
        activity.adapter.originData?.forEach {
            if (it.title != null) {
                if (orderStatus != null) {
                    val result =
                        orderStatus!!.find { item -> item.value.toString() == it.title.status }
                    if (result != null) {
                        it.title.statusStr = result.key
                    }
                }

                if (typeStatus != null) {
                    val result =
                        typeStatus!!.find { item -> item.value.toString() == it.title.shipmentType }
                    if (result != null) {
                        it.title.shipmentTypeStr = result.key
                    }
                }
            } else {
                if (goodsStatus != null) {
                    val result =
                        goodsStatus!!.find { item -> item.value.toString() == it.child.lotAtt04 }
                    if (result != null) {
                        it.child.lotAtt04Str = result.key
                    }
                }
            }
        }
        activity.adapter.notifyDataSetChanged()

        if (!TextUtils.isEmpty(statueStr.get())) {
            val result = orderStatus?.find { item -> item.value.toString() == statueStr.get() }
            if (result != null) {
                statueStr.set(result.key)
            }
        }
    }

    fun back() {
        activity.finish()
    }

    @SuppressLint("CheckResult")
    fun cancelPick(bean: OutPickCancelBean.OutShipmentHeader) {
        AlertDialogUtil.showOkAndCancelDialog(activity,
            // "请确认是否取消波次${bean?.waveNo}的拣货任务?",
            // 20211204 测试说 改成提示出库单 而不是波次单
            "请确认是否取消出库单${bean?.shipmentCode}的拣货任务?",
            { _, _ ->  //点了确定
                activity.waitingDialogHelp.showDialog()
                RetrofitHelper.getOutStorageAPI()
                    .outTaskHeaderCancel(activity.intent.getStringExtra("taskId"), bean.id)
                    .compose(NetworkScheduler.compose())
                    .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
                    .subscribe(object : RequestCallback<Any>(activity) {
                        override fun success(data: Any?) {
                            activity.waitingDialogHelp.hidenDialog()
                            ToastUtils.getInstance().showSuccessToastWithSound(activity, "操作成功!")
                            bean.isCancel = true
                            bean.statusStr = "已分配"
                            bean.status = "300"

                            val results =
                                mBeans?.outShipmentDetailResponses?.filter { it.outShipmentHeader.status != "300" }
                            if (results.isNullOrEmpty()) {
                                mBeans!!.outTaskHeaderDto.status = "999"
                                setConfirmedBy()
                                activity.setTvStatue(R.color.btn_orange)
                            }
                            activity.adapter.notifyDataSetChanged()
                        }

                        override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                            activity.waitingDialogHelp.hidenDialog()
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(activity, apiErrorModel.message)
                        }
                    })
            },
            { _, _ ->  //点了取消
            })

//        val ob1 = RetrofitHelper.getOutStorageAPI().cancelCheck(bean.id)
//        val ob2 = RetrofitHelper.getOutStorageAPI()
//            .outTaskHeaderCancel(activity.intent.getStringExtra("taskId"), bean.id)
//
//        ob1.map {
//            val b = it.code == 0L
//            b
//        }.flatMap {
//            if (it) {
//                ob2
//            } else {
//                Observable.error(Throwable())
//            }
//        }.compose(NetworkScheduler.compose())
//            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
//            .subscribe(object : RequestCallback<Any>(activity) {
//                override fun success(data: Any?) {
//                    activity.waitingDialogHelp.hidenDialog()
//                    ToastUtils.getInstance().showErrorToastWithSound(activity, "操作成功!")
//                }
//
//                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
//                    ToastUtils.getInstance()
//                        .showErrorToastWithSound(activity, apiErrorModel.message)
//                }
//            })
    }
}