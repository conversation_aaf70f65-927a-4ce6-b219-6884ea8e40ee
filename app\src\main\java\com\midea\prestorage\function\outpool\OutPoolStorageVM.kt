package com.midea.prestorage.function.outpool

import CheckUtil
import android.content.Intent
import android.text.TextUtils
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.ContainerConfigBean
import com.midea.prestorage.beans.net.OutPoolStorageList
import com.midea.prestorage.beans.net.PointDivideDetailBean
import com.midea.prestorage.dialog.ConfirmFilterDialog
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.instorage.response.HandlingGroup
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.function.outstorage.OutStorageActivity
import com.midea.prestorage.function.pick.PickPoolStartActivity
import com.midea.prestorage.function.pointjoin.PointJoinDetailActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody


@Suppress("UNCHECKED_CAST")
class OutPoolStorageVM(val activity: OutPoolStorageActivity) {

    val isRefreshing = ObservableBoolean(false)
    var filter = ObservableField("筛选")
    val isNoData = ObservableBoolean(false)
    var orderStatus: MutableList<DCBean>? = null
    var typeStatus: MutableList<DCBean>? = null
    var shipWayStatus: MutableList<DCBean>? = null
    var engineers: MutableList<DCBean>? = null

    var dayInfo = DCUtils.days[1]
    var sendInfoDC: DCBean? = null
    var orderStatue: MutableList<DCBean>? = mutableListOf(
        DCBean("创建", 100, DCBean.SHOW_KEY),
        DCBean("已分配", 300, DCBean.SHOW_KEY)
    )
    var typeStatue: MutableList<DCBean>? = null

    var orderNo = ObservableField("")
    var orderNoHint = ObservableField("出库单/客户订单号/波次号")
    var statusInfo = ObservableField("创建,已分配")
    var typeInfo = ObservableField("")
    var dayInfoTv = ObservableField(dayInfo.key)
    var sendInfo = ObservableField("")
    var engineerInfo = ObservableField("全部")
    var btnInfo = ObservableField("交接")

    var isShowEngineer = ObservableField(false)

    lateinit var handlingGroupDialog: ConfirmFilterDialog

    private var configValue = 0 //1定位到库位，0定位到条码
    private val shipments = mutableListOf<String?>()

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        isRefreshing.set(true)
        initOrderList()
    }

    fun init() {
        onRefreshCommand.onRefresh()
        initFilterInfo()

        DCUtils.fuShipmentStatus(activity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                orderStatus = statusDC
                combineDCInfo()
            }
        })

        DCUtils.fuShipmentType(activity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                typeStatus = statusDC
                combineDCInfo()
            }
        })

        DCUtils.shipType(activity as RxAppCompatActivity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                shipWayStatus = AppUtils.cloneObj(statusDC)
                shipWayStatus?.add(DCBean("全部", "", DCBean.SHOW_KEY))
            }
        })

        initEngineerList()
        initChangeType()
        initHandingDialog()
    }

    private fun initHandingDialog() {
        handlingGroupDialog = ConfirmFilterDialog(activity as RxAppCompatActivity)
        handlingGroupDialog.setTitle("选择装卸组")
        handlingGroupDialog.dismissEdit()
        handlingGroupDialog.setBack(object : ConfirmFilterDialog.ConfirmFilterChooseBack {
            override fun confirmFilterChooseBack(baseInfo: BaseItemShowInfo) {
                baseInfo as HandlingGroup
                sendGoods(baseInfo)
                handlingGroupDialog.dismiss()
            }
        })
    }

    private fun sendGoods(it: HandlingGroup) {
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "shipmentCodes" to shipments,
            "waveNo" to "",
            "handingGroupCode" to it.handlingCode,
            "handingGroupName" to it.handlingName
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .shippingConfirm(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    ToastUtils.getInstance()
                        .showSuccessToastWithSound(activity, "发运成功!")
                    initOrderList()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun initChangeType() {
        RetrofitHelper.getDirectionAPI()
            .controlParam()
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ContainerConfigBean>(activity) {
                override fun success(data: ContainerConfigBean?) {
                    data?.let {
                        configValue = it.value

                        if (configValue == 1) {
                            initHandlingGroupList()
                            btnInfo.set("发运")
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
//                    AppUtils.showToast(activity, apiErrorModel.message)
                }
            })
    }

    private fun initHandlingGroupList() {
        RetrofitHelper.getBasicDataAPI()
            .queryPageHandlingGroup(Constants.tenantCode, (activity as BaseActivity).getWhCode(), "02")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<PageResult<HandlingGroup>>(activity as BaseActivity) {
                override fun success(data: PageResult<HandlingGroup>?) {
                    val beans = mutableListOf<BaseItemShowInfo>()

                    if (data != null && data.list != null && data.list.size > 0) {
                        data.list.forEach {
                            it.showInfo = it.handlingName
                            if (!it.showInfo.isNullOrBlank()) {
                                beans.add(it)
                            }
                        }
                    }

                    if (activity.getWhCode() == "W00514") {
                        val handlingGroup = HandlingGroup()
                        handlingGroup.showInfo = "虚拟装卸组"
                        handlingGroup.handlingCode = "zx0001"
                        handlingGroup.handlingName = "虚拟装卸组"
                        beans.add(0, handlingGroup)
                    }

                    if (beans.isEmpty()) {
                        ToastUtils.getInstance().showErrorToastWithSound(activity, "必须配置装卸组!")
                        return
                    }

                    handlingGroupDialog.addAllData(beans)

                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun sendInfoCheck() {
        isShowEngineer.set(sendInfoDC?.value == "NET_MATCHING")
    }

    val goodsEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                activity.popDismiss()
            }
        }
    }

    fun initFilterInfo() {
        val filters = mutableListOf<String>()
        filters.add(dayInfo.key)

        if (!TextUtils.isEmpty(statusInfo.get())) {
            filters.add(statusInfo.get()!!)
        }

        activity.resetFilterInfo(filters)
    }

    fun onEnterOrderNo() {
        if (!orderNo.get().isNullOrBlank()) {
            //activity.popupWindow.dismiss()
            isRefreshing.set(true)
            initOrderList()
        }
    }

    fun initEngineerList() {
        val status = StringBuffer()
        orderStatue?.forEach {
            status.append(it.value.toString() + ",")
        }
        val types = StringBuffer()
        typeStatue?.forEach {
            types.append(it.value.toString() + ",")
        }

        val no = if (!TextUtils.isEmpty(orderNo.get())) {
            orderNo.get()!!
        } else {
            if (orderNoHint.get()!!.contains("出库单")) {
                ""
            } else {
                orderNoHint.get()!!
            }
        }

        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "orderNo" to no.trim(),
            "dayNum" to dayInfo.value,
            "statusStr" to status,
            "shipmentType" to types
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .headerEngineerList(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<String>>(activity) {
                override fun success(data: MutableList<String>?) {
                    val beans = mutableListOf<DCBean>()
                    data?.forEach {
                        if (!TextUtils.isEmpty(it)) {
                            beans.add(DCBean(it, it))
                        }
                    }
                    beans.add(0, DCBean("全部", "全部"))
                    engineers = beans
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun initOrderList() {
        val status = StringBuffer()
        orderStatue?.forEach {
            status.append(it.value.toString() + ",")
        }
        val types = StringBuffer()
        typeStatue?.forEach {
            types.append(it.value.toString() + ",")
        }

        val no = if (!TextUtils.isEmpty(orderNo.get())) {
            orderNo.get()!!
        } else {
            if (orderNoHint.get()!!.contains("出库单")) {
                ""
            } else {
                orderNoHint.get()!!
            }
        }

        val param = mutableMapOf(
            "whCode" to activity.getWhCode(), "orderNo" to no.trim(),
            "dayNum" to dayInfo.value,
            "statusStr" to status,
            "shipmentType" to types
        )

        if (!engineerInfo.get().isNullOrEmpty()
            && engineerInfo.get() != "全部"
            && sendInfoDC?.value == "NET_MATCHING"
        ) {
            param["netEngineerName"] = engineerInfo.get()!!
        } else {
            param["netEngineerName"] = ""
        }

        if (sendInfoDC != null) {
            param["shippingWay"] = sendInfoDC!!.value
        }

        loadByParam(param)
    }

    private fun loadByParam(param: MutableMap<String, Any>) {
        val searchParam: MutableMap<String, Any>

        if (activity.isLoadParamFromMainActivity) {
            activity.isLoadParamFromMainActivity = false
            searchParam = activity.paramFromMainActivity
        } else {
            searchParam = param
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(searchParam)
        )

        RetrofitHelper.getOutStorageAPI()
            .outShipmentHeaderList(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<OutPoolStorageList>>(activity) {
                override fun success(data: MutableList<OutPoolStorageList>?) {
                    if (data != null) {
                        activity.setCbSelectChecked(false)
                        activity.showData(data)
                        filter.set("筛选(${data.size})")
                        combineDCInfo()
                    } else {
                        filter.set("筛选(0)")
                    }
                    isNoData.set(data == null || data.isEmpty())
                    isRefreshing.set(false)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isRefreshing.set(false)
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun clearOrderNo() {
        orderNo.set("")
    }

    fun back() {
        activity.finish()
    }

    fun popShow() {
        val no = orderNo.get()
        val hint = orderNoHint.get()
        if (TextUtils.isEmpty(no)) {
            if (TextUtils.isEmpty(hint)) {
                orderNoHint.set("出库单/客户订单号/波次号")
            } else {
                orderNoHint.set(hint)
            }
        } else {
            orderNoHint.set(no)
            orderNo.set("")
        }
//        if (TextUtils.isEmpty(no)) {
//            orderNoHint.set("出库单/客户订单号/波次号")
//        } else {
//            orderNoHint.set(no)
//            orderNo.set("")
//        }
    }

    /**
     * 融合数据字典
     */
    fun combineDCInfo() {
        activity.adapter.data.forEach {
            if (orderStatus != null) {
                val result =
                    orderStatus!!.find { item -> item.value.toString() == it.status.toString() }
                if (result != null) {
                    it.statusStr = result.key
                }
            }
            if (typeStatus != null) {
                val result = typeStatus!!.find { item -> item.value.toString() == it.shipmentType }
                if (result != null) {
                    it.shipmentTypeStr = result.key
                }
            }
        }
        activity.adapter.notifyDataSetChanged()
    }

    fun statueClick() {
        if (orderStatus == null) {
            return
        }
        activity.showStatueDialog(orderStatus as MutableList<BaseItemShowInfo>)
    }

    fun sendClick() {
        if (shipWayStatus == null) {
            return
        }
        activity.showSendDialog(shipWayStatus as MutableList<BaseItemShowInfo>)
    }

    fun engineerClick() {
        if (engineers == null) {
            return
        }
        activity.showEngineerDialog(engineers as MutableList<BaseItemShowInfo>)
    }

    fun dayClick() {
        val days = DCUtils.days
        activity.showDaysDialog(days as MutableList<BaseItemShowInfo>)
    }

    fun typeClick() {
        if (typeStatus == null) {
            return
        }
        activity.showTypeDialog(typeStatus as MutableList<BaseItemShowInfo>)
    }

    // 重置按钮，点击
    fun resetClick() {
        orderNo.set("")
        orderNoHint.set("出库单/客户订单号/波次号")

        dayInfo = DCUtils.days[1]
        dayInfoTv.set(dayInfo.key)

        typeInfo.set("")
        typeStatue = null
        typeStatus?.forEach {
            it.isSelected = false
        }

        statusInfo.set("已分配")
        orderStatue = mutableListOf(DCBean("已分配", 300, DCBean.SHOW_KEY))
        orderStatus?.forEach {
            it.isSelected = false
        }
    }

    /**
     * 点击拣货
     */
    fun pick() {
        if (CheckUtil.isFastDoubleClick()) {
            val it = Intent(activity, PickPoolStartActivity::class.java)
            val checkBeans = activity.adapter.returnBeans

            if (checkBeans.isNotEmpty()) {
                if (checkBeans.size > 100) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "最多勾选100个!")
                    return
                }
                val set = mutableSetOf<String?>()
                checkBeans.forEach {
                    set.add(it.waveNo)
                }
                if (set.size > 1) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, "只有同一波次的订单才能一起拣货，请检查勾选的订单是否都是同一波次!")
                    return
                } else {
                    if (!TextUtils.isEmpty(set.iterator().next())) {
                        it.putExtra("id", checkBeans[0].waveNo)
                    } else {
                        //这里就是都没波次单的情况，掉接口组波次
                        makeWaveNo(checkBeans)
                        return
                    }
                }
            } else {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "请勾选最少一个出库单来进行拣货!")
                return
            }
            activity.startActivity(it)
        }
    }

    private fun makeWaveNo(checkBeans: MutableList<OutPoolStorageList>) {
        val results = checkBeans.filter { it.status != 300 }
        if (results.isNotEmpty()) {
            ToastUtils.getInstance()
                .showErrorToastWithSound(activity, "只有已分配的订单才能组波次,请确认勾选的订单是否都为已分配订单!")
            return
        }
        activity.waitingDialogHelp.showDialog()
        val shipmentIds = mutableListOf<String>()
        checkBeans.forEach {
            shipmentIds.add(it.id)
        }
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "userCode" to Constants.userInfo?.name,
            "shipmentIds" to shipmentIds
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .outTask(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<String>(activity) {
                override fun success(data: String?) {
                    activity.waitingDialogHelp.hidenDialog()
                    val it = Intent(activity, PickPoolStartActivity::class.java)
                    it.putExtra("id", data)
                    activity.startActivity(it)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun reCheck() {
        if (CheckUtil.isFastDoubleClick()) {
            val checkBeans = activity.adapter.returnBeans

            if (checkBeans.isNotEmpty()) {
                val results = checkBeans.filter { it.status < 401 }
                if (results.isNotEmpty()) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, "订单未完成拣货，不能复核!")
                    return
                } else {
                    val set = mutableSetOf<String?>()
                    checkBeans.forEach {
                        set.add(it.waveNo)
                    }
                    if (set.size > 1) {
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, "只有同一波次的订单才能一起复核，请检查勾选的订单是否都是同一波次!")
                        return
                    } else {
                        val it = Intent(activity, OutStorageActivity::class.java)
                        if (!TextUtils.isEmpty(set.iterator().next())) {
                            it.putExtra("orderNo", checkBeans[0].waveNo)
                        } else {
                            //这里就是都没波次单的情况，掉接口组波次
                            it.putExtra("orderNo", checkBeans[0].shipmentCode)
                        }
                        activity.startActivity(it)
                    }
                }
            } else {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "请勾选最少一个波次单来进行复核!")
                return
            }
        }
    }

    private fun checkDetail(ids: MutableList<String?>) {
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "shipmentCodeList" to ids
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .engineerDetail(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<MutableList<PointDivideDetailBean>>(activity as RxAppCompatActivity) {
                override fun success(data: MutableList<PointDivideDetailBean>?) {
                    if (!data.isNullOrEmpty()) {
                        val it = Intent(activity, PointJoinDetailActivity::class.java)
                        it.putExtra("beans", java.util.ArrayList(data))
                        activity.startActivity(it)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    /**
     * 按单收货
     */
    fun orderReceive() {
        if (CheckUtil.isFastDoubleClick()) {
            val checkBeans = activity.adapter.returnBeans

            if (checkBeans.isNotEmpty()) {
                if (checkBeans.size > 10) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "最多勾选10个!")
                    return
                }
                val results = checkBeans.filter { it.status != 600 }
                if (results.isNotEmpty()) {
                    ToastUtils.getInstance().showErrorToastWithSound(activity, "只有复核完成的订单才能交接!")
                    return
                } else {
                    val set = mutableSetOf<String?>()
                    checkBeans.forEach {
                        set.add(it.shipmentCode)
                    }
                    val list = set.toMutableList()
                    if (configValue == 1) {
                        shipments.clear()
                        shipments.addAll(list)
                        handlingGroupDialog.show()
                    } else {
                        checkDetail(list)
                    }
                }
            } else {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "请勾选最少一个出库单来进行交接!")
                return
            }
        }
    }

    fun onItemClick(bean: OutPoolStorageList) {
        val it = Intent(activity, OutPoolStorageDetailActivity::class.java)
        it.putExtra("bean", bean)
        activity.startActivity(it)
    }

    fun showStatus() {
        if (orderStatus?.size == orderStatue?.size) {
            statusInfo.set("全部状态")
        } else {
            val status = orderStatue?.joinToString(separator = ",") { it.key }
            statusInfo.set(status)
        }
    }

    fun showType() {
        val status = StringBuffer("")
        typeStatue?.forEach {
            status.append(it.key.toString() + ",")
        }
        typeInfo.set(status.toString())
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        orderNo.set(result)
    }
}