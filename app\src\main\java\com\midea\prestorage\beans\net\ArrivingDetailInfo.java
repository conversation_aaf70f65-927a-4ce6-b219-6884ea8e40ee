package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.utils.AppUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class ArrivingDetailInfo implements Serializable {

    private BigDecimal totalVolume;
    private String note;
    private String orderNo;
    private String siteCode;
    private BigDecimal totalQty;
    private String customerCode;
    private String orderStatus;
    private String relationOrderNo;
    private String shippingWay;
    private String excuteStatus;
    private String custOrderNo;
    private String taskType;
    private String WHCode;
    private String shipFromAddress;
    private String taskCode;
    private String createTime;
    private String businessType;
    private String taskStatus;
    private String waybillNo;
    private List<Details> details;

    private String shipToAddress;
    private String senderName;
    private String shipToMobile;
    private String shipFromAttentionTo;
    private String shipToAttentionTo;
    private String transhubName;
    private String transhubCode;
    private String transhubAddress;
    private String scheduledArriveDate;
    private String shipFromMobile;

    public String getShipToAddress() {
        return shipToAddress;
    }

    public void setShipToAddress(String shipToAddress) {
        this.shipToAddress = shipToAddress;
    }

    public String getShipToMobile() {
        return shipToMobile;
    }

    public void setShipToMobile(String shipToMobile) {
        this.shipToMobile = shipToMobile;
    }

    public String getShipToAttentionTo() {
        return shipToAttentionTo;
    }

    public void setShipToAttentionTo(String shipToAttentionTo) {
        this.shipToAttentionTo = shipToAttentionTo;
    }

    public String getTranshubName() {
        return transhubName;
    }

    public void setTranshubName(String transhubName) {
        this.transhubName = transhubName;
    }

    public String getTranshubCode() {
        return transhubCode;
    }

    public void setTranshubCode(String transhubCode) {
        this.transhubCode = transhubCode;
    }

    public String getTranshubAddress() {
        return transhubAddress;
    }

    public void setTranshubAddress(String transhubAddress) {
        this.transhubAddress = transhubAddress;
    }

    public String getScheduledArriveDate() {
        return scheduledArriveDate;
    }

    public void setScheduledArriveDate(String scheduledArriveDate) {
        this.scheduledArriveDate = scheduledArriveDate;
    }

    public String getShipFromMobile() {
        return shipFromMobile;
    }

    public void setShipFromMobile(String shipFromMobile) {
        this.shipFromMobile = shipFromMobile;
    }

    public BigDecimal getTotalVolume() {
        return totalVolume;
    }

    public void setTotalVolume(BigDecimal totalVolume) {
        this.totalVolume = totalVolume;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public BigDecimal getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getRelationOrderNo() {
        return relationOrderNo;
    }

    public void setRelationOrderNo(String relationOrderNo) {
        this.relationOrderNo = relationOrderNo;
    }

    public String getShippingWay() {
        return shippingWay;
    }

    public void setShippingWay(String shippingWay) {
        this.shippingWay = shippingWay;
    }

    public String getExcuteStatus() {
        return excuteStatus;
    }

    public void setExcuteStatus(String excuteStatus) {
        this.excuteStatus = excuteStatus;
    }

    public String getCustOrderNo() {
        return custOrderNo;
    }

    public void setCustOrderNo(String custOrderNo) {
        this.custOrderNo = custOrderNo;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getWHCode() {
        return WHCode;
    }

    public void setWHCode(String WHCode) {
        this.WHCode = WHCode;
    }

    public String getShipFromAddress() {
        return shipFromAddress;
    }

    public void setShipFromAddress(String shipFromAddress) {
        this.shipFromAddress = shipFromAddress;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getWaybillNo() {
        return waybillNo;
    }

    public void setWaybillNo(String waybillNo) {
        this.waybillNo = waybillNo;
    }

    public List<Details> getDetails() {
        return details;
    }

    public void setDetails(List<Details> details) {
        this.details = details;
    }

    public String getShipFromAttentionTo() {
        return shipFromAttentionTo;
    }

    public void setShipFromAttentionTo(String shipFromAttentionTo) {
        this.shipFromAttentionTo = shipFromAttentionTo;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public class Details {

        @ShowAnnotation
        private String goodsInfo;
        @ShowAnnotation
        private String itemSuiteCode;
        @ShowAnnotation
        private String custItemCode;
        @ShowAnnotation
        private String itemName;
        @ShowAnnotation
        private String status;

        private BigDecimal volume;
        private String logicWHCode;
        private BigDecimal planQty;
        private String anntoItemCode;
        private BigDecimal weight;
        private BigDecimal actQty;
        private BigDecimal pkgQty;

        public BigDecimal getVolume() {
            return volume;
        }

        public void setVolume(BigDecimal volume) {
            this.volume = volume;
        }

        public String getLogicWHCode() {
            return logicWHCode;
        }

        public void setLogicWHCode(String logicWHCode) {
            this.logicWHCode = logicWHCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getAnntoItemCode() {
            return anntoItemCode;
        }

        public void setAnntoItemCode(String anntoItemCode) {
            this.anntoItemCode = anntoItemCode;
        }

        public String getCustItemCode() {
            return custItemCode;
        }

        public void setCustItemCode(String custItemCode) {
            this.custItemCode = custItemCode;
        }

        public BigDecimal getWeight() {
            return weight;
        }

        public void setWeight(BigDecimal weight) {
            this.weight = weight;
        }

        public BigDecimal getActQty() {
            return actQty;
        }

        public void setActQty(BigDecimal actQty) {
            this.actQty = actQty;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getGoodsInfo() {
            return AppUtils.getBigDecimalValue(planQty).toPlainString() +
                    "/" + AppUtils.getBigDecimalValue(actQty).toPlainString() +
                    "/" + AppUtils.getBigDecimalValue(pkgQty).toPlainString();
        }

        public BigDecimal getPlanQty() {
            return planQty;
        }

        public void setPlanQty(BigDecimal planQty) {
            this.planQty = planQty;
        }

        public void setGoodsInfo(String goodsInfo) {
            this.goodsInfo = goodsInfo;
        }

        public BigDecimal getPkgQty() {
            return pkgQty;
        }

        public void setPkgQty(BigDecimal pkgQty) {
            this.pkgQty = pkgQty;
        }

        public String getItemSuiteCode() {
            return itemSuiteCode;
        }

        public void setItemSuiteCode(String itemSuiteCode) {
            this.itemSuiteCode = itemSuiteCode;
        }
    }
}
