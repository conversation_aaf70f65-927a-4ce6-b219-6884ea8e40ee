package com.midea.prestorage.function.outstorage.dialog

import android.text.TextUtils
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.net.OutStorageScan
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal

class InputDialogVM(val dialog: InputDialog) {

    val title = ObservableField<String>("请输入数量")
    val itemCode = ObservableField<String>("")
    val itemName = ObservableField<String>("")
    val goodsNo = ObservableField<String>("")

    fun show() {
        itemCode.set(dialog.deleteInfo?.custItemCode)
        itemName.set(dialog.deleteInfo?.itemName)
    }

    val goodsEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                if (!TextUtils.isEmpty(goodsNo.get())) {
                    confirm()
                }
            }
        }
    }


    fun close() {
        dialog.inputBack?.inputFail()
        dialog.dismiss()
    }

    fun confirm() {
        if (TextUtils.isEmpty(goodsNo.get())) {
            AppUtils.showToast(dialog.mContext, "数量不能为空!")
            return
        }

        if (goodsNo.get()!! == "0") {
            AppUtils.showToast(dialog.mContext, "数量不能为0!")
            return
        }

        if (Constants.maxInputNum != -1 && Constants.maxInputNum != null) {
            if (BigDecimal(goodsNo.get()!!).compareTo(BigDecimal(Constants.maxInputNum!!)) == 1) {
                AppUtils.showToast(dialog.mContext, "数量超出仓库规定数量!")
                return
            }
        }

        val num = BigDecimal(goodsNo.get()!!)
        if (dialog.deleteInfo != null && dialog.deleteInfo!!.unScanQty != null) {
            if (num.compareTo(dialog.deleteInfo!!.unScanQty) == 1) {
                AppUtils.showToast(dialog.mContext, "数量超出!")
                return
            }
        }
        dialog.mContext.waitingDialogHelp.showDialog()
        val param = mutableMapOf(
            "serialNo" to "",
            "custItemCode" to dialog.deleteInfo?.custItemCode,
            "whCode" to  dialog.mContext.getWhCode(),
            "waveNo" to dialog.deleteInfo?.waveNo,
            "barcode" to dialog.deleteInfo?.barcode,
            "qty" to goodsNo.get(),
            "unScanItemCodes" to dialog.deleteInfo?.unScanItemCodes
        )
        val results =
            dialog.bean?.outShipmentDetailInfoVOS?.filter { it.custItemCode == dialog.deleteInfo?.custItemCode }
        if (results.isNullOrEmpty()) {
            param["outShipmentDetailInfoVOS"] = dialog.bean?.outShipmentDetailInfoVOS
        } else {
            param["outShipmentDetailInfoVOS"] = results
        }
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .scan(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(dialog.mContext as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<OutStorageScan>(dialog.mContext) {
                override fun success(data: OutStorageScan?) {
                    dialog.mContext.waitingDialogHelp.hidenDialog()
                    dialog.inputBack?.inputOk(data)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    dialog.mContext.waitingDialogHelp.hidenDialog()
                    AppUtils.showToast(dialog.mContext, apiErrorModel.message)
                    dialog.inputBack?.inputFail()
                }
            })
    }
}