package com.midea.prestorage.function.instorage

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.dialog.SettingOptionDialogFragment
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityInStoragePutawayTaskBinding

class InStoragePutAwayTaskActivity : BaseViewModelActivity<InStoragePutAwayTaskViewModel>(),
    SettingOptionDialogFragment.ReceivingSettingListener {

    private lateinit var binding: ActivityInStoragePutawayTaskBinding
    private var adapter: InStoragePutAwayTaskAdapter? = null

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_in_storage_putaway_task)
        immersionBar {
            titleBarMarginTop(binding.clTitleLayout)
        }
        vm = ViewModelProvider(this).get(InStoragePutAwayTaskViewModel::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this
        vm.initSetting()
        initView()
    }

    override fun onResume() {
        super.onResume()
        vm.onRefreshCommand.onRefresh()
    }

    private fun initView() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        binding.rv.apply {
            <EMAIL> = InStoragePutAwayTaskAdapter(vm.settingViewModel)
            adapter = <EMAIL>
        }
        adapter?.setOnItemClickListener { _, _, position ->
            startActivity(
                InStoragePutAwayTaskDetailActivity.newIntent(
                    this,
                    adapter?.data?.get(position)!!
                )
            )
        }
        vm.settingViewModel.showLotAttCount.observe(this) {}
        vm.settingViewModel.firstOpenLotAtt.observe(this) {}
        vm.settingViewModel.secondOpenLotAtt.observe(this) {}
        vm.settingViewModel.thirdOpenLotAtt.observe(this) {}
        vm.settingViewModel.fourthOpenLotAtt.observe(this) {}
        vm.scanCodeResult.observe(this) {
            startActivity(InStoragePutAwayTaskDetailActivity.newIntent(this, it))
        }
        vm.embeddedFilterViewModel.displayMaskLiveData.observe(this) { display ->
            binding.vMask.visibility = if (display == true) View.VISIBLE else View.GONE
        }
        vm.taskListLiveData.observe(this) {
            adapter?.setList(it)
        }
        vm.showSettingDialogEvent.observe(this) {
            SettingOptionDialogFragment.newInstance(
                it,
                InStoragePutAwayTaskSettingViewModel.settingName
            ).show(supportFragmentManager, "setting")
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun updateReceivingSetting(setting: Map<String, Boolean>) {
        SPUtils.run(applicationContext) {
            it.putBoolean(
                InStoragePutAwayTaskSettingViewModel.SHOW_LOT_ATT_01,
                setting[InStoragePutAwayTaskSettingViewModel.SHOW_LOT_ATT_01]
                    ?: InStoragePutAwayTaskSettingViewModel.DEFAULT_LOT_ATT_01
            )
            it.putBoolean(
                InStoragePutAwayTaskSettingViewModel.SHOW_LOT_ATT_03,
                setting[InStoragePutAwayTaskSettingViewModel.SHOW_LOT_ATT_03]
                    ?: InStoragePutAwayTaskSettingViewModel.DEFAULT_LOT_ATT_03
            )
            it.putBoolean(
                InStoragePutAwayTaskSettingViewModel.SHOW_LOT_ATT_05,
                setting[InStoragePutAwayTaskSettingViewModel.SHOW_LOT_ATT_05]
                    ?: InStoragePutAwayTaskSettingViewModel.DEFAULT_LOT_ATT_05
            )
            it.putBoolean(
                InStoragePutAwayTaskSettingViewModel.SHOW_TRACE_ID,
                setting[InStoragePutAwayTaskSettingViewModel.SHOW_TRACE_ID]
                    ?: InStoragePutAwayTaskSettingViewModel.DEFAULT_TRACE_ID
            )
        }
        vm.settingViewModel.updateSetting(setting)
        vm.onRefreshCommand.onRefresh()
    }

}