package com.midea.prestorage.function.mainyg

import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R

class FunctionAdapter(data: MutableList<FunctionBeanHelp>?) :
    BaseMultiItemQuickAdapter<FunctionBeanHelp, BaseViewHolder>(data) {

    init {
        addItemType(0, R.layout.item_main_title)
        addItemType(1, R.layout.item_func_child)

        addChildClickViewIds(R.id.ll_child)
    }

    override fun convert(holder: BaseViewHolder, item: FunctionBeanHelp) {
        if (item.itemType == 0) {
            holder.setText(R.id.tv_title, item.title)
        } else if (item.itemType == 1) {
            holder.setText(R.id.tv_child, item.child.funcName)
            holder.setImageResource(R.id.img_icon, item.child.iconResource)
        }
    }
}
