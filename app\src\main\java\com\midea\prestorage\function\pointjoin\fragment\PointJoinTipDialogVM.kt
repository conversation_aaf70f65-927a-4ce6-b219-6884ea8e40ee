package com.midea.prestorage.function.pointjoin.fragment

import android.view.View
import androidx.databinding.ObservableField

class PointJoinTipDialogVM(var dialog: PointJoinTipDialog) {
    val titleName = ObservableField<String>("")
    val msg = ObservableField<String>("")

    var listener: PointJoinTipDialog.OnTipBack? = null

    /**
     * 确认按钮
     */
    val confirmClick = View.OnClickListener {
        dialog.dismiss()
        listener?.onConfirmClick()
    }

    /**
     * 取消按钮
     */
    var cancelClick = View.OnClickListener {
        dialog.dismiss()
        listener?.onDismissClick()
    }
}