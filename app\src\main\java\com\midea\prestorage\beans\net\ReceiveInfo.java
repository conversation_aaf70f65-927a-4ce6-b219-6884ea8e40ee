package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.function.inv.response.PackageRelation;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class ReceiveInfo implements Serializable {

    private BigDecimal totalSkuQty;
    private BigDecimal receiptedSkuQty;
    private BigDecimal totalQty;
    private BigDecimal receiptedQty;
    private BigDecimal csPlQty;
    private String startReceiveDate;
    private List<ReceiveList> details;

    public BigDecimal getTotalSkuQty() {
        return totalSkuQty;
    }

    public void setTotalSkuQty(BigDecimal totalSkuQty) {
        this.totalSkuQty = totalSkuQty;
    }

    public BigDecimal getReceiptedSkuQty() {
        return receiptedSkuQty;
    }

    public void setReceiptedSkuQty(BigDecimal receiptedSkuQty) {
        this.receiptedSkuQty = receiptedSkuQty;
    }

    public BigDecimal getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public BigDecimal getReceiptedQty() {
        return receiptedQty;
    }

    public void setReceiptedQty(BigDecimal receiptedQty) {
        this.receiptedQty = receiptedQty;
    }

    public BigDecimal getCsPlQty() {
        return csPlQty;
    }

    public void setCsPlQty(BigDecimal csPlQty) {
        this.csPlQty = csPlQty;
    }

    public String getStartReceiveDate() {
        return startReceiveDate;
    }

    public void setStartReceiveDate(String startReceiveDate) {
        this.startReceiveDate = startReceiveDate;
    }

    public List<ReceiveList> getDetails() {
        return details;
    }

    public void setDetails(List<ReceiveList> details) {
        this.details = details;
    }

    public class ReceiveList {

        @ShowAnnotation
        private String custItemCode;
        @ShowAnnotation
        private String lotAtt04Str;
        @ShowAnnotation
        private String itemName;

        private String receiptCode;
        private String whCode;
        private String ownerCode;
        private String itemCode;
        private String lotAtt04;
        private String lotAtt01;
        private String lotAtt02;
        private String lotAtt03;
        private String lotAtt05;
        private BigDecimal totalQty;
        private BigDecimal receiptQty;
        private BigDecimal scanNum;
        private String custOrderLineNum;
        private String unit;
        private BigDecimal volume;
        private BigDecimal weight;
        private BigDecimal netWeight;
        private BigDecimal itemSuiteQty;
        private String itemClass;
        private String isNeedScan69;
        private List<PackageRelation> packageRelationList;

        private String whBarcode69;
        private String whCsBarcode69;

        private String cdpaFormat;

        public String getWhCsBarcode69() {
            return whCsBarcode69;
        }

        public void setWhCsBarcode69(String whCsBarcode69) {
            this.whCsBarcode69 = whCsBarcode69;
        }

        public String getCdpaFormat() {
            return cdpaFormat;
        }

        public void setCdpaFormat(String cdpaFormat) {
            this.cdpaFormat = cdpaFormat;
        }

        public List<PackageRelation> getPackageRelationList() {
            return packageRelationList;
        }

        public void setPackageRelationList(List<PackageRelation> packageRelationList) {
            this.packageRelationList = packageRelationList;
        }

        public String getReceiptCode() {
            return receiptCode;
        }

        public void setReceiptCode(String receiptCode) {
            this.receiptCode = receiptCode;
        }

        public String getWhCode() {
            return whCode;
        }

        public void setWhCode(String whCode) {
            this.whCode = whCode;
        }

        public String getOwnerCode() {
            return ownerCode;
        }

        public void setOwnerCode(String ownerCode) {
            this.ownerCode = ownerCode;
        }

        public String getCustItemCode() {
            return custItemCode;
        }

        public void setCustItemCode(String custItemCode) {
            this.custItemCode = custItemCode;
        }

        public String getItemCode() {
            return itemCode;
        }

        public void setItemCode(String itemCode) {
            this.itemCode = itemCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getLotAtt04() {
            return lotAtt04;
        }

        public void setLotAtt04(String lotAtt04) {
            this.lotAtt04 = lotAtt04;
        }

        public String getItemClass() {
            return itemClass;
        }

        public void setItemClass(String itemClass) {
            this.itemClass = itemClass;
        }

        public BigDecimal getTotalQty() {
            return totalQty;
        }

        public void setTotalQty(BigDecimal totalQty) {
            this.totalQty = totalQty;
        }

        public BigDecimal getReceiptQty() {
            return receiptQty;
        }

        public void setReceiptQty(BigDecimal receiptQty) {
            this.receiptQty = receiptQty;
        }

        public BigDecimal getScanNum() {
            return scanNum;
        }

        public void setScanNum(BigDecimal scanNum) {
            this.scanNum = scanNum;
        }

        public String getCustOrderLineNum() {
            return custOrderLineNum;
        }

        public void setCustOrderLineNum(String custOrderLineNum) {
            this.custOrderLineNum = custOrderLineNum;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public BigDecimal getVolume() {
            return volume;
        }

        public void setVolume(BigDecimal volume) {
            this.volume = volume;
        }

        public BigDecimal getWeight() {
            return weight;
        }

        public void setWeight(BigDecimal weight) {
            this.weight = weight;
        }

        public BigDecimal getNetWeight() {
            return netWeight;
        }

        public void setNetWeight(BigDecimal netWeight) {
            this.netWeight = netWeight;
        }

        public BigDecimal getItemSuiteQty() {
            return itemSuiteQty;
        }

        public void setItemSuiteQty(BigDecimal itemSuiteQty) {
            this.itemSuiteQty = itemSuiteQty;
        }

        public String getLotAtt04Str() {
            return lotAtt04Str;
        }

        public void setLotAtt04Str(String lotAtt04Str) {
            this.lotAtt04Str = lotAtt04Str;
        }

        public String getIsNeedScan69() {
            return isNeedScan69;
        }

        public void setIsNeedScan69(String isNeedScan69) {
            this.isNeedScan69 = isNeedScan69;
        }

        public String getLotAtt05() {
            return lotAtt05;
        }

        public void setLotAtt05(String lotAtt05) {
            this.lotAtt05 = lotAtt05;
        }

        public String getLotAtt01() {
            return lotAtt01;
        }

        public void setLotAtt01(String lotAtt01) {
            this.lotAtt01 = lotAtt01;
        }

        public String getLotAtt02() {
            return lotAtt02;
        }

        public void setLotAtt02(String lotAtt02) {
            this.lotAtt02 = lotAtt02;
        }

        public String getLotAtt03() {
            return lotAtt03;
        }

        public void setLotAtt03(String lotAtt03) {
            this.lotAtt03 = lotAtt03;
        }

        public String getWhBarcode69() {
            return whBarcode69;
        }

        public void setWhBarcode69(String whBarcode69) {
            this.whBarcode69 = whBarcode69;
        }
    }
}
