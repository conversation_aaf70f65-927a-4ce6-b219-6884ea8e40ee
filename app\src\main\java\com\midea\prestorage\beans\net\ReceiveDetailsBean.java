package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.beans.base.BaseItemForPopup;

import java.io.Serializable;
import java.util.List;

/**
 * @Author:wuqx14
 * @Date:2020/8/11
 * @Content:构造一个入库单号和波次号统一字段的item
 */
public class ReceiveDetailsBean implements Serializable {
    private int receivedQty;
    private int actualQty;
    private String containerOpenTime;
    private String createUserCode;

    private List<ReceiveDetailsListBean> containerDtos;

    public int getReceivedQty() {
        return receivedQty;
    }

    public void setReceivedQty(int receivedQty) {
        this.receivedQty = receivedQty;
    }

    public List<ReceiveDetailsListBean> getContainerDtos() {
        return containerDtos;
    }

    public void setContainerDtos(List<ReceiveDetailsListBean> containerDtos) {
        this.containerDtos = containerDtos;
    }

    public int getActualQty() {
        return actualQty;
    }

    public void setActualQty(int actualQty) {
        this.actualQty = actualQty;
    }

    public String getContainerOpenTime() {
        return containerOpenTime;
    }

    public void setContainerOpenTime(String containerOpenTime) {
        this.containerOpenTime = containerOpenTime;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public class ReceiveDetailsListBean extends BaseItemForPopup {
        @ShowAnnotation
        private String itemCode;
        @ShowAnnotation
        private String itemName;
        @ShowAnnotation
        private String batch;
        @ShowAnnotation
        private String inventoryStsName;
        @ShowAnnotation
        private String quantity;
        @ShowAnnotation
        private String expirationDate;
        private String receiptCode;
        private boolean isChecked;
        private String inventorySts;
        private String containerId;

        public String getItemCode() {
            return itemCode;
        }

        public void setItemCode(String itemCode) {
            this.itemCode = itemCode;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getBatch() {
            return batch;
        }

        public void setBatch(String batch) {
            this.batch = batch;
        }

        public String getInventorySts() {
            return inventorySts;
        }

        public void setInventorySts(String inventorySts) {
            this.inventorySts = inventorySts;
        }

        public boolean isChecked() {
            return isChecked;
        }

        public void setChecked(boolean checked) {
            isChecked = checked;
        }

        public String getContainerId() {
            return containerId;
        }

        public void setContainerId(String containerId) {
            this.containerId = containerId;
        }

        public String getQuantity() {
            return quantity;
        }

        public void setQuantity(String quantity) {
            this.quantity = quantity;
        }

        public String getExpirationDate() {
            return expirationDate;
        }

        public void setExpirationDate(String expirationDate) {
            this.expirationDate = expirationDate;
        }

        public String getInventoryStsName() {
            return inventoryStsName;
        }

        public void setInventoryStsName(String inventoryStsName) {
            this.inventoryStsName = inventoryStsName;
        }

        public String getReceiptCode() {
            return receiptCode;
        }

        public void setReceiptCode(String receiptCode) {
            this.receiptCode = receiptCode;
        }
    }
}
