package com.midea.prestorage.beans.net;

import androidx.annotation.Nullable;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.utils.LotAttUnit;

import java.io.Serializable;
import java.math.BigDecimal;

public class ShippingDetailsInfo implements Serializable {

    @ShowAnnotation
    private int index;
    @ShowAnnotation
    private String whBarcode69;
    @ShowAnnotation
    private String custItemCode;
    @ShowAnnotation
    private String status;
    @ShowAnnotation
    private String itemName;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal planQty;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal pickQty;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal oqcQty;
    @ShowAnnotation
    private String createUserCode;
    @ShowAnnotation
    private String createTime;
    @ShowAnnotation
    private String oqcBy;
    @ShowAnnotation
    private String oqcEndTime;
    @ShowAnnotation
    private String pickContainerCode;
    @ShowAnnotation
    private String itemCodeStr;

    private String shipmentCode;
    private String itemCode;
    @Nullable
    private BigDecimal planCsQty;
    @Nullable
    private BigDecimal planEaQty;
    @Nullable
    private BigDecimal pickCsQty;
    @Nullable
    private BigDecimal pickEaQty;
    @Nullable
    private BigDecimal oqcCsQty;
    @Nullable
    private BigDecimal oqcEaQty;
    @Nullable
    private String csUnitDesc;
    @Nullable
    private String eaUnitDesc;

    @ShowAnnotation
    private String planQtyStr;
    @ShowAnnotation
    private String pickQtyStr;
    @ShowAnnotation
    private String oqcQtyStr;

    public String getPlanQtyStr() {
        return planQtyStr;
    }

    public void setPlanQtyStr(String planQtyStr) {
        this.planQtyStr = planQtyStr;
    }

    public String getPickQtyStr() {
        return pickQtyStr;
    }

    public void setPickQtyStr(String pickQtyStr) {
        this.pickQtyStr = pickQtyStr;
    }

    public String getOqcQtyStr() {
        return oqcQtyStr;
    }

    public void setOqcQtyStr(String oqcQtyStr) {
        this.oqcQtyStr = oqcQtyStr;
    }

    @Nullable
    public BigDecimal getPlanCsQty() {
        return planCsQty;
    }

    public void setPlanCsQty(@Nullable BigDecimal planCsQty) {
        this.planCsQty = planCsQty;
    }

    @Nullable
    public BigDecimal getPlanEaQty() {
        return planEaQty;
    }

    public void setPlanEaQty(@Nullable BigDecimal planEaQty) {
        this.planEaQty = planEaQty;
    }

    @Nullable
    public BigDecimal getPickCsQty() {
        return pickCsQty;
    }

    public void setPickCsQty(@Nullable BigDecimal pickCsQty) {
        this.pickCsQty = pickCsQty;
    }

    @Nullable
    public BigDecimal getPickEaQty() {
        return pickEaQty;
    }

    public void setPickEaQty(@Nullable BigDecimal pickEaQty) {
        this.pickEaQty = pickEaQty;
    }

    @Nullable
    public BigDecimal getOqcCsQty() {
        return oqcCsQty;
    }

    public void setOqcCsQty(@Nullable BigDecimal oqcCsQty) {
        this.oqcCsQty = oqcCsQty;
    }

    @Nullable
    public BigDecimal getOqcEaQty() {
        return oqcEaQty;
    }

    public void setOqcEaQty(@Nullable BigDecimal oqcEaQty) {
        this.oqcEaQty = oqcEaQty;
    }

    @Nullable
    public String getCsUnitDesc() {
        return csUnitDesc;
    }

    public void setCsUnitDesc(@Nullable String csUnitDesc) {
        this.csUnitDesc = csUnitDesc;
    }

    @Nullable
    public String getEaUnitDesc() {
        return eaUnitDesc;
    }

    public void setEaUnitDesc(@Nullable String eaUnitDesc) {
        this.eaUnitDesc = eaUnitDesc;
    }
    private String whCsBarcode69;

    public String getItemCodeStr() {
        return LotAttUnit.formatWhBarcode69(whCsBarcode69, whBarcode69);
    }

    public void setItemCodeStr(String itemCodeStr) {
        this.itemCodeStr = itemCodeStr;
    }

    public String getShipmentCode() {
        return shipmentCode;
    }

    public void setShipmentCode(String shipmentCode) {
        this.shipmentCode = shipmentCode;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getWhBarcode69() {
        return whBarcode69;
    }

    public void setWhBarcode69(String whBarcode69) {
        this.whBarcode69 = whBarcode69;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getOqcBy() {
        return oqcBy;
    }

    public void setOqcBy(String oqcBy) {
        this.oqcBy = oqcBy;
    }

    public String getOqcEndTime() {
        return oqcEndTime;
    }

    public void setOqcEndTime(String oqcEndTime) {
        this.oqcEndTime = oqcEndTime;
    }

    public String getPickContainerCode() {
        return pickContainerCode;
    }

    public void setPickContainerCode(String pickContainerCode) {
        this.pickContainerCode = pickContainerCode;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public BigDecimal getPlanQty() {
        return planQty;
    }

    public void setPlanQty(BigDecimal planQty) {
        this.planQty = planQty;
    }

    public BigDecimal getPickQty() {
        return pickQty;
    }

    public void setPickQty(BigDecimal pickQty) {
        this.pickQty = pickQty;
    }

    public BigDecimal getOqcQty() {
        return oqcQty;
    }

    public void setOqcQty(BigDecimal oqcQty) {
        this.oqcQty = oqcQty;
    }
}