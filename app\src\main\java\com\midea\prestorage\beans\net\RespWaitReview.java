package com.midea.prestorage.beans.net;

import androidx.annotation.Nullable;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.math.BigDecimal;

public class RespWaitReview {
    @ShowAnnotation
    private int index;
    private String pickContainerCode;
    @ShowAnnotation
    private String containerCode;
    private String status;
    @ShowAnnotation
    private String waveNo;
    @ShowAnnotation
    private String closeContainerTime;
    @ShowAnnotation
    private String pickUserCode;
    @ShowAnnotation
    private String pickUserName;
    @ShowAnnotation
    private BigDecimal pickedCsQty;
    @ShowAnnotation
    private BigDecimal pickedEaQty;
    @ShowAnnotation
    private String statusName;
    @ShowAnnotation
    private String oqcBy;
    @ShowAnnotation
    private String oqcByName;
    private String id;
    @Nullable
    @ShowAnnotation
    private BigDecimal prePackageNum;
    @Nullable
    @ShowAnnotation
    private String shippingLoc;
    @Nullable
    @ShowAnnotation
    private String prePackageUnit;
    @Nullable
    private String prePackage;
    @Nullable
    @ShowAnnotation
    private String shipToCustomerName;

    @Nullable
    public String getShipToCustomerName() {
        return shipToCustomerName;
    }

    public void setShipToCustomerName(@Nullable String shipToCustomerName) {
        this.shipToCustomerName = shipToCustomerName;
    }

    @Nullable
    public String getPrePackage() {
        return prePackage;
    }

    public void setPrePackage(@Nullable String prePackage) {
        this.prePackage = prePackage;
    }

    @Nullable
    public String getPrePackageUnit() {
        return prePackageUnit;
    }

    public void setPrePackageUnit(@Nullable String prePackageUnit) {
        this.prePackageUnit = prePackageUnit;
    }

    @Nullable
    public BigDecimal getPrePackageNum() {
        return prePackageNum;
    }

    public void setPrePackageNum(@Nullable BigDecimal prePackageNum) {
        this.prePackageNum = prePackageNum;
    }

    @Nullable
    public String getShippingLoc() {
        return shippingLoc;
    }

    public void setShippingLoc(@Nullable String shippingLoc) {
        this.shippingLoc = shippingLoc;
    }

    public String getOqcByName() {
        return oqcByName;
    }

    public void setOqcByName(String oqcByName) {
        this.oqcByName = oqcByName;
    }

    public String getPickUserName() {
        return pickUserName;
    }

    public void setPickUserName(String pickUserName) {
        this.pickUserName = pickUserName;
    }

    public String getOqcBy() {
        return oqcBy;
    }

    public void setOqcBy(String oqcBy) {
        this.oqcBy = oqcBy;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getPickContainerCode() {
        return pickContainerCode;
    }

    public void setPickContainerCode(String pickContainerCode) {
        this.pickContainerCode = pickContainerCode;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getCloseContainerTime() {
        return closeContainerTime;
    }

    public void setCloseContainerTime(String closeContainerTime) {
        this.closeContainerTime = closeContainerTime;
    }

    public String getPickUserCode() {
        return pickUserCode;
    }

    public void setPickUserCode(String pickUserCode) {
        this.pickUserCode = pickUserCode;
    }

    public BigDecimal getPickedCsQty() {
        return pickedCsQty;
    }

    public void setPickedCsQty(BigDecimal pickedCsQty) {
        this.pickedCsQty = pickedCsQty;
    }

    public BigDecimal getPickedEaQty() {
        return pickedEaQty;
    }

    public void setPickedEaQty(BigDecimal pickedEaQty) {
        this.pickedEaQty = pickedEaQty;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
