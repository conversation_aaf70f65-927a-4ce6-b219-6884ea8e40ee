package com.midea.prestorage.function.pick.fragment;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.midea.prestoragesaas.R;
import com.midea.prestorage.base.adapter.CommonAdapter;
import com.midea.prestorage.beans.net.OutPickPoolStorageDetail;
import com.midea.prestorage.http.constants.Constants;

public class PickPoolAdapter extends CommonAdapter<OutPickPoolStorageDetail.DetailResponses> {
    private boolean isStart;
    private boolean isStarted;
    private Context mContext;

    public PickPoolAdapter(Context context, int layoutResId) {
        super(layoutResId);
        mContext = context;

        addChildClickViewIds(R.id.tv_start_pick);
    }

    @Override
    protected void convert(BaseViewHolder helper, OutPickPoolStorageDetail.DetailResponses item) {
        super.convert(helper, item);

        if (isStarted) {
            helper.setVisible(R.id.tv_start_pick, false);
            helper.setVisible(R.id.tv_pick_status, false);
            return;
        }
        View itemView = helper.itemView.findViewById(R.id.ll_item);
        TextView tvStartPick = helper.itemView.findViewById(R.id.tv_start_pick);
        if (!isStart) {
            tvStartPick.setBackground(ContextCompat.getDrawable(mContext, R.drawable.bg_bt_gray));
            itemView.setBackgroundColor(ContextCompat.getColor(mContext, R.color.colorBg));
            tvStartPick.setEnabled(false);
        } else {
            //if (!TextUtils.isEmpty(item.getConfirmBy()) && !Constants.INSTANCE.getUserInfo().getName().equals(item.getConfirmBy())) {
            if (!TextUtils.isEmpty(item.getConfirmBy()) && !Constants.Companion.getUserInfo().getName().equals(item.getConfirmBy())) {
                tvStartPick.setBackground(ContextCompat.getDrawable(mContext, R.drawable.bg_bt_gray));
                tvStartPick.setEnabled(false);
            } else {
                tvStartPick.setBackground(ContextCompat.getDrawable(mContext, R.drawable.bg_bt_blue));
                tvStartPick.setEnabled(true);

                if (item.getStatus() == 100) {
                    tvStartPick.setText("开始拣货");
                    tvStartPick.setBackground(ContextCompat.getDrawable(mContext, R.drawable.bg_bt_blue));
                    itemView.setBackgroundColor(ContextCompat.getColor(mContext, R.color.colorWhite));
                    helper.setText(R.id.tv_pick_status, "待拣货");
                } else if (item.getStatus() == 300) {
                    tvStartPick.setText("结束拣货");
                    tvStartPick.setBackground(ContextCompat.getDrawable(mContext, R.drawable.bg_bt_yellow));
                    itemView.setBackgroundColor(ContextCompat.getColor(mContext, R.color.colorWhite));
                    helper.setText(R.id.tv_pick_status, "拣货中");
                } else {
                    tvStartPick.setText("拣货完成");
                    tvStartPick.setBackground(ContextCompat.getDrawable(mContext, R.drawable.bg_bt_gray));
                    itemView.setBackgroundColor(ContextCompat.getColor(mContext, R.color.colorWhite));
                    helper.setText(R.id.tv_pick_status, "完成拣货");
                    tvStartPick.setEnabled(false);
                }
            }
        }
    }

    public void setStart(boolean start) {
        isStart = start;
    }

    public void setStarted(boolean started) {
        isStarted = started;
    }
}