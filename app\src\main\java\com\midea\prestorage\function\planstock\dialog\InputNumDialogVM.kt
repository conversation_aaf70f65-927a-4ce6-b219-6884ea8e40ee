package com.midea.prestorage.function.planstock.dialog

import android.annotation.SuppressLint
import android.graphics.Color
import android.text.TextUtils
import android.view.View
import androidx.core.content.ContextCompat
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import com.bigkoo.pickerview.TimePickerView
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.net.OutStorageScan
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.LotAttUnit
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.utils.isNull
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.midea.prestoragesaas.R
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.util.*

class InputNumDialogVM(val dialog: InputNumDialog) {

    val title = ObservableField<String>("盘点数量录入确认")
    val itemName = ObservableField<String>("")
    val explain = ObservableField<String>("")
    val goodsNo = ObservableField<String>("")
    val isShowLot = ObservableBoolean(false)
    val lotAtt01 = ObservableField<String>("")
    val lotAtt02 = ObservableField<String>("")
    val lotAtt05 = ObservableField<String>("")
    val itemCode = ObservableField<String>("")
    val itemCode69 = ObservableField<String>("")

    @SuppressLint("SimpleDateFormat")
    var format = SimpleDateFormat("yyyy-MM-dd")

    fun show() {
        itemName.set(dialog.deleteInfo?.itemName)
        itemCode.set(
            dialog.deleteInfo?.custItemCode
        )
        if (dialog.deleteInfo?.whCsBarcode69.isNullOrEmpty() && dialog.deleteInfo?.whBarcode69.isNullOrEmpty()) {
            dialog.binding.tvCarcode69.visibility  = View.GONE
        } else {
            dialog.binding.tvCarcode69.visibility  = View.VISIBLE
            itemCode69.set(
                LotAttUnit.formatWhBarcode69(dialog.deleteInfo?.whCsBarcode69, dialog.deleteInfo?.whBarcode69)
            )
        }
    }

    val goodsEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                if (!TextUtils.isEmpty(goodsNo.get())) {
                    confirm()
                }
            }
        }
    }

    fun close() {
        dialog.inputBack?.inputFail()
        dialog.dismiss()
    }

    fun confirm() {
        var num = BigDecimal.ZERO
        dialog.adapterNumber.getData().forEach {
            try {
                if (!it.num.isNullOrEmpty() && it.cdprQuantity?.compareTo(BigDecimal.ZERO) == 1) {
                    num += AppUtils.getBigDecimalValue(it.num)
                        .multiply(it.cdprQuantity)
                }
            } catch (e: NumberFormatException) {
                AppUtils.showToast(dialog.mContext, "数量格式错误")
            }
        }
        if (!AppUtils.isZero(num)) {
            goodsNo.set(AppUtils.getBigDecimalValueStr(num))
        }
        if (goodsNo.get().isNullOrEmpty()) {
            if (dialog.isEnterKeyPress) {
                AppUtils.showToast(dialog.mContext, "新增盘盈商品，【盘点数量】必须 > 0 !")
            } else {
                AppUtils.showToast(dialog.mContext, "请输入数量!")
            }
            return
        }
        if (!dialog.existTaskDetail && dialog.deleteInfo?.isValidity?.toLowerCase(Locale.ROOT) == "y") {
            if (lotAtt01.get().toString().isEmpty()) {
                AppUtils.showToast(dialog.mContext, "效期商品，请选择生产日期!")
                return
            }
            if (lotAtt02.get().toString().isEmpty()) {
                AppUtils.showToast(dialog.mContext, "效期商品，请选择失效日期!")
                return
            }
        }
        dialog.inputBack?.inputOk(
            dialog.deleteInfo!!,
            AppUtils.getBigDecimalValue(goodsNo.get()),
            dialog.isEnterKeyPress,
            dialog.binding.addSwitch.isChecked,
            lotAtt01.get().toString().trim(),
            lotAtt02.get().toString().trim(),
            lotAtt05.get().toString().trim()
        )
        dialog.dismiss()
    }

    fun showTimePick(tag: Int) {
        val pvTime = TimePickerView.Builder(
            dialog.mContext,
            TimePickerView.OnTimeSelectListener { date2, _ ->
                when (tag) {
                    1 -> {
                        lotAtt01.set(format.format(date2))
                        calculateDate(lotAtt01.get())
                    }
                    2 -> {
                        lotAtt02.set(format.format(date2))
                        calculateDateAfter(lotAtt02.get())
                    }
                }
            })
        var title: String? = null
        when (tag) {
            1 -> title = "生产日期"
            2 -> title = "失效日期"
        }
        pvTime.setType(TimePickerView.Type.YEAR_MONTH_DAY)//默认全部显示
            .setCancelText("取消")//取消按钮文字
            .setSubmitText("确定")//确认按钮文字
            .setContentSize(20)//滚轮文字大小
            .setTitleSize(20)//标题文字大小
            .setOutSideCancelable(true)//点击屏幕，点在控件外部范围时，是否取消显示
            .setTitleText(title)
            .setTextColorCenter(Color.BLACK)//设置选中项的颜色
            .setTitleColor(Color.BLACK)//标题文字颜色
            .setSubmitColor(ContextCompat.getColor(dialog.mContext, R.color.colorBlue))//确定按钮文字颜色
            .setCancelColor(ContextCompat.getColor(dialog.mContext, R.color.colorOrange))//取消按钮文字颜色
            .isDialog(true)
            .setDate(Calendar.getInstance())
            .isCenterLabel(false) //是否只显示中间选中项的label文字，false则每项item全部都带有label。
            .build()

        when (tag) {
            1 -> {
                val startDate = Calendar.getInstance()
                startDate.set(1900, 10, 30)
                val endDate = Calendar.getInstance()
                pvTime.setRangDate(startDate, endDate)
            }
            2 -> {
                val startDate = Calendar.getInstance()
                val endDate = Calendar.getInstance()
                endDate.add(Calendar.YEAR, 100)
                pvTime.setRangDate(startDate, endDate)
            }
            else -> {
                return
            }
        }

        pvTime.build().show()
    }

    private fun calculateDate(lotAtt01: String?) {
        dialog.deleteInfo?.let { bean ->
            if (bean?.periodOfValidity.isNull()) {
                return
            }
            if (bean?.isValidity?.toLowerCase(Locale.ROOT) == "y") {
                if (!bean.validityUnit.isNullOrEmpty()) {
                    if (!lotAtt01.isNullOrEmpty()) {
                        val cale = Calendar.getInstance()
                        cale.time = format.parse(lotAtt01)
                        when {
                            bean.validityUnit?.toLowerCase(Locale.ROOT) == "y" -> {
                                cale.add(Calendar.YEAR, bean.periodOfValidity.toInt())
                            }
                            bean.validityUnit?.toLowerCase(Locale.ROOT) == "m" -> {
                                cale.add(
                                    Calendar.MONTH,
                                    AppUtils.getBigDecimalValue(bean.periodOfValidity).toInt()
                                )
                            }
                            bean.validityUnit?.toLowerCase(Locale.ROOT) == "w" -> {
                                cale.add(
                                    Calendar.WEEK_OF_YEAR,
                                    bean.periodOfValidity.toInt()
                                )
                            }
                            bean.validityUnit?.toLowerCase(Locale.ROOT) == "d" -> {
                                cale.add(Calendar.DATE, bean.periodOfValidity.toInt())
                            }
                        }
                        lotAtt02.set(format.format(cale.time))
                    }
                }
            }
        }
    }

    private fun calculateDateAfter(lotAtt02: String?) {
        dialog.deleteInfo?.let { bean ->
            if (bean?.periodOfValidity.isNull()) {
                return
            }
            if (bean?.isValidity?.toLowerCase(Locale.ROOT) == "y") {
                if (!bean.validityUnit.isNullOrEmpty()) {
                    if (!lotAtt02.isNullOrEmpty()) {
                        val cale = Calendar.getInstance()
                        cale.time = format.parse(lotAtt02)
                        when {
                            bean.validityUnit?.toLowerCase(Locale.ROOT) == "y" -> {
                                cale.add(Calendar.YEAR, -bean.periodOfValidity.toInt())
                            }
                            bean.validityUnit?.toLowerCase(Locale.ROOT) == "m" -> {
                                cale.add(
                                    Calendar.MONTH,
                                    -AppUtils.getBigDecimalValue(bean.periodOfValidity).toInt()
                                )
                            }
                            bean.validityUnit?.toLowerCase(Locale.ROOT) == "w" -> {
                                cale.add(
                                    Calendar.WEEK_OF_YEAR,
                                    -bean.periodOfValidity.toInt()
                                )
                            }
                            bean.validityUnit?.toLowerCase(Locale.ROOT) == "d" -> {
                                cale.add(Calendar.DATE, -bean.periodOfValidity.toInt())
                            }
                        }
                        lotAtt01.set(format.format(cale.time))
                    }
                }
            }
        }
    }
}