package com.midea.prestorage.function.pointjoin.fragment

import android.text.TextUtils
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.PointDivideDetailBean
import com.midea.prestorage.beans.setting.HandingInfoDb
import com.midea.prestorage.function.instorage.response.HandlingGroup
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.function.pointjoin.PointJoinDetailActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.DbUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import okhttp3.MediaType
import okhttp3.RequestBody
import org.xutils.db.sqlite.WhereBuilder

class PointJoinDialogFragmentVM(val fragment: PointJoinDialogFragment) {

    val custOrderNo = ObservableField("")
    val waveNo = ObservableField("")
    val shipmentCode = ObservableField("")
    val handlingGroupName = ObservableField("")

    var bean: PointDivideDetailBean? = null
    var handlingBean: HandlingGroup? = null

    var netApplyDC: MutableList<DCBean>? = null
    var cNum: Int? = 0
    private var allNum: Int? = 0

    private lateinit var tipDialog: PointJoinTipDialog
    val db = DbUtils.db

    fun init() {
        DCUtils.netApply(fragment.activity as RxAppCompatActivity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                netApplyDC = statusDC
                combineDCInfo()
            }
        })
        if (bean == null) {
            bean = fragment.arguments?.getSerializable("bean") as PointDivideDetailBean?
        }
        cNum = fragment.arguments?.getInt("currentNum", 0)
        allNum = fragment.arguments?.getInt("allNum", 0)
        if (bean != null) {
            fragment.setTvData(bean!!)
            fragment.showRv(bean?.engineerSignDetailResponses)

            val shippingWay = bean?.engineerSignResponse?.shippingWay
            if (shippingWay == "ZT" || shippingWay == "DOT" || shippingWay == "DELIVERY") {
                fragment.isZT(true, bean?.engineerSignResponse?.isDisable == 1)
            } else {
                fragment.isZT(zt = false, showVeryCode = false)
            }
            combineDCInfo()

            if (bean!!.engineerSignResponse.engineerStatus == "950"
                || bean!!.engineerSignResponse.engineerStatus == "900"
                || bean!!.engineerSignResponse.engineerStatus == "701"
            ) {
                fragment.setConfirmUnable()
            } else {
                fragment.setConfirmEnable()
            }
        }
        fragment.setCurrentNum((cNum!! + 1).toString())
        fragment.setTotalNum(allNum.toString())

        tipDialog = PointJoinTipDialog(fragment.activity as BaseActivity)
        tipDialog.setTitle("提示")
        tipDialog.setMsg("该客户订单还存在其他出库单,请选择是否批量完成提货。")
        tipDialog.setOnTipBackListener(object : PointJoinTipDialog.OnTipBack {
            override fun onConfirmClick() {
                confirm("1")
            }

            override fun onDismissClick() {
                confirmLy()
            }
        })

        initHandlingGroupList()
        initHandling()
    }

    private fun combineDCInfo() {
        if (bean != null && !TextUtils.isEmpty(bean!!.engineerSignResponse.engineerStatus)) {
            if (netApplyDC != null) {
                val result =
                    netApplyDC!!.filter { it.value.toString() == bean!!.engineerSignResponse.engineerStatus }
                fragment.setTvStatue(result[0].key)
            }
        }
    }

    var isRevers = false
    val inputTextChange = object : ViewBindingAdapter.TextChangedListener {

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            if (isRevers) {
                fragment.editChange(TextUtils.isEmpty(s))
                isRevers = false
            } else {
                fragment.editChange(!TextUtils.isEmpty(s))
            }
        }
    }

    fun confirmLy() {
        val verify = fragment.getVerify()
        if (bean?.engineerSignResponse?.shippingWay == "ZT" && bean?.engineerSignResponse?.isDisable == 1) {
            if (TextUtils.isEmpty(verify) || verify!!.length != 4) {
                ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "请完成验证码输入!")
                return
            }
        }
        fragment.waitingDialogHelp.showDialog()
        val param = mutableMapOf(
            "userCode" to Constants.userInfo?.name,
            "whCode" to (fragment.activity as BaseActivity).getWhCode(),
            "shipmentCode" to bean?.engineerSignResponse?.shipmentCode,
            "engInnerId" to bean?.engineerSignResponse?.enginnerId,
            "shipBy" to Constants.userInfo?.name,
            "verificationCode" to fragment.getVerify()
        )

        if (handlingBean == null) {
            AppUtils.showToast(fragment.requireActivity(), "请先选择装卸组!")
            selectHandlingGroup()
            return
        } else {
            param["handingGroupCode"] = handlingBean?.handlingCode
            param["handingGroupName"] = handlingBean?.handlingName
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .engineerConfirm(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: Any?) {
                    fragment.waitingDialogHelp.hidenDialog()
                    if (allNum == 1) {
                        (fragment.activity as BaseActivity).finish()
                        AppUtils.showToast(fragment.activity as BaseActivity, "确认成功!")
                        return
                    }
                    bean!!.engineerSignResponse.engineerStatus = "900"
                    ToastUtils.getInstance().showSuccessToastWithSound(fragment.activity, "确认成功!")
                    (fragment.activity as PointJoinDetailActivity).next()
                    fragment.setTvStatue("交接完成")
                    fragment.setConfirmBy(Constants.userInfo?.name)
                    bean?.engineerSignResponse?.confirmBy = Constants.userInfo?.name
                    fragment.setConfirmUnable()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    if (statusCode == 603019L) {
                        fragment.cleanVerifyCode()
                    }
                    fragment.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showErrorToastWithSound(
                        fragment.activity as RxAppCompatActivity,
                        apiErrorModel.message
                    )
                }
            })
    }

    fun confirmClick() {
        if (CheckUtil.isFastDoubleClick()) {
            confirm(null)
        }
    }

    fun confirm(checkFlag: String? = null) {
        val verify = fragment.getVerify()
        if (bean?.engineerSignResponse?.shippingWay == "ZT" && bean?.engineerSignResponse?.isDisable == 1) {
            if (TextUtils.isEmpty(verify) || verify!!.length != 4) {
                ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "请完成验证码输入!")
                return
            }
        }
        fragment.waitingDialogHelp.showDialog()
        val param = mutableMapOf(
            "userCode" to Constants.userInfo?.name,
            "whCode" to (fragment.activity as BaseActivity).getWhCode(),
            "shipmentCode" to bean?.engineerSignResponse?.shipmentCode?.trim(),
            "engInnerId" to bean?.engineerSignResponse?.enginnerId,
            "shipBy" to Constants.userInfo?.name,
            "verificationCode" to fragment.getVerify(),
            "checkFlag" to checkFlag
        )

        if (handlingBean == null) {
            AppUtils.showToast(fragment.requireActivity(), "请先选择装卸组!")
            selectHandlingGroup()
            return
        } else {
            param["handingGroupCode"] = handlingBean?.handlingCode
            param["handingGroupName"] = handlingBean?.handlingName
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .checkConfirm(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: Any?) {
                    fragment.waitingDialogHelp.hidenDialog()
                    if (allNum == 1) {
                        (fragment.activity as BaseActivity).finish()
                        AppUtils.showToast(fragment.activity as BaseActivity, "确认成功!")
                        return
                    }
                    if (checkFlag == "1") {
                        (fragment.activity as BaseActivity).finish()
                        return
                    }
                    bean!!.engineerSignResponse.engineerStatus = "900"
                    ToastUtils.getInstance().showSuccessToastWithSound(fragment.activity, "确认成功!")
                    (fragment.activity as PointJoinDetailActivity).next()
                    fragment.setTvStatue("交接完成")
                    fragment.setConfirmBy(Constants.userInfo?.name)
                    bean?.engineerSignResponse?.confirmBy = Constants.userInfo?.name
                    fragment.setConfirmUnable()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    if (statusCode == 603019L) {
                        fragment.cleanVerifyCode()
                    }

                    if (statusCode == 603098L) {
                        tipDialog.show()
                        return
                    }
                    fragment.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showErrorToastWithSound(
                        fragment.activity as RxAppCompatActivity,
                        apiErrorModel.message
                    )
                }
            })
    }

    fun cancel() {
        fragment.waitingDialogHelp.showDialog()
        val param = mutableMapOf(
            "whCode" to (fragment.activity as BaseActivity).getWhCode(),
            "shipmentCode" to bean?.engineerSignResponse?.shipmentCode,
            "engInnerId" to bean?.engineerSignResponse?.enginnerId,
            "shipBy" to Constants.userInfo?.name
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .engineerCancel(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: Any?) {
                    fragment.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showSuccessToastWithSound(fragment.activity, "驳回成功!")
                    (fragment.activity as PointJoinDetailActivity).next()
                    fragment.setTvStatue("已驳回")
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    fragment.waitingDialogHelp.hidenDialog()
                    AppUtils.showToast(
                        fragment.activity as RxAppCompatActivity,
                        apiErrorModel.message
                    )
                }
            })
    }

    private fun initHandlingGroupList() {
        RetrofitHelper.getBasicDataAPI()
            .queryPageHandlingGroup(Constants.tenantCode, (fragment.activity as BaseActivity).getWhCode(), "02")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<PageResult<HandlingGroup>>(fragment.activity as BaseActivity) {
                override fun success(data: PageResult<HandlingGroup>?) {
                    if (data != null && data.list != null && data.list.size > 0) {
                        val beans = mutableListOf<BaseItemShowInfo>()
                        data.list.forEach {
                            it.showInfo = it.handlingName
                            if (!it.showInfo.isNullOrBlank()) {
                                beans.add(it)
                            }
                        }
                        if (Constants.whInfo!!.whCode == "W00514") {
                            val handlingGroup = HandlingGroup()
                            handlingGroup.showInfo = "虚拟装卸组"
                            handlingGroup.handlingCode = "zx0001"
                            handlingGroup.handlingName = "虚拟装卸组"
                            beans.add(0, handlingGroup)
                        }

                        if (beans.isEmpty()) {
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(fragment.activity, "必须配置装卸组!")
                            return
                        }

                        fragment.handlingGroupDialog.addAllData(beans)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(fragment.activity, apiErrorModel.message)
                }
            })
    }

    private fun initHandling() {
        Observable.create<HandingInfoDb> {
            var handingInfo =
                db.selector(HandingInfoDb::class.java).where("userId", "==", Constants.userInfo?.id)
                    .and(WhereBuilder.b("mode", "==", 3))
                    .findFirst()

            if (handingInfo == null) {
                it.onNext(HandingInfoDb())
            } else {
                it.onNext(handingInfo)
            }
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as BaseActivity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<HandingInfoDb> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: HandingInfoDb) {
                    if (t.userId != null) {
                        handlingBean = HandlingGroup()
                        handlingBean!!.handlingName = t.handlingName
                        handlingBean!!.handlingCode = t.handlingCode
                        handlingGroupName.set(t.handlingName)
                    }
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    fun selectHandlingGroup() {
        fragment.handlingGroupDialog.show()
    }
}