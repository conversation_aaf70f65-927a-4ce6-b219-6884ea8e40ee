package com.midea.prestorage.function.containerpick

import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestoragesaas.databinding.ActivityOutContainerPickThirdBinding
import com.midea.prestorage.function.containerpick.adapter.TaskGridAdapter
import com.midea.prestorage.function.main.ImageAdapter
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.FilterDigitTextWatcher

class ContainerPickThirdActivity : BaseActivity() {

    lateinit var binding: ActivityOutContainerPickThirdUnionBinding
    private var vm = ContainerPickThirdVM(this)
    val adapterNumber = TaskGridAdapter(this)
    var textWatcher: FilterDigitTextWatcher? = null
    var textWatcher02: FilterDigitTextWatcher? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityOutContainerPickThirdUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_out_container_pick_third_care
                )
            )
        } else {
            ActivityOutContainerPickThirdUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_out_container_pick_third
                )
            )
        }
        binding.vm = vm

        binding.gridNumber.adapter = adapterNumber

        initWatcher()
        vm.init()
    }

    private fun initWatcher() {
        if (textWatcher == null) {
            textWatcher = FilterDigitTextWatcher(binding.edEa, 0, true) {
                if ("只能输入正整数" == it) {
                    binding.edEa.setText(binding.edEa.text.toString().replace(".", ""))
                    if (binding.edEa.text!!.isNotEmpty()) {
                        binding.edEa.setSelection(binding.edEa.text!!.length)
                    }
                }
                ToastUtils.getInstance().showErrorToastWithSound(this, it)
            }
        }

        if (textWatcher02 == null) {
            textWatcher02 = FilterDigitTextWatcher(binding.edEaSecond, 0, true) {
                if ("只能输入正整数" == it) {
                    binding.edEaSecond.setText(binding.edEaSecond.text.toString().replace(".", ""))
                    if (binding.edEaSecond.text!!.isNotEmpty()) {
                        binding.edEaSecond.setSelection(binding.edEaSecond.text!!.length)
                    }
                }
                ToastUtils.getInstance().showErrorToastWithSound(this, it)
            }
        }

    }

    fun hideGv() {
        binding.gridNumber.visibility = View.GONE
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun csRequestFocus() {
        binding.edCs.post {
            binding.edCs.requestFocus()
            binding.edCs.setSelection(binding.edCs.text.toString().length)
        }
    }

    fun eaRequestFocus() {
        val editText = when {
            binding.llIp.visibility == View.VISIBLE -> binding.edIp
            binding.llEa.visibility == View.VISIBLE -> binding.edEa
            binding.llEaSecond.visibility == View.VISIBLE -> binding.edEaSecond
            else -> null
        }
        editText?.post {
            editText.requestFocus()
            editText.setSelection(editText.text.toString().length)
        }
    }

    fun ipRequestFocus() {
        val editText = if (binding.llEa.visibility == View.VISIBLE) {
            binding.edEa
        } else {
            binding.edEaSecond
        }
        editText.post {
            editText.requestFocus()
            editText.setSelection(editText.text.toString().length)
        }
    }

    fun csEnable(isEnable: Boolean) {
        binding.edCs.isEnabled = isEnable
    }

    fun ipEnable(isEnable: Boolean) {
        binding.edIp.isEnabled = isEnable
    }

}