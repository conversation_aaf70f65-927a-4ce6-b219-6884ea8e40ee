package com.midea.prestorage.beans.net;

import android.text.TextUtils;

import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.beans.base.BaseItemForPopup;

import java.math.BigDecimal;

public class OutPoolStorageList extends BaseItemForPopup {

    @ShowAnnotation
    private String shipmentCode;
    @ShowAnnotation
    private String shipmentTypeStr;
    @ShowAnnotation
    private String statusStr;
    @ShowAnnotation
    private String custOrderCode;
    @ShowAnnotation
    private String waveNo;
    @ShowAnnotation
    private BigDecimal totalQty;
    @ShowAnnotation
    private String dispatchNo;
    @ShowAnnotation
    private String netEngineerName;
    @ShowAnnotation
    private String netEngineerMobile;

    private String id;
    private int status;
    private String shipmentType;
    private String businessType;

    private String unscanMark;

    String whCode;
    String ownerCode;
    String siteCode;
    String siteName;

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getUnscanMark() {
        return unscanMark;
    }

    public void setUnscanMark(String unscanMark) {
        this.unscanMark = unscanMark;
    }

    public String getShipmentCode() {
        return shipmentCode;
    }

    public void setShipmentCode(String shipmentCode) {
        this.shipmentCode = shipmentCode;
    }

    public String getShipmentType() {
        return shipmentType;
    }

    public void setShipmentType(String shipmentType) {
        this.shipmentType = shipmentType;
    }

    public String getCustOrderCode() {
        return custOrderCode;
    }

    public void setCustOrderCode(String custOrderCode) {
        this.custOrderCode = custOrderCode;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public int getTotalQty() {
        if (totalQty == null) {
            return 0;
        }
        return totalQty.intValue();
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public String getShipmentTypeStr() {
        if (!TextUtils.isEmpty(businessType)) {
            return shipmentTypeStr + "/" + businessType;
        }
        return shipmentTypeStr;
    }

    public void setShipmentTypeStr(String shipmentTypeStr) {
        this.shipmentTypeStr = shipmentTypeStr;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getDispatchNo() {
        return dispatchNo;
    }

    public void setDispatchNo(String dispatchNo) {
        this.dispatchNo = dispatchNo;
    }

    public String getNetEngineerName() {
        return netEngineerName;
    }

    public void setNetEngineerName(String netEngineerName) {
        this.netEngineerName = netEngineerName;
    }

    public String getNetEngineerMobile() {
        return netEngineerMobile;
    }

    public void setNetEngineerMobile(String netEngineerMobile) {
        this.netEngineerMobile = netEngineerMobile;
    }
}
