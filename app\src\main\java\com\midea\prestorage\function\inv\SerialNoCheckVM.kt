package com.midea.prestorage.function.inv

import android.text.TextUtils
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.SerialNoCheckBeans
import com.midea.prestorage.beans.net.WhInfo
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.MySoundUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody


class SerialNoCheckVM(val activity: SerialNoCheckActivity) {

    val title = ObservableField("条码流向查询")
    val isOrderOk = ObservableField(false)
    val orderNo = ObservableField("")
    val sn = ObservableField("")
    val sku = ObservableField("")
    val batch = ObservableField("")
    val itemName = ObservableField("")
    var goodsStatus: LinkedHashMap<String, Any>? = null
    var inStorageStatus: MutableList<DCBean>? = null

    val isNoData = ObservableField(false)

    init {
        DCUtils.initLot4dicts(activity, object : DCUtils.OnInitFinish {
            override fun finish() {
                goodsStatus = DCUtils.lot4TypeC2N
                combineDCInfo()
            }
        })

        DCUtils.inStorageDC(activity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                inStorageStatus = statusDC
                combineDCInfo()
            }
        })
    }

    val orderEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            queryShipmentDetail()
        }
    }

    /**
     * 融合数据字典
     */
    fun combineDCInfo() {
        activity.adapter.data.forEach {
            if (goodsStatus != null) {
                val result = goodsStatus!![it.lotAtt04]
                if (result != null) {
                    it.lotAtt04Str = result.toString()
                }
            }
            if (inStorageStatus != null) {
                val result =
                    inStorageStatus!!.find { item -> item.value.toString() == it.operateType }
                if (result != null) {
                    it.operateTypeStr = result.key
                }
            }
        }
        activity.adapter.notifyDataSetChanged()
    }

    fun queryShipmentDetail() {
        if (TextUtils.isEmpty(orderNo.get())) {
            return
        }
        activity.waitingDialogHelp.showDialog()
        val param = mutableMapOf(
            "serialNo" to orderNo.get()
        )

        if (Constants.whInfo?.bearingSystem == "3") {
            param["sourceSystem"] = "WMS2B"
        } else {
            param["sourceSystem"] = "AWMS"
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .traceQuery(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<SerialNoCheckBeans>>(activity) {
                override fun success(data: MutableList<SerialNoCheckBeans>?) {
                    activity.waitingDialogHelp.hidenDialog()

                    //成功提示音
                    MySoundUtils.getInstance().dingSound()

                    if (data != null && data.isNotEmpty()) {
                        isOrderOk.set(true)
                        activity.showData(data)

                        sn.set(orderNo.get())
                        sku.set(data[0].custItemCode)
                        batch.set(data[0].lotAtt05)
                        itemName.set(data[0].itemName)

                        combineDCInfo()
                        isNoData.set(false)
                    } else {
                        sn.set("")
                        sku.set("")
                        batch.set("")
                        itemName.set("")
                        activity.showData(ArrayList())
                        isNoData.set(true)
                        isOrderOk.set(false)
                    }
                    orderNo.set("")
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    orderNo.set("")
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun back() {
        activity.finish()
    }

    fun scanResult(result: String?) {
        orderNo.set(result)
        queryShipmentDetail()
    }
}