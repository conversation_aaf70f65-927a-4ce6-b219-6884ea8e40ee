package com.midea.prestorage.dialog

import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestorage.function.receive.ContainerReceiveDetailVM
import com.midea.prestorage.function.receive.ReceiveDetailUnionBinding
import com.midea.prestoragesaas.databinding.ActivityContainerReceiveDetailBinding
import com.midea.prestoragesaas.databinding.ActivityContainerReceiveDetailCareBinding
import com.midea.prestoragesaas.databinding.DialogTipBinding
import com.midea.prestoragesaas.databinding.DialogTipCareBinding

sealed class DialogTipUnionBinding {
    abstract var vm: TipDialogVM?

    class V2(val binding: DialogTipCareBinding) : DialogTipUnionBinding() {
        override var vm: TipDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
    }

    class V1(val binding: DialogTipBinding) : DialogTipUnionBinding() {
        override var vm: TipDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
    }
}
