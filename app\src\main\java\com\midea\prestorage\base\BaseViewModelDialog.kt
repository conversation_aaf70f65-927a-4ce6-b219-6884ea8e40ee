package com.midea.prestorage.base

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.Observer
import com.midea.prestoragesaas.R
import com.midea.prestorage.utils.WaitingDialogHelp


abstract class BaseViewModelDialog<VM : BaseViewModel> : DialogFragment() {

    lateinit var vm: VM

    lateinit var waitingDialogHelp: WaitingDialogHelp

    companion object {
        const val QR_CODE_BACK = 0X01
        const val QR_CODE_BACK1 = 0X02
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        waitingDialogHelp = WaitingDialogHelp(requireActivity())

        setStyle(STYLE_NORMAL, R.style.MyDialog)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = beforeOnCreateView(inflater, container, savedInstanceState)
        initCircleDialog()
        vm.init()
        return view
    }

    abstract fun beforeOnCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View?

    /**
     * 防空指向
     */
    override fun onDestroy() {
        super.onDestroy()

        if (this::waitingDialogHelp.isInitialized) {
            waitingDialogHelp.hidenDialog()
        }
    }

    private fun initCircleDialog() {
        //展示转圈圈
        vm.isDialogShow.observe(this, Observer<Int> {
            when (it) {
                1 -> waitingDialogHelp.showDialog()
                2 -> waitingDialogHelp.showDialogUnCancel()
                else -> waitingDialogHelp.hidenDialog()
            }
        })
    }
}