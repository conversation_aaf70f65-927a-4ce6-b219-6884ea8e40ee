package com.midea.prestorage.function.inv

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.View
import android.view.WindowManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.beans.net.InvSetDetailList
import com.midea.prestorage.beans.net.InvSetList
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestoragesaas.databinding.ActivityWaitSetPackageBinding
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.utils.isNull
import com.xuexiang.xqrcode.XQRCode

/**
 * 待集托
 */
class WaitSetPackageActivity : BaseActivity() {
    lateinit var binding: ActivityWaitSetPackageUnionBinding
    private var vm = WaitSetPackageVM(this)
    val adapter = OutStorageListAdapter(this)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityWaitSetPackageUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_wait_set_package_care
                )
            )
        } else {
            ActivityWaitSetPackageUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_wait_set_package
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        binding.vm = vm

        initData()
        initView()
        initRecycle()
        inputRequest()
    }

    fun initData() {
        vm.flag.value = intent.getStringExtra("flag") //标识是从哪个页面进入的，main：主列表页面，detail：详情页
        val setCode = intent.getStringExtra("setCode")
        if (setCode.isNullOrEmpty()) {
            vm.hasCode.set(false)
        } else {
            vm.setCode.set(intent.getStringExtra("setCode"))
            vm.hasCode.set(true)
        }
        if (!intent.getSerializableExtra("bean").isNull()) {
            vm.bean = intent.getSerializableExtra("bean") as InvSetList
        }
    }

    override fun onResume() {
        super.onResume()
        binding.etSearchOrderNo.onFocusChangeListener = onFocusChangeListener
        vm.init()
    }

    private val onFocusChangeListener =
        View.OnFocusChangeListener { view: View, hasFocus: Boolean ->
            if (!hasFocus) {
                if (vm.isNoData.get()) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(this, "请先回车查询待集托信息")
                    view.post {
                        binding.etSearchOrderNo.requestFocus()
                    }
                }
            }
        }

    override fun onPause() {
        super.onPause()
        binding.etSearchOrderNo.onFocusChangeListener = null
    }

    private fun initView() {
        binding.llAllChoose.setOnClickListener {
            binding.cbSelect.performClick()
        }

        binding.cbSelect.setOnClickListener {
            if (!binding.cbSelect.isChecked) {
                adapter.allSelect(false)
                vm.wholeNum.set("0")
                vm.partNum.set("0")
                val results = adapter.data.filter { !it.isSelected }
                results.forEach {
                    it.opNum = AppUtils.getBigDecimalValueStr(it.num) //取消勾选时，把需要操作的数量默认回货品的集托数量
                }
            }
        }

        binding.cbSelect.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                adapter.allSelect(true)
                getNum()
            }
        }
    }

    /**
     * 统计勾选的操作的整件数和散包裹数
     */
    fun getNum() {
        val results = adapter.data.filter { it.isSelected }
        var wNum = 0
        var pNum = 0
        results.forEach {
            if (!it.opNum.isNullOrEmpty()) {
                if (it.unit == "CS") {
                    wNum += it.opNum.toInt()
                } else {
                    pNum += it.opNum.toInt()
                }
            }
        }
        vm.wholeNum.set(wNum.toString())
        vm.partNum.set(pNum.toString())
        adapter.notifyDataSetChanged()
        dataSetChanged()
    }

    fun dataSetChanged() {
        val results = adapter.data.filter { !it.isSelected }
        binding.cbSelect.isChecked = results.isEmpty()
    }

    private fun initRecycle() {
        adapter.setHasStableIds(true)
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.itemAnimator = null
        binding.recycle.adapter = adapter

        adapter.setClickId(R.id.ll_checked)

        adapter.setChangeSelectStatus {
            val results = adapter.data.filter { !it.isSelected }
            results.forEach {
                it.opNum = AppUtils.getBigDecimalValueStr(it.num) //取消勾选时，把需要操作的数量默认回货品的集托数量
            }
            binding.cbSelect.isChecked = results.isEmpty()
            getNum()
        }

    }

    fun inputRequest() {
        binding.etSearchOrderNo.requestFocus()
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun showData(data: MutableList<InvSetDetailList>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
        adapter.allSelect(false)
        binding.cbSelect.isChecked = false
        getNum()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            vm.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    class OutStorageListAdapter(private val activity: WaitSetPackageActivity?) :
        ListCheckBoxAdapter<InvSetDetailList>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_wait_set_package_care else R.layout.item_wait_set_package) {

        @SuppressLint("SetTextI18n")
        override fun convert(helper: BaseViewHolder, item: BaseItemForPopup) {
            super.convert(helper, item as InvSetDetailList)

            if (item.unit == "CS") {
                helper.setText(R.id.tv_set_num, AppUtils.getBigDecimalValueStr(item.num) + "整件")
            } else {
                helper.setText(R.id.tv_set_num, AppUtils.getBigDecimalValueStr(item.num) + "散包裹")
            }

            if (item.whBarcode69.isNullOrEmpty()) {
                helper.setText(R.id.tv_barCode69, item.custItemCode)
            } else {
                helper.setText(R.id.tv_barCode69, item.whBarcode69)
            }

            val check = helper.getView<ImageView>(R.id.img_select)
            val editText: EditText = helper.getView(R.id.et_set_num)
            val container: RelativeLayout = helper.getView(R.id.rl_tag)

            editText.setOnEditorActionListener { v, actionId, event ->
                if (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER) {
                    activity?.binding?.etSearchGoodsNo?.requestFocus()
                }
                false
            }

            if (editText.tag is TextWatcher) {
                editText.removeTextChangedListener(editText.tag as TextWatcher)
            }

            if (item.isSelected()) {
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    check.setImageResource(R.drawable.select_selected_care)
                } else {
                    check.setImageResource(R.drawable.ic_check_sel)
                }
                if (item.opNum.isNullOrBlank()) {
                    helper.setText(R.id.et_set_num, AppUtils.getBigDecimalValueStr(item.num))
                    editText.setSelection(AppUtils.getBigDecimalValueStr(item.num).length)
                } else {
                    helper.setText(R.id.et_set_num, item.opNum)
                    editText.setSelection(item.opNum.length)
                }
            } else {
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                    check.setImageResource(R.drawable.select_normal_care)
                } else {
                    check.setImageResource(R.drawable.ic_check_unsel)
                }
                helper.setText(R.id.et_set_num, "")
            }

            val watcher: TextWatcher = object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(
                    s: CharSequence,
                    start: Int,
                    before: Int,
                    count: Int
                ) {
                    if (s.toString().startsWith("0")) {
                        editText.setText("1")
                        editText.setSelection(1)
                    }
                }

                override fun afterTextChanged(editable: Editable) {
                    val innerItem = container.tag as InvSetDetailList
                    if (!TextUtils.isEmpty(editable)) {
                        if (editable.toString().toInt() > innerItem.num.toInt()) {
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(activity, "本次集托数量不能大于待集托包裹数量")
                            editText.setText(AppUtils.getBigDecimalValueStr(innerItem.num))
                            editText.setSelection(AppUtils.getBigDecimalValueStr(innerItem.num).length)
                            innerItem.opNum = AppUtils.getBigDecimalValueStr(innerItem.num)
                            innerItem.isSelected = true
                            activity?.getNum()
                        } else {
                            if (editable.toString() == "0") {
                                innerItem.opNum = "1"
                                innerItem.isSelected = true
                                activity?.getNum()
                            } else {
                                innerItem.opNum = editable.toString()
                                innerItem.isSelected = true
                                activity?.getNum()
                            }
                        }
                    } else {
                        innerItem.opNum = ""
                        innerItem.isSelected = false
                        activity?.getNum()
                    }
                }
            }

            editText.addTextChangedListener(watcher)
            editText.tag = watcher
            container.tag = item
        }

        override fun getItemId(position: Int): Long {
            return position.toLong()
        }
    }
}