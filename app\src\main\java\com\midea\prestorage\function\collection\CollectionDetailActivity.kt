package com.midea.prestorage.function.collection

import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.DispatchInfoDot
import com.midea.prestoragesaas.databinding.ActivityCollectionDetailBinding

class CollectionDetailActivity : BaseActivity() {

    lateinit var binding: ActivityCollectionDetailBinding
    val adapter = OutPoolStorageAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_collection_detail)
        binding.vm = CollectionDetailVM(this)

        initRecycle()
    }

    private fun initRecycle() {
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter
    }

    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }

    class OutPoolStorageAdapter : CommonAdapter<DispatchInfoDot>(R.layout.item_collection_detail) {
        override fun convert(holder: BaseViewHolder?, item: DispatchInfoDot?) {
            super.convert(holder, item)
        }
    }
}