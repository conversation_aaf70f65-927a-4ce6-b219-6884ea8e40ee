package com.midea.prestorage.function.inv

import android.annotation.SuppressLint
import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.InvSetDetailList
import com.midea.prestorage.beans.net.InvSetList
import com.midea.prestoragesaas.databinding.ActivitySetPackageDetailBinding
import com.midea.prestorage.function.inv.dialog.DeleteNumDialog
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.AppUtils

/**
 * 集托详情
 */
class SetPackageDetailActivity : BaseActivity() {
    lateinit var binding: ActivitySetPackageDetailBinding
    private var vm = SetPackageDetailVM(this)
    val adapter = OutStorageListAdapter()
    lateinit var deleteDialog: DeleteNumDialog

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_set_package_detail)
        binding.vm = vm

        initData()
        initRecycle()
        initDialog()
    }

    fun initData() {
        vm.flag.value = intent.getStringExtra("flag") //标识是从哪个页面进入的，main：主列表页面，wait：待集托页
        if (intent.getStringExtra("status") == "集托中") {
            vm.setStatus.set(false)
        } else {
            vm.setStatus.set(true)
        }
        vm.setCode.set(intent.getStringExtra("setCode"))
        vm.bean = intent.getSerializableExtra("bean") as InvSetList
        vm.bean?.let {
            vm.setArea.set(it.setArea)
            vm.setStartTime.set(it.setStartTime)
            if(it.setEndTime.isNullOrEmpty()) {
                vm.setEndTime.set(it.setStartTime)
            }else {
                vm.setEndTime.set(it.setEndTime)
            }
            vm.setUserName.set(it.setUserName)
        }
    }

    override fun onResume() {
        super.onResume()
        vm.init()
    }

    private fun initRecycle() {
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter

        adapter.setOnItemClickListener { adapter, _, position ->
            if (!vm.setStatus.get()) {
                val bean = adapter.data[position] as InvSetDetailList
                deleteDialog.setData(bean)
                deleteDialog.show()
            }
        }
    }

    private fun initDialog() {

        //删除条码dialog
        deleteDialog = DeleteNumDialog(this)
        deleteDialog.setTitle("提示")
        deleteDialog.setDeleteBack(object : DeleteNumDialog.DeleteBarcodeBack {
            override fun deleteBarcodeBack(num: String, ids: String) {
                deleteDialog.dismiss()
                vm.deleteNum(num, ids)
            }
        })

    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun showData(data: MutableList<InvSetDetailList>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
    }

    override fun onDestroy() {
        super.onDestroy()
        Printer.closeBluetooth()
    }

    class OutStorageListAdapter :
        CommonAdapter<InvSetDetailList>(R.layout.item_set_package_detail) {

        @SuppressLint("SetTextI18n")
        override fun convert(helper: BaseViewHolder, item: InvSetDetailList) {
            super.convert(helper, item)

            if(item.whBarcode69.isNullOrEmpty()) {
                helper.setText(R.id.tv_barCode69, item.custItemCode)
            }else {
                helper.setText(R.id.tv_barCode69, item.whBarcode69)
            }

            if (item.unit == "CS") {
                helper.setText(R.id.tv_set_num, AppUtils.getBigDecimalValueStr(item.num) + "整件")
            } else {
                helper.setText(R.id.tv_set_num, AppUtils.getBigDecimalValueStr(item.num) + "散包裹")
            }
        }
    }
}