package com.midea.prestorage.function.outstorage.dialog

import android.text.TextUtils
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.function.outstorage.OutStorageDeleteActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody

class DeleteTipDialogVM(val dialog: DeleteTipDialog) {

    val title = ObservableField<String>("确定删除?")
    val serialNo = ObservableField<String>("")
    val itemCode = ObservableField<String>("")
    val itemName = ObservableField<String>("")
    val goodsNo = ObservableField<String>("")
    val isUnScan = ObservableField(false)

    fun show() {
        serialNo.set(dialog.deleteInfo?.serialNo)
        itemCode.set(dialog.deleteInfo?.custItemCode)
        itemName.set(dialog.deleteInfo?.itemName)

        isUnScan.set(dialog.deleteInfo?.serialType == "1")
    }

    fun close() {
        dialog.dismiss()
    }

    val goodsEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                if (!TextUtils.isEmpty(goodsNo.get())) {
                    confirm()
                }
            }
        }
    }

    fun confirm() {
        if (isUnScan.get()!!) {
            if (TextUtils.isEmpty(goodsNo.get())) {
                AppUtils.showToast(dialog.mContext, "数量不能为空!")
                return
            }

            if (goodsNo.get()!!.toInt() == 0) {
                goodsNo.set("")
                AppUtils.showToast(dialog.mContext, "数量不能为0!")
                return
            }

            if (goodsNo.get()!!.toInt() > dialog.deleteInfo!!.qty) {
                goodsNo.set("")
                AppUtils.showToast(dialog.mContext, "数量超出!")
                return
            }
        }

        dialog.mContext.waitingDialogHelp.showDialog()
        val param = mutableMapOf(
            "id" to dialog.deleteInfo?.id,
            "whCode" to  (dialog.mContext as BaseActivity).getWhCode(),
            "serialNo" to dialog.deleteInfo?.serialNo,
            "shipmentCode" to dialog.deleteInfo?.shipmentCode,
            "serialType" to dialog.deleteInfo?.serialType,
            "qty" to goodsNo.get()
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .deleteOutShipmentSerial(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(dialog.mContext as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(dialog.mContext) {
                override fun success(data: Any?) {
                    dialog.mContext.waitingDialogHelp.hidenDialog()
                    dialog.deleteBack?.deleteOk()
                    close()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    dialog.deleteBack?.deleteFail(apiErrorModel.message)
                    dialog.mContext.waitingDialogHelp.hidenDialog()
                    close()
                }
            })
    }
}