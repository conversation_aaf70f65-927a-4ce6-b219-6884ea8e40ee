package com.midea.prestorage.function.inv.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

// 移库界面中 按库移位 返回的数据
public class RespGetInventoryByLocation implements Serializable {
    private List<FuInvLocationInventory> fuInvLocationInventoryList;
    private Integer pageSize;
    private String sku;
    private BigDecimal onHandQtySum;


    public List<FuInvLocationInventory> getFuInvLocationInventoryList() {
        return fuInvLocationInventoryList;
    }

    public void setFuInvLocationInventoryList(List<FuInvLocationInventory> fuInvLocationInventoryList) {
        this.fuInvLocationInventoryList = fuInvLocationInventoryList;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public BigDecimal getOnHandQtySum() {
        return onHandQtySum;
    }

    public void setOnHandQtySum(BigDecimal onHandQtySum) {
        this.onHandQtySum = onHandQtySum;
    }
}

