package com.midea.prestorage.function.pointjoin.fragment

import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import androidx.core.content.ContextCompat
import androidx.core.view.children
import androidx.core.view.get
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.net.PointDivideDetailBean
import com.midea.prestoragesaas.databinding.FragmentPointJoinDialogFragmentBinding
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.instorage.response.HandlingGroup
import com.midea.prestorage.utils.WaitingDialogHelp
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.components.support.RxFragment

class PointJoinDialogFragment : RxFragment() {

    companion object {

        fun newInstance(
            position: PointDivideDetailBean,
            currentNum: Int,
            allNum: Int
        ): PointJoinDialogFragment {
            val bundle = Bundle()
            bundle.putSerializable("bean", position)
            bundle.putSerializable("currentNum", currentNum)
            bundle.putSerializable("allNum", allNum)
            val fragment = PointJoinDialogFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    private lateinit var binding: FragmentPointJoinDialogFragmentBinding
    private var vm: PointJoinDialogFragmentVM? = null
    private lateinit var adapter: CommonAdapter<PointDivideDetailBean.EngineerSignDetailResponse>
    lateinit var waitingDialogHelp: WaitingDialogHelp
    lateinit var handlingGroupDialog: FilterDialog

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = inflater.let {
            FragmentPointJoinDialogFragmentBinding.inflate(it, container, false)
        }
        initView()
        return binding.root
    }

    private fun initRecycle() {
        adapter = CommonAdapter(R.layout.item_pending_details)
        binding.rv.layoutManager = LinearLayoutManager(activity)
        binding.rv.adapter = adapter
    }

    private fun initView() {
        if (vm == null) {
            vm = PointJoinDialogFragmentVM(this)
            binding.vm = vm
            notified()
        }
        initRecycle()
        vm?.init()
        waitingDialogHelp = WaitingDialogHelp(activity)

        val it = binding.llCode.children.iterator()
        while (it.hasNext()) {
            val next = it.next() as EditText
            next.setOnKeyListener(onKeyBack)
        }

        handlingGroupDialog = FilterDialog(activity as RxAppCompatActivity)
        handlingGroupDialog.setTitle("选择装卸组")
        handlingGroupDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            it as HandlingGroup
            binding.vm!!.handlingBean = it
            binding.vm!!.handlingGroupName.set(it.handlingName)
            handlingGroupDialog.dismiss()
        })
    }

    fun editChange(isUp: Boolean) {
        val verify = getVerify()
        binding.llCode.post {
            val index = if (isUp) {
                if (verify!!.length < binding.llCode.childCount) {
                    verify.length
                } else {
                    verify.length - 1
                }
            } else {
                if (verify!!.length - 1 >= 0) {
                    verify.length - 1
                } else {
                    verify.length
                }
            }
            val edit = binding.llCode[index] as EditText
            edit.isEnabled = true
            edit.requestFocus()

            val it = binding.llCode.children.iterator()
            while (it.hasNext()) {
                val next = it.next() as EditText
                if (next == edit) {
                    continue
                }
                next.isEnabled = false
            }
        }
    }

    fun setTvData(bean: PointDivideDetailBean) {
        binding.tvCustOrderNo.text = getNotNumTv(bean.engineerSignResponse.custOrderCode)
        binding.tvWaveNo.text = getNotNumTv(bean.engineerSignResponse.waveNo)
        binding.tvShipmentCode.text = getNotNumTv(bean.engineerSignResponse.shipmentCode)
        binding.tvRequestQty.text = getNotNumTv(bean.engineerSignResponse.requestQty.toString())
        binding.tvRequestBy.text = getNotNumTv(bean.engineerSignResponse.requestedBy)
        binding.tvRequestTime.text = getNotNumTv(bean.engineerSignResponse.requestedTime)
        binding.tvConfirmBy.text = getNotNumTv(bean.engineerSignResponse.confirmBy)
        binding.tvAddress.text = getNotNumTv(bean.engineerSignResponse.address)
    }

    fun setConfirmBy(confirmBy:String?) {
        binding.tvConfirmBy.text = getNotNumTv(confirmBy)
    }

    fun setTvStatue(statue: String) {
        binding.tvStatue.text = statue
    }

    fun setCurrentNum(statue: String) {
        binding.tvCurrentNum.text = statue
    }

    fun setTotalNum(statue: String) {
        binding.tvTotalNum.text = statue
    }

    fun setConfirmEnable() {
        binding.tvConfirm.setBackgroundColor(
            ContextCompat.getColor(
                requireActivity(),
                R.color.btn_blue
            )
        )
        binding.tvConfirm.isEnabled = true

        binding.tvCancel.setBackgroundColor(
            ContextCompat.getColor(
                requireActivity(),
                R.color.btn_blue
            )
        )
        binding.tvCancel.isEnabled = true
    }

    fun setConfirmUnable() {
        binding.tvConfirm.setBackgroundColor(
            ContextCompat.getColor(
                requireActivity(),
                R.color.button_gray
            )
        )
        binding.tvConfirm.isEnabled = false

        binding.tvCancel.setBackgroundColor(
            ContextCompat.getColor(
                requireActivity(),
                R.color.button_gray
            )
        )
        binding.tvCancel.isEnabled = false
    }

    fun isZT(zt: Boolean, showVeryCode: Boolean) {
        if (zt) {
            binding.llGoodsMain.visibility = View.GONE
            binding.llAddress.visibility = View.GONE
            if (!showVeryCode) {
                binding.llVerify.visibility = View.GONE
            } else {
                binding.llVerify.visibility = View.VISIBLE
            }
            binding.tvCancel.visibility = View.GONE
            binding.viewLine.visibility = View.GONE
        } else {
            binding.llGoodsMain.visibility = View.VISIBLE
            binding.llAddress.visibility = View.VISIBLE
            binding.llVerify.visibility = View.GONE
            binding.tvCancel.visibility = View.VISIBLE
            binding.viewLine.visibility = View.VISIBLE
        }
    }

    private fun getNotNumTv(str: String?): String {
        return if (TextUtils.isEmpty(str)) "" else str!!
    }

    private var onKeyBack = View.OnKeyListener { v, keyCode, event ->
        if (event?.action == KeyEvent.ACTION_DOWN) {
            if (keyCode == KeyEvent.KEYCODE_DEL) {
                val verify = getVerify()
                if (verify!!.isNotEmpty()) {
                    vm!!.isRevers = true
                    (binding.llCode[verify.length - 1] as EditText).setText("")
                }
            } else if (keyCode == KeyEvent.KEYCODE_ENTER) {
                if (v.id == R.id.ed_final) {
                    vm!!.confirm()
                }
            }
        }
        false
    }

    fun cleanVerifyCode() {
        val it = binding.llCode.children.iterator()
        while (it.hasNext()) {
            val next = it.next() as EditText
            next.isEnabled = false
            next.setText("")
        }

        binding.llCode[0].isEnabled = true
    }

    fun getVerify(): String? {
        val sb = StringBuffer()
        val it = binding.llCode.children.iterator()
        while (it.hasNext()) {
            val edit = it.next() as EditText
            sb.append(edit.text.toString())
        }
        return sb.toString()
    }

    fun showRv(engineerSignDetailResponses: MutableList<PointDivideDetailBean.EngineerSignDetailResponse>?) {
        adapter.setNewInstance(engineerSignDetailResponses)
        adapter.notifyDataSetChanged()
    }

    fun notified() {
        if (this::binding.isInitialized) {
            //有时候获取不到焦点，调整一下
            if (vm?.cNum == 0) {
                binding.llCode.children.iterator().next().requestFocus()
            } else {
                binding.llCode.post {
                    binding.llCode.children.iterator().next().requestFocus()
                }
            }
        }
    }
}