package com.midea.prestorage.function.main

import CheckUtil
import android.content.Intent
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.App
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.net.LoginResp
import com.midea.prestorage.beans.net.ProfileSetting
import com.midea.prestorage.beans.net.TenantResp
import com.midea.prestorage.beans.setting.BluetoothInfo
import com.midea.prestorage.beans.setting.HandingInfoDb
import com.midea.prestorage.beans.setting.ImplWarehouse
import com.midea.prestorage.beans.setting.ProfileSettingDbV2
import com.midea.prestorage.beans.setting.WorkingInfoDb
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.about.AboutActivity
import com.midea.prestorage.function.instorage.response.HandlingGroup
import com.midea.prestorage.function.instorage.response.WorkingGroup
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.function.login.LoginActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.RetrofitHelperTenant
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.http.constants.PickModes
import com.midea.prestorage.http.constants.PrinterModes
import com.midea.prestorage.http.constants.Printers
import com.midea.prestorage.utils.*
import com.midea.prestoragesaas.BuildConfig
import com.midea.prestoragesaas.databinding.ActivityProfileBinding
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import okhttp3.MediaType
import okhttp3.RequestBody
import org.xutils.db.sqlite.WhereBuilder
import org.xutils.ex.DbException
import java.util.Locale

class ProfileVM(val activity: ProfileActivity, val binding: ProfileUnionBinding) {

    val loginUserAccount = ObservableField<String>()
    val loginTenantInfo = ObservableField<String>()
    val systemEnv = ObservableField<String>()
    var curProfileSetting = ProfileSetting()

    val handlingGroupName = ObservableField<String>("")
    val workingGroupName = ObservableField<String>("")

    // rf本地db  用来加载本地缓存的 看板时间范围设定
    val db = DbUtils.db

    // 是否展示 看板取数时间设置 (选择开始时间-结束时间)
    val isShowSelectDay = ObservableField<Boolean>(false)

    val isShowHanding = ObservableField(true)
    val isShowWorking = ObservableField(true)

    // 看板取数时间设置  标题
    val textSelectDayTitle = ObservableField<String>()

    private var bluetoothInfo: BluetoothInfo? = null
    private var bluetoothAddress: String? = null

    var handlingGroupDialog: FilterDialog
    var handlingBean: HandlingGroup? = null

    var workingGroupDialog: FilterDialog

    var tenantInfo: TenantResp.TenantsDTO? = null
    var accessToken: String? = null
    var whInfo: ImplWarehouse? = null
    var whInfoList: MutableList<ImplWarehouse>? = null

    companion object {
        const val CARE_MODE = "CARE_MODE" //关怀模式开关
    }

    init {
        loginUserAccount.set(Constants.userInfo!!.userName)
        loginTenantInfo.set(Constants.tenantCode + " | " + Constants.tenantName)

        if (BuildConfig.BUILD_TYPE.lowercase(Locale.getDefault()).equals("debug")) {
            systemEnv.set(LoginActivity.SERVER_NAME + " [debug包]")
        } else {
            systemEnv.set(LoginActivity.SERVER_NAME)
        }
        readProperty()

        if ("3" == Constants.whInfo?.bearingSystem) {
            initHandlingGroupList()
        }
        initHandling()

        handlingGroupDialog = FilterDialog(activity as RxAppCompatActivity)
        handlingGroupDialog.setTitle("选择装卸组")
        handlingGroupDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            it as HandlingGroup
            handlingBean = it
            handlingGroupName.set(it.handlingName)
            saveUserInfo()
            handlingGroupDialog.dismiss()
        })

        isShowHanding.set(Constants.whInfo?.bearingSystem == "3")

        //initWorkingGroupList()
        initWorking()

        workingGroupDialog = FilterDialog(activity as RxAppCompatActivity)
        workingGroupDialog.setTitle("选择作业组")
        workingGroupDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            it as WorkingGroup
            workingGroupName.set(it.workGroupName)
            saveWorkingUserInfo(it)
            workingGroupDialog.dismiss()
        })

        isShowWorking.set(Constants.whInfo?.bearingSystem == "3")
    }

    fun showHanding() {
        handlingGroupDialog.show()
    }

    fun showWorking() {
        workingGroupDialog.show()
    }

    fun onChangeStartHour(startHour: String) {
        curProfileSetting.dashboardStartHour = startHour
        checkDayTime()
    }

    fun onChangeEndHour(endHour: String) {
        curProfileSetting.dashboardEndHour = endHour
        checkDayTime()
    }

    fun onChangeStartDay(startDay: Int) {
        curProfileSetting.dashboardStartDay = startDay
        checkDayTime()
    }

    fun onChangeEndDay(endDay: Int) {
        curProfileSetting.dashboardEndDay = endDay
        checkDayTime()
    }

    fun onChangePrinter(printer: Printers) {
        saveUserInfo(printer.code)
    }

    fun onChangePrinterModes(printer: PrinterModes) {
        saveUserInfoPrintMode(printer.modeCode)
    }

    fun onChangePickModes(printer: PickModes) {
        savePickMode(printer.modeCode)
    }

    fun toggleSelectDay() {
        isShowSelectDay.set(!isShowSelectDay.get()!!)
    }

    //检查用户当前设定 正不正确，
    // 正确就存储到db 同步到ui，
    // 不正确就从db读取上一次配置 并同步到ui
    fun checkDayTime() {
        if (curProfileSetting.dashboardEndDay > curProfileSetting.dashboardStartDay) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "开始日期不能晚于结束日期")
            loadSettingFromDB()
            syncCurSettingToUI()
            return
        }

        // 如果开始日 等于 结束日  则 开始小时 不能 晚于结束小时
        if (curProfileSetting.dashboardEndDay.equals(curProfileSetting.dashboardStartDay)) {
            // 18:00 -> 1800   00:01 -> 0001 -> 1
            val sHour = curProfileSetting.dashboardStartHour.replace(":", "").toInt()
            val eHour = curProfileSetting.dashboardEndHour.replace(":", "").toInt()
            if (sHour >= eHour) {
                ToastUtils.getInstance().showErrorToastWithSound(activity, "开始时间不能 大于等于 结束时间")
                loadSettingFromDB()
                syncCurSettingToUI()
                return
            }
        }

        // 以上时间校验没有错 才 存储当前正确的设定到本地db
        saveCurSettingToDB()
        // 同步设定到界面上
        syncCurSettingToUI()
    }

    // 从本地缓存加载之前的设置  没有的话写入一份默认设置
    fun loadSettingFromDB() {
        try {
            val listDbRecords = db.findAll(ProfileSettingDbV2::class.java)
            if (null == listDbRecords || listDbRecords.size == 0) {
                // 默认设定
                createDefaultSetting()
                // 把当前设定 同步到界面
                syncCurSettingToUI()
                // 把当前设定 存储到本地DB
                saveCurSettingToDB()
            } else {
                val jsonStr = listDbRecords.get(0)!!.setting
                curProfileSetting = Gson().fromJson(jsonStr, ProfileSetting::class.java)
                // 把当前设定 同步到界面
                syncCurSettingToUI()
            }
        } catch (e: Exception) {
            // 默认设定
            createDefaultSetting()
            // 把当前设定 同步到界面
            syncCurSettingToUI()
            // 把当前设定 存储到本地DB
            saveCurSettingToDB()
        }
    }

    fun createDefaultSetting() {
        // 默认设定
        curProfileSetting = ProfileSetting()
        curProfileSetting.dashboardStartDay =
            ProfileActivity.defaultStartDay     // 开始时间 1天 前
        curProfileSetting.dashboardEndDay =
            ProfileActivity.defaultEndDay       // 结束时间 当天
        curProfileSetting.dashboardStartHour =
            ProfileActivity.defaultStartHour // 开始时间 小时
        curProfileSetting.dashboardEndHour =
            ProfileActivity.defaultEndHour  // 结束时间 小时
    }

    // 把当前设定 同步到界面
    fun syncCurSettingToUI() {
        for (index in 1..activity.hourOptions.size) {
            if (curProfileSetting.dashboardStartHour.equals(activity.hourOptions.get(index - 1))) {
                activity.binding.spinnerStartHour.selectedIndex = (index - 1)
            }
            if (curProfileSetting.dashboardEndHour.equals(activity.hourOptions.get(index - 1))) {
                activity.binding.spinnerEndHour.selectedIndex = (index - 1)
            }
        }

        for (index in 1..activity.dayOptions.size) {
            if (activity.dayOptionVals.get(index - 1) == curProfileSetting.dashboardStartDay) {
                activity.binding.spinnerStartDay.selectedIndex = (index - 1)
                // 显示标题
                textSelectDayTitle.set(activity.dayOptions.get(index - 1))
            }

            if (activity.dayOptionVals.get(index - 1) == curProfileSetting.dashboardEndDay) {
                activity.binding.spinnerEndDay.selectedIndex = (index - 1)
            }
        }
    }

    // 把当前设定 保存到本地 db
    fun saveCurSettingToDB() {
        try {
            // 先删除旧的
            db.delete(ProfileSettingDbV2::class.java)
            // 再保存新的
            val dbRecord =
                ProfileSettingDbV2()
            dbRecord.setting = Gson().toJson(curProfileSetting)
            db.saveOrUpdate(dbRecord)
        } catch (e: DbException) {
            e.printStackTrace()
        }
    }

    private fun saveUserInfo(brandName: Int) {
        Thread(Runnable {
            try {
                if (brandName != bluetoothInfo?.printBrand) {
                    bluetoothInfo?.bluetoothAddress = ""
                } else {
                    bluetoothInfo?.bluetoothAddress = bluetoothAddress
                }
                bluetoothInfo?.printBrand = brandName
                db.saveOrUpdate(bluetoothInfo)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
        }).start()
    }

    private fun savePickMode(modeCode: Int) {
        Thread(Runnable {
            try {
                Constants.userInfo?.isAutoStart = modeCode == 0
                db.saveOrUpdate(Constants.userInfo)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
        }).start()
    }

    private fun saveUserInfoPrintMode(modeCode: Int) {
        Thread(Runnable {
            try {
                bluetoothInfo?.printMode = modeCode
                db.saveOrUpdate(bluetoothInfo)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
        }).start()
    }

    fun onLogout() {
        SPUtils.remove(activity, "accessToken")
        SPUtils.remove(activity, "host")
        SPUtils.remove(activity, "serverName")
        //清除当前activity 跳到登录界面
        val intent = Intent(activity, LoginActivity::class.java)
        //跳Activity时清除掉当前Activity
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
        activity.startActivity(intent)
    }

    fun go2About() {
        activity.startActivity(Intent(activity, AboutActivity::class.java))
    }

    //切换租户
    fun onSwitchTenant() {
        if (CheckUtil.isFastDoubleClick()) {
            getTenant()
        }
    }

    //后退键
    val back = View.OnClickListener {
        activity.finish()
    }

    private fun readProperty() {
        Observable.create<Any> {
            bluetoothInfo = db.findFirst(BluetoothInfo::class.java)
            if (bluetoothInfo == null) {
                bluetoothInfo = BluetoothInfo()
            }
            it.onNext(bluetoothInfo!!)
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .subscribe(object : Observer<Any> {
                override fun onComplete() {
                    activity.waitingDialogHelp.hidenDialog()
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: Any) {
                    bluetoothInfo = t as BluetoothInfo
                    bluetoothAddress = bluetoothInfo?.bluetoothAddress
                    binding.spinnerPrinter.selectedIndex = bluetoothInfo!!.printBrand
                    binding.spinnerIsAutoConnect.selectedIndex = bluetoothInfo!!.printMode

                    if (Constants.userInfo != null) {
                        if (Constants.userInfo!!.isAutoStart) {
                            binding.spinnerPickMode.selectedIndex = 0
                        } else {
                            binding.spinnerPickMode.selectedIndex = 1
                        }
                    } else {
                        binding.spinnerPickMode.selectedIndex = 1
                    }
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    private fun initHandling() {
        Observable.create<HandingInfoDb> {
            var handingInfo =
                if (Constants.whInfo?.bearingSystem != "3") {
                    db.selector(HandingInfoDb::class.java)
                        .where("userId", "==", Constants.userInfo?.id)
                        .and(WhereBuilder.b("mode", "==", 3))
                        .findFirst()
                } else {
                    db.selector(HandingInfoDb::class.java)
                        .where("userId", "==", Constants.userInfo?.id)
                        .and(WhereBuilder.b("mode", "==", 5))
                        .findFirst()
                }

            if (handingInfo == null) {
                it.onNext(HandingInfoDb())
            } else {
                it.onNext(handingInfo)
            }
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<HandingInfoDb> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: HandingInfoDb) {
                    if (t.userId == null) {
                        // 默认选择 第一个装卸组
                        handlingGroupName.set("")
                    } else {
                        handlingGroupName.set(t.handlingName)
                        handlingBean = HandlingGroup()
                        handlingBean!!.handlingName = t.handlingName
                        handlingBean!!.handlingCode = t.handlingCode
                    }
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    private fun initWorking() {
        Observable.create<WorkingInfoDb> {
            var handingInfo = db.selector(WorkingInfoDb::class.java)
                .where("userId", "==", Constants.userInfo?.id)
                .and(WhereBuilder.b("whCode", "==", Constants.whInfo?.whCode))
                .findFirst()

            if (handingInfo == null) {
                it.onNext(WorkingInfoDb())
            } else {
                it.onNext(handingInfo)
            }
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<WorkingInfoDb> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: WorkingInfoDb) {
                    if (t.userId == null) {
                        workingGroupName.set("无")
                    } else {
                        workingGroupName.set(t.workGroupName)
                    }
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    fun saveUserInfo() {
        Observable.create<String> {
            try {
                var findFirst = db.selector(HandingInfoDb::class.java)
                    .where("userId", "==", Constants.userInfo?.id)
                    .findFirst()

                if (findFirst == null) {
                    findFirst = HandingInfoDb()
                }
                findFirst.userId = Constants.userInfo?.id
                findFirst.handlingCode = handlingBean?.handlingCode
                findFirst.handlingName = handlingBean?.handlingName
                findFirst.supplierName = handlingBean?.supplierName
                findFirst.supplierCode = handlingBean?.supplierCode
                if (Constants.whInfo?.bearingSystem != "3") {
                    findFirst.mode = 3
                } else {
                    findFirst.mode = 5
                }

                db.saveOrUpdate(findFirst)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    private fun saveWorkingUserInfo(workingGroup: WorkingGroup) {
        Observable.create<String> {
            try {
                var findFirst = db.selector(WorkingInfoDb::class.java)
                    .where("userId", "==", Constants.userInfo?.id)
                    .and(WhereBuilder.b("whCode", "==", Constants.whInfo?.whCode))
                    .findFirst()

                if (findFirst == null) {
                    findFirst = WorkingInfoDb()
                }
                findFirst.userId = Constants.userInfo?.id
                findFirst.workGroupCode = workingGroup.workGroupCode
                if (findFirst.workGroupCode.isNullOrEmpty()) {
                    findFirst.workGroupName = ""
                } else {
                    findFirst.workGroupName = workingGroup.workGroupName
                }
                findFirst.whCode = Constants.whInfo?.whCode

                db.saveOrUpdate(findFirst)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }


    private fun initHandlingGroupList() {
        RetrofitHelper.getBasicDataAPI()
            .queryPageHandlingGroup(
                Constants.tenantCode,
                (activity as BaseActivity).getWhCode(),
                "03"
            )
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<PageResult<HandlingGroup>>(activity as BaseActivity) {
                override fun success(data: PageResult<HandlingGroup>?) {
                    if (data != null && data.list != null && data.list.size > 0) {
                        val beans = mutableListOf<BaseItemShowInfo>()
                        data.list.forEach {
                            it.showInfo = it.handlingName
                            if (!it.showInfo.isNullOrBlank()) {
                                beans.add(it)
                            }
                        }

                        handlingGroupDialog.addAllData(beans)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun initWorkingGroupList() {
        RetrofitHelper.getBasicDataAPI()
            .workGroupPage((activity as BaseActivity).getWhCode())
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<PageResult<WorkingGroup>>(activity as BaseActivity) {
                override fun success(data: PageResult<WorkingGroup>?) {
                    val beans = mutableListOf<BaseItemShowInfo>()
                    val workingGroup = WorkingGroup()
                    workingGroup.workGroupName = "无"
                    workingGroup.workGroupCode = ""
                    workingGroup.showInfo = workingGroup.workGroupName
                    beans.add(workingGroup)

                    if (data != null && data.list != null && data.list.size > 0) {
                        data.list.forEach {
                            it.showInfo = it.workGroupName
                            beans.add(it)
                        }
                    }
                    workingGroupDialog.addAllData(beans)

                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun getTenant() {
        //弹出等待框
        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getBasicDataAPI()
            .getTenant(BuildConfig.APPLICATION_CODE)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<TenantResp>(activity) {
                override fun success(data: TenantResp?) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (data?.tenants.isNullOrEmpty()) {
                        val emptyDatas = mutableListOf<TenantResp.TenantsDTO>()
                        activity.setTenantInfo(emptyDatas)
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, "暂无租户权限，请先开通!")
                    } else {
                        if (data?.tenants?.size == 1) {//如果只有1个，则默认该租户
                            activity.resetWarehouse()
                            switchTenant(data.tenants[0].tenantCode)
                            tenantInfo = data.tenants[0]
                            activity.showWhDialog(data.tenants[0].tenantCode, data.tenants[0].tenantName)
                        } else {
                            if (data?.currentTenant.isNull()) {
                                activity.showWhDialog("", "")
                            } else {
                                activity.resetWarehouse()
                                switchTenant(data?.currentTenant?.tenantCode ?: "")
                                tenantInfo =
                                    data?.tenants?.find { it.tenantCode == data?.currentTenant?.tenantCode && it.tenantName == data?.currentTenant?.tenantName }
                                activity.showWhDialog(data?.currentTenant?.tenantCode ?: "", data?.currentTenant?.tenantName ?: "")
                            }
                        }
                        data?.tenants?.forEach {
                            it.showInfo = it.tenantName
                        }
                        activity.setTenantInfo(data?.tenants)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    val emptyDatas = mutableListOf<TenantResp.TenantsDTO>()
                    activity.setTenantInfo(emptyDatas)
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    //切换租户
    fun switchTenant(tenantCode: String) {
        val map = mutableMapOf<String, Any?>()
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )
        //activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getBasicDataAPI()
            .switchTenant(requestBody, tenantCode, BuildConfig.APPLICATION_CODE)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<LoginResp>>(activity) {
                override fun success(data: MutableList<LoginResp>?) {
                    accessToken = data?.get(0)?.accessToken
                    //选择完租户之后，才允许选择仓库，只能选择已选租户下该用户数据权限范围内的仓库
                    getCodeAndName()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    //获取仓库
    fun getCodeAndName() {
        val map = mutableMapOf<String, Any?>()
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )
        RetrofitHelperTenant.getBasicDataAPI()
            .getCodeAndName(requestBody, tenantInfo?.tenantCode, accessToken)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<MutableList<ImplWarehouse>>(activity) {
                override fun success(result: MutableList<ImplWarehouse>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    var data = result?.filter { it.cdwhIsStop == 0 }?.toMutableList()
                    if (data.isNullOrEmpty()) {
                        val emptyDatas = mutableListOf<ImplWarehouse>()
                        activity.setWhInfo(emptyDatas)
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, "暂无仓库权限，请先开通!")
                    } else {
                        if (data.size == 1) {//如果只有1个，则默认该仓库
                            activity.setWareHouse(data[0].whCode, data[0].cdwhName)
                            whInfo = data[0]
                        }
                        data.forEach {
                            it.showInfo = it.cdwhName
                            it.whSystem = "2"
                        }
                        activity.setWhInfo(data)
                        whInfoList = data
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    val emptyDatas = mutableListOf<ImplWarehouse>()
                    activity.setWhInfo(emptyDatas)
                    ToastUtilsCare.toastBig(activity, apiErrorModel.message, Toast.LENGTH_SHORT)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun saveWhInfoList(list: MutableList<ImplWarehouse>?) {
        Observable.create<String> {
            try {
                db.delete(ImplWarehouse::class.java)
                db.saveOrUpdate(list)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    fun saveWhInfo(it: ImplWarehouse) {
        Constants.whInfo = it
        Constants.userInfo?.whName = it.cdwhName
        Constants.userInfo?.whCode = it.whCode
        Constants.userInfo?.whType = 2

        Observable.create<String> {
            try {
                if (Constants.whInfo != null) {
                    db.saveOrUpdate(Constants.whInfo)
                    db.saveOrUpdate(Constants.userInfo)
                }
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {

                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }
}