package com.midea.prestorage.function.containerpick.dialog

import android.app.AlertDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.function.inv.CountInTimeSettingVM
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogCombinedSettingBinding

class CombinedSettingDialog(
    var mContext: BaseActivity
) : AlertDialog(mContext) {

    var binding: DialogCombinedSettingBinding
    var oldSortCondition = 1 //排序条件
    var oldSortMode = 1 //排序方式

    init {
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        window?.setGravity(Gravity.CENTER)
        window?.attributes?.run {
            gravity = Gravity.CENTER
        }
        val contentView =
            LayoutInflater.from(mContext).inflate(R.layout.dialog_combined_setting, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding.vm = CombinedSettingDialogVM(this)

        setCanceledOnTouchOutside(true)
    }

    fun setCurrentStatus(sortCondition: Int, sortMode: Int) {
        oldSortCondition = sortCondition
        oldSortMode = sortMode
    }

    override fun show() {
        super.show()

        binding.cbSearchType1.isChecked = oldSortCondition == 1
        binding.cbSearchType2.isChecked = oldSortCondition == 2
        binding.cbSearchType3.isChecked = oldSortCondition == 3
        binding.cbSearchType4.isChecked = oldSortMode == 1
        binding.cbSearchType5.isChecked = oldSortMode == 2

        binding.btnSearchType1.setOnClickListener {
            oldSortCondition = 1
            binding.cbSearchType1.isChecked = true
            binding.cbSearchType2.isChecked = false
            binding.cbSearchType3.isChecked = false
        }
        binding.btnSearchType2.setOnClickListener {
            oldSortCondition = 2
            binding.cbSearchType1.isChecked = false
            binding.cbSearchType2.isChecked = true
            binding.cbSearchType3.isChecked = false
        }
        binding.btnSearchType3.setOnClickListener {
            oldSortCondition = 3
            binding.cbSearchType1.isChecked = false
            binding.cbSearchType2.isChecked = false
            binding.cbSearchType3.isChecked = true
        }
        binding.btnSearchType4.setOnClickListener {
            oldSortMode = 1
            binding.cbSearchType4.isChecked = true
            binding.cbSearchType5.isChecked = false
        }
        binding.btnSearchType5.setOnClickListener {
            oldSortMode = 2
            binding.cbSearchType4.isChecked = false
            binding.cbSearchType5.isChecked = true
        }
    }

    fun setOnSettingBack(listener: OnSettingBack) {
        binding.vm!!.listener = listener
    }

    interface OnSettingBack {
        fun onConfirmClick(condition: Int, mode: Int)
    }
}