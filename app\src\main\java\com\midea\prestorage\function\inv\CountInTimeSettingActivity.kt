package com.midea.prestorage.function.inv

import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.function.inv.CountInTimeSettingVM.Companion.ADD
import com.midea.prestorage.function.inv.CountInTimeSettingVM.Companion.BOXES_NUM
import com.midea.prestorage.function.inv.CountInTimeSettingVM.Companion.COVER
import com.midea.prestorage.function.inv.CountInTimeSettingVM.Companion.MIN_UNITS
import com.midea.prestorage.function.inv.CountInTimeSettingVM.Companion.ONE
import com.midea.prestorage.function.inv.CountInTimeSettingVM.Companion.countingMethod
import com.midea.prestorage.function.inv.CountInTimeSettingVM.Companion.quantityEntryMode
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityCountInTimeSettingBinding

class CountInTimeSettingActivity : BaseViewModelActivity<CountInTimeSettingVM>() {

    lateinit var binding: ActivityCountInTimeSettingBinding

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_count_in_time_setting)
        vm = ViewModelProvider.AndroidViewModelFactory(application).create(CountInTimeSettingVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        initCheckBox()
    }

    private fun initCheckBox() {

        binding.cbSelectCover.isChecked = countingMethod == COVER
        binding.cbSelectAccumulation.isChecked = countingMethod == ADD
        binding.cbSelectOne.isChecked = countingMethod == ONE

        binding.cbSelectMinUnits.isChecked = quantityEntryMode == MIN_UNITS
        binding.cbSelectByNum.isChecked = quantityEntryMode == BOXES_NUM

        binding.cbSelectByNum.isClickable = countingMethod != ONE

        binding.cbSelectCover.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.cbSelectAccumulation.isChecked = false
                binding.cbSelectOne.isChecked = false
            } else if (!binding.cbSelectAccumulation.isChecked && !binding.cbSelectOne.isChecked) {
                binding.cbSelectCover.isChecked = true
            }
            countingMethod = COVER
            binding.cbSelectByNum.isClickable = true
        }

        binding.cbSelectAccumulation.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.cbSelectCover.isChecked = false
                binding.cbSelectOne.isChecked = false
            } else if (!binding.cbSelectCover.isChecked && !binding.cbSelectOne.isChecked) {
                binding.cbSelectAccumulation.isChecked = true
            }
            countingMethod = ADD
            binding.cbSelectByNum.isClickable = true
        }

        binding.cbSelectOne.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.cbSelectCover.isChecked = false
                binding.cbSelectAccumulation.isChecked = false
            } else if (!binding.cbSelectCover.isChecked && !binding.cbSelectAccumulation.isChecked) {
                binding.cbSelectOne.isChecked = true
            }
            countingMethod = ONE
            binding.cbSelectByNum.isClickable = false
            binding.cbSelectMinUnits.isChecked = true
            quantityEntryMode = MIN_UNITS
        }

        binding.cbSelectMinUnits.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.cbSelectByNum.isChecked = false
            } else if (!binding.cbSelectByNum.isChecked) {
                binding.cbSelectMinUnits.isChecked = true
            }
            quantityEntryMode = MIN_UNITS
        }

        binding.cbSelectByNum.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.cbSelectMinUnits.isChecked = false
            } else if (!binding.cbSelectMinUnits.isChecked) {
                binding.cbSelectByNum.isChecked = true
            }
            quantityEntryMode = BOXES_NUM
        }
    }

    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }

}