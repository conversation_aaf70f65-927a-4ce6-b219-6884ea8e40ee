package com.midea.prestorage.function.instorage

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.*
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestoragesaas.databinding.ActivityInStorageConfirmBinding
import com.midea.prestorage.function.instorage.response.InReceiptSerial
import com.midea.prestorage.function.inv.response.BsLocation
import com.midea.prestorage.utils.DCUtils
import java.util.ArrayList
import android.widget.TextView

import android.view.ViewGroup

import android.widget.BaseAdapter
import com.xuexiang.xqrcode.XQRCode


class InStorageConfirmActivity : BaseActivity() {

    lateinit var binding: ActivityInStorageConfirmBinding
    var adapter = ListInReceiptSerialAdapter()
    var spinnerAdapter = SpinnerLocationAdapter()
    //lateinit var locationDialog: LocationChooseDialog


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_in_storage_confirm)
        binding.vm = InStorageConfirmVM(this)

        initRecycleView()

        binding.etLocationCode.setAdapter(spinnerAdapter)


        // 初始化 可供选择的上架库位列表
        binding.vm!!.initLocationList()

        // 光标自动定位到 库位编辑框
        binding.etLocationCode.requestFocus()

    }

    override fun getTvInfo(): TextView {
        return binding.tvNotification
    }

    // 弹框选择库位
    /*private fun initLocationDialog() {
        locationDialog = LocationChooseDialog(this)
        locationDialog.setTitle("选择上架库位")
        locationDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            var locCode = (it as BsLocation).locCode.toString()
            binding.vm!!.locCode.set(locCode)
            locationDialog.dismiss()
        })
    }*/



    fun initRecycleView() {

        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter

        /*  点击监听
        adapter.setOnCheckListener {
        }
        */

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == BaseActivity.QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果
            binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }


    class SpinnerLocationAdapter : BaseAdapter(), Filterable {

        private var searchResults = ArrayList<BsLocation>()
        private var originDatas = mutableListOf<BsLocation>()

        fun setData(locations : MutableList<BsLocation> ) {
            originDatas  = locations
        }

        fun copyOriginDataAsResult() {
            if(searchResults.isEmpty()) {
                originDatas.forEach {
                    searchResults.add(it)
                }
            }
        }


        override fun getCount(): Int {
            return searchResults.size
        }

        override fun getItem(position: Int): Any {
            if (!searchResults[position].locCode.isNullOrBlank()) {
                return searchResults[position].locCode
            }
            return ""
        }

        override fun getItemId(position: Int): Long {
            return position.toLong()
        }

        override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
            val layout: LinearLayout
            if (convertView == null) {
                layout = LayoutInflater.from(parent?.context).inflate(R.layout.item_for_spinner_location, null) as LinearLayout
            } else {
                layout = convertView as LinearLayout
            }
            layout.findViewById<TextView>(R.id.tvLocCode).setText(searchResults[position].locCode)
            layout.findViewById<TextView>(R.id.tvZoneName).setText(searchResults[position].zoneName)
            return layout
        }

        override fun getFilter(): Filter {
            return object : Filter() {
                override fun performFiltering(constraint: CharSequence?): FilterResults {
                    val results = FilterResults()
                    val newData: ArrayList<BsLocation> = ArrayList()
                    if (constraint != null ) {
                        for (item in originDatas) {
                            if(!item.locCodeAndZoneName.isNullOrBlank() && item.locCodeAndZoneName.contains(constraint)) {
                                newData.add(item)
                            }
                        }
                    }
                    results.values = newData
                    results.count = newData.size
                    return results
                }

                override fun publishResults(constraint: CharSequence?, results: FilterResults) {
                    searchResults = results.values as ArrayList<BsLocation>
                    notifyDataSetChanged()
                }
            }
        }
    }

    class ListInReceiptSerialAdapter :
        ListChoiceClickAdapter<InReceiptSerial>(R.layout.item_for_confirm_inceipt) {

        override fun convert(helper: BaseViewHolder, item: InReceiptSerial) {
            super.convert(helper, item)

            //货品描述
            if (!item.itemName.isNullOrEmpty()) {
                helper.setText(R.id.tvItemName, item.itemName)
            }

            // 客户商品编码
            if (item.custItemCode != null) {
                helper.setText(R.id.tvCustItemCode, item.custItemCode.toString())
            }

            // 数量
            if (item.qty != null) {
                helper.setText(R.id.tvScanNum, item.qty.toInt().toString())
            }

            // 属性4  状态
            if (!item.lotAtt04.isNullOrEmpty()) {
                if (DCUtils.lot4TypeC2N.get(item.lotAtt04) != null) {
                    helper.setText(R.id.tvLot4, DCUtils.lot4TypeC2N.get(item.lotAtt04).toString())
                } else {
                    helper.setText(R.id.tvLot4, item.lotAtt04)
                }

                if (item.lotAtt04.toUpperCase().equals("Y")) {  //正品
                    helper.setTextColorRes(R.id.tvLot4, R.color.ui_font_color_blue)
                } else if (item.lotAtt04.toUpperCase().equals("N")) {  //不良品
                    helper.setTextColorRes(R.id.tvLot4, R.color.ui_font_color_orange)
                } else if (item.lotAtt04.toUpperCase().equals("B")) { //包装破损
                    helper.setTextColorRes(R.id.tvLot4, R.color.ui_font_color_red)
                } else {
                    helper.setTextColorRes(R.id.tvLot4, R.color.ui_font_color_default)
                }
            }

            // 除了 状态 lot4 以外的其他属性，如果有就显示
            var lotInfos = ""
            val arr = arrayListOf<String>()

            if (!item.lotAtt05.isNullOrEmpty()) {
                arr.add("批次: " + item.lotAtt05)
            }
            if (!item.lotAtt01.isNullOrEmpty()) {
                var info = item.lotAtt01 // 把日期格式转成 yyyymmdd
                if (info.split(" ").size > 0) {
                    info = item.lotAtt01.split(" ")[0]
                }
                arr.add("生产日期: " + info.replace("-", ""))
            }
            if (!item.lotAtt02.isNullOrEmpty()) {
                var info = item.lotAtt02 // 把日期格式转成 yyyymmdd
                if (info.split(" ").size > 0) {
                    info = item.lotAtt02.split(" ")[0]
                }
                arr.add("失效日期: " + info.replace("-", ""))
            }
            if (!item.lotAtt03.isNullOrEmpty()) {
                var info = item.lotAtt03 // 把日期格式转成 yyyymmdd
                if (info.split(" ").size > 0) {
                    info = item.lotAtt03.split(" ")[0]
                }
                arr.add("入库日期: " + info.replace("-", ""))
            }
            if (!item.lotAtt06.isNullOrEmpty()) {
                arr.add("属性06: " + item.lotAtt06)
            }
            if (!item.lotAtt07.isNullOrEmpty()) {
                arr.add("属性07: " + item.lotAtt07)
            }
            if (!item.lotAtt08.isNullOrEmpty()) {
                arr.add("属性08: " + item.lotAtt08)
            }
            if (!item.lotAtt09.isNullOrEmpty()) {
                arr.add("属性09: " + item.lotAtt09)
            }
            if (!item.lotAtt10.isNullOrEmpty()) {
                arr.add("属性10: " + item.lotAtt10)
            }
            if (!item.lotAtt11.isNullOrEmpty()) {
                arr.add("属性11: " + item.lotAtt11)
            }
            if (!item.lotAtt12.isNullOrEmpty()) {
                arr.add("属性12: " + item.lotAtt12)
            }
            lotInfos = arr.joinToString(separator = " / ")
            helper.setText(R.id.tvLotInfos, lotInfos)

        }
    }




}