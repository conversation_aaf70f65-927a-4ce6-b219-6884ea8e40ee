package com.midea.prestorage.function.containerpick.dialog

import android.app.AlertDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestorage.dialog.DialogTipUnionBinding
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogCopyTextBinding
import com.midea.prestoragesaas.databinding.DialogErrorTipBinding
import com.midea.prestorage.dialog.TipDialog
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.SPUtils
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

class ErrorTipDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {

    private var binding: DialogErrorTipUnionBinding

    init {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window?.setGravity(Gravity.CENTER)
            window?.attributes?.run {
                gravity = Gravity.CENTER
            }
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_error_tip_care, null)
            setView(contentView)
            DialogErrorTipUnionBinding.V2(DataBindingUtil.bind(contentView)!!)
        } else {
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_error_tip, null)
            setView(contentView)
            DialogErrorTipUnionBinding.V1(DataBindingUtil.bind(contentView)!!)
        }
        binding.vm = ErrorTipDialogVM(this)
    }

    fun getCheckedText(): String {
        return when {
            binding.rbA.isChecked -> {
                binding.rbA.text.toString()
            }
            binding.rbB.isChecked -> {
                binding.rbB.text.toString()
            }
            else -> {
                binding.rbC.text.toString()
            }
        }
    }

    fun setOnErrorTipBackListener(listener: OnErrorTipBack) {
        binding.vm!!.listener = listener
    }

    interface OnErrorTipBack {
        fun onConfirmClick(reasonStr: String)
    }
}
