package com.midea.prestorage.function.inv

import android.view.View
import androidx.databinding.ObservableField

// 计划盘点  项目一期 不做，代码留着先
class CountInPlanVM (val activity: CountInPlanActivity) {

    //盘点库位编码 countLocationCode
    val countLocationCode = ObservableField("")

    init {

    }


    //后退键
    val back = View.OnClickListener {
        activity.finish()
    }


    //释放
    val release = View.OnClickListener {
    }


    //盘点完成
    val finishCount = View.OnClickListener {
    }
}