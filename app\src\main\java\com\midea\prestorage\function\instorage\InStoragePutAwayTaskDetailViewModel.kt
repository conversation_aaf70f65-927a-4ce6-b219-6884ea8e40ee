package com.midea.prestorage.function.instorage

import CheckUtil
import android.app.Application
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Transformations
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.*
import com.midea.prestorage.event.LiveEvent
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.MySoundUtils
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal

class InStoragePutAwayTaskDetailViewModel(application: Application) : BaseViewModel(application) {

    val qtyLiveData = MutableLiveData<String>()
    val locLiveData = MutableLiveData<String>()
    val custItemCode = MutableLiveData<String>()
    val itemName = MutableLiveData<String>()
    val lotAtt01 = MutableLiveData<String>()
    val lotAtt03 = MutableLiveData<String>()
    val lotAtt05 = MutableLiveData<String>()
    val statusStr = MutableLiveData<String>()
    val recommendLoc1 = MutableLiveData<String>()
    val showRecommendLoc1 = Transformations.map(recommendLoc1) {
        it.isNotEmpty()
    }
    val recommendLoc2 = MutableLiveData<String>()
    val showRecommendLoc2 = Transformations.map(recommendLoc2) {
        it.isNotEmpty()
    }
    val recommendLoc3 = MutableLiveData<String>()
    val showRecommendLoc3 = Transformations.map(recommendLoc3) {
        it.isNotEmpty()
    }

    val finishEvent = LiveEvent<Unit>()

    var totalQty: BigDecimal = BigDecimal.ZERO
    var recordIds: List<String> = emptyList()

    val settingViewModel = InStoragePutAwayTaskSettingViewModel(application)

    var putAwayTask: PutAwayTask? = null

    override fun init() {
    }

    fun update(putAwayTask: PutAwayTask) {
        totalQty = putAwayTask.totalQty ?: BigDecimal.ZERO
        recordIds = putAwayTask.recordIds?.split(",")?.toMutableList() ?: emptyList()
        custItemCode.value = (putAwayTask.custItemCode ?: "") + if (putAwayTask.whBarcode69?.isNotEmpty() == true) " (${putAwayTask.whBarcode69})" else ""
        itemName.value = putAwayTask.itemName ?: ""
        lotAtt01.value = putAwayTask.lotAtt01 ?: ""
        lotAtt03.value = putAwayTask.lotAtt03 ?: ""
        lotAtt05.value = putAwayTask.lotAtt05 ?: ""
        qtyLiveData.value = putAwayTask.totalQty?.toInt()?.toString()
        recommendLoc()
    }

    fun updateGoodStatus(putAwayTask: PutAwayTask) {
        statusStr.value = putAwayTask.lotAtt04Str ?: ""
    }

    fun initSetting() {
        settingViewModel.init()
    }

    private fun fillRecommendLoc(locCode: String?) {
        val locList = locCode?.split(",")
        recommendLoc1.value = locList?.getOrNull(0) ?: ""
        recommendLoc2.value = locList?.getOrNull(1) ?: ""
        recommendLoc3.value = locList?.getOrNull(2) ?: ""
    }

    fun setRecommendLoc(index: Int) {
        locLiveData.value = when (index) {
            0 -> recommendLoc1.value
            1 -> recommendLoc2.value
            else -> recommendLoc3.value
        }
    }

    fun confirm() = launch(true, error = {}) {
        if (CheckUtil.isFastDoubleClick()) {
            checkUserInput()
            val total = totalQty.toInt()
            val submit = qtyLiveData.value!!.toInt()
            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getAppAPI().putAwayTaskConfirm(
                    PutAwayTaskConfirm(
                        recordIds = recordIds.joinToString(","),
                        locCode = locLiveData.value ?: "",
                        submitQty = submit,
                        putAwayQty = total
                    )
                )
            }
            if (result.code == 0L) {
                MySoundUtils.getInstance().dingSound()
                finishEvent.value = Unit
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    @JvmOverloads
    fun recommendLoc() = launch(showDialog = true) {
        if (CheckUtil.isFastDoubleClick()) {
            val map = mutableMapOf(
                "whCode" to Constants.whInfo?.whCode,
                "ownerCode" to putAwayTask?.ownerCode,
                "scenario" to "receipt",
                "orderType" to putAwayTask?.receiptType,
                "qty" to qtyLiveData.value,
                "packageUnit" to "EA",
                "lotAtt01" to putAwayTask?.lotAtt01,
                "lotAtt02" to putAwayTask?.lotAtt02,
                "lotAtt04" to putAwayTask?.lotAtt04,
                "lotAtt05" to putAwayTask?.lotAtt05,
                "lotAtt06" to putAwayTask?.lotAtt06,
                "containerCode" to putAwayTask?.containerCode,
                "itemCode" to putAwayTask?.itemCode,
                "locCode" to ""
            )
            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(map)
            )

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getAppAPI().getRecommendLocNew(requestBody)
            }
            if (result.code == 0L) {
                fillRecommendLoc(result.data?.locCode)
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    @VisibleForTesting
    @Throws(IllegalArgumentException::class)
    fun checkUserInput() {
        if (qtyLiveData.value.isNullOrBlank()) {
            throw IllegalArgumentException("上架数量不能为空")
        }
        if (qtyLiveData.value == "0") {
            throw IllegalArgumentException("上架数量不能为0")
        }
        if (BigDecimal(qtyLiveData.value) > totalQty) {
            throw IllegalArgumentException("上架数量不能大于待上架数量")
        }
        if (locLiveData.value.isNullOrBlank()) {
            throw IllegalArgumentException("上架库位不能为空")
        }
    }

}