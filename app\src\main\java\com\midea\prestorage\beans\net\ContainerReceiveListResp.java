package com.midea.prestorage.beans.net;

import androidx.annotation.Nullable;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;
import java.math.BigDecimal;

public class ContainerReceiveListResp implements Serializable {

    @ShowAnnotation
    private int index;
    @ShowAnnotation
    private String ownerCode;
    @ShowAnnotation
    private String ownerName;
    @ShowAnnotation
    private String receiptCode;
    @ShowAnnotation
    private String statusStr;
    @ShowAnnotation
    private String custOrderNo;
    @ShowAnnotation
    private String carNo;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal planQty;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal realQty;
    @ShowAnnotation
    private String supplierName;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal skuNum;
    @Nullable
    private String shipFromName;
    @Nullable
    private String receiptType;
    @ShowAnnotation
    @Nullable
    private String relationOrderNo;
    @ShowAnnotation
    @Nullable
    private String receiptTypeStr;
    @ShowAnnotation
    @Nullable
    private String waveNo;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal orderNum;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal totalQty;
    @ShowAnnotation(isDecimal = true)
    private BigDecimal receiptQty;

    private String csUnit;
    private String eaUnit;
    private BigDecimal csQty;
    private BigDecimal eaQty;
    private String status;
    @Nullable
    private String serviceType;
    @Nullable
    private BigDecimal receiptPercent;
    @ShowAnnotation
    @Nullable
    private String startReceiveDate;
    @ShowAnnotation
    @Nullable
    private String receiptCost;

    @Nullable
    public String getReceiptCost() {
        return receiptCost;
    }

    public void setReceiptCost(@Nullable String receiptCost) {
        this.receiptCost = receiptCost;
    }

    @Nullable
    public String getStartReceiveDate() {
        return startReceiveDate;
    }

    public void setStartReceiveDate(@Nullable String startReceiveDate) {
        this.startReceiveDate = startReceiveDate;
    }

    public BigDecimal getReceiptQty() {
        return receiptQty;
    }

    public void setReceiptQty(BigDecimal receiptQty) {
        this.receiptQty = receiptQty;
    }

    public BigDecimal getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public BigDecimal getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(BigDecimal orderNum) {
        this.orderNum = orderNum;
    }

    @Nullable
    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(@Nullable String waveNo) {
        this.waveNo = waveNo;
    }

    @Nullable
    public BigDecimal getReceiptPercent() {
        return receiptPercent;
    }

    public void setReceiptPercent(@Nullable BigDecimal receiptPercent) {
        this.receiptPercent = receiptPercent;
    }

    @Nullable
    public String getReceiptTypeStr() {
        return receiptTypeStr;
    }

    public void setReceiptTypeStr(@Nullable String receiptTypeStr) {
        this.receiptTypeStr = receiptTypeStr;
    }

    @Nullable
    public String getRelationOrderNo() {
        return relationOrderNo;
    }

    public void setRelationOrderNo(@Nullable String relationOrderNo) {
        this.relationOrderNo = relationOrderNo;
    }

    @Nullable
    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(@Nullable String serviceType) {
        this.serviceType = serviceType;
    }

    @Nullable
    public String getReceiptType() {
        return receiptType;
    }

    public void setReceiptType(@Nullable String receiptType) {
        this.receiptType = receiptType;
    }

    @Nullable
    public String getShipFromName() {
        return shipFromName;
    }

    public void setShipFromName(@Nullable String shipFromName) {
        this.shipFromName = shipFromName;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public BigDecimal getSkuNum() {
        return skuNum;
    }

    public void setSkuNum(BigDecimal skuNum) {
        this.skuNum = skuNum;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getReceiptCode() {
        return receiptCode;
    }

    public void setReceiptCode(String receiptCode) {
        this.receiptCode = receiptCode;
    }

    public String getCustOrderNo() {
        return custOrderNo;
    }

    public void setCustOrderNo(String custOrderNo) {
        this.custOrderNo = custOrderNo;
    }

    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }

    public BigDecimal getPlanQty() {
        return planQty;
    }

    public void setPlanQty(BigDecimal planQty) {
        this.planQty = planQty;
    }

    public BigDecimal getRealQty() {
        return realQty;
    }

    public void setRealQty(BigDecimal realQty) {
        this.realQty = realQty;
    }

    public String getCsUnit() {
        return csUnit;
    }

    public void setCsUnit(String csUnit) {
        this.csUnit = csUnit;
    }

    public String getEaUnit() {
        return eaUnit;
    }

    public void setEaUnit(String eaUnit) {
        this.eaUnit = eaUnit;
    }

    public BigDecimal getCsQty() {
        return csQty;
    }

    public void setCsQty(BigDecimal csQty) {
        this.csQty = csQty;
    }

    public BigDecimal getEaQty() {
        return eaQty;
    }

    public void setEaQty(BigDecimal eaQty) {
        this.eaQty = eaQty;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
