package com.midea.prestorage.function.containerpick.dialog

import androidx.databinding.ObservableField

class CombinedSettingDialogVM(val dialog: CombinedSettingDialog) {

    val title = ObservableField<String>("请选择")
    var listener: CombinedSettingDialog.OnSettingBack? = null

    fun show() {

    }

    fun close() {
        dialog.dismiss()
    }

    fun confirm() {
        dialog.dismiss()
        listener?.onConfirmClick(dialog.oldSortCondition, dialog.oldSortMode)
    }
}