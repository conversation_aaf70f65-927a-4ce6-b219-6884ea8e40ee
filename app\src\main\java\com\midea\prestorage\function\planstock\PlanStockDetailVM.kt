package com.midea.prestorage.function.planstock

import CheckUtil
import android.os.Handler
import android.text.TextUtils
import android.view.View
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.PlanStockDetailList
import com.midea.prestorage.beans.net.PlanStockList
import com.midea.prestorage.beans.net.SerialScanDto
import com.midea.prestorage.dialog.TipDialog
import com.midea.prestorage.function.inv.response.ItemRfVO
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.function.planstock.dialog.InputNumDialog
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal


class PlanStockDetailVM(val activity: PlanStockDetailActivity) {

    val title = ObservableField("")

    val qty = ObservableField("")
    val goodsEnable = ObservableField(true)
    val qtyEnable = ObservableField(false)

    val taskCode = ObservableField("")
    val locCode = ObservableField("")
    val serialNo = ObservableField("")

    val itemName = ObservableField("")

    var planTask = activity.intent.getSerializableExtra("bean") as PlanStockList

    var goodsStatusCode: DCBean? = null //Y为正品，N为不良品, B为包装破损
    var goodsStatueBeans: MutableList<DCBean>? = null
    var serialScanDto: ItemRfVO? = null

    // 当前页码
    var pageNo = 1

    private var tipDialog: TipDialog
    var inputDialog: InputNumDialog

    init {
        DCUtils.goodsStatue(activity as RxAppCompatActivity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                goodsStatueBeans = statusDC
                statusDefault()
                setDict(activity.adapter.data)
            }
        })

        taskCode.set(planTask.taskCode)
        locCode.set(planTask.zoneCode + "/" + planTask.locCode)

        initData(false)

        tipDialog = TipDialog(activity)

        inputDialog = InputNumDialog(activity)
        inputDialog.inputBack = object : InputNumDialog.InputNumBack {
            override fun inputOk(
                data: PlanStockDetailList,
                num: BigDecimal,
                isEnterKeyPress: Boolean,
                isAddFirstQty: Boolean,
                lotAtt01: String,
                lotAtt02: String,
                lotAtt05: String
            ) {
                if (isEnterKeyPress) {
                    planSubmit(
                        num, data.itemName,
                        data.custItemCode,
                        data.itemCode,
                        isAddFirstQty,
                        data.ownerCode,
                        data.ownerName,
                        lotAtt01,
                        lotAtt02,
                        lotAtt05
                    )
                } else {
                    planSubmitClick(data, num, isAddFirstQty)
                }
            }

            override fun inputFail() {
            }
        }
    }

    val serialKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                serialScan()
            }
        }
    }

    fun setOnItemClick(item: PlanStockDetailList) {
        inputDialog.deleteInfo = item
        inputDialog.isEnterKeyPress = false
        inputDialog.existTaskDetail = true
        inputDialog.show()
    }

    fun planSubmitClick(
        data: PlanStockDetailList,
        num: BigDecimal,
        isAddFirstQty: Boolean
    ) {
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "taskCode" to planTask.taskCode,
            "locCode" to planTask.locCode,
            "amount" to num,
            "lotAtt04" to data.lotAtt04,
            "custItemCode" to data.custItemCode,
            "itemName" to data.itemName,
            "ownerName" to data.ownerName,
            "ownerCode" to data.ownerCode,
            "zoneCode" to planTask.zoneCode,
            "itemCode" to data.itemCode,
            "isAddFirstQty" to isAddFirstQty
        )

        if (data.isLot05Check == 1) {
            param["lotAtt05"] = data.lotAtt05
        }

        if (data.isLot01Check == 1) {
            param["lotAtt01"] = data.lotAtt01
        }

        if (data.isLot02Check == 1) {
            param["lotAtt02"] = data.lotAtt02
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        // 货品条码校验
        RetrofitHelper.getWareManageAPI()
            .planSubmit(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()

                    serialNo.set("")
                    goodsEnable.set(true)
                    activity.goodsFocus()

                    itemName.set("")


                    qty.set("")
                    qtyEnable.set(false)

                    initData(false)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }


    fun initData(isLoadMore: Boolean) {
        if (!isLoadMore) {
            pageNo = 1
        }
        val map = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "taskCode" to planTask.taskCode,
            "pageSize" to "10",
            "pageNo" to pageNo.toString()
        )
        RetrofitHelper.getWareManageAPI()
            .detailPage(map)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PageResult<PlanStockDetailList>>(activity) {
                override fun success(data: PageResult<PlanStockDetailList>?) {
                    activity.adapter.loadMoreModule.loadMoreComplete()

                    data?.let {
                        if (pageNo < it.totalPage) {
                            pageNo++
                        } else {
                            Handler(activity.mainLooper).post {
                                //加载到了最后一页
                                activity.adapter.loadMoreModule.loadMoreEnd()
                            }
                        }
                        if (isLoadMore) {
                            activity.adapter.addData(it.list)
                        } else {
                            activity.adapter.setNewInstance(it.list)
                        }
                        setDict(it.list)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.adapter.loadMoreModule.loadMoreComplete()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun setDict(list: MutableList<PlanStockDetailList>) {
        if (goodsStatueBeans == null || list.isEmpty()) {
            return
        }

        list.forEach {
            val resultStatue =
                goodsStatueBeans?.find { item -> it.lotAtt04 == item.value.toString() }
            if (resultStatue != null) {
                it.lotAtt04Str = resultStatue.key.toString()
            }
        }

        activity.adapter.notifyDataSetChanged()
    }

    fun serialScan() {
        // 清除前后空格
        val snNo = serialNo.get().toString().trim()

        if (TextUtils.isEmpty(snNo)) {
            return
        }

        //先校验 当前输入的 货品编码 或sn码
        val param = mutableMapOf(
            "serialNo" to snNo,
            "whCode" to activity.getWhCode(),
            "tackCode" to planTask.taskCode
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        // 货品条码校验
        RetrofitHelper.getAppAPI()
            .serialStockScan(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<SerialScanDto>(activity) {
                override fun success(data: SerialScanDto?) {

                    if (data != null) {
                        if (!data.itemRfVOS.isNullOrEmpty() && data.itemRfVOS.size == 1) {
                            itemName.set(data.itemRfVOS[0].itemName)
                            serialScanDto = data.itemRfVOS.getOrNull(0)

                            qty.set("")
                            qtyEnable.set(true)
                            existTaskDetail(data.itemRfVOS[0].custItemCode, data.itemRfVOS[0].ownerCode)
                        } else if (!data.itemRfVOS.isNullOrEmpty() && data.itemRfVOS.size > 1) {
                            activity.waitingDialogHelp.hidenDialog()
                            serialNo.set("")
                            //扫69码 后端没有返回 custItemCode  如果有custItemCode数组，就弹框选择
                            activity.dlgSelectCustItemCode.show()
                            activity.popAdapterSelectCustItemCode.data.clear()
                            data.itemRfVOS.forEach {
                                activity.popAdapterSelectCustItemCode.addData(it)
                            }
                            activity.popAdapterSelectCustItemCode.notifyDataSetChanged()

                        } else if (!data.serialNo.isNullOrBlank()) {
                            activity.waitingDialogHelp.hidenDialog()
                            activity.binding.edGoods.setText(data.serialNo)
                        }
                    }else {
                        activity.waitingDialogHelp.hidenDialog()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    serialNo.set("")
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun planSubmit(
        amount: BigDecimal,
        name: String?,
        custItemCode: String?,
        itemCode: String?,
        isAddFirstQty: Boolean,
        ownerCode: String,
        ownerName: String,
        lotAtt01: String,
        lotAtt02: String,
        lotAtt05: String
    ) {
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "taskCode" to planTask.taskCode,
            "locCode" to planTask.locCode,
            "amount" to amount,
            "lotAtt04" to if (goodsStatusCode == null) "Y" else goodsStatusCode?.value.toString(),
            "custItemCode" to custItemCode,
            "itemName" to name,
            "zoneCode" to planTask.zoneCode,
            "itemCode" to itemCode,
            "isAddFirstQty" to isAddFirstQty,
            "ownerName" to ownerName,
            "ownerCode" to ownerCode,
        )

        if (lotAtt01.isNotEmpty()) {
            param["lotAtt01"] = lotAtt01
        }

        if (lotAtt02.isNotEmpty()) {
            param["lotAtt02"] = lotAtt02
        }

        if (lotAtt05.isNotEmpty()) {
            param["lotAtt05"] = lotAtt05
        }


        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        // 货品条码校验
        RetrofitHelper.getWareManageAPI()
            .planSubmit(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()

                    serialNo.set("")
                    goodsEnable.set(true)
                    activity.goodsFocus()

                    itemName.set("")


                    qty.set("")
                    qtyEnable.set(false)

                    initData(false)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun stockFinish() {
        tipDialog.setTitle("盘点完成确认")
        if (activity.adapter.data.isEmpty()) {
            tipDialog.setMsg("该库位尚未添加任何商品，确认结束盘点吗?")
            tipDialog.setCancelTitle("否")
            tipDialog.setConfirmTitle("是")

        } else {
            tipDialog.setMsg("是否完成该盘点任务?")
        }
        tipDialog.setOnTipBackListener(object : TipDialog.OnTipBack {
            override fun onConfirmClick() {
                planCheck()
            }

            override fun onDismissClick() {
            }
        })
        tipDialog.show()
    }

    fun planCheck() {
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "taskCode" to planTask.taskCode,
            "locCode" to planTask.locCode
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        // 货品条码校验
        RetrofitHelper.getWareManageAPI()
            .planCheck(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    stockFinishOperation()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (statusCode == 716108L) {
                        tipDialog.setMsg(apiErrorModel.message)
                        tipDialog.setOnTipBackListener(object : TipDialog.OnTipBack {
                            override fun onConfirmClick() {
                                stockFinishOperation()
                            }

                            override fun onDismissClick() {
                            }
                        })
                        tipDialog.show()
                    } else {
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, apiErrorModel.message)
                    }
                }
            })
    }

    fun stockFinishOperation() {
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "taskCode" to planTask.taskCode,
            "locCode" to planTask.locCode
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        // 货品条码校验
        RetrofitHelper.getWareManageAPI()
            .planDone(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    AppUtils.showToast(activity, "盘点完成!")
                    activity.waitingDialogHelp.hidenDialog()
                    activity.finish()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun existTaskDetail(custItemCode: String, ownerCode: String) {
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "taskCode" to planTask.taskCode,
            "locCode" to planTask.locCode,
            "custItemCode" to custItemCode,
            "lotAtt04" to if (goodsStatusCode == null) "Y" else goodsStatusCode?.value.toString(),
            "ownerCode" to ownerCode
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        // 货品条码校验
        RetrofitHelper.getWareManageAPI()
            .existTaskDetail(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Boolean>(activity) {
                override fun success(data: Boolean?) {
                    activity.waitingDialogHelp.hidenDialog()
                    data?.let {
                        activity.qtyFocus(it)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun statusDefault() {
        if (goodsStatueBeans == null) {
            return
        }

        val result = goodsStatueBeans!!.find { it.value.toString() == "Y" }
        var indexOf = 0
        goodsStatusCode = if (result != null) {
            indexOf = goodsStatueBeans!!.indexOf(result)

            val result = result
            result
        } else {
            goodsStatueBeans!![0]
        }
        activity.initSpinner(goodsStatueBeans!!, indexOf)
    }

    fun onChangeStatue(dcBean: DCBean) {
        goodsStatusCode = dcBean
    }

    fun back() {
        activity.finish()
    }

    fun startSearch() {
        if (pageNo > 1) {
            initData(true)
        }
    }
}