package com.midea.prestorage.function.inv.dialog

import android.view.View
import androidx.databinding.ObservableField
import com.midea.prestorage.beans.net.RespAdjustDetailPage
import com.midea.prestorage.utils.AppUtils
import java.math.BigDecimal

class InvRecDeleteDialogVM(val dialog: InvRecDeleteDialog) {
    var bean: RespAdjustDetailPage? = null
    var backDataListener: InvRecDeleteDialog.BackData? = null
    val cdpaFormat = ObservableField("")
    val qty = ObservableField("")
    var cdprQuantity = BigDecimal.ONE //这个是包装系数

    /**
     * 确认按钮
     */
    val confirmClick = View.OnClickListener {
        if (CheckUtil.isFastDoubleClick()) {
            if (qty.get().toString().trim().isNullOrEmpty()) {
                AppUtils.showToast(dialog.mContext, "请输入数量")
                return@OnClickListener
            }
            backDataListener?.onConfirmClick(
                bean?.id,
                AppUtils.getBigDecimalValue(qty.get()).multiply(cdprQuantity)
            )
        }
    }

    /**
     * 取消按钮
     */
    var cancelClick = View.OnClickListener {
        backDataListener?.onCancelClick()
        dialog.dismiss()
    }
}