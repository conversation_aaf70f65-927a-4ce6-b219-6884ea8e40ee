package com.midea.prestorage.base

import android.app.Application
import android.content.Intent
import android.graphics.drawable.ColorDrawable
import androidx.core.content.ContextCompat
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.midea.prestoragesaas.R
import com.midea.prestorage.function.MainDialog
import com.midea.prestorage.utils.MySoundUtils
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext

abstract class BaseViewModel(app: Application) : AndroidViewModel(app) {

    var isDialogShow = MutableLiveData<Int>() //0为停止，1为转圈15秒，2为永久转
    var notificationInfo = MutableLiveData<String>()
    var isShowNotification = MutableLiveData(false)
    var backNotification = MutableLiveData(false)
    var statusColor = MutableLiveData<ColorDrawable>()
    val toActivity = MutableLiveData<Map<Intent, Class<*>>>()

    fun showNotification(msg: String, isSuccess: Boolean, playSound: Boolean = true) {
        if (isSuccess) {
            statusColor.value =
                ColorDrawable(
                    ContextCompat.getColor(getApplication(), R.color.colorGreen)
                )
            if (playSound) {
                MySoundUtils.getInstance().dingSound()
            }
        } else {
            statusColor.value =
                ColorDrawable(
                    ContextCompat.getColor(getApplication(), R.color.colorRed)
                )
            if (playSound) {
                MySoundUtils.getInstance().errorSound()
            }
        }
        notificationInfo.value = msg
        isShowNotification.value = true
        launch(showDialog = false) {
            val result = withContext(Dispatchers.IO) {
                delay(15000)
                1
            }
            if (result == 1) {
                isShowNotification.value = false
            }
        }
    }

    fun toActivity(
        intent: Intent,
        clazz: Class<*>
    ) {
        toActivity.value = mutableMapOf(intent to clazz)
    }


    abstract fun init()

    fun back() {
        backNotification.value = true
    }
}