package com.midea.prestorage.beans.setting;

import org.xutils.db.annotation.Column;
import org.xutils.db.annotation.Table;

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/9/23$
 */
@Table(name = "InStorageInfo")
public class InStorageInfo {

    @Column(name = "id", isId = true, autoGen = true, property = "NOT NULL")
    private int id;

    //记录订单详情是否汇总的字段  0为明细显示，1为汇总显示
    @Column(name = "orderDetailShowMode")
    private int orderDetailShowMode;

    //记录订单详情是否汇总的字段  0为明细显示，1为汇总显示(出库订单池详情)
    @Column(name = "orderDetailShowModeOut")
    private int orderDetailShowModeOut;

    public InStorageInfo(){
    }

    public int getOrderDetailShowMode() {
        return orderDetailShowMode;
    }

    public void setOrderDetailShowMode(int orderDetailShowMode) {
        this.orderDetailShowMode = orderDetailShowMode;
    }
}
