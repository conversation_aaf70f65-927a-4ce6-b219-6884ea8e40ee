package com.midea.prestorage.function.main

import android.os.Bundle
import android.widget.AdapterView
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.setting.ImplWarehouse
import com.midea.prestoragesaas.databinding.ActivityMainBinding
import com.midea.prestorage.function.main.dialog.WhChooseDialog
import com.midea.prestorage.utils.ToastUtils


class MainActivity : BaseActivity(), WhChooseDialog.ServerRefresh {

    private lateinit var binding: ActivityMainBinding
    private lateinit var whDialog: WhChooseDialog

    val adapterIn = ImageAdapter(this)
    val adapterOut = ImageAdapter(this)
    val adapterStorage = ImageAdapter(this)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_main)
        binding.vm = MainVM(this)

        whDialog = WhChooseDialog(this)
        whDialog.setTitle("请选择仓库")
        whDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            binding.vm!!.whInfoCheck(it)
            binding.vm!!.uploadDevicesInfo()
            whDialog.dismiss()
            binding.vm!!.loadCardData(false)
        })

        binding.vm!!.checkUpGrade(false)

        binding.gridIn.adapter = adapterIn
        binding.gridOut.adapter = adapterOut
        binding.gridStorage.adapter = adapterStorage

        binding.gridIn.onItemClickListener = listener
        binding.gridOut.onItemClickListener = listener
        binding.gridStorage.onItemClickListener = listener
    }

    var listener = AdapterView.OnItemClickListener { parent, _, position, _ ->
        val adapter = parent.adapter as ImageAdapter
        val item = adapter.getItem(position)
        binding.vm!!.onItemClick(item)
    }

    override fun onResume() {
        super.onResume()

        //小卡片数据
        binding.vm!!.loadCardData(false)
        binding.vm!!.loadSettingFromDB()
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun showStorageInfo(list: MutableList<ImplWarehouse>?) {
        if (!list.isNullOrEmpty()) {
            whDialog.setNewData(list)
            whDialog.notifyDataChange(list)
            if (!whDialog.isShowing) {
                whDialog.show()
            }
        } else {
            ToastUtils.getInstance().showErrorToastWithSound(this, "暂无数据!")
        }
    }

    fun whCancelAble(cancelAble: Boolean) {
        whDialog.isAbleCancel(cancelAble)
    }

    override fun refreshByServer() {
        binding.vm!!.resetWhCodeInfo(true)
    }
}