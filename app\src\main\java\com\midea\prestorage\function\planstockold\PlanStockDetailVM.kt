package com.midea.prestorage.function.planstockold

import CheckUtil
import android.os.Handler
import android.text.TextUtils
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.InPoolStorageDetail
import com.midea.prestorage.beans.net.PlanStockDetailList
import com.midea.prestorage.beans.net.PlanStockList
import com.midea.prestorage.beans.net.SerialScanDto
import com.midea.prestorage.dialog.TipDialog
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.function.planstock.dialog.InputNumDialog
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.Observer
import okhttp3.MediaType
import okhttp3.RequestBody
import java.math.BigDecimal
import java.util.concurrent.TimeUnit


class PlanStockDetailVM(val activity: PlanStockDetailActivity) {

    val title = ObservableField("")

    val qty = ObservableField("")
    val goodsEnable = ObservableField(true)
    val qtyEnable = ObservableField(false)

    val taskCode = ObservableField("")
    val locCode = ObservableField("")
    val serialNo = ObservableField("")

    val itemName = ObservableField("")

    var planTask = activity.intent.getSerializableExtra("bean") as PlanStockList

    var goodsStatusCode: DCBean? = null //Y为正品，N为不良品, B为包装破损
    var goodsStatueBeans: MutableList<DCBean>? = null
    var serialScanDto: SerialScanDto? = null

    // 当前页码
    var pageNo = 1

    private var tipDialog: TipDialog
    private var inputDialog: InputNumDialog

    init {
        DCUtils.getGoodsStatusDC(activity as RxAppCompatActivity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                goodsStatueBeans = statusDC
                statusDefault()
                setDict(activity.adapter.data)
            }
        })

        taskCode.set(planTask.taskCode)
        locCode.set(planTask.areaCode + "/" + planTask.locCode)

        initData(false)

        tipDialog = TipDialog(activity)
        inputDialog = InputNumDialog(activity)
        inputDialog.inputBack = object : InputNumDialog.InputNumBack {
            override fun inputOk(
                data: PlanStockDetailList,
                num: BigDecimal,
                isEnterKeyPress: Boolean,
                isAddFirstQty: Boolean,
                lotAtt01: String,
                lotAtt02: String,
                lotAtt05: String
            ) {
                planSubmitClick(data, num)
            }

            override fun inputFail() {
            }
        }
    }

    val serialKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                serialScan()
            }
        }
    }

    val qtyKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                planSubmit()
            }
        }
    }

    fun setOnItemClick(item: PlanStockDetailList) {
        inputDialog.deleteInfo = item
        inputDialog.show()
    }

    fun planSubmitClick(data: PlanStockDetailList, num: BigDecimal) {
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "taskCode" to planTask.taskCode,
            "locCode" to planTask.locCode,
            "amount" to num,
            "lotAtt04" to data.lotAtt04,
            "custItemCode" to data.custItemCode,
            "itemName" to data.itemName,
            "ownerName" to data.ownerName,
            "ownerCode" to data.ownerCode,
            "zoneCode" to planTask.zoneCode
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        // 货品条码校验
        RetrofitHelper.getWareManageAPI()
            .planSubmit(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()

                    serialNo.set("")
                    goodsEnable.set(true)
                    activity.goodsFocus()

                    itemName.set("")


                    qty.set("")
                    qtyEnable.set(false)
                    initData(false)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }


    fun initData(isLoadMore: Boolean) {
        if (!isLoadMore) {
            pageNo = 1
        }
        val map = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "taskCode" to planTask.taskCode,
            "pageSize" to "10",
            "pageNo" to pageNo.toString()
        )
        RetrofitHelper.getWareManageAPI()
            .detailPageOld(map)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PageResult<PlanStockDetailList>>(activity) {
                override fun success(data: PageResult<PlanStockDetailList>?) {
                    activity.adapter.loadMoreModule.loadMoreComplete()

                    data?.let {
                        if (pageNo < it.totalPage) {
                            pageNo++
                        } else {
                            Handler(activity.mainLooper).post {
                                //加载到了最后一页
                                activity.adapter.loadMoreModule.loadMoreEnd()
                            }
                        }
                        if (isLoadMore) {
                            activity.adapter.addData(it.list)
                        } else {
                            activity.adapter.setNewInstance(it.list)
                        }
                        setDict(it.list)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.adapter.loadMoreModule.loadMoreComplete()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun setDict(list: MutableList<PlanStockDetailList>) {
        if (goodsStatueBeans == null || list.isEmpty()) {
            return
        }

        list.forEach {
            val resultStatue =
                goodsStatueBeans?.find { item -> it.lotAtt04 == item.value.toString() }
            if (resultStatue != null) {
                it.lotAtt04Str = resultStatue.key.toString()
            }
        }

        activity.adapter.notifyDataSetChanged()
    }

    fun serialScan() {
        // 清除前后空格
        val snNo = serialNo.get().toString().trim()

        if (TextUtils.isEmpty(snNo)) {
            return
        }

        //先校验 当前输入的 货品编码 或sn码
        val param = mutableMapOf(
            "serialNo" to snNo,
            "whCode" to activity.getWhCode()
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        // 货品条码校验
        RetrofitHelper.getAppAPI()
            .scanCodeOnTransferGood(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<SerialScanDto>(activity) {
                override fun success(data: SerialScanDto?) {
                    activity.waitingDialogHelp.hidenDialog()

                    if (data != null) {
                        if (data.itemRfVOS?.size != 1) {
                            serialNo.set("")
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(activity, "${snNo}维护了多个商品编码，请点击对应行盘点!")
                        } else {
                            itemName.set(data.itemRfVOS[0].itemName)
                            serialScanDto = data

                            goodsEnable.set(false)
                            qty.set("")
                            qtyEnable.set(true)
                            activity.qtyFocus()
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    serialNo.set("")
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun planSubmit() {
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "taskCode" to planTask.taskCode,
            "locCode" to planTask.locCode,
            "amount" to qty.get(),
            "lotAtt04" to if (goodsStatusCode == null) "Y" else goodsStatusCode?.value.toString(),
            "custItemCode" to serialScanDto?.itemRfVOS?.get(0)?.custItemCode,
            "itemName" to itemName.get(),
            "zoneCode" to planTask.zoneCode
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        // 货品条码校验
        RetrofitHelper.getWareManageAPI()
            .planSubmitOld(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()

                    serialNo.set("")
                    goodsEnable.set(true)
                    activity.goodsFocus()

                    itemName.set("")

                    qty.set("")
                    qtyEnable.set(false)
                    initData(false)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun stockFinish() {
        tipDialog.setTitle("盘点完成确认")
        tipDialog.setMsg("是否完成该盘点任务?")
        tipDialog.setOnTipBackListener(object : TipDialog.OnTipBack {
            override fun onConfirmClick() {
                planCheck()
            }

            override fun onDismissClick() {
            }
        })
        tipDialog.show()
    }

    fun planCheck() {
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "taskCode" to planTask.taskCode,
            "locCode" to planTask.locCode
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        // 货品条码校验
        RetrofitHelper.getWareManageAPI()
            .planCheckOld(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    stockFinishOperation()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (statusCode == 716108L) {
                        tipDialog.setMsg(apiErrorModel.message)
                        tipDialog.setOnTipBackListener(object : TipDialog.OnTipBack {
                            override fun onConfirmClick() {
                                stockFinishOperation()
                            }

                            override fun onDismissClick() {
                            }
                        })
                        tipDialog.show()
                    } else {
                        ToastUtils.getInstance()
                            .showErrorToastWithSound(activity, apiErrorModel.message)
                    }
                }
            })
    }

    fun stockFinishOperation() {
        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "taskCode" to planTask.taskCode,
            "locCode" to planTask.locCode
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        // 货品条码校验
        RetrofitHelper.getWareManageAPI()
            .planDoneOld(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun success(data: Any?) {
                    AppUtils.showToast(activity, "盘点完成!")
                    activity.waitingDialogHelp.hidenDialog()
                    activity.finish()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun statusDefault() {
        if (goodsStatueBeans == null) {
            return
        }

        val result = goodsStatueBeans!!.find { it.value.toString() == "Y" }
        goodsStatusCode = if (result != null) {
            val result = result
            result
        } else {
            goodsStatueBeans!![0]
        }
        activity.initSpinner(goodsStatueBeans!!)
    }

    fun onChangeStatue(dcBean: DCBean) {
        goodsStatusCode = dcBean
    }

    fun back() {
        activity.finish()
    }

    fun startSearch() {
        if (pageNo > 1) {
            initData(true)
        }
    }
}