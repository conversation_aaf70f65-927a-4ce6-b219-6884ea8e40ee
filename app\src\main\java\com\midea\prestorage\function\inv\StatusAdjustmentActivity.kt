package com.midea.prestorage.function.inv

import android.content.Intent
import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.ListChoiceClickPositionAdapter
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestoragesaas.databinding.ActivityStatusAdjustmentBinding
import com.midea.prestorage.function.inv.response.HeaderSearchResp
import com.midea.prestorage.utils.AppUtils

class StatusAdjustmentActivity : BaseViewModelActivity<StatusAdjustmentVM>() {
    private lateinit var binding: ActivityStatusAdjustmentBinding
    private lateinit var adapter: InStorageOrderAdapter

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_status_adjustment)
        vm = ViewModelProvider.AndroidViewModelFactory(application)
            .create(StatusAdjustmentVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        //返回
        vm.finishActivity.observe(this, Observer<Boolean> {
            if (it) {
                finish()
            }
        })

        vm.showDatas.observe(this, Observer<MutableList<HeaderSearchResp>> {
            showData(it)
        })

        vm.adjustStatus.observe(this, Observer<MutableList<DCBean>> { result ->
            vm.onRefreshCommand.onRefresh()
        })
        vm.initAdjustStatus()

        initRecycleView()
        initLoadMore()

        //进入扫码页面
        vm.toScanActivity.observe(this, Observer<MutableList<String>> {
            if (it.size > 0) {
                val intent = Intent(this, StatusAdjustmentScanActivity::class.java)
                // 这里塞一个 后端返回的波次号或入库单号
                intent.putExtra("adjustCode", it[0])
                startActivity(intent)
            }
        })

        vm.loadMoreDatas.observe(this, Observer<MutableList<HeaderSearchResp>> {
            loadMoreData(it)
        })

        vm.loadMoreComplete.observe(this, Observer<Int> {
            if (it == 1) {
                adapter.loadMoreModule.loadMoreComplete()
            } else if (it == 2) {
                adapter.loadMoreModule.loadMoreEnd()
            }
        })

        initSpinner()
    }

    private fun initSpinner() {
        val beans = mutableListOf<String>(
            "1天",
            "3天",
            "7天",
            "30天"
        )
        binding.spinnerStatus.setItems(beans)
        binding.spinnerStatus.selectedIndex = 0

        binding.spinnerStatus.setOnItemSelectedListener { _, position, _, _ ->
            vm.dayInfo.value = beans[position]
            vm.onRefreshCommand.onRefresh()
        }
    }

    fun initRecycleView() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        adapter = InStorageOrderAdapter(vm)
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter

        adapter.setOnCheckListener { t, position ->
            val intent = Intent(this, StatusAdjustmentScanActivity::class.java)
            // 这里塞一个 后端返回的波次号或入库单号
            if (!t.adjustCode.isNullOrBlank()) {
                intent.putExtra("adjustCode", t.adjustCode)
            }
            startActivity(intent)
        }
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.loadMore()
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    fun showData(data: MutableList<HeaderSearchResp>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()

        vm.isNoData.value = adapter.data.isNullOrEmpty()
        vm.isRefreshing.value = false
    }

    fun loadMoreData(data: MutableList<HeaderSearchResp>?) {
        data?.let { adapter.addData(it) }
        adapter.notifyDataSetChanged()
    }

    override fun onResume() {
        super.onResume()
        if (!vm.isFirstEnter.value!!) {
            vm.onRefreshCommand.onRefresh()
        }
    }

    class InStorageOrderAdapter(private val vm: StatusAdjustmentVM?) :
        ListChoiceClickPositionAdapter<HeaderSearchResp>(
            R.layout.item_adjust_order
        ),
        LoadMoreModule {

        override fun convert(helper: BaseViewHolder, item: HeaderSearchResp) {
            super.convert(helper, item)

            if(!item.adjustCode.isNullOrEmpty()) {
                helper.setText(R.id.tv_receipt_code, item.adjustCode)
            }else {
                helper.setText(R.id.tv_receipt_code, "")
            }

            if(!item.custOrderNo.isNullOrEmpty()) {
                helper.setText(R.id.tv_order_no, item.custOrderNo)
            }else {
                helper.setText(R.id.tv_order_no, "")
            }

            if(!item.ownerName.isNullOrEmpty()) {
                helper.setText(R.id.tv_dispatch_no, item.ownerName)
            }else {
                helper.setText(R.id.tv_dispatch_no, "")
            }

            if(!AppUtils.getBigDecimalValueStr(item.totalQty).isNullOrBlank()) {
                helper.setText(R.id.tv_total_qty, AppUtils.getBigDecimalValueStr(item.totalQty))
            }else {
                helper.setText(R.id.tv_total_qty, "")
            }

            vm?.adjustStatus?.value?.forEach {
                if (it.key == item?.status) {
                    helper.setText(R.id.tv_status, it.value.toString())
                }
            }

        }
    }

}