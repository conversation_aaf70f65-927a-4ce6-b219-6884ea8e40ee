package com.midea.prestorage.function.inv.dialog

import androidx.databinding.ObservableField
import com.midea.prestorage.beans.net.InvSetDetailList
import com.midea.prestorage.beans.net.InvSetList
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.ViewBindingAdapter

class DeleteNumDialogVM(val dialog: DeleteNumDialog) {
    val title = ObservableField<String>("提示")
    val etInfo = ObservableField("")
    val defaultNum = ObservableField<String>("1")
    var setCode = ObservableField("")
    var bean: InvSetDetailList? = null

    fun onEnterAnyCode() {
        if (CheckUtil.isFastDoubleClick()) {
            if (etInfo.get().isNullOrBlank()) {
                return
            }
            etInfo.get()?.let { dialog.backDeleteBarcode(it) }
        }
    }

    val inputTextChange = object : ViewBindingAdapter.TextChangedListener {

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            if (s.toString().startsWith("0")) {
                dialog.binding.edDelete.setText("1")
                dialog.binding.edDelete.setSelection(1)
            }
            if (!s.isNullOrEmpty()) {
                val inputNumStr = s.toString().toInt()
                if (inputNumStr > defaultNum.get().toString().toInt()) {
                    ToastUtils.getInstance().showErrorToastWithSound(dialog.mContext, "删除数量不允许超过已集托数量")
                    etInfo.set("")
                }
            }

        }
    }

    fun close() {
        etInfo.set("")
        dialog.dismiss()
    }

    fun confirm() {
        if (CheckUtil.isFastDoubleClick()) {
            if (etInfo.get().isNullOrBlank()) {
                return
            }
            etInfo.get()?.let { dialog.backDeleteBarcode(it) }
        }
    }

}