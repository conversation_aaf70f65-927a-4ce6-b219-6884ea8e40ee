package com.midea.prestorage.dialog

import android.app.AlertDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.receive.dialog.DialogContainerDeleteUnionBinding
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.databinding.DialogTipBinding

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/6/26$
 */
class TipDialog(
    var mContext: BaseActivity
) : AlertDialog(mContext) {

    private var binding: DialogTipUnionBinding
    var tag:Any? = null

    init {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as <PERSON><PERSON><PERSON>) {
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window?.setGravity(Gravity.CENTER)
            window?.attributes?.run {
                gravity = Gravity.CENTER
            }
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_tip_care, null)
            setView(contentView)
            DialogTipUnionBinding.V2(DataBindingUtil.bind(contentView)!!)
        } else {
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_tip, null)
            setView(contentView)
            DialogTipUnionBinding.V1(DataBindingUtil.bind(contentView)!!)
        }
        binding.vm = TipDialogVM(this)

        setCanceledOnTouchOutside(true)
    }

    fun setUnCancel() {
        setCanceledOnTouchOutside(false)
        setCancelable(false)
    }

    fun setTitle(title: String) {
        binding.vm!!.titleName.set(title)
    }

    fun setMsg(msg: String) {
        binding.vm!!.msg.set(msg)
    }

    fun setConfirmTitle(str: String) {
        binding.vm!!.confirmTitle.set(str)
    }

    fun setCancelTitle(str: String) {
        binding.vm!!.cancelTitle.set(str)
    }

    fun setOnTipBackListener(listener: OnTipBack) {
        binding.vm!!.listener = listener
    }

    interface OnTipBack {
        fun onConfirmClick()
        fun onDismissClick()
    }
}
