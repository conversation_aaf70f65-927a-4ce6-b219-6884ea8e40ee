package com.midea.prestorage.function.inv

import android.widget.EditText
import android.widget.GridLayout
import android.widget.RelativeLayout
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestoragesaas.databinding.ActivitySortGoodsBinding
import com.midea.prestoragesaas.databinding.ActivitySortGoodsCareBinding
import com.midea.prestoragesaas.databinding.ActivitySortGoodsDetailBinding
import com.midea.prestoragesaas.databinding.ActivitySortGoodsDetailCareBinding

sealed class ActivitySortGoodsDetailUnionBinding{
    abstract var vm: SortGoodsDetailVM?
    abstract val llTitleBar: RelativeLayout
    abstract val recycle: RecyclerView
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: ActivitySortGoodsDetailCareBinding) : ActivitySortGoodsDetailUnionBinding() {
        override var vm: SortGoodsDetailVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val recycle = binding.recycle
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: ActivitySortGoodsDetailBinding) : ActivitySortGoodsDetailUnionBinding() {
        override var vm: SortGoodsDetailVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val recycle = binding.recycle
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
