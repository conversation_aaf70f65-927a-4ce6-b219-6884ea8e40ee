package com.midea.prestorage.function.outstorage

import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestoragesaas.databinding.ActivityOutStorageOrderBinding
import com.midea.prestorage.printer.SNBCConnectUtils
import com.xuexiang.xqrcode.XQRCode

// 按单收货
class OutStorageOrderActivity : BaseActivity() {

    lateinit var binding: ActivityOutStorageOrderBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_out_storage_order)
        binding.vm = OutStorageOrderVM(this)

        binding.vm!!.init()
    }

    override fun onDestroy() {
        super.onDestroy()
        SNBCConnectUtils.closeBluetooth()
    }

    override fun onResume() {
        super.onResume()
        binding.vm!!.queryShipmentDetail(false)
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }
}