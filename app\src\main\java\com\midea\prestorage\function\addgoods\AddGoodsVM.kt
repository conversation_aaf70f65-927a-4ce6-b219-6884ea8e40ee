package com.midea.prestorage.function.addgoods

import androidx.databinding.ObservableField
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.net.AreaList
import com.midea.prestorage.beans.net.PackageResp
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent

/**
 * Created by LIXK5 on 2019/4/9.
 */
class AddGoodsVM(val activity: AddGoodsActivity) {

    var poolDataNum = ObservableField("任务池(0)")
    var myDataNum = ObservableField("我的任务(0)")
    var completeDataNum = ObservableField("已完成(0)")
    var areaArgs: MutableList<AreaList>? = null
    var areaArgsIn: MutableList<AreaList>? = null

    init {
//        initDistrict()
//        initBatch()
        initAreaOut()
        initAreaIn()
    }

    private fun initDistrict() {
        RetrofitHelper.getAddGoodsService()
            .queryDict()
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PackageResp>(activity) {
                override fun success(data: PackageResp?) {
                    activity.packageResp = data
                    activity.fragmentList.forEach {
                        (it as AddGoodsActivity.OnUnitInitOk).unitOk()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun initAreaOut() {//补货上架
        RetrofitHelper.getAddGoodsService()
            .queryZoneCode(activity.getWhCode(), areaType = "PICK")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PageResult<AreaList>>(activity) {
                override fun success(data: PageResult<AreaList>?) {
                    if (data != null && data.list.isNotEmpty()) {
                        data.list.forEach {
                            it.showInfo = it.zoneName
                            it.taskType = 1
                        }
                        areaArgs = data.list
                    }
                    activity.fragmentList.forEach {
                        (it as AddGoodsActivity.OnUnitInitOk).unitOk()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun initAreaIn() {//补货下架
        RetrofitHelper.getAddGoodsService()
            .queryZoneCode(activity.getWhCode(), isReplenishment = "1")
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PageResult<AreaList>>(activity) {
                override fun success(data: PageResult<AreaList>?) {
                    if (data != null && data.list.isNotEmpty()) {
                        data.list.forEach {
                            it.showInfo = it.zoneName
                            it.taskType = 2
                        }
                        areaArgsIn = data.list
                    }
                    activity.fragmentList.forEach {
                        (it as AddGoodsActivity.OnUnitInitOk).unitOk()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun initBatch() {
        RetrofitHelper.getAddGoodsService()
            .queryBatch()
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PackageResp>(activity) {
                override fun success(data: PackageResp?) {
                    activity.batchResp = data
                    activity.fragmentList.forEach {
                        (it as AddGoodsActivity.OnUnitInitOk).unitOk()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun back() {
        activity.finish()
    }
}