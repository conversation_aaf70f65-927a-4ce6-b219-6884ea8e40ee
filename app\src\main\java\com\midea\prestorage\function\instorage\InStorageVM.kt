package com.midea.prestorage.function.instorage

import android.app.Activity
import android.content.Intent
import android.text.TextUtils
import android.util.Log
import androidx.activity.result.contract.ActivityResultContracts
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.function.instorage.response.InReceiptHeaderCheckRFVO
import com.midea.prestorage.function.instorage.response.ReceiptSerialInitVO
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody


class InStorageVM(val activity: InStorageActivity) {

    val title = ObservableField("入库扫码")

    //单号类型
    var curOrderReceiveType = ObservableField("")   // receipt 入库单  wave波次单

    // 单号 (入库单号或波次单号)
    var curOrderNo = ObservableField("")

    var containerCode: String? = null

    // 第一步：初始化收货容器
    //  任务49 当操作用户存在未收货确认的入库扫码记录时，系统默认加载作业中的数据继续作业
    fun initContainer() {
        activity.waitingDialogHelp.showDialog()

        //var receiptFlag = 0  //  0:按单，1：组单
        Log.e("tao", "初始化收货容器,获取容器号")

        RetrofitHelper.getAppAPI()
            .initReceiptContainer(activity.getWhCode())
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ReceiptSerialInitVO>(activity) {
                override fun success(data: ReceiptSerialInitVO?) {
                    activity.waitingDialogHelp.hidenDialog()

                    if (data == null) return

                    if (data.code.equals("0")) {

                        // 得到一个容器号，开始根据单号收货
                        containerCode = data.containerCode
                        // 检查有没有从入库订单池传过来的单号，有的话加载单号对应的数据进行收货
                        checkDataNoFromActivity()

                    } else if (data.code.equals("1")) {

                        containerCode = data.containerCode
                        //收货类型不对 ，继续扫码收货
                        val tipText = "容器号" + data.containerCode + "未收货确认，收货类型为:" + data.receiptChannel
                        AlertDialogUtil.showOnlyOkDialog(
                            activity, tipText, {
                                // 检查有没有从入库订单池传过来的单号，有的话加载单号对应的数据进行收货
                                checkDataNoFromActivity()
                            }, "继续"
                        )

                    } else if (data.code.equals("2")) {

                        if (!data.receiptCodeOrWaveNo.isNullOrBlank() && !data.containerCode.isNullOrBlank()) {
                            // 当前用户还有未完成的收货，跳到收货确认界面   InStorageConfirmActivity
                            containerCode = data.containerCode
                            curOrderNo.set(data.receiptCodeOrWaveNo)
                            val tipText = "当前用户存在未收货确认的收货记录，请继续完成确认！"
                            activity.isNeedFinish = true //从收货确认界面回来时直接关掉此activity
                            AlertDialogUtil.showOnlyOkDialog(
                                activity, tipText, {
                                    onEnterOrderNo()
                                }, "继续"
                            )
                        }

                    }

                    Log.e("tao", "得到容器号:$")
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }


    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        result?.let {
            curOrderNo.set(result)
            onEnterOrderNo()
        }
    }

    fun checkDataNoFromActivity() {
        //如果有其他Activity传来的单号，则赋值给 orderNo
        val orderNo = activity.intent.getStringExtra("orderNo")
        val orderReceiveType = activity.intent.getStringExtra("orderReceiveType")
        if (!orderNo.isNullOrBlank() && !orderReceiveType.isNullOrEmpty()) {

            curOrderNo.set(orderNo)
            curOrderReceiveType.set(orderReceiveType)
            // 这个时候已经有波次单或入库单号了 直接自动回车
            onEnterOrderNo()
        }
    }


    fun onEnterOrderNo() {
        if (curOrderNo.get().isNullOrBlank()) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "单号不能为空")
            return
        }

        // 先执行单号校验 单号ok才 加载对应的 入库单或波次单的商品数据
        val param = mutableMapOf(
            "receiptCodeOrWaveNo" to curOrderNo.get(),
            "whCode" to activity.getWhCode()
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getAppAPI()
            .checkOrderNoOrWaveNo(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<InReceiptHeaderCheckRFVO>(activity) {
                override fun success(data: InReceiptHeaderCheckRFVO?) {
                    activity.waitingDialogHelp.hidenDialog()

                    if (data != null) {
                        if (data.isWave) {
                            curOrderReceiveType.set("wave")
                        } else {
                            curOrderReceiveType.set("receipt")
                        }
                        curOrderNo.set(data.orderNo)

                        toReceiveScanActivityWithDataNo()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance().showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private val startActivity =
        activity.registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            //此处是跳转的result回调方法
            if (it.data != null && it.resultCode == Activity.RESULT_OK) {
                val result = it.data?.getStringExtra("containerCode")
                if (!TextUtils.isEmpty(result)) {
                    containerCode =  result
                }
            }
    }

    //带 波次号或入库单号  进入扫码收货界面 InStorageReceiveActivity
    fun toReceiveScanActivityWithDataNo() {

        val it = Intent(activity, InStorageScanActivity::class.java)
        // 这里塞一个 后端返回的波次号或入库单号
        it.putExtra("orderNo", curOrderNo.get())
        it.putExtra("orderReceiveType", curOrderReceiveType.get())
        it.putExtra("containerCode", containerCode)

        startActivity.launch(it)
    }


    fun back() {
        activity.finish()
    }


}

