package com.midea.prestorage.beans.base;

import java.io.Serializable;

/**
 * Created by LUCY6 on 2017-5-23.
 */

public class BaseItemForPopup implements Serializable {
    private boolean isSelected;
    private boolean tempSelected; // 缓存选中状态

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        setTempSelected(selected);
        isSelected = selected;
    }

    public boolean isTempSelected() {
        return tempSelected;
    }

    public void setTempSelected(boolean tempSelected) {
        this.tempSelected = tempSelected;
    }
}
