package com.midea.prestorage.function.inv

import android.widget.*
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestoragesaas.databinding.ActivitySetPackageBinding
import com.midea.prestoragesaas.databinding.ActivitySetPackageCareBinding
import com.midea.prestoragesaas.databinding.ActivityWaitSetPackageBinding
import com.midea.prestoragesaas.databinding.ActivityWaitSetPackageCareBinding

sealed class ActivityWaitSetPackageUnionBinding{
    abstract var vm: WaitSetPackageVM?
    abstract val llTitleBar: RelativeLayout
    abstract val llAllChoose: LinearLayout
    abstract val cbSelect: CheckBox
    abstract val etSearchGoodsNo: EditText
    abstract val recycle: RecyclerView
    abstract val etSearchOrderNo: EditText
    abstract val tvNotification: TextView

    class V2(val binding: ActivityWaitSetPackageCareBinding) : ActivityWaitSetPackageUnionBinding() {
        override var vm: WaitSetPackageVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val llAllChoose = binding.llAllChoose
        override val cbSelect = binding.cbSelect
        override val etSearchGoodsNo = binding.etSearchGoodsNo
        override val recycle = binding.recycle
        override val etSearchOrderNo = binding.etSearchOrderNo
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityWaitSetPackageBinding) : ActivityWaitSetPackageUnionBinding() {
        override var vm: WaitSetPackageVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val llAllChoose = binding.llAllChoose
        override val cbSelect = binding.cbSelect
        override val etSearchGoodsNo = binding.etSearchGoodsNo
        override val recycle = binding.recycle
        override val etSearchOrderNo = binding.etSearchOrderNo
        override val tvNotification = binding.tvNotification
    }
}
