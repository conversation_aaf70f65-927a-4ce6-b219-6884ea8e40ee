package com.midea.prestorage.function.pick

import android.content.Intent
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.OutPickPoolStorageList
import com.midea.prestoragesaas.databinding.ActivityPickStoragePoolBinding
import com.midea.prestoragesaas.databinding.PopViewPickPoolBinding
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.dialog.MultiChooseDialog
import com.xuexiang.xqrcode.XQRCode

// 入库订单池
class PickPoolStorageActivity : BaseActivity() {

    private lateinit var binding: ActivityPickStoragePoolBinding
    private lateinit var popBinding: PopViewPickPoolBinding
    private lateinit var popupWindow: PopupWindow
    private lateinit var daysDialog: FilterDialog
    private lateinit var statueDialog: MultiChooseDialog
    private lateinit var personDialog: FilterDialog
    private var vm = PickPoolStorageVM(this)
    val adapter = CommonAdapter<OutPickPoolStorageList>(R.layout.item_order_pick_pool)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_pick_storage_pool)
        binding.vm = vm

        initPop()
        initRecycle()
        initDialog()
        vm.init()
    }

    private fun initPop() {
        val popView = LayoutInflater.from(this).inflate(R.layout.pop_view_pick_pool, null)
        popBinding = DataBindingUtil.bind(popView)!!
        popBinding.vm = vm

        popupWindow = PopupWindow(
            popView,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        popupWindow.setBackgroundDrawable(
            ColorDrawable(
                ContextCompat.getColor(
                    this,
                    R.color.colorOutlineGrey
                )
            )
        )

        popupWindow.setOnDismissListener {
            vm.initFilterInfo()
            vm.initOrderList()
        }

        popupWindow.isOutsideTouchable = true

        binding.llContainer.setOnClickListener {
            popupWindow.showAsDropDown(it)

            popBinding.etOrderNo.requestFocus()
        }
        popBinding.llFilterClose.setOnClickListener {
            popupWindow.dismiss()
        }
    }

    override fun onResume() {
        super.onResume()

        vm.onRefreshCommand.onRefresh()
    }

    private fun initRecycle() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter

        adapter.setOnItemClickListener { adapter, _, position ->
            val bean = adapter.data[position] as OutPickPoolStorageList
            vm.onItemClick(bean)
        }
    }

    private fun initDialog() {
        daysDialog = FilterDialog(this)
        daysDialog.setTitle("请选择天数")
        daysDialog.dismissEdit()
        daysDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            popBinding.tvDays.text = it.showInfo
            vm.dayInfo = it as DCBean
            vm.dayChange()
            daysDialog.dismiss()
        })

        statueDialog = MultiChooseDialog(this)
        statueDialog.setTitle("请选择任务状态")
        statueDialog.setBack(object : MultiChooseDialog.MultiChooseBack {
            override fun multiChooseBack(baseInfo: MutableList<BaseItemShowInfo>) {
                vm.orderStatue = baseInfo as MutableList<DCBean>
                vm.showStatus()
                statueDialog.dismiss()
            }
        })

        personDialog = FilterDialog(this)
        personDialog.setTitle("请选择创建人")
        personDialog.dismissEdit()
        personDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            vm.personInfo.set(it.showInfo)
            vm.creatorStatue = it as DCBean
            personDialog.dismiss()
        })
    }

    fun resetFilterInfo(filters: MutableList<String>) {
        binding.warpLinear.removeAllViews()
        filters.forEach {
            binding.warpLinear.addView(getShowView(it))
        }
    }

    private fun getShowView(text: String): View {
        var view: View = LayoutInflater.from(this).inflate(R.layout.item_filter, null)
        view.findViewById<TextView>(R.id.tv_item).text = text
        return view
    }

    fun showDaysDialog(days: MutableList<BaseItemShowInfo>) {
        daysDialog.addAllData(days)
        daysDialog.show()
    }

    fun showStatueDialog(days: MutableList<BaseItemShowInfo>) {
        statueDialog.addAllData(days)
        statueDialog.show()
    }

    fun showPersonDialog() {
        personDialog.show()
    }

    fun personDataChange(days: MutableList<BaseItemShowInfo>) {
        personDialog.addAllData(days)
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    fun showData(data: MutableList<OutPickPoolStorageList>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()
    }
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == BaseActivity.QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果（在界面上显示）
            vm.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }
}