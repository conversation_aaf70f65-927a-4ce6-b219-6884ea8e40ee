package com.midea.prestorage.function.arriving

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.net.ArrivingDetailInfo
import com.midea.prestoragesaas.databinding.ActivityArrivingDetailBinding
import com.midea.prestoragesaas.databinding.ActivityArrivingDetailHeadBinding


class ArrivingDetailActivity : BaseActivity() {

    private lateinit var binding: ActivityArrivingDetailBinding
    var adapter: CommonAdapter<ArrivingDetailInfo.Details> =
        CommonAdapter(R.layout.item_arriving_detail)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_arriving_detail)
        binding.vm = ArrivingDetailVM(this)
        initHead()

        binding.vm!!.init()
        initRecycle()
    }

    private fun initHead() {
        val header = DataBindingUtil.inflate<ActivityArrivingDetailHeadBinding>(
            LayoutInflater.from(this),
            R.layout.activity_arriving_detail_head,
            null,
            false
        )
        header.vm = binding.vm!!
        adapter.addHeaderView(header.root, LinearLayout.VISIBLE)
    }

    private fun initRecycle() {
        binding.recycle.layoutManager = LinearLayoutManager(this)
        binding.recycle.adapter = adapter
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }
}