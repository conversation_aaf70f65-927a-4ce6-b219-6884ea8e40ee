package com.midea.prestorage.function.inv.response;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

// 加载 实时盘点 任务
public class RespCountInTimeTask implements Serializable {
    private List<InvStocktakeDetail> invStocktakeDetailList;
    private Integer totalCount;
    private Integer totalPage;
    private Integer pageNo;
    private Integer pageSize;
    private Integer offset;

    Long id;
    String createTime;
    String updateTime;
    String tenantCode;  // T201904230000000014,
    String createUserCode; // tanght2,
    String createUserName; // 唐4893,
    String updateUserCode; // tanght2,
    String updateUserName; // 唐4893,
    String whCode;          // Gz1001,
    String stocktakeCode;   // PD20211016000004,
    String ownerCode;   // Gw1001,
    String stocktakeType;   // 200,
    String status;     // 300,
    Integer totalSku;  // 1,
    Integer totalQty;  // 800,
    String locCode;    // Gx1001,


    public RespCountInTimeTask deepCoy() {
        RespCountInTimeTask copy = new RespCountInTimeTask();
        copy.createTime = createTime;
        copy.updateTime = updateTime;
        copy.whCode = createTime;
        copy.stocktakeCode = stocktakeCode;
        copy.ownerCode = ownerCode;
        copy.stocktakeType = stocktakeType;
        copy.status = status;
        copy.totalSku = totalSku;
        copy.totalQty = totalQty;
        copy.locCode = locCode;

        copy.invStocktakeDetailList = new LinkedList<>();
        if (invStocktakeDetailList != null && invStocktakeDetailList.size() > 0) {
            for (InvStocktakeDetail d : invStocktakeDetailList) {
                copy.invStocktakeDetailList.add(d);
            }
        }
        return copy;
    }

    public List<InvStocktakeDetail> getInvStocktakeDetailList() {
        return invStocktakeDetailList;
    }

    public void setInvStocktakeDetailList(List<InvStocktakeDetail> invStocktakeDetailList) {
        this.invStocktakeDetailList = invStocktakeDetailList;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(Integer totalPage) {
        this.totalPage = totalPage;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getStocktakeCode() {
        return stocktakeCode;
    }

    public void setStocktakeCode(String stocktakeCode) {
        this.stocktakeCode = stocktakeCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getStocktakeType() {
        return stocktakeType;
    }

    public void setStocktakeType(String stocktakeType) {
        this.stocktakeType = stocktakeType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getTotalSku() {
        return totalSku;
    }

    public void setTotalSku(Integer totalSku) {
        this.totalSku = totalSku;
    }

    public Integer getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(Integer totalQty) {
        this.totalQty = totalQty;
    }

    public String getLocCode() {
        return locCode;
    }

    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }
}

