package com.midea.prestorage.function.outstorage

import CheckUtil
import android.app.Application
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Transformations
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.BackTaskConfirmReq
import com.midea.prestorage.beans.net.EwmsRecommendLocReq
import com.midea.prestorage.beans.net.OutStorageBackTaskResp
import com.midea.prestorage.beans.net.PackageRelation
import com.midea.prestorage.event.LiveEvent
import com.midea.prestorage.function.inv.response.PackageRelationList
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.utils.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.math.BigDecimal

class OutStorageBackTaskDetailViewModel(application: Application) : BaseViewModel(application) {

    val locLiveData = MutableLiveData<String>()
    val custItemCode = MutableLiveData<String>()
    val itemName = MutableLiveData<String>()
    val lotAtt01 = MutableLiveData<String>()
    val cdpaFormat = MutableLiveData<String>()
    val whBarcode69 = MutableLiveData<String>()
    val planEaQtyStr = MutableLiveData<String>()
    val originLoc = MutableLiveData<String>()
    val recommendLoc1 = MutableLiveData<String>()
    val showRecommendLoc1 = Transformations.map(recommendLoc1) {
        it.isNotEmpty()
    }
    val recommendLoc2 = MutableLiveData<String>()
    val showRecommendLoc2 = Transformations.map(recommendLoc2) {
        it.isNotEmpty()
    }
    val recommendLoc3 = MutableLiveData<String>()
    val showRecommendLoc3 = Transformations.map(recommendLoc3) {
        it.isNotEmpty()
    }
    val qty1 = MutableLiveData<String>()
    val unit1 = MutableLiveData<String>()
    val showUnit1 = MutableLiveData<Boolean>()
    val qty2 = MutableLiveData<String>()
    val unit2 = MutableLiveData<String>()
    val showUnit2 = MutableLiveData<Boolean>()
    val qty3 = MutableLiveData<String>()
    val unit3 = MutableLiveData<String>()
    val showUnit3 = MutableLiveData<Boolean>()
    val qty4 = MutableLiveData<String>()
    val unit4 = MutableLiveData<String>()
    val showUnit4 = MutableLiveData<Boolean>()

    val finishEvent = LiveEvent<Unit>()

    var totalQty: BigDecimal = BigDecimal.ZERO
    var ids: List<Long> = emptyList()
    private var packageRelationList: List<PackageRelation>? = null
    private var task: OutStorageBackTaskResp? = null
    private val unitToQtyMap = mutableMapOf<String, MutableLiveData<String>>()
    val scanEvent = LiveEvent<Unit>()

    override fun init() {
    }

    fun update(task: OutStorageBackTaskResp) {
        task.packageRelationList = MathUtils.handlePackageRelation(
            task.fromQty ?: BigDecimal.ZERO,
            task.packageRelationList ?: mutableListOf()
        )
        packageRelationList =
            MathUtils.sortPackageRelation(task.packageRelationList.orEmpty()).reversed()
        this.task = task
        totalQty = task.fromQty ?: BigDecimal.ZERO
        ids = task.ids.orEmpty()
        custItemCode.value = task.custItemCode ?: ""
        itemName.value = task.itemName ?: ""
        lotAtt01.value = task.lotAtt01 ?: ""
        whBarcode69.value = LotAttUnit.formatWhBarcode69(task.whCsBarcode69, task.whBarcode69)
        cdpaFormat.value = task.cdpaFormat ?: ""
        originLoc.value = task.toLoc ?: ""
        locLiveData.value = task.toLoc ?: ""

        val inputCount = packageRelationList!!.size
        showUnit4.value = inputCount >= 1
        showUnit3.value = inputCount >= 2
        showUnit2.value = inputCount >= 3
        showUnit1.value = inputCount >= 4
        if (showUnit4.value == true) {
            val unit4Relation = packageRelationList!!.getOrNull(0)
            unit4.value = unit4Relation?.cdprDesc.orEmpty()
            if (!AppUtils.isZero(unit4Relation?.num)) {
                qty4.value = AppUtils.getBigDecimalValueStr(unit4Relation?.num)
            }
            unitToQtyMap[unit4Relation?.cdprUnit.orEmpty()] = qty4
        }
        if (showUnit3.value == true) {
            val unit3Relation = packageRelationList!!.getOrNull(1)
            unit3.value = unit3Relation?.cdprDesc.orEmpty()
            if (!AppUtils.isZero(unit3Relation?.num)) {
                qty3.value = AppUtils.getBigDecimalValueStr(unit3Relation?.num)
            }
            unitToQtyMap[unit3Relation?.cdprUnit.orEmpty()] = qty3
        }
        if (showUnit2.value == true) {
            val unit2Relation = packageRelationList!!.getOrNull(2)
            unit2.value = unit2Relation?.cdprDesc.orEmpty()
            if (!AppUtils.isZero(unit2Relation?.num)) {
                qty2.value = AppUtils.getBigDecimalValueStr(unit2Relation?.num)
            }
            unitToQtyMap[unit2Relation?.cdprUnit.orEmpty()] = qty2
        }
        if (showUnit1.value == true) {
            val unit1Relation = packageRelationList!!.getOrNull(3)
            unit1.value = unit1Relation?.cdprDesc.orEmpty()
            if (!AppUtils.isZero(unit1Relation?.num)) {
                qty1.value = AppUtils.getBigDecimalValueStr(unit1Relation?.num)
            }
            unitToQtyMap[unit1Relation?.cdprUnit.orEmpty()] = qty1
        }

        planEaQtyStr.value = "${AppUtils.getBigDecimalValueStr(task.fromQty)}${unit4.value}"
    }

    private fun fillRecommendLoc(locCode: String?) {
        val locList = locCode?.split(",")
        recommendLoc1.value = locList?.getOrNull(0) ?: ""
        recommendLoc2.value = locList?.getOrNull(1) ?: ""
        recommendLoc3.value = locList?.getOrNull(2) ?: ""
    }

    fun setRecommendLoc(index: Int) {
        locLiveData.value = when (index) {
            0 -> recommendLoc1.value
            1 -> recommendLoc2.value
            else -> recommendLoc3.value
        }
    }

    fun confirm() = launch(true, error = {}) {
        if (CheckUtil.isFastDoubleClick()) {
            checkUserInput()
            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getOutStorageAPI().backTaskConfirm(
                    BackTaskConfirmReq(
                        id = task?.id ?: "",
                        toLoc = locLiveData.value ?: "",
                        qty = submitQty(),
                    )
                )
            }
            if (result.code == 0L) {
                MySoundUtils.getInstance().dingSound()
                finishEvent.value = Unit
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    fun recommendLoc() = launch(showDialog = true) {
        if (CheckUtil.isFastDoubleClick()) {
            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getAppAPI().ewmsRecommendLocationSus(
                    EwmsRecommendLocReq(
                        itemCode = task?.itemCode,
                        lotNum = task?.lotNum,
                        ownerCode = task?.ownerCode
                    )
                )
            }
            if (result.code == 0L) {
                fillRecommendLoc(result.data)
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    @VisibleForTesting
    @Throws(IllegalArgumentException::class)
    fun checkUserInput() {
        if (qty1.value.isNullOrBlank() && qty2.value.isNullOrBlank() && qty3.value.isNullOrBlank() && qty4.value.isNullOrBlank()) {
            throw IllegalArgumentException("上架数量不能为空")
        }
        val submit = submitQty()
        if (AppUtils.isZero(submit)) {
            throw IllegalArgumentException("上架数量不能为0")
        }
        if (submit > totalQty) {
            throw IllegalArgumentException("上架数量不能大于待上架数量")
        }
        if (locLiveData.value.isNullOrBlank()) {
            throw IllegalArgumentException("上架库位不能为空")
        }
    }

    private fun submitQty(): BigDecimal {
        val otQty = unitToQtyMap["OT"]?.value?.toBigDecimalOrNull()
        val csQty = unitToQtyMap["CS"]?.value?.toBigDecimalOrNull()
        val ipQty = unitToQtyMap["IP"]?.value?.toBigDecimalOrNull()
        val eaQty = unitToQtyMap["EA"]?.value?.toBigDecimalOrNull()
        return MathUtils.toEaQty(otQty, csQty, ipQty, eaQty, packageRelationList)
    }

    fun scan() {
        if (CheckUtil.isFastDoubleClick()) {
            scanEvent.value = Unit
        }
    }

    fun scanResult(result: String?) {
        locLiveData.value = result ?: ""
    }

}