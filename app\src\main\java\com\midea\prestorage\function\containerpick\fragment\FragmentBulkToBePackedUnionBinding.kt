package com.midea.prestorage.function.containerpick.fragment

import android.view.View
import android.widget.RelativeLayout
import androidx.lifecycle.LifecycleOwner
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.midea.prestorage.widgets.InterceptRecyclerView
import com.midea.prestoragesaas.databinding.FragmentBulkToBePackedBinding
import com.midea.prestoragesaas.databinding.FragmentBulkToBePackedCareBinding

sealed class FragmentBulkToBePackedUnionBinding{
    abstract var vm: BulkToBePackedVM?
    abstract val root: View
    abstract val recycle: InterceptRecyclerView
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: FragmentBulkToBePackedCareBinding) : FragmentBulkToBePackedUnionBinding() {
        override var vm: BulkToBePackedVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val recycle = binding.recycle
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: FragmentBulkToBePackedBinding) : FragmentBulkToBePackedUnionBinding() {
        override var vm: BulkToBePackedVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val recycle = binding.recycle
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
