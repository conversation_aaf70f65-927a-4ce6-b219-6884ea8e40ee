package com.midea.prestorage.beans.net;

import android.text.TextUtils;

import java.io.Serializable;
import java.math.BigDecimal;

public class PrintInfo implements Serializable {

    private String whName;
    private String waybillNo;
    private String seqNum;
    private BigDecimal totalQty;
    private String shipToAttentionTo;
    private String shipToMobile;
    private String shipToPhoneNum;
    private String shipToAddress;
    private String customerName;
    private String eblcDetailAddress;
    private String requestedDeliveryDate;
    private String sourcePlatform;
    private String sourceSystem;
    private String shippingWay;
    private String custItemCode;
    private String itemName;
    private String orderNo;
    private BigDecimal qty;

    public String getWhName() {
        return whName;
    }

    public void setWhName(String whName) {
        this.whName = whName;
    }

    public String getWaybillNo() {
        return TextUtils.isEmpty(waybillNo) ? "111" : waybillNo;
    }

    public void setWaybillNo(String waybillNo) {
        this.waybillNo = waybillNo;
    }

    public String getSeqNum() {
        return TextUtils.isEmpty(seqNum) ? "" : seqNum;
    }

    public void setSeqNum(String seqNum) {
        this.seqNum = seqNum;
    }

    public int getTotalQtyInt() {
        if (totalQty == null) {
            return 0;
        }
        return totalQty.intValue();
    }

    public String getShipToAttentionTo() {
        if (!TextUtils.isEmpty(shipToAttentionTo) && shipToAttentionTo.length() > 6) {
            shipToAttentionTo = shipToAttentionTo.substring(0, 5);
        }
        return TextUtils.isEmpty(shipToAttentionTo) ? "" : shipToAttentionTo;
    }

    public void setShipToAttentionTo(String shipToAttentionTo) {
        this.shipToAttentionTo = shipToAttentionTo;
    }

    public String getShipToMobile() {
        return shipToMobile;
    }

    public void setShipToMobile(String shipToMobile) {
        this.shipToMobile = shipToMobile;
    }

    public String getShipToPhoneNum() {
        return TextUtils.isEmpty(shipToPhoneNum) ? "" : shipToPhoneNum;
    }

    public void setShipToPhoneNum(String shipToPhoneNum) {
        this.shipToPhoneNum = shipToPhoneNum;
    }

    public String getShipToAddress() {
        if (!TextUtils.isEmpty(shipToAddress) && shipToAddress.length() > 46) {
            shipToAddress = shipToAddress.substring(0, 46);
        }
        return TextUtils.isEmpty(shipToAddress) ? "" : shipToAddress;
    }

    public void setShipToAddress(String shipToAddress) {
        this.shipToAddress = shipToAddress;
    }

    public String getCustomerName() {
        return TextUtils.isEmpty(customerName) ? "" : customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getEblcDetailAddress() {
        return TextUtils.isEmpty(eblcDetailAddress) ? "" : eblcDetailAddress;
    }

    public void setEblcDetailAddress(String eblcDetailAddress) {
        this.eblcDetailAddress = eblcDetailAddress;
    }

    public String getRequestedDeliveryDate() {
        return TextUtils.isEmpty(requestedDeliveryDate) ? "" : requestedDeliveryDate;
    }

    public void setRequestedDeliveryDate(String requestedDeliveryDate) {
        this.requestedDeliveryDate = requestedDeliveryDate;
    }

    public String getSourcePlatform() {
        return sourcePlatform;
    }

    public void setSourcePlatform(String sourcePlatform) {
        this.sourcePlatform = sourcePlatform;
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getCustItemCode() {
        return TextUtils.isEmpty(custItemCode) ? "" : custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemName() {
        return TextUtils.isEmpty(itemName) ? "" : itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public int getQtyInt() {
        if (qty == null) {
            return 0;
        }
        return qty.intValue();
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public BigDecimal getTotalQty() {
        return totalQty;
    }

    public int getTotalInt() {
        if (totalQty == null) {
            return 0;
        }
        return totalQty.intValue();
    }

    public String getOrderNo() {
        return TextUtils.isEmpty(orderNo) ? "" : orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getShippingWay() {
        if (TextUtils.isEmpty(shippingWay)) {
            return "";
        }
        if ("ZT".equals(shippingWay)) {
            return "自";
        } else if ("DOT".equals(shippingWay)) {
            return "宅";
        } else if ("DELIVERY".equals(shippingWay)) {
            return "配";
        } else {
            return "网";
        }
    }

    public void setShippingWay(String shippingWay) {
        this.shippingWay = shippingWay;
    }
}
