package com.midea.prestorage.function.containerpick.fragment

import android.view.View
import androidx.lifecycle.LifecycleOwner
import com.midea.prestorage.widgets.InterceptRecyclerView
import com.midea.prestoragesaas.databinding.FragmentBulkAlreadyPackedBinding
import com.midea.prestoragesaas.databinding.FragmentBulkAlreadyPackedCareBinding

sealed class FragmentBulkAlreadyPackedUnionBinding{
    abstract var vm: BulkAlreadyPackedVM?
    abstract val root: View
    abstract val recycle: InterceptRecyclerView
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: FragmentBulkAlreadyPackedCareBinding) : FragmentBulkAlreadyPackedUnionBinding() {
        override var vm: BulkAlreadyPackedVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val recycle = binding.recycle
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: FragmentBulkAlreadyPackedBinding) : FragmentBulkAlreadyPackedUnionBinding() {
        override var vm: BulkAlreadyPackedVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val root = binding.root
        override val recycle = binding.recycle
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
