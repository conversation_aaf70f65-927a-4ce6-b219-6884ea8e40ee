package com.midea.prestorage.function.containerpick

import android.annotation.SuppressLint
import android.content.Intent
import android.text.Html
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.ContainerPickList
import com.midea.prestorage.function.inv.response.PageResult
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.printer.BluetoothConnectBack
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import java.text.SimpleDateFormat
import java.util.*

class ContainerPickVM(val activity: ContainerPickActivity) {

    val isNoData = ObservableBoolean(false)

    var orderNo = ObservableField("")
    var totalNum = ObservableField("0")
    var waitNum = ObservableField("0")

    var orderNoStr: String = ""
    val isRefreshing = ObservableBoolean(false)
    var pageNo = 1

    val isPrintOk = ObservableField(false)
    var jumpFlag = false //判断是否跳转到扫码页面

    private val blueBack = object : BluetoothConnectBack {
        override fun success() {
            activity.waitingDialogHelp.hidenDialog()
            isPrintOk.set(true)
        }

        override fun fail() {
            isPrintOk.set(false)
            AppUtils.showToast(activity, "打印机连接失败，请重启打印机!")
        }

        override fun connect() {
            bluetoothOpen()
        }
    }

    fun bluetoothOpen(isAuto: Boolean = true) {
        if (!Printer.isPrintOk()) {
            Printer.openBluetooth(activity, blueBack, isAuto)
        } else {
            isPrintOk.set(true)
        }
    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        // 下拉刷新
        pageNo = 1
        isRefreshing.set(true)
        orderNoStr = ""
        initList(orderNoStr, true)
    }

    @SuppressLint("SimpleDateFormat")
    val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

    @SuppressLint("SimpleDateFormat")
    fun onEnterOrderNo() {
        if (CheckUtil.isFastDoubleClick()) {
            if (orderNo.get().isNullOrEmpty()) {
                return
            }

            activity.waitingDialogHelp.showDialog()

            orderNoStr = orderNo.get().toString()
            pageNo = 1
            isRefreshing.set(true)
            initList(orderNoStr, true)
        }
    }

    fun initList(orderStr: String, isRefresh: Boolean = false) {
        val cal = Calendar.getInstance()
        cal.add(Calendar.DATE, -30)
        val format = SimpleDateFormat("yyyy-MM-dd")

        val startDate = "${format.format(cal.time)} 00:00:00"
        val endDate = "${format.format(Date())} 23:59:59"

        RetrofitHelper.getOutStorageAPI()
            .queryList(
                orderStr.trim(),
                startDate,
                endDate,
                mobile = Constants.mobile,
                pageNo = pageNo
            )
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<PageResult<ContainerPickList>>(activity) {
                override fun success(data: PageResult<ContainerPickList>?) {
                    activity.waitingDialogHelp.hidenDialog()
                    orderNo.set("")

                    data?.let {
                        pageNo = it.pageNo + 1

                        it.list.forEachIndexed { index, item ->
                            item.statusStr = outTaskStatus[item.status]
                            item.taskTypeStr = outTaskType[item.taskType]

                            if (isRefresh) {
                                item.index = index + 1
                            } else {
                                item.index = activity.adapter.data.size + index + 1
                            }
                            item.shippingLoc =
                                item.shippingLocList?.distinct()?.joinToString(separator = ",")
                        }

                        if (isRefresh) {
                            activity.showData(it.list)
                        } else {
                            activity.addData(it.list)
                        }
                        isNoData.set(activity.adapter.data.isEmpty())

                        if (pageNo > it.totalPage) {
                            //加载到了最后一页
                            activity.adapter.loadMoreModule.loadMoreEnd()
                        } else {
                            activity.adapter.loadMoreModule.loadMoreComplete()
                        }

                        totalNum.set(it.totalCount.toString())
                        if (it.list != null && it.list.isNotEmpty()) {
                            waitNum.set(it.list[0].waitCount.toString())
                        } else {
                            waitNum.set("0")
                        }
                    }
                    isRefreshing.set(false)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isRefreshing.set(false)
                    if (isRefresh) {
                        totalNum.set("0")
                        waitNum.set("0")
                    }
                    orderNo.set("")
                    orderNoStr = ""
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                    activity.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun clearOrderNo() {
        orderNo.set("")
    }

    fun back() {
        activity.finish()
    }

    fun onItemClick(bean: ContainerPickList) {
        if (!bean.confirmedBy.isNullOrEmpty() && bean.confirmedBy != Constants.userInfo?.name) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "不是您的任务不允许操作!")
            return
        }
        val it = Intent(activity, ContainerPickSecondActivity::class.java)
        it.putExtra("ContainerPickList", bean)
        activity.startActivity(it)
    }

    fun startScan() {
        jumpFlag = true
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        orderNo.set(result)
        onEnterOrderNo()
    }

    fun bottomClick() {
        if (CheckUtil.isFastDoubleClick()) {
            val it = Intent(activity, ContainerReprintActivity::class.java)
            activity.startActivity(it)
        }
    }

    //数据字典CL_SHIPMENT_STATUS
    val outTaskStatus = mutableMapOf(
        100 to "未执行",
        300 to "已指派",
        600 to "已领取",
        750 to "执行中",
        900 to "已完成"
    )

    //数据字典CL_PICK_TASK_TYPE
    val outTaskType = mutableMapOf(
        "CSPICK" to "整拣",
        "EAPICK" to "零拣",
        "BACKPICK" to "拣货反库",
        "NORMAL" to "通用",
        "SORTPICK" to "边拣边分"
    )
}