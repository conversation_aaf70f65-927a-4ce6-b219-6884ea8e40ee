package com.midea.prestorage.function.pointjoin.fragment

import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.Creators
import com.midea.prestorage.beans.net.PointDivideDetailBean
import com.midea.prestorage.beans.net.PointJoinList
import com.midea.prestorage.function.pointjoin.PointJoinActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody

class PointJoinFragmentVM(val fragment: PointJoinFragment) {
    //天数
    var days = mutableListOf(
        DCBean("1天", 1, DCBean.SHOW_KEY),
        DCBean("3天", 3, DCBean.SHOW_KEY),
        DCBean("7天", 7, DCBean.SHOW_KEY),
        DCBean("15天", 15, DCBean.SHOW_KEY),
        DCBean("30天", 30, DCBean.SHOW_KEY),
        DCBean("90天", 90, DCBean.SHOW_KEY)
    )

    var dayInfo: DCBean? = days[2]
    var orderNo: String? = null
    var phone: String? = null
    var person: String? = null
    var position: Int? = 0

    val mode = ObservableField(false)
    var filter = ObservableField("筛选")

    val isRefreshing = ObservableBoolean(false)
    val isNoData = ObservableBoolean(false)

    var typeDC: MutableList<DCBean>? = null
    var statueDC: MutableList<DCBean>? = null
    var netApplyDC: MutableList<DCBean>? = null
    var creatorStatus = mutableSetOf<DCBean>()

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        isRefreshing.set(true)
        initData()
    }

    fun init() {
        position = fragment.arguments?.getInt("position")
        if (position == 0) {
            orderNo = fragment.activity?.intent?.getStringExtra("orderNo")
        }

        DCUtils.shipType(fragment.activity as RxAppCompatActivity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                typeDC = statusDC
                combineDCInfo()
            }
        })

        DCUtils.fuShipmentStatus(fragment.activity as RxAppCompatActivity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                statueDC = statusDC
                combineDCInfo()
            }
        })

        DCUtils.netApply(fragment.activity as RxAppCompatActivity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                netApplyDC = statusDC
                combineDCInfo()
            }
        })
        initCreator()
    }

    private fun combineDCInfo() {
        fragment.adapter.data.forEach {
            if (typeDC != null) {
                val result =
                    typeDC!!.find { item -> item.value.toString() == it.shippingWay }
                if (result != null) {
                    it.shippingWayStr = result.key
                }
            }
            if (statueDC != null) {
                val result = statueDC!!.find { item -> item.value.toString() == it.status }
                if (result != null) {
                    it.statusStr = result.key
                }
            }
            if (netApplyDC != null) {
                val result =
                    netApplyDC!!.find { item -> item.value.toString() == it.engineerStatus }
                if (result != null) {
                    it.engineerStatusStr = result.key
                }
            }
        }
        fragment.adapter.notifyDataSetChanged()
    }

    fun dayChange() {
        initCreator()
    }

    private fun initCreator() {
        val param = mutableMapOf(
            "whCode" to (fragment.activity as BaseActivity).getWhCode(),
            "dayNum" to dayInfo?.value,
            "status" to position?.plus(1)
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .engineerRequestedBy(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<MutableList<Creators>>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: MutableList<Creators>?) {
                    if (!data.isNullOrEmpty()) {
                        creatorStatus.clear()
                        data!!.forEach {
                            creatorStatus.add(
                                DCBean(
                                    it.requestedBy,
                                    it.requestedBy,
                                    DCBean.SHOW_KEY
                                )
                            )
                        }
                        fragment.personDataChange(creatorStatus.toMutableList() as MutableList<BaseItemShowInfo>)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(fragment.activity, apiErrorModel.message)
                }
            })
    }


    fun setDefault() {
        dayInfo = days[1]
        orderNo = null
        phone = null
        person = null
    }

    private fun initData() {
        val param = mutableMapOf(
            "whCode" to (fragment.activity as BaseActivity).getWhCode(),
            "dayNum" to dayInfo?.value,
            "orderNo" to fragment.binding.edOrderNo.text.toString().trim(),
            "requestedBy" to person,
            "requestedTel" to phone,
            "status" to position?.plus(1)
        )

        if ((fragment.activity as PointJoinActivity).isLoadParamFromMainActivity) {
            (fragment.activity as PointJoinActivity).isLoadParamFromMainActivity = false
            param.put("beginTime", (fragment.activity as PointJoinActivity).beginTime)
            param.put("endTime", (fragment.activity as PointJoinActivity).endTime)
            param.put("status", 3)
            param.put("dayNum", null)
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .engineerList(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<MutableList<PointJoinList>>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: MutableList<PointJoinList>?) {
                    if (data != null && data.isNotEmpty()) {
                        fragment.showListData(data)
                        isNoData.set(false)
                        filter.set("筛选(${data.size})")

                        if (position == 0) {
                            (fragment.activity as PointJoinActivity).setTabNum1(data.size)
                        } else {
                            (fragment.activity as PointJoinActivity).setTabNum2(data.size)
                        }
                        combineDCInfo()
                    } else {
                        isNoData.set(true)
                        filter.set("筛选(0)")

                        if (position == 0) {
                            (fragment.activity as PointJoinActivity).setTabNum1(0)
                        } else {
                            (fragment.activity as PointJoinActivity).setTabNum2(0)
                        }
                    }
                    fragment.setCbSelectChecked(false)
                    fragment.setEdOrderNoSelectAll()
                    isRefreshing.set(false)
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(fragment.activity, apiErrorModel.message)
                    isRefreshing.set(false)
                }
            })
    }

    fun batchChange() {
        val beans = fragment.adapter.returnBeans
        if (beans.isEmpty()) {
            ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "需选择至少1个订单进行交接!")
            return
        } else if (beans.size > 100) {
            ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, "最多勾选100个!")
            return
        }

        upLoadData(beans)
    }

    private fun upLoadData(beans: MutableList<PointJoinList>) {
        val ids = mutableListOf<String>()
        val enginnerIds = mutableListOf<String>()
        beans.forEach {
            ids.add(it.shipmentCode)
            enginnerIds.add(it.enginnerId)
        }

        checkDetail(ids, enginnerIds)
    }

    private fun checkDetail(
        ids: MutableList<String>,
        enginnerIds: MutableList<String>
    ) {
        val param = mutableMapOf(
            "whCode" to (fragment.activity as BaseActivity).getWhCode(),
            "shipmentCodeList" to ids,
            "engInnerIdList" to enginnerIds
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .engineerDetail(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object :
                RequestCallback<MutableList<PointDivideDetailBean>>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: MutableList<PointDivideDetailBean>?) {
                    if (data != null) {
                        if (data.isEmpty()) {
                            ToastUtils.getInstance()
                                .showErrorToastWithSound(fragment.activity, "详情为空!")
                            return
                        }
                        fragment.showPointDialog(data)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(fragment.activity, apiErrorModel.message)
                }
            })
    }

    fun startScan() {
        XQRCode.startScan(fragment, BaseActivity.QR_CODE_BACK)
    }

    fun itemClick(bean: PointJoinList) {
        if (position == 0) {
            upLoadData(mutableListOf(bean))
        }
    }

    fun refreshData() {
        initData()
    }
}