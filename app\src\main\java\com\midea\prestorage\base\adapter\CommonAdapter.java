package com.midea.prestorage.base.adapter;

import android.text.TextUtils;
import android.util.Log;
import android.widget.TextView;


import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.midea.prestorage.base.annotation.ShowAnnotation;
import com.midea.prestorage.utils.AppUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.math.BigDecimal;

public class CommonAdapter<T> extends BaseQuickAdapter<T, BaseViewHolder> {

    public CommonAdapter(int layoutResId) {
        super(layoutResId);
    }

    @Override
    protected void convert(BaseViewHolder helper, T item) {
        Class clazz = item.getClass();

        while (clazz != null) {
            // 获得字段注解
            Field[] fields = clazz.getDeclaredFields();
            for (int i = 0; i < fields.length; i++) {
                String fieldName = fields[i].getName();
                String values = getFieldValue(item, clazz, fields[i], fieldName);

                TextView textView = helper.itemView.findViewWithTag(fieldName);
                if (textView != null) {
                    if (!TextUtils.isEmpty(values)) {
                        textView.setText(values);
                    } else {
                        textView.setText("");
                    }
                }
            }
            clazz = clazz.getSuperclass();
        }
    }

    private String getFieldValue(T item, Class clazz, Field field, String fieldName) {
        ShowAnnotation annotation = field.getAnnotation(ShowAnnotation.class);
        if (annotation != null) {
            String upperChar = fieldName.substring(0, 1).toUpperCase();
            String anotherStr = fieldName.substring(1);
            String methodName = "get" + upperChar + anotherStr;
            Method method = null;
            try {
                method = clazz.getMethod(methodName, new Class[]{});
                method.setAccessible(true);
                Object resultValue = method.invoke(item, new Object[]{});
                if (resultValue instanceof BigDecimal) {
                    return AppUtils.getBigDecimalValue((BigDecimal) resultValue).toPlainString();
                } else {
                    if (resultValue == null && annotation.isDecimal()) {
                        return AppUtils.getBigDecimalValue((BigDecimal) resultValue).toPlainString();
                    } else {
                        return resultValue.toString();
                    }
                }
            } catch (Exception e) {
                Log.e("wq", e.getMessage());
            }
        }
        return "";
    }
}