package com.midea.prestorage.function.picktask


import android.app.ActionBar
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestoragesaas.databinding.ActivityPickTaskBinding
import com.midea.prestorage.function.inv.response.InvStockTakeTask
import com.midea.prestorage.function.picktaskdetail.PickTaskDetailActivity

class PickTaskActivity : BaseViewModelActivity<PickTaskVm>() {

    private lateinit var binding: ActivityPickTaskBinding
    private lateinit var popupWindowDropDown: PopupWindow
    private lateinit var recyclerView: RecyclerView
    private lateinit var recyclerViewAdapter: RecyclerViewDaysAdapter
    private lateinit var dataListAdapter: DataListAdapter
    private lateinit var view: View

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_pick_task)
        vm = ViewModelProvider.AndroidViewModelFactory(application).create(PickTaskVm::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        initDateChooseDropDown()
        initObserveLister()
        initOther()
        initMainRecycleView()//初始化

        vm.onRefreshCommand.onRefresh()//第一次进入请求网络获取数据
    }

    //初始化
    private fun initMainRecycleView() {
        binding.srl.setOnRefreshListener(vm.onRefreshCommand)

        binding.dataList.apply {
            layoutManager = LinearLayoutManager(this.context) //设置线性布局
            dataListAdapter = DataListAdapter(this@PickTaskActivity)
            adapter = dataListAdapter
        }

        //项目列表单击
        dataListAdapter.setOnItemClickListener { _, _, position ->
            vm.getDetails(dataListAdapter.data[position])
        }
    }

    private fun initOther() {
        binding.dropDownDay.text = "1天"
    }

    private fun initObserveLister() {
        binding.vm?.drowDownBool?.observe(this, Observer<Boolean> {
            if (it) {
                //弹出界面
                popupWindowDropDown = PopupWindow(
                    view,
                    binding.dropDownDay.measuredWidth,
                    ActionBar.LayoutParams.WRAP_CONTENT
                ) //参数为1.View 2.宽度 3.高度
                popupWindowDropDown.isOutsideTouchable = true
                popupWindowDropDown.showAsDropDown(binding.dropDownDay, 0, 5)
            }
        })

        binding.vm?.notificationDataListChange?.observe(this, Observer<Boolean> {
            if (it) {
                dataListAdapter.setNewInstance(binding.vm?.returnData?.value)
                dataListAdapter.notifyDataSetChanged()
//                android.util.Log.d("test", "通知改变");
            }
        })
    }

    private fun initDateChooseDropDown() {
        //获取列表
        view = LayoutInflater.from(this)
            .inflate(
                R.layout.activity_recycle_view_drop_down_list,
                binding.root as ViewGroup, false
            )
        recyclerView =
            view.findViewById(R.id.recycle_view_drop_down_list) as RecyclerView

        recyclerView.layoutManager = LinearLayoutManager(this) //设置线性布局

        //设置更新数据
        recyclerViewAdapter = RecyclerViewDaysAdapter()
        recyclerView.apply {
            adapter = recyclerViewAdapter
        }
        val dayList = mutableListOf<String>("1天", "3天", "7天")
        recyclerViewAdapter.setNewInstance(dayList)
        vm.dropDownDay.value = "1天"

        //天数单击
        recyclerViewAdapter.setOnItemClickListener { adapter, view, position ->
            vm.dropDownDay.value =
                (view.findViewById<TextView>(R.id.item_recyle) as TextView).text.toString()
            popupWindowDropDown.dismiss()
            vm.onRefreshCommand.onRefresh() //切换天数后需要自动按条件刷新下任务列表
        }
    }

    //天数采集器
    class RecyclerViewDaysAdapter() : CommonAdapter<String>(R.layout.activity_recycle_view_status) {
        override fun convert(holder: BaseViewHolder?, item: String?) {
            super.convert(holder, item)
            holder?.setText(R.id.item_recyle, item)
        }
    }

    //列表采集器
    class DataListAdapter(private val contexts: Context) :
        CommonAdapter<InvStockTakeTask>(R.layout.activity_pick_task_list_item) {
        override fun convert(holder: BaseViewHolder?, item: InvStockTakeTask?) {
            super.convert(holder, item)

            //设置字体颜色
            if (item?.status == "100") {
                holder?.setText(R.id.status, "未开始")
                holder?.setTextColor(
                    R.id.status,
                    ContextCompat.getColor(contexts, R.color.button_gray)
                )
                holder?.setBackgroundResource(R.id.status, R.drawable.bg_bt_litter_gray)
            } else {
                holder?.setText(R.id.status, "拣货中")
                holder?.setTextColor(
                    R.id.status,
                    ContextCompat.getColor(contexts, R.color.colorRed)
                )
                holder?.setBackgroundResource(R.id.status, R.drawable.bg_bt_litter_blue)
            }
        }
    }
}