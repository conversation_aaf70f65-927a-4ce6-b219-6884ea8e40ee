package com.midea.prestorage.function.inv.response;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class InvStockTakeTaskDetail implements Comparable<InvStockTakeTaskDetail>, Serializable {
    @ShowAnnotation
    private String custItemCode;

    @ShowAnnotation
    private String fromLoc;

    @ShowAnnotation
    private String itemName;

    @ShowAnnotation(isDecimal = true)
    private BigDecimal fromQty;

    @ShowAnnotation(isDecimal = true)
    private BigDecimal toQty;

    @ShowAnnotation
    private String lotAtt04Str;

    private String lotAtt04;

    private String lotAtt01;

    private String lotAtt02;

    private String lotAtt03;

    private String lotAtt05;

    private String ids;//返回的id

    private int orderByGood;//默认按库位排序

    private String barcode;

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getFromLoc() {
        return fromLoc;
    }

    public void setFromLoc(String fromLoc) {
        this.fromLoc = fromLoc;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public BigDecimal getFromQty() {
        return fromQty;
    }

    public void setFromQty(BigDecimal fromQty) {
        this.fromQty = fromQty;
    }

    public BigDecimal getToQty() {
        return toQty;
    }

    public void setToQty(BigDecimal toQty) {
        this.toQty = toQty;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getLotAtt01() {
        return lotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        this.lotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return lotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        this.lotAtt02 = lotAtt02;
    }

    public String getLotAtt03() {
        return lotAtt03;
    }

    public void setLotAtt03(String lotAtt03) {
        this.lotAtt03 = lotAtt03;
    }

    public String getLotAtt05() {
        return lotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        this.lotAtt05 = lotAtt05;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public int getOrderByGood() {
        return orderByGood;
    }

    public void setOrderByGood(int orderByGood) {
        this.orderByGood = orderByGood;
    }

    @Override
    public int compareTo(InvStockTakeTaskDetail o) {
        if (orderByGood == 0) {
            return fromLoc.compareTo(o.fromLoc);
        } else {
            return custItemCode.compareTo(o.custItemCode);
        }
    }


    public String getLotAtt04Str() {
        return lotAtt04Str;
    }

    public void setLotAtt04Str(String lotAtt04Str) {
        this.lotAtt04Str = lotAtt04Str;
    }
}
