package com.midea.prestorage.beans.net

import com.midea.prestorage.base.annotation.ShowAnnotation
import java.math.BigDecimal

data class RespPackInfo(
    val shippingContainerId: String?,
    val shipContainerCode: String?,
    val custOrderNo: String?,
    val waveNo: String?,
    val pickContainerId: String?,
    val details: List<PackInfoDetail>?,
)
data class PackInfoDetail(
    @ShowAnnotation
    var itemCode: String? = null,
    @ShowAnnotation
    var custItemCode: String? = null,
    @ShowAnnotation
    var itemName: String? = null,
    @ShowAnnotation
    var unit: String? = null,
    @ShowAnnotation
    var barcode69: String? = null,
    @ShowAnnotation
    var cdpaFormat: String? = null,
    @ShowAnnotation
    var packedQty: BigDecimal? = null
)

