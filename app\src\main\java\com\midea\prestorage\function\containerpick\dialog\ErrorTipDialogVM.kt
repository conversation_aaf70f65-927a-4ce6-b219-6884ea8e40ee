package com.midea.prestorage.function.containerpick.dialog

import android.view.View

class ErrorTipDialogVM(val dialog: ErrorTipDialog) {

    var listener: ErrorTipDialog.OnErrorTipBack? = null

    fun close() {
        dialog.dismiss()
    }

    /**
     * 确认按钮
     */
    val confirmClick = View.OnClickListener {
        dialog.dismiss()
        listener?.onConfirmClick(dialog.getCheckedText())
    }
}
