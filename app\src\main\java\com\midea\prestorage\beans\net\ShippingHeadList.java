package com.midea.prestorage.beans.net;

import androidx.annotation.Nullable;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;
import java.math.BigDecimal;

public class ShippingHeadList implements Serializable {

    @ShowAnnotation
    private int index;
    @ShowAnnotation
    private String shipmentCode;
    @ShowAnnotation
    private String statusStr;
    @ShowAnnotation
    private BigDecimal planQty;
    @ShowAnnotation
    private BigDecimal pickQty;
    @ShowAnnotation
    private BigDecimal oqcQty;
    @ShowAnnotation
    private String custOrderNo;
    @ShowAnnotation
    private String ownerName;
    @ShowAnnotation
    private String waveNo;
    @ShowAnnotation
    private String dispatchNo;
    @ShowAnnotation
    private String shipToCustomerName;
    @ShowAnnotation
    private String oqcEndTime;
    @ShowAnnotation
    @Nullable
    private BigDecimal oqcCsQty;
    @ShowAnnotation
    @Nullable
    private BigDecimal oqcEaQty;

    private int status;
    private String id;
    private String createTime;
    private String updateTime;
    private String tenantCode;
    private String createUserCode;
    private String createUserName;
    private String updateUserCode;
    private String updateUserName;
    private String remark;
    private String version;
    private String deleteFlag;
    private String pageNo;
    private String pageSize;
    private String offset;
    private String orderBy;
    private String orderByType;
    private String ids;
    private String tenantCodes;
    private String count;
    private String startTime;
    private String endTime;
    private String ownerCode;
    private String shipToCustomerCode;
    private String standardCustomerCode;
    private String standardCustomerName;

    @Nullable
    public BigDecimal getOqcCsQty() {
        return oqcCsQty;
    }

    public void setOqcCsQty(@Nullable BigDecimal oqcCsQty) {
        this.oqcCsQty = oqcCsQty;
    }

    @Nullable
    public BigDecimal getOqcEaQty() {
        return oqcEaQty;
    }

    public void setOqcEaQty(@Nullable BigDecimal oqcEaQty) {
        this.oqcEaQty = oqcEaQty;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getPageNo() {
        return pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getOffset() {
        return offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderByType() {
        return orderByType;
    }

    public void setOrderByType(String orderByType) {
        this.orderByType = orderByType;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getTenantCodes() {
        return tenantCodes;
    }

    public void setTenantCodes(String tenantCodes) {
        this.tenantCodes = tenantCodes;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getShipmentCode() {
        return shipmentCode;
    }

    public void setShipmentCode(String shipmentCode) {
        this.shipmentCode = shipmentCode;
    }

    public String getCustOrderNo() {
        return custOrderNo;
    }

    public void setCustOrderNo(String custOrderNo) {
        this.custOrderNo = custOrderNo;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getDispatchNo() {
        return dispatchNo;
    }

    public void setDispatchNo(String dispatchNo) {
        this.dispatchNo = dispatchNo;
    }

    public String getShipToCustomerCode() {
        return shipToCustomerCode;
    }

    public void setShipToCustomerCode(String shipToCustomerCode) {
        this.shipToCustomerCode = shipToCustomerCode;
    }

    public String getShipToCustomerName() {
        return shipToCustomerName;
    }

    public void setShipToCustomerName(String shipToCustomerName) {
        this.shipToCustomerName = shipToCustomerName;
    }

    public String getStandardCustomerCode() {
        return standardCustomerCode;
    }

    public void setStandardCustomerCode(String standardCustomerCode) {
        this.standardCustomerCode = standardCustomerCode;
    }

    public String getStandardCustomerName() {
        return standardCustomerName;
    }

    public void setStandardCustomerName(String standardCustomerName) {
        this.standardCustomerName = standardCustomerName;
    }

    public String getOqcEndTime() {
        return oqcEndTime;
    }

    public void setOqcEndTime(String oqcEndTime) {
        this.oqcEndTime = oqcEndTime;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public BigDecimal getPlanQty() {
        return planQty;
    }

    public void setPlanQty(BigDecimal planQty) {
        this.planQty = planQty;
    }

    public BigDecimal getPickQty() {
        return pickQty;
    }

    public void setPickQty(BigDecimal pickQty) {
        this.pickQty = pickQty;
    }

    public BigDecimal getOqcQty() {
        return oqcQty;
    }

    public void setOqcQty(BigDecimal oqcQty) {
        this.oqcQty = oqcQty;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}