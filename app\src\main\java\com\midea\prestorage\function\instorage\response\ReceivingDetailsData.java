package com.midea.prestorage.function.instorage.response;

import java.io.Serializable;

public class ReceivingDetailsData implements Serializable {
    String itemType;
    String itemName;
    String itemValue;
    String lotAtt;
    RespNoReceived.CdWhLotDetailDTO info;

    public ReceivingDetailsData(String itemType, String itemName, String itemValue, String lotAtt, RespNoReceived.CdWhLotDetailDTO info) {
        this.itemType = itemType;
        this.itemName = itemName;
        this.itemValue = itemValue;
        this.lotAtt = lotAtt;
        this.info = info;
    }

    public String getLotAtt() {
        return lotAtt;
    }

    public void setLotAtt(String lotAtt) {
        this.lotAtt = lotAtt;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemValue() {
        return itemValue;
    }

    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }

    public RespNoReceived.CdWhLotDetailDTO getInfo() {
        return info;
    }

    public void setInfo(RespNoReceived.CdWhLotDetailDTO info) {
        this.info = info;
    }
}
