package com.midea.prestorage.function.inv

import CheckUtil
import android.app.Application
import android.content.Intent
import android.text.TextUtils
import androidx.databinding.ObservableBoolean
import androidx.lifecycle.MutableLiveData
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.ReqAddAdjust
import com.midea.prestorage.beans.net.RespAdjustList
import com.midea.prestorage.beans.net.RespCustomerPage
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody

class InvReconciliationVM(application: Application) : BaseViewModel(application) {
    val title = MutableLiveData("库存调账")
    var mCustomer = MutableLiveData("")
    var mCustomerCode = MutableLiveData("")
    var adjustNo = MutableLiveData("")
    val showCustomerDialog = MutableLiveData(false)
    val isRefreshing = ObservableBoolean(false)
    val isNoData = ObservableBoolean(false)
    val customerDataNotify = MutableLiveData<MutableList<RespCustomerPage>>()
    var dialogType = 0

    // 当前页码
    var pageNo = 1

    val loadMoreComplete = MutableLiveData(0)
    var showDatas = MutableLiveData<MutableList<RespAdjustList>>()
    var loadMoreDatas = MutableLiveData<MutableList<RespAdjustList>>()

    companion object {
        const val TYPE_SELECT_CUSTOMER = 1 //选择客户
        const val TYPE_ADD_ADJUST = 2 //新增调账单
    }

    override fun init() {
        getCustomer() //获取客户列表
    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        pageNo = 1
        isRefreshing.set(true)
        initOrderList()
    }

    private fun getCustomer() {
        launch(showDialog = false,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getWareManageAPI()
                    .queryCustomer(Constants.whInfo?.whCode.toString(), 1, 100)
            }

            if (result.code == 0L) {
                result.data?.list?.let { list ->
                    list.forEach {
                        it.showInfo = it.customerName
                    }
                    customerDataNotify.value = list
                }
            } else {
                customerDataNotify.value = mutableListOf()
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    fun initOrderList() {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
                isRefreshing.set(false)
            }) {

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getWareManageAPI()
                    .adjustListPage(
                        Constants.whInfo?.whCode.toString(),
                        pageNo,
                        10,
                        mCustomerCode.value.toString().trim(),
                        adjustNo.value.toString().trim()
                    )
            }

            if (result.code == 0L) {
                loadMoreComplete.value = 1

                result.data?.let {
                    if (pageNo == 1) {
                        showDatas.value = it.list
                    } else {
                        loadMoreDatas.value = it.list
                    }
                    if (pageNo < it.totalPage) {
                        pageNo++
                    } else {
                        loadMoreComplete.value = 2
                    }
                }
            } else {
                result.msg?.let { showNotification(it, false) }
                if (pageNo == 1) {
                    val emptyList = mutableListOf<RespAdjustList>()
                    showDatas.value = emptyList
                }
            }
        }
    }

    fun addAdjust(data: RespCustomerPage) {
        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getWareManageAPI()
                    .addAdjust(
                        ReqAddAdjust(
                            customerCode = data.customerCode ?: "",
                            customerName = data.customerName ?: "",
                            ownerCode = data.ownerCode ?: "",
                            ownerName = data.ownerName ?: ""
                        )
                    )
            }

            if (result.code == 0L) {
                showNotification("已创建调账单${result.data?.adNo ?: ""}", true)
                onRefreshCommand.onRefresh()
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    fun onEnterItemCode() {
        if (CheckUtil.isFastDoubleClick()) {
            if (TextUtils.isEmpty(adjustNo.value)) {
                showNotification("调账单不能为空", false)
                return
            }
            onRefreshCommand.onRefresh()
        }
    }

    fun addAdjustClick() {
        if (CheckUtil.isFastDoubleClick()) {
            dialogType = TYPE_ADD_ADJUST
            showCustomerDialog.value = true
        }
    }

    fun checkAdjust() {
        if (CheckUtil.isFastDoubleClick()) {
            onRefreshCommand.onRefresh()
        }
    }

    fun showErrorNotification(msg: String, isSuccess: Boolean) {
        showNotification(msg, isSuccess)
    }

    fun onSelectCustomer() {
        dialogType = TYPE_SELECT_CUSTOMER
        showCustomerDialog.value = true
    }


    fun clearCustomer() {
        if (CheckUtil.isFastDoubleClick()) {
            mCustomer.value = ""
            mCustomerCode.value = ""
            onRefreshCommand.onRefresh()
        }
    }

    fun clearAdjustNo() {
        if (CheckUtil.isFastDoubleClick()) {
            adjustNo.value = ""
            onRefreshCommand.onRefresh()
        }
    }

    fun onItemClick(bean: RespAdjustList) {
        val it = Intent()
        it.putExtra("RespAdjustList", bean)
        toActivity(it, InvReconciliationDetailActivity::class.java)
    }

}