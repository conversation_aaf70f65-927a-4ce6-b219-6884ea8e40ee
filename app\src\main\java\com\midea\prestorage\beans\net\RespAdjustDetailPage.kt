package com.midea.prestorage.beans.net

import com.midea.prestorage.base.annotation.ShowAnnotation
import java.math.BigDecimal

data class RespAdjustDetailPage(
    @ShowAnnotation
    val actQty: BigDecimal?,
    val adNo: String?,
    @ShowAnnotation
    val cdpaFormat: String?,
    val id: Long?,
    val isPoint: String?,
    val itemCode: String?,
    @ShowAnnotation
    val custItemCode: String?,
    @ShowAnnotation
    val itemName: String?,
    @ShowAnnotation
    val locCode: String?,
    var lotAtt01: String?,
    var lotAtt02: String?,
    var lotAtt03: String?,
    val lotAtt04: String?,
    val lotAtt04Cn: String?,
    val lotAtt05: String?,
    val lotAtt06: String?,
    val lotAtt07: String?,
    val lotAtt08: String?,
    val lotAtt09: String?,
    val lotAtt10: String?,
    val lotAtt11: String?,
    val lotAtt12: String?,
    @ShowAnnotation
    var lotAttStr: String?,
    val packageDetails: List<PackageDetail>?
)

data class PackageDetail(
    val cdpaCode: String?,
    val cdpaFormat: String?,
    val cdprQuantity: BigDecimal?,
    val seqNo: Int?,
    val cdprUnit: String?,
    val cdprDesc: String?
)