package com.midea.prestorage.function.put

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.text.TextWatcher
import android.util.Log
import android.widget.EditText
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.net.ReceiptListInfo
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestoragesaas.databinding.ActivityInStorageScanOperationBinding
import com.midea.prestorage.function.put.dialog.SettingDialog
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.LotAttUnit
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.FilterDigitTextWatcher
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

// 入库订单池
class InStorageScanPutOperationActivity : BaseActivity() {

    lateinit var binding: InStorageScanOperationUnionBinding
    private var vm = InStorageScanPutOperationVM(this)
    val adapter = OutPoolStorageAdapter()

    private lateinit var settingDialog: SettingDialog

    var textWatcher: FilterDigitTextWatcher? = null

    var textWatcher1: FilterDigitTextWatcher? = null
    var textWatcher2: FilterDigitTextWatcher? = null
    var textWatcher3: FilterDigitTextWatcher? = null
    var textWatcher4: FilterDigitTextWatcher? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            InStorageScanOperationUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_in_storage_scan_operation_care
                )
            )
        } else {
            InStorageScanOperationUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_in_storage_scan_operation
                )
            )
        }
        binding.vm = vm
        binding.lifecycleOwner = this

        containerLock()
        initRecycle()
        initWatcher()

        settingDialog = SettingDialog(this)
        settingDialog.setOnSettingClick(object : SettingDialog.SettingBack {
            override fun onConfirmClick(mode: Int) {
                saveUserInfo(mode)

                vm.scanMode = mode
                vm.resume()
            }
        })

        init()
        vm.init()
    }

    private fun initWatcher() {
        if (textWatcher == null) {
            textWatcher = FilterDigitTextWatcher(binding.edQty, 0, true) {
                if ("只能输入正整数" == it) {
                    binding.edQty.setText(binding.edQty.text.toString().replace(".", ""))
                    if (binding.edQty.text!!.isNotEmpty()) {
                        binding.edQty.setSelection(binding.edQty.text!!.length)
                    }
                }
                ToastUtils.getInstance().showErrorToastWithSound(this, it)
            }
        }

        textWatcher1 = createTextWatcher(binding.itemQtyInput1.edQty)
        textWatcher2 = createTextWatcher(binding.itemQtyInput2.edQty)
        textWatcher3 = createTextWatcher(binding.itemQtyInput3.edQty)
        textWatcher4 = createTextWatcher(binding.itemQtyInput4.edQty)

        binding.rgUnit.setOnCheckedChangeListener { _, _ ->
        }

    }

    private fun createTextWatcher(editText: EditText): FilterDigitTextWatcher {
        return FilterDigitTextWatcher(editText, 0, true) {
            if ("只能输入正整数" == it) {
                editText.setText(editText.text.toString().replace(".", ""))
                if (editText.text!!.isNotEmpty()) {
                    editText.setSelection(editText.text!!.length)
                }
            }
            ToastUtils.getInstance().showErrorToastWithSound(this, it)
        }
    }

    private fun saveUserInfo(scanPutMode: Int) {
        Observable.create<String> {
            try {
                Constants.userInfo?.scanPutMode = scanPutMode
                db.saveOrUpdate(Constants.userInfo)
            } catch (e: Exception) {
                Log.e("wq", e.message!!)
            }
            it.onComplete()
        }.subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .bindUntilEvent(this, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }

    private fun lockEditText(edit: EditText) {
        edit.setOnFocusChangeListener { view, hasFocus ->
            if (!hasFocus) {
                if (TextUtils.isEmpty(edit.text.toString())) {
                    view.post {
                        edit.setText("")
                        view.requestFocus()
                    }
                }
            }
        }
    }

    private fun initRecycle() {
        binding.rv.layoutManager = LinearLayoutManager(this)
        binding.rv.adapter = adapter

        adapter.setOnItemClickListener { adapter, _, position ->
            if (CheckUtil.isFastDoubleClick()) {
                val bean = adapter.data[position] as ReceiptListInfo
                vm.onItemClick(bean)
            }
        }
    }

    fun qtySelectAll() {
        binding.edQty.selectAll()
    }

    fun qtyEditUnable() {
        binding.edQty.isEnabled = false
    }

    fun qtyEditEnable() {
        binding.edQty.isEnabled = true
    }

    fun inputRequestFocus() {
        binding.etScan.isEnabled = true
        binding.etScan.setText("")
        binding.etScan.requestFocus()
        lockEditText(binding.etScan)
    }

    fun goodsRequestFocus() {
        binding.etScan.isEnabled = false
        goodsLock()
    }

    private fun containerLock() {
        binding.etScan.requestFocus()
        binding.etScan.isEnabled = true
    }

    fun goodsLock() {
        binding.edGoods.isEnabled = true
        binding.edGoods.requestFocus()
    }

    fun qtyLock() {
        binding.edQty.requestFocus()
    }

    override fun getTvInfo(): TextView? {
        return binding.tvNotification
    }

    /**
     * 20240725版本需求修改后，此方法目前只在逐件上架时使用,只有cs和ea
     */
    fun getPackageStr(): String = when {
        binding.rbCs.isChecked -> "CS"
        binding.rbEa.isChecked -> "EA"
        else -> ""
    }

    fun clean() {
        binding.etScan.setText("")
        binding.edGoods.setText("")
        if (vm.scanMode == 0) {
            binding.edQty.setText("1")
        } else {
            binding.edQty.setText("")
        }
        containerLock()
    }

    fun init() {
        vm.chooseReceiptItemLiveData.observe(this) {
            showChooseItemDialog(it)
        }
    }

    private fun showChooseItemDialog(list: List<Pair<String, String>>) {
        val dialog = FilterDialog(this)
        dialog.setTitle("请选择上架的商品")
        dialog.dismissEdit()
        dialog.addAllDataV2(list.map { pair ->
            BaseItemShowInfo().also {
                it.showInfo = "${pair.second}(${pair.first})"
            }
        }.toMutableList())
        dialog.setOnCheckListener {
            vm.onChooseItem(it.showInfo)
            dialog.dismiss()
        }
        dialog.show()
    }

    class OutPoolStorageAdapter :
        CommonAdapter<ReceiptListInfo>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_put_details_goods_care else R.layout.item_put_details_goods) {
        override fun convert(
            holder: BaseViewHolder?,
            item: ReceiptListInfo?
        ) {
            super.convert(holder, item)

            holder?.setText(
                R.id.tv_cust_item_code, item?.custItemCode
            )

            if (item?.whCsBarcode69.isNullOrEmpty() && item?.whBarcode69.isNullOrEmpty()) {
                holder?.setGone(R.id.tv_carcode69, true)
            } else {
                holder?.setGone(R.id.tv_carcode69, false)
                holder?.setText(
                    R.id.tv_carcode69,
                    LotAttUnit.formatWhBarcode69(item?.whCsBarcode69, item?.whBarcode69)
                )
            }

            holder?.setText(
                R.id.tv_qty_info_left,
                AppUtils.getBigDecimalValue(item?.checkQty).toPlainString() + "/"
            )
            holder?.setText(
                R.id.tv_qty_info_right, AppUtils.getBigDecimalValue(item!!.qty).toPlainString()
            )
            holder?.setGone(R.id.ll_create, TextUtils.isEmpty(item?.lotAtt01))
            holder?.setGone(R.id.ll_lose, TextUtils.isEmpty(item?.lotAtt02))
            holder?.setGone(R.id.ll_in, TextUtils.isEmpty(item?.lotAtt03))
            holder?.setGone(R.id.ll_recommend, TextUtils.isEmpty(item?.locZoneAreaDto?.locCode))

            if (!TextUtils.isEmpty(item?.locZoneAreaDto?.locCode)) {
                holder?.setText(R.id.tv_recommend, item?.locZoneAreaDto?.locCode)
            }
            if (item?.packageRelationList.isNullOrEmpty()) {
                holder?.setText(
                    R.id.tv_wait_for,
                    AppUtils.getBigDecimalValue(item?.unitQty).toPlainString()
                )
            } else {
                holder?.setText(
                    R.id.tv_wait_for,
                    AppUtils.getBigDecimalValue(item?.unitQty)
                        .toPlainString() + (item?.packageRelationList?.find { item?.unit == it.cdprUnit }?.cdprDesc
                        ?: "")
                )
            }
            when {
                AppUtils.isZero(item?.checkQty) -> {
                    holder?.setBackgroundResource(R.id.ll_bg, R.drawable.bg_round_rectangle_white)
                }
                item?.checkQty?.compareTo(item.qty)!! > -1 -> {
                    holder?.setBackgroundResource(R.id.ll_bg, R.drawable.bg_round_rectangle_green)
                }
                else -> {
                    holder?.setBackgroundResource(
                        R.id.ll_bg,
                        R.drawable.bg_round_rectangle_light_blue
                    )
                }
            }

            when (item?.lotAtt04) {
                "Y" -> holder?.setBackgroundResource(R.id.tv_status, R.drawable.bg_bt_green)
                else -> holder?.setBackgroundResource(
                    R.id.tv_status,
                    R.drawable.bg_bt_red_no_circle
                )
            }
        }
    }
}