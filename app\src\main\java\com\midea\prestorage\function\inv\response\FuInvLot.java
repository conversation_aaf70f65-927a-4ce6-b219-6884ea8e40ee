package com.midea.prestorage.function.inv.response;


import com.midea.prestorage.beans.base.BaseItemForPopup;

public class FuInvLot extends BaseItemForPopup {

    /**
     * 批属性号
     */
    private String lotNum;

    /**
     * 仓库
     */
    private String whCode;

    /**
     * 安得货品编号
     */
    private String itemCode;

    /**
     * 批次属性1(生产日期)
     */
    private String lotAtt01;

    /**
     * 批次属性2(失效日期)
     */
    private String lotAtt02;

    /**
     * 批次属性3
     */
    private String lotAtt03;

    /**
     * 批次属性4
     */
    private String lotAtt04;

    /**
     * 批次属性5
     */
    private String lotAtt05;

    /**
     * 批次属性6
     */
    private String lotAtt06;

    /**
     * 批次属性7
     */
    private String lotAtt07;

    /**
     * 批次属性8
     */
    private String lotAtt08;

    /**
     * 批次属性9
     */
    private String lotAtt09;

    /**
     * 批次属性10
     */
    private String lotAtt10;

    /**
     * 批次属性11
     */
    private String lotAtt11;

    /**
     * 批次属性12
     */
    private String lotAtt12;

    /**
     * 创建用户
     */
    private String createUserCode;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改用户
     */
    private String upStringUserCode;

    /**
     * 修改人名称
     */
    private String upStringUserName;

    /**
     * upStringTime
     */
    private String upStringTime;

    /**
     * 租户
     */
    private String tenantCode;

    /**
     * 备注
     */
    private String remark;



    private String qty ;//可用库存


    public String getLotNum() {
        return lotNum;
    }

    public void setLotNum(String lotNum) {
        this.lotNum = lotNum;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getLotAtt01() {
        return lotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        this.lotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return lotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        this.lotAtt02 = lotAtt02;
    }

    public String getLotAtt03() {
        return lotAtt03;
    }

    public void setLotAtt03(String lotAtt03) {
        this.lotAtt03 = lotAtt03;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getLotAtt05() {
        return lotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        this.lotAtt05 = lotAtt05;
    }

    public String getLotAtt06() {
        return lotAtt06;
    }

    public void setLotAtt06(String lotAtt06) {
        this.lotAtt06 = lotAtt06;
    }

    public String getLotAtt07() {
        return lotAtt07;
    }

    public void setLotAtt07(String lotAtt07) {
        this.lotAtt07 = lotAtt07;
    }

    public String getLotAtt08() {
        return lotAtt08;
    }

    public void setLotAtt08(String lotAtt08) {
        this.lotAtt08 = lotAtt08;
    }

    public String getLotAtt09() {
        return lotAtt09;
    }

    public void setLotAtt09(String lotAtt09) {
        this.lotAtt09 = lotAtt09;
    }

    public String getLotAtt10() {
        return lotAtt10;
    }

    public void setLotAtt10(String lotAtt10) {
        this.lotAtt10 = lotAtt10;
    }

    public String getLotAtt11() {
        return lotAtt11;
    }

    public void setLotAtt11(String lotAtt11) {
        this.lotAtt11 = lotAtt11;
    }

    public String getLotAtt12() {
        return lotAtt12;
    }

    public void setLotAtt12(String lotAtt12) {
        this.lotAtt12 = lotAtt12;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpStringUserCode() {
        return upStringUserCode;
    }

    public void setUpStringUserCode(String upStringUserCode) {
        this.upStringUserCode = upStringUserCode;
    }

    public String getUpStringUserName() {
        return upStringUserName;
    }

    public void setUpStringUserName(String upStringUserName) {
        this.upStringUserName = upStringUserName;
    }

    public String getUpStringTime() {
        return upStringTime;
    }

    public void setUpStringTime(String upStringTime) {
        this.upStringTime = upStringTime;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}