package com.midea.prestorage.function.inv

import android.app.Application
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.function.instorage.response.InOrderData
import com.midea.prestorage.function.instorage.response.InOrderDataSort
import com.midea.prestorage.function.inv.response.AdjustDetailSearchResp
import com.midea.prestorage.function.inv.response.AdjustDetailSearchSort
import com.midea.prestorage.function.outstorage.response.RespShipmentDetail
import com.midea.prestorage.function.outstorage.response.ShipmentDetailSort
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DbUtils
import com.midea.prestorage.utils.isNull
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody

class StatusAdjustmentScanVM(application: Application) : BaseViewModel(application) {
    var finishActivity = MutableLiveData(false)
    var curOrderNo = MutableLiveData<String>("") // 单号 (入库单号或波次单号)
    var anyCode = MutableLiveData<String>("")
    val processInfo = MutableLiveData<String>("0/0")
    var isShowDeleteDialog = MutableLiveData(false)
    var isConfirmSuc = MutableLiveData(false)
    var goodsRequest = MutableLiveData(false)
    val db = DbUtils.db
    var inOrderDatas = MutableLiveData<MutableList<AdjustDetailSearchSort>>()
    var whCode: String? = null
    var ownerCode = MutableLiveData<String>("")
    var headerId = MutableLiveData<String>()

    override fun init() {

    }

    fun onEnterAnyCode() {
        if (CheckUtil.isFastDoubleClick()) {
            if (anyCode.value.isNullOrEmpty()) {
                showNotification("条码不能为空", false)
                return
            }

            launch(showDialog = true,
                error = {
                }, finish = {
                }) {

                val param = mutableMapOf(
                    "adjustCode" to curOrderNo.value.toString().trim(),
                    "whCode" to whCode,
                    "ownerCode" to ownerCode.value.toString(),
                    "serialNo" to anyCode.value.toString()
                )

//                if (Constants.whInfo?.getBearingSystem() == "3") {
//                    param["bearingSystem"] = "BWMS"
//                }else {
//                    param["bearingSystem"] = "AWMS"
//                }

                if (Constants.whInfo?.getBearingSystem() == "3") {
                    param["bearingSystem"] = "WMS2B"
                }else {
                    param["bearingSystem"] = "AWMS"
                }

                val requestBody = RequestBody.create(
                    MediaType.parse("Content-Type, application/json"),
                    Gson().toJson(param)
                )

                val result = withContext(Dispatchers.IO) {
                    val resp = async { RetrofitHelper.getAppAPI().adjustScanBarcode(requestBody) }
                    resp.await()
                }

                if (result.code == 0.toLong()) {
                    // 不需要前端再处理，直接刷新界面即可
                    showNotification("扫码成功", true)
                    //重置条码编辑框
                    anyCode.value = ""
                    // 扫码成功 后 刷新
                    whCode?.let { loadInOrderDatas(it) }

                } else {
                    result.msg?.let { showNotification(it, false) }
                    anyCode.value = ""
                }
            }
        }
    }

    // 加载 入库单或波次单 对应的所有商品信息
    fun loadInOrderDatas(whCode: String) {
        this.whCode = whCode
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf(
                "adjustCode" to curOrderNo.value.toString().trim()
            )

            if (Constants.whInfo?.getBearingSystem() == "3") {
                param["bearingSystem"] = "WMS2B"
            }else {
                param["bearingSystem"] = "AWMS"
            }

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().adjustDetailSearch(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {

                if (result.data != null && result.data!!.size > 0) {

                    ownerCode.value = result.data!![0].ownerCode
                    headerId.value = result.data!![0].headerId

                    //合并操作
                    val combines = mutableListOf<AdjustDetailSearchResp>()
                    result.data!!.forEach {
                        if (combines.contains(it)) {
                            val result = combines.find { item -> item == it }

                            result?.planQty = result?.planQty?.add(it.planQty)
                            if(!it.allocatedQty.isNull()) {
                                if (!result?.allocatedQty.isNull()) {
                                    result?.allocatedQty = result?.allocatedQty?.add(it.allocatedQty)
                                }else {
                                    result?.allocatedQty = it.allocatedQty
                                }
                            }
                            result?.scanNum = result?.scanNum?.add(it.scanNum)
                        } else {
                            combines.add(it)
                        }
                    }

                    val temp = mutableListOf<AdjustDetailSearchSort>()
                    combines.forEach {
                        temp.add(AdjustDetailSearchSort(it))
                    }
                    inOrderDatas.value = temp
                    var scanQty = 0
                    var planQty = 0
                    combines.forEach {
                        scanQty += AppUtils.getBigDecimalValueStr(it.scanNum).toInt()
                        planQty += AppUtils.getBigDecimalValueStr(it.allocatedQty).toInt()
                    }
                    processInfo.value = "$scanQty/$planQty"
                }

            } else {
                curOrderNo.value = ""
                result.msg?.let { showNotification(it, false) }
                //showNotification("单号不存在或无待扫码商品", false)
            }
        }
    }

    fun deleteSerial(whCode: String, code: String) {

        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf(
                "whCode" to whCode,
                "adjustCode" to curOrderNo.value.toString().trim(),
                "serialNo" to code
            )

            if (Constants.whInfo?.getBearingSystem() == "3") {
                param["bearingSystem"] = "WMS2B"
            }else {
                param["bearingSystem"] = "AWMS"
            }

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().adjustDeleteBarcode(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                result.msg?.let { showNotification(it, true) }
                loadInOrderDatas(whCode)
            } else {
                result.msg?.let { showNotification(it, false) }
                loadInOrderDatas(whCode)
            }
        }
    }

    fun adjustConfirm() {

        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf<String, Any?>(
                "headerId" to headerId.value
            )
            if (Constants.whInfo?.getBearingSystem() == "3") {
                param["bearingSystem"] = "WMS2B"
            }else {
                param["bearingSystem"] = "AWMS"
            }

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getAppAPI().adjustConfirm(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                isConfirmSuc.value = true
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    fun showErrorNotification(msg: String, isSuccess: Boolean) {
        showNotification(msg, isSuccess)
    }

    fun deleteBarcode() {
        if (CheckUtil.isFastDoubleClick()) {
            isShowDeleteDialog.value = true
        }
    }

    fun confirm() {
        if (CheckUtil.isFastDoubleClick()) {
            adjustConfirm()
        }
    }
}