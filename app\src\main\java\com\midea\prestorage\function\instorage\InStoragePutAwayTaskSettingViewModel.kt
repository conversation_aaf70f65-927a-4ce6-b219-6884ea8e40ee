package com.midea.prestorage.function.instorage

import android.app.Application
import android.view.View
import android.widget.TextView
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Transformations.map
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.PutAwayTask
import com.midea.prestorage.utils.SPUtils
import java.util.TreeMap

class InStoragePutAwayTaskSettingViewModel(application: Application) : BaseViewModel(application) {

    companion object {
        const val SHOW_LOT_ATT_01 = "IN_STORAGE_PUT_AWAY_TASK_LIST_SHOW_LOT_ATT_01"
        const val SHOW_LOT_ATT_03 = "IN_STORAGE_PUT_AWAY_TASK_LIST_SHOW_LOT_ATT_03"
        const val SHOW_LOT_ATT_05 = "IN_STORAGE_PUT_AWAY_TASK_LIST_SHOW_LOT_ATT_05"
        const val SHOW_TRACE_ID = "IN_STORAGE_PUT_AWAY_TASK_LIST_SHOW_TRACE_ID"
        const val DEFAULT_LOT_ATT_01 = true
        const val DEFAULT_LOT_ATT_03 = true
        const val DEFAULT_LOT_ATT_05 = true
        const val DEFAULT_TRACE_ID = true
        private val LOT_ATT_COMPARE_MAP = mapOf(
            SHOW_LOT_ATT_01 to 1,
            SHOW_LOT_ATT_03 to 2,
            SHOW_LOT_ATT_05 to 3,
            SHOW_TRACE_ID to 4,
        )
        private val lotAttComparator: (o1: String, o2: String) -> Int = { o1, o2 ->
            LOT_ATT_COMPARE_MAP[o1]!! - LOT_ATT_COMPARE_MAP[o2]!!
        }
        val settingName = mutableMapOf(
            SHOW_LOT_ATT_01 to "生产日期",
            SHOW_LOT_ATT_03 to "入库日期",
            SHOW_LOT_ATT_05 to "批次",
            SHOW_TRACE_ID to "跟踪号",
        )
    }

    val settingLiveData = MutableLiveData<MutableMap<String, Boolean>>()
    private val openSettingLiveData = map(settingLiveData) {
        it.filterTo(TreeMap(lotAttComparator)) { entry -> entry.value }
            .map { entry -> entry.key to settingName[entry.key] }
    }

    val showLotAttCount = map(openSettingLiveData) {
        it.size
    }

    val firstOpenLotAtt = map(openSettingLiveData) {
        it.getOrNull(0)
    }

    val secondOpenLotAtt = map(openSettingLiveData) {
        it.getOrNull(1)
    }

    val thirdOpenLotAtt = map(openSettingLiveData) {
        it.getOrNull(2)
    }

    val fourthOpenLotAtt = map(openSettingLiveData) {
        it.getOrNull(3)
    }

    override fun init() {
        initSetting()
    }

    private fun initSetting() {
        val map = mutableMapOf<String, Boolean>()
        map[SHOW_LOT_ATT_01] = SPUtils[SHOW_LOT_ATT_01, DEFAULT_LOT_ATT_01] as Boolean
        map[SHOW_LOT_ATT_03] = SPUtils[SHOW_LOT_ATT_03, DEFAULT_LOT_ATT_03] as Boolean
        map[SHOW_LOT_ATT_05] = SPUtils[SHOW_LOT_ATT_05, DEFAULT_LOT_ATT_05] as Boolean
        map[SHOW_TRACE_ID] = SPUtils[SHOW_TRACE_ID, DEFAULT_TRACE_ID] as Boolean
        settingLiveData.value = map
    }

    fun updateSetting(setting: Map<String, Boolean>) {
        val map = settingLiveData.value ?: mutableMapOf()
        map.clear()
        map.putAll(setting)
        settingLiveData.value = map
    }

    fun lotAttNameToValue(name: String?, putAwayTask: PutAwayTask?) =
        when (name) {
            "生产日期" -> putAwayTask?.lotAtt01 ?: ""
            "入库日期" -> putAwayTask?.lotAtt03 ?: ""
            "批次" -> putAwayTask?.lotAtt05 ?: ""
            "跟踪号" -> putAwayTask?.traceId ?: ""
            else -> ""
        }

    fun updateDynamicShowView(
        dynamicText1: TextView?, dynamicValueText1: TextView?,
        dynamicText2: TextView?, dynamicValueText2: TextView?,
        dynamicText3: TextView?, dynamicValueText3: TextView?,
        dynamicText4: TextView?, dynamicValueText4: TextView?,
        item: PutAwayTask?,
    ) {
        dynamicText1?.visibility = dynamic1TextVisibility()
        dynamicValueText1?.visibility = dynamic1TextVisibility()
        dynamicText2?.visibility = dynamic2TextVisibility()
        dynamicValueText2?.visibility = dynamic2TextVisibility()
        dynamicText3?.visibility = dynamic3TextVisibility()
        dynamicValueText3?.visibility = dynamic3TextVisibility()
        dynamicText4?.visibility = dynamic4TextVisibility()
        dynamicValueText4?.visibility = dynamic4TextVisibility()

        var pair = dynamic1TextValue(item)
        dynamicText1?.text = pair.first
        dynamicValueText1?.text = pair.second
        pair = dynamic2TextValue(item)
        dynamicText2?.text = pair.first
        dynamicValueText2?.text = pair.second
        pair = dynamic3TextValue(item)
        dynamicText3?.text = pair.first
        dynamicValueText3?.text = pair.second
        pair = dynamic4TextValue(item)
        dynamicText4?.text = pair.first
        dynamicValueText4?.text = pair.second
    }

    private fun dynamic1TextVisibility() =
        if (showLotAttCount.value!! > 0) View.VISIBLE else View.GONE

    private fun dynamic2TextVisibility() =
        if (showLotAttCount.value!! > 1) View.VISIBLE else View.GONE

    private fun dynamic3TextVisibility() =
        if (showLotAttCount.value!! > 2) View.VISIBLE else View.GONE

    private fun dynamic4TextVisibility() =
        if (showLotAttCount.value!! > 3) View.VISIBLE else View.GONE

    private fun dynamic1TextValue(item: PutAwayTask?): Pair<String?, String?> {
        val name = firstOpenLotAtt.value?.second
        val value = lotAttNameToValue(name, item)
        return name to value
    }

    private fun dynamic2TextValue(item: PutAwayTask?): Pair<String?, String?> {
        val name = secondOpenLotAtt.value?.second
        val value = lotAttNameToValue(name, item)
        return name to value
    }

    private fun dynamic3TextValue(item: PutAwayTask?): Pair<String?, String?> {
        val name = thirdOpenLotAtt.value?.second
        val value = lotAttNameToValue(name, item)
        return name to value
    }

    private fun dynamic4TextValue(item: PutAwayTask?): Pair<String?, String?> {
        val name = fourthOpenLotAtt.value?.second
        val value = lotAttNameToValue(name, item)
        return name to value
    }

}