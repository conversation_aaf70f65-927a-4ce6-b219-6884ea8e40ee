package com.midea.prestorage.beans.net;

import androidx.annotation.Nullable;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.math.BigDecimal;

public class RespPlanStockList {

    @ShowAnnotation
    private String stocktakeCode;
    @ShowAnnotation
    private String stocktakeName;
    @ShowAnnotation
    private BigDecimal locNum;
    @ShowAnnotation
    private String statusStr;
    @ShowAnnotation
    private String strategyTypeStr;
    @ShowAnnotation
    private String stocktakeRangeStr;
    @ShowAnnotation
    private BigDecimal usedLocNum;
    @Nullable
    private BigDecimal locUsePercent;
    @ShowAnnotation
    private String stocktakeCost;
    @ShowAnnotation
    private String strategyTypeName;
    @ShowAnnotation
    private String rangeName;
    @ShowAnnotation
    private String statusName;

    public String getStrategyTypeName() {
        return strategyTypeName;
    }

    public void setStrategyTypeName(String strategyTypeName) {
        this.strategyTypeName = strategyTypeName;
    }

    public String getRangeName() {
        return rangeName;
    }

    public void setRangeName(String rangeName) {
        this.rangeName = rangeName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getStocktakeCost() {
        return stocktakeCost;
    }

    public void setStocktakeCost(String stocktakeCost) {
        this.stocktakeCost = stocktakeCost;
    }

    @Nullable
    public BigDecimal getLocUsePercent() {
        return locUsePercent;
    }

    public void setLocUsePercent(@Nullable BigDecimal locUsePercent) {
        this.locUsePercent = locUsePercent;
    }

    public String getStocktakeCode() {
        return stocktakeCode;
    }

    public void setStocktakeCode(String stocktakeCode) {
        this.stocktakeCode = stocktakeCode;
    }

    public String getStocktakeName() {
        return stocktakeName;
    }

    public void setStocktakeName(String stocktakeName) {
        this.stocktakeName = stocktakeName;
    }

    public BigDecimal getLocNum() {
        return locNum;
    }

    public void setLocNum(BigDecimal locNum) {
        this.locNum = locNum;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getStrategyTypeStr() {
        return strategyTypeStr;
    }

    public void setStrategyTypeStr(String strategyTypeStr) {
        this.strategyTypeStr = strategyTypeStr;
    }

    public String getStocktakeRangeStr() {
        return stocktakeRangeStr;
    }

    public void setStocktakeRangeStr(String stocktakeRangeStr) {
        this.stocktakeRangeStr = stocktakeRangeStr;
    }

    public BigDecimal getUsedLocNum() {
        return usedLocNum;
    }

    public void setUsedLocNum(BigDecimal usedLocNum) {
        this.usedLocNum = usedLocNum;
    }
}
