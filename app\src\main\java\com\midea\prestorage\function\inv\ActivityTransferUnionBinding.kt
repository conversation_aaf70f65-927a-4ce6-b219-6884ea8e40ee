package com.midea.prestorage.function.inv

import android.widget.RelativeLayout
import android.widget.TextView
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.midea.prestoragesaas.databinding.ActivityTransferBinding
import com.midea.prestoragesaas.databinding.ActivityTransferCareBinding

sealed class ActivityTransferUnionBinding{
    abstract var vm: TransferVM?
    abstract val llTitleBar: RelativeLayout
    abstract val viewPager: ViewPager2
    abstract val tabLayout: TabLayout
    abstract val tvNotification: TextView

    class V2(val binding: ActivityTransferCareBinding) : ActivityTransferUnionBinding() {
        override var vm: TransferVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val viewPager = binding.viewPager
        override val tabLayout = binding.tabLayout
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityTransferBinding) : ActivityTransferUnionBinding() {
        override var vm: TransferVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val viewPager = binding.viewPager
        override val tabLayout = binding.tabLayout
        override val tvNotification = binding.tvNotification
    }
}
