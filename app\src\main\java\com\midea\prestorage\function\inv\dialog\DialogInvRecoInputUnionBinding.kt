package com.midea.prestorage.function.inv.dialog

import android.widget.EditText
import android.widget.GridView
import com.midea.prestoragesaas.databinding.DialogInvRecoInputBinding
import com.midea.prestoragesaas.databinding.DialogInvRecoInputCareBinding

sealed class DialogInvRecoInputUnionBinding{
    abstract var vm: InvReconciliationInputDialogVM?
    abstract val etNum: EditText
    abstract val gridNumber: GridView

    class V2(val binding: DialogInvRecoInputCareBinding) : DialogInvRecoInputUnionBinding() {
        override var vm: InvReconciliationInputDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val etNum = binding.etNum
        override val gridNumber = binding.gridNumber
    }

    class V1(val binding: DialogInvRecoInputBinding) : DialogInvRecoInputUnionBinding() {
        override var vm: InvReconciliationInputDialogVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val etNum = binding.etNum
        override val gridNumber = binding.gridNumber
    }
}
