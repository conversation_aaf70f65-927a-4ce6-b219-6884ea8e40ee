package com.midea.prestorage.function.outstorage

import android.text.TextUtils
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.OutStorageDeleteQuery
import com.midea.prestorage.beans.net.OutStorageDeleteQueryBean
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.ToastUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import com.xuexiang.xqrcode.XQRCode
import okhttp3.MediaType
import okhttp3.RequestBody


class OutStorageDeleteVM(val activity: OutStorageDeleteActivity) {

    val isRefreshing = ObservableBoolean(false)
    val isNoData = ObservableBoolean(false)
    val title = ObservableField("")
    val orderNo = ObservableField("")
    var pageNo = 1
    var queryType = 1

    fun init() {
        val orderNoStr = activity.intent.getStringExtra("orderNo")
        queryType = activity.intent.getIntExtra("queryType", 1)
        title.set(orderNoStr)
        onRefreshCommand.onRefresh()
    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        pageNo = 1
        isRefreshing.set(true)
        serialPage(title.get())
    }

    val deleteEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (!TextUtils.isEmpty(orderNo.get())) {
                queryByBarCode(orderNo.get()!!)
            }
        }
    }

    fun itemClick(bean: OutStorageDeleteQuery.OutStorageDeleteList) {
        if (bean.serialType == "1") {
            showClickDialog(bean)
        }
    }

    private fun showClickDialog(bean: OutStorageDeleteQuery.OutStorageDeleteList) {
        val data = OutStorageDeleteQueryBean()
        data.id = bean.id
        data.serialNo = bean.serialNo
        data.shipmentCode = bean.shipmentCode
        data.serialType = bean.serialType
        data.custItemCode = bean.custItemCode
        data.itemName = bean.itemName
        data.setQty(bean.qtyBig)
        activity.showDeleteTipDialog(data)
    }

    fun queryByBarCode(orderNo: String) {
        activity.waitingDialogHelp.showDialog()

        val param = mutableMapOf(
            "whCode" to activity.getWhCode(),
            "serialNo" to orderNo.trim()
        )

        if (queryType == 1) {
            param["shipmentCode"] = title.get().toString()
        } else {
            param["waveNo"] = title.get().toString()
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .queryByBarCode(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<OutStorageDeleteQueryBean>(activity) {
                override fun success(data: OutStorageDeleteQueryBean?) {
                    activity.waitingDialogHelp.hidenDialog()
                    if (data != null) {
                        activity.showDeleteTipDialog(data)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun serialPage(orderNoStr: String?) {
        var referenceNo: String? = null
        var shipmentCode: String? = null
        if (queryType == 1) {
            shipmentCode = orderNoStr
        } else {
            referenceNo = orderNoStr
        }
        RetrofitHelper.getOutStorageAPI()
            .serialPage(referenceNo, shipmentCode, pageNo)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<OutStorageDeleteQuery>(activity) {
                override fun success(data: OutStorageDeleteQuery?) {
                    activity.waitingDialogHelp.hidenDialog()
                    isRefreshing.set(false)
                    activity.stopLoad()
                    if (data != null) {
                        if (pageNo == 1) {
                            activity.showData(data.list)
                        } else {
                            activity.addNewData(data.list)
                        }
                        pageNo = data.pageNo + 1
                        if (data.pageSize > data.list.size) {
                            //如果不够一页,显示没有更多数据布局
                            activity.endLoad()
                        }
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isRefreshing.set(false)
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun back() {
        activity.finish()
    }

    fun startScan() {
        XQRCode.startScan(activity, BaseActivity.QR_CODE_BACK)
    }

    fun scanResult(result: String?) {
        orderNo.set(result)
        deleteEnterKeyPress.onEnterKey()
    }

    fun loadMore() {
        serialPage(title.get())
    }
}