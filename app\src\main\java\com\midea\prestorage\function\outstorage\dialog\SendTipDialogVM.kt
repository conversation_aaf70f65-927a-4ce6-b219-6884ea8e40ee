package com.midea.prestorage.function.outstorage.dialog

import androidx.databinding.ObservableField

class SendTipDialogVM(val dialog: SendTipDialog) {
    val title = ObservableField<String>("提示")
    val tipInfo = ObservableField<String>("")

    fun close() {
        dialog.dismiss()
    }

    fun confirm() {
        if (CheckUtil.isFastDoubleClick()) {
            dialog.backConfirm()
        }
    }


}