package com.midea.prestorage.beans.base;

import androidx.annotation.Nullable;

import com.midea.prestorage.base.annotation.ShowAnnotation;


/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2020/6/24$
 */
public class BaseItemShowInfo extends BaseItemForPopup {

    @ShowAnnotation
    private String showInfo;

    @Nullable
    private Object payload;

    public BaseItemShowInfo() {
    }

    public BaseItemShowInfo(String showInfo) {
        this.showInfo = showInfo;
    }

    public void setShowInfo(String showInfo) {
        this.showInfo = showInfo;
    }

    public String getShowInfo() {
        return showInfo;
    }

    @Nullable
    public Object getPayload() {
        return payload;
    }

    public void setPayload(@Nullable Object payload) {
        this.payload = payload;
    }
}
