package com.midea.prestorage.beans.net;

public class InReceiptDetailInfoVO {

    private String id;

    private String totalQty;

    private String custItemCode;

    private String receiptCode;

    private String waveNo;

    private String unit;

    private String status;

    private String itemCode;

    private String lotAtt04;

    private String itemName;

    private String custOrderNo;

    private String ownerCode;

    private String itemSuiteCode;

    private Integer serialQty = 0;

    private Boolean updateFlag;

    private boolean isUnScanMark = false;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(String totalQty) {
        this.totalQty = totalQty;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getReceiptCode() {
        return receiptCode;
    }

    public void setReceiptCode(String receiptCode) {
        this.receiptCode = receiptCode;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getCustOrderNo() {
        return custOrderNo;
    }

    public void setCustOrderNo(String custOrderNo) {
        this.custOrderNo = custOrderNo;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getItemSuiteCode() {
        return itemSuiteCode;
    }

    public void setItemSuiteCode(String itemSuiteCode) {
        this.itemSuiteCode = itemSuiteCode;
    }

    public Integer getSerialQty() {
        return serialQty;
    }

    public void setSerialQty(Integer serialQty) {
        this.serialQty = serialQty;
    }

    public Boolean getUpdateFlag() {
        return updateFlag;
    }

    public void setUpdateFlag(Boolean updateFlag) {
        this.updateFlag = updateFlag;
    }

    public boolean isUnScanMark() {
        return isUnScanMark;
    }

    public void setUnScanMark(boolean unScanMark) {
        isUnScanMark = unScanMark;
    }
}