package com.midea.prestorage.function.outpool;

import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.midea.prestorage.beans.net.InPoolStorageDetail;
import com.midea.prestorage.beans.net.OutPoolStorageDetail;


/**
 * Created by LUCY6 on 2017-5-23.
 */

public class OutPoolStorageDetailHelp implements MultiItemEntity {

    public OutPoolStorageDetail.OutShipmentHeader title;
    public OutPoolStorageDetail.OutShipmentDetail child;

    public OutPoolStorageDetailHelp(OutPoolStorageDetail.OutShipmentHeader title) {
        this.title = title;
    }

    public OutPoolStorageDetailHelp(OutPoolStorageDetail.OutShipmentDetail child) {
        this.child = child;
    }

    @Override
    public int getItemType() {
        if (title != null) {
            return 0;
        } else {
            return 1;
        }
    }

    public void setTitle(OutPoolStorageDetail.OutShipmentHeader title) {
        this.title = title;
    }

    public OutPoolStorageDetail.OutShipmentHeader getTitle() {
        return title;
    }

    public void setChild(OutPoolStorageDetail.OutShipmentDetail child) {
        this.child = child;
    }

    public OutPoolStorageDetail.OutShipmentDetail getChild() {
        return child;
    }
}
