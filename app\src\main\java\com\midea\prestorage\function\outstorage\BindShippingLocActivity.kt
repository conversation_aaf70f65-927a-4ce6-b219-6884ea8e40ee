package com.midea.prestorage.function.outstorage

import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.daimajia.swipe.SimpleSwipeListener
import com.daimajia.swipe.SwipeLayout
import com.gyf.immersionbar.ktx.immersionBar
import com.midea.prestorage.base.BaseViewModelActivity
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickPositionAdapter
import com.midea.prestorage.beans.net.OutStorageScan
import com.midea.prestorage.beans.net.PrintBean
import com.midea.prestorage.beans.net.RespPickContainerInfo
import com.midea.prestorage.function.inv.InfoCollectionActivity
import com.midea.prestorage.function.inv.InventorySearchActivity
import com.midea.prestorage.function.inv.dialog.PrintNumDialog
import com.midea.prestorage.function.inv.response.ItemRfVO
import com.midea.prestorage.function.inv.response.RespMaterial
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.outstorage.dialog.BindShippingLocDialog
import com.midea.prestorage.function.outstorage.dialog.InputDialog
import com.midea.prestorage.function.sendcheck.ActivitySerialCheckFirstUnionBinding
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.printer.BluetoothConnectBack
import com.midea.prestorage.printer.Printer
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.MySoundUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.ActivityBindShippingLocBinding
import com.midea.prestoragesaas.databinding.PopViewForSelectCustemItemCodeBinding
import com.xuexiang.xqrcode.XQRCode

class BindShippingLocActivity : BaseViewModelActivity<BindShippingLocVM>() {
    private lateinit var binding: ActivityBindShippingLocUnionBinding
    private lateinit var adapter: InStorageOrderAdapter

    //69码或sn码 查询出多个custItemCodes的时候 弹框让用户选择custItemCode
    lateinit var dlgSelectCustItemCode: AlertDialog
    private lateinit var popBindingSelectCustItemCode: PopViewForSelectCustemItemCodeBinding
    lateinit var popAdapterSelectCustItemCode: PopListAdapter
    lateinit var printNumDialog: PrintNumDialog

    override fun beforeCreate(savedInstanceState: Bundle?) {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            ActivityBindShippingLocUnionBinding.V2(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_bind_shipping_loc_care
                )
            )
        } else {
            ActivityBindShippingLocUnionBinding.V1(
                DataBindingUtil.setContentView(
                    this,
                    R.layout.activity_bind_shipping_loc
                )
            )
        }
        if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            immersionBar {
                titleBarMarginTop(binding.llTitleBar)
            }
        }
        vm = ViewModelProvider.AndroidViewModelFactory(application)
            .create(BindShippingLocVM::class.java)
        binding.vm = vm
        binding.lifecycleOwner = this

        //扫码
        vm.mScanCode.observe(this, Observer<Boolean> {
            if (it) {
                XQRCode.startScan(this, QR_CODE_BACK)
            }
        })

        //重置
        vm.mResetSearch.observe(this, Observer<Boolean> {
            if (it) {
                adapter.data.clear()
                adapter.notifyDataSetChanged()
            }
        })

        vm.mBluetoothOpen.observe(this, Observer<Boolean> {
            if (it) {
                bluetoothOpen(false)
            }
        })

        vm.showDatas.observe(this, Observer<MutableList<RespPickContainerInfo>> {
            showData(it)
        })

        vm.showPopDatas.observe(this, Observer<MutableList<ItemRfVO>> { data ->
            dlgSelectCustItemCode.show()
            popAdapterSelectCustItemCode.data.clear()
            data.forEach {
                popAdapterSelectCustItemCode.addData(it)
            }
            popAdapterSelectCustItemCode.notifyDataSetChanged()
        })

        vm.bindLocLiveEvent.observe(this, Observer {
            showDialog()
        })

        initSpinner()
        initRecycleView()
        initPopWinSelectCustItemCode()
        initDialog()
//        if (Constants.isShowGoodsSearch) {
//            bluetoothOpen(true)
//        }

        AppUtils.requestFocus(binding.etCode)
    }

    private val blueBack = object : BluetoothConnectBack {
        override fun success() {
            waitingDialogHelp.hidenDialog()
            vm.isPrintOk.set(true)
        }

        override fun fail() {
            vm.isPrintOk.set(false)
            AppUtils.showToast(this@BindShippingLocActivity, "打印机连接失败，请重启打印机!")
        }

        override fun connect() {
            bluetoothOpen()
        }
    }

    fun bluetoothOpen(isAuto: Boolean = true) {
        if (!Printer.isPrintOk()) {
            Printer.openBluetooth(this, blueBack, isAuto)
        }
    }

    private fun initDialog() {

        //打印条码dialog
        printNumDialog = PrintNumDialog(this)
        printNumDialog.setTitle("请输入打印数量")
        printNumDialog.setPrintBack(object : PrintNumDialog.PrintBarcodeBack {
            override fun printBarcodeBack(printBean: PrintBean) {
                printNumDialog.dismiss()
                Printer.printBarcode(printBean)
            }
        })

        printNumDialog.setOnDismissListener {
            adapter.notifyItemChanged(binding.vm?.currentItemFlag?.get()!!)
        }

    }

    fun initSpinner() {
        val beans = mutableListOf<String>(
            "货品编码",
            "库位编码"
        )
        binding.spinnerStatus.setItems(beans)
        binding.spinnerStatus.selectedIndex = 0

        binding.spinnerStatus.setOnItemSelectedListener { _, position, _, _ ->
            if (position == 0) {
                vm.searchMode = 0
                binding.etCode.hint = "输入客户货品编码或69码"
            } else {
                vm.searchMode = 1
                binding.etCode.hint = "输入库位编码"
            }
            vm.searchType.set(beans[position])
            vm.curCode.set("")
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == QR_CODE_BACK && resultCode == RESULT_OK) {
            //处理扫描结果
            binding.vm!!.scanResult(data?.extras?.getString(XQRCode.RESULT_DATA))
        }
    }

    fun initRecycleView() {
        adapter = InStorageOrderAdapter(vm)
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter
    }

    // 初始化选择客户商品编码的弹窗
    private fun initPopWinSelectCustItemCode() {

        val popViewSelectCustItemCode =
            LayoutInflater.from(this).inflate(R.layout.pop_view_for_select_custem_item_code, null)
        popBindingSelectCustItemCode = DataBindingUtil.bind(popViewSelectCustItemCode)!!

        val alertDialogBuilder = AlertDialog.Builder(this)
        alertDialogBuilder.setView(popViewSelectCustItemCode)
        dlgSelectCustItemCode = alertDialogBuilder.create()

        popAdapterSelectCustItemCode = PopListAdapter()
        popBindingSelectCustItemCode.recyclerViewInPop.layoutManager = LinearLayoutManager(this)
        popBindingSelectCustItemCode.recyclerViewInPop.adapter = popAdapterSelectCustItemCode

        popAdapterSelectCustItemCode.setOnItemClickListener { adapter, view, position ->
            val item = adapter.getItem(position) as ItemRfVO
            binding.etCode.setText(item.custItemCode)
            vm.curSearchCode = item.custItemCode.trim()
            dlgSelectCustItemCode.dismiss()
            binding.vm?.startSearch()
        }

        popBindingSelectCustItemCode.closePop.setOnClickListener {
            dlgSelectCustItemCode.dismiss()
        }

    }

    fun showData(data: MutableList<RespPickContainerInfo>?) {
        adapter.setNewInstance(data)
        adapter.notifyDataSetChanged()

        vm.isNoData.value = adapter.data.isNullOrEmpty()
        vm.showBottom.value = adapter.data.isNotEmpty()
        if (adapter.data.isNullOrEmpty()) {
            binding.etCode.selectAll()
        } else {
            vm.curCode.set("")
        }

//        if (data?.getOrNull(0)?.shippingLoc.isNullOrEmpty()) {
//            binding.tvBind.setBackgroundResource(R.drawable.bg_bt_blue)
//            binding.tvBind.isClickable = true
//        }else {
//            binding.tvBind.setBackgroundResource(R.drawable.bg_bt_gray_dark)
//            binding.tvBind.isClickable = false
//        }
    }

    fun showDialog() {
        var bindDialog = BindShippingLocDialog(this)
        bindDialog.deleteInfo = adapter.data.getOrNull(0)
        bindDialog.inputBack = object : BindShippingLocDialog.InputBack {
            override fun inputOk() {
                bindDialog.dismiss()
                binding.vm?.startSearch()
            }

            override fun inputFail() {

            }
        }
        bindDialog.show()
    }

    override fun onResume() {
        super.onResume()
        if (!vm.isFirst && adapter.data.size > 0) {
            vm.startSearch()
        }
        vm.isFirst = false
    }

    override fun onDestroy() {
        super.onDestroy()
        Printer.closeBluetooth()
    }

    class InStorageOrderAdapter(private val vm: BindShippingLocVM?) :
        ListChoiceClickPositionAdapter<RespPickContainerInfo>(
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_bind_shipping_loc_care else R.layout.item_bind_shipping_loc
        ) {

        override fun convert(helper: BaseViewHolder, item: RespPickContainerInfo) {
            super.convert(helper, item)

            if (item.shippingLoc.isNullOrEmpty()) {
                helper.setText(R.id.tv_shippingLoc, "无集货位")
            }

        }

    }

    class PopListAdapter :
        CommonAdapter<ItemRfVO>(R.layout.item_pop_view_for_select_cust_item_code_pop) {
        override fun convert(holder: BaseViewHolder?, item: ItemRfVO) {
            super.convert(holder, item)

            holder?.setGone(R.id.tv_item_name, item.itemName.isNullOrEmpty())
        }
    }
}