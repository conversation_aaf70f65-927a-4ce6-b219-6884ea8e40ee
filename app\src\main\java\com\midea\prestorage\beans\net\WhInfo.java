package com.midea.prestorage.beans.net;

import com.midea.prestorage.beans.base.BaseItemShowInfo;

import java.util.List;


public class WhInfo extends BaseItemShowInfo {

    private List<WhInfoList> list;
    private int totalCount;
    private int totalPage;
    private int pageNo;
    private int pageSize;
    private int offset;

    public List<WhInfoList> getList() {
        return list;
    }

    public void setList(List<WhInfoList> list) {
        this.list = list;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }
}
