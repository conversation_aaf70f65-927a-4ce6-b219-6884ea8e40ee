package com.midea.prestorage.function.containerpick.provider

import android.widget.CheckBox
import android.widget.CompoundButton
import com.chad.library.adapter.base.entity.node.BaseNode
import com.chad.library.adapter.base.provider.BaseNodeProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestorage.function.containerpick.adapter.BulkToBePackedAdapter
import com.midea.prestorage.function.containerpick.fragment.BulkToBePackedVM
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R

class BulkPickHeaderProvider(private val vm: BulkToBePackedVM) : BaseNodeProvider() {
    override val itemViewType: Int
        get() = BulkToBePackedAdapter.TYPE_HEADER

    override val layoutId: Int
        get() = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_to_be_bulk_pick_header_care else R.layout.item_to_be_bulk_pick_header

    private val checkBoxMap =
        mutableMapOf<BulkPickToBeWrap, CompoundButton.OnCheckedChangeListener>()

    override fun convert(helper: BaseViewHolder, item: BaseNode) {
        val wrap = item as BulkPickToBeWrap
        val task = wrap.respBulkPacking
        helper.setText(R.id.tv_ship_to_customer_name, task.shipToCustomerName)
        helper.setText(R.id.tv_cust_order_no, task.custOrderNo)
        helper.setGone(R.id.tv_cust_order_no_num, true)
        helper.setText(R.id.tv_index, "${task.index}")
//        helper.setGone(R.id.tv_cust_order_no_num, task.custOrderNoNum.isNullOrEmpty())
//        helper.setText(R.id.tv_cust_order_no_num, task.custOrderNoNum)

        val checkBox = helper.getView<CheckBox>(R.id.cb_select)
        checkBox.setOnCheckedChangeListener(null)
        checkBox.isChecked = wrap.selected
        val checkedChangeListener = if (!checkBoxMap.containsKey(wrap)) {
            CompoundButton.OnCheckedChangeListener { _, isChecked ->
                wrap.selected = isChecked
                vm.onParentCheckedChang(wrap)
            }.also {
                checkBoxMap[wrap] = it
            }
        } else {
            checkBoxMap[wrap]
        }
        checkBox.setOnCheckedChangeListener(checkedChangeListener)
    }

}