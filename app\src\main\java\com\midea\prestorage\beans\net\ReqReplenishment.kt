package com.midea.prestorage.beans.net

import com.midea.prestorage.http.constants.Constants
import java.math.BigDecimal

data class ReqReplenishment(
    val whCode: String = Constants.whInfo?.whCode ?: "",
    val confirmedBy: String = Constants.userInfo?.name ?: "",
    val details: List<DetailsItems>?
)

data class DetailsItems(
    val taskDetailId: String?,
    val qty: BigDecimal?,
    val locationCode: String
)
