package com.midea.prestorage.function.inv.dialog

import android.app.AlertDialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestorage.beans.net.PrintBean
import com.midea.prestorage.dialog.DialogTipUnionBinding
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.SPUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogPrintNumBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity

class PrintNumDialog(var mContext: RxAppCompatActivity) : AlertDialog(mContext) {
    var binding: DialogPrintNumUnionBinding
    private var printBack: PrintBarcodeBack? = null

    init {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window?.setGravity(Gravity.CENTER)
            window?.attributes?.run {
                gravity = Gravity.CENTER
            }
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_print_num_care, null)
            setView(contentView)
            DialogPrintNumUnionBinding.V2(DataBindingUtil.bind(contentView)!!)
        } else {
            val contentView =
                LayoutInflater.from(mContext).inflate(R.layout.dialog_print_num, null)
            setView(contentView)
            DialogPrintNumUnionBinding.V1(DataBindingUtil.bind(contentView)!!)
        }
        binding.vm = PrintNumDialogVM(this)

        setCanceledOnTouchOutside(false)
    }

    fun setTitle(title: String) {
        if (!TextUtils.isEmpty(title)) {
            binding!!.vm!!.title.set(title)
        }
    }

    fun setPrintBack(backImpl: PrintBarcodeBack) {
        printBack = backImpl
    }

    fun setData(data: PrintBean) {
        binding!!.vm!!.etInfo.set(data.printCount.toString()) //默认为：计划数 / CS包装系数 向上取整
        binding!!.vm!!.bean = data
    }

    fun backDeleteBarcode(num: String) {
        if (printBack != null) {
            binding!!.vm!!.bean?.printCount = num.toIntOrNull() ?: 1
            printBack!!.printBarcodeBack(binding!!.vm!!.bean!!)
            binding!!.vm!!.etInfo.set("")
        }
    }

    interface PrintBarcodeBack {
        fun printBarcodeBack(printBean: PrintBean)
    }
}