package com.midea.prestorage.function.outpool

import android.text.TextUtils
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R


class OutPoolDetailAdapter(data: MutableList<OutPoolStorageDetailHelp>?, vm : OutPoolStorageDetailVM ?) :
    BaseMultiItemQuickAdapter<OutPoolStorageDetailHelp, BaseViewHolder>(data) {

    lateinit var outPoolStorageDetailVM : OutPoolStorageDetailVM

    init {
        addItemType(0, R.layout.item_order_out_pool_detail_title)
        addItemType(1, R.layout.item_order_out_pool_detail_child)

        if (vm != null) {
            outPoolStorageDetailVM = vm
        }
    }

    override fun convert(helper: BaseViewHolder, item: OutPoolStorageDetailHelp) {
        if (item.itemType == 0) {
            val title = item.title
            helper.setText(R.id.tv_tk_order, title.shipmentCode)
            helper.setText(R.id.tv_customer, title.customerName)
            helper.setText(R.id.tv_car_no, title.custOrderCode)
            helper.setText(R.id.tv_status, title.statusStr)
            helper.setText(R.id.tv_engineer, "${title.netEngineerName} ${title.netEngineerMobile}")
            helper.setGone(R.id.ll_engineer, TextUtils.isEmpty(title.netEngineerName))

            helper.setText(R.id.tv_order_num, title.totalQty.toString())
            helper.setText(R.id.tv_address, title.shipToAddress)


            // 取消申请不扫码的按钮 ：已申请不扫码 并且还没被审核的单 才显示 取消申请不扫码按钮
            if (!title.unscanMark.isNullOrBlank() && title.unscanMark == "00") {
                // 显示 取消申请不扫码 按钮 并监听事件
                helper.getView<LinearLayout>(R.id.isShowBtnCancelUnscan).visibility = View.VISIBLE
                helper.getView<Button>(R.id.btnCancelUnScan).setOnClickListener {
                    outPoolStorageDetailVM.onSubmitCancelUnScan(item.title.shipmentCode)
                }
            } else {
                helper.getView<LinearLayout>(R.id.isShowBtnCancelUnscan).visibility = View.GONE
            }

            // unscanMark 不为空 的 都显示印章  (申请中，审核通过)
            if (!title.unscanMark.isNullOrBlank()) {
                // 显示 “已申请不扫码” 红色印章
                helper.getView<LinearLayout>(R.id.redMarkUnScan).visibility = View.VISIBLE

                if (title.unscanMark == "00") {
                    helper.getView<TextView>(R.id.tvUnscanText).text = "不扫码申请中"
                } else if (title.unscanMark == "01") {
                    helper.getView<TextView>(R.id.tvUnscanText).text = "不扫码已审核"
                }
            } else {
                helper.getView<LinearLayout>(R.id.redMarkUnScan).visibility = View.INVISIBLE
            }


        } else if (item.itemType == 1) {
            val child = item.child
            helper.setText(R.id.tv_num, child.planQtyInt.toString())
            helper.setText(R.id.tv_unit, child.unit)
            helper.setText(R.id.tv_goods_code, child.custItemCode)
            helper.setText(R.id.tv_status, child.statusStr)
            helper.setText(R.id.tv_goods, child.itemName)
        }
    }
}