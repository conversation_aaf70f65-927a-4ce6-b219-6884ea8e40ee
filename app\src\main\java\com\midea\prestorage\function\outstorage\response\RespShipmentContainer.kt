package com.midea.prestorage.function.outstorage.response

import com.midea.prestorage.base.annotation.ShowAnnotation
import com.midea.prestorage.beans.base.BaseItemForPopup
import java.math.BigDecimal

data class RespShipmentContainer(
    var shipContainerId: String? = null,
    @ShowAnnotation
    var shipContainerCode: String? = null,
    var custOrderNo: String? = null,
    var custName: String? = null,
    var contacts: String? = null,
    var phoneNum: String? = null,
    var shipToAddress: String? = null,
    var whCode: String? = null,
    var itemCode: String? = null,
    var itemName: String? = null,
    var qty: BigDecimal? = null,
    @ShowAnnotation
    var totalQty: BigDecimal? = null,
    var totalWeight: BigDecimal? = null,
    var totalVolumn: BigDecimal? = null,
    var closeTime: String? = null,
    var seqNum: String? = null,
    var unit: String? = null,
    @ShowAnnotation
    var shipmentCode: String? = null,
    var id: Long? = null,
    @ShowAnnotation
    var waveNo: String? = null,
    var custItemCode: String? = null,
    var itemCount: BigDecimal? = null,
    @ShowAnnotation
    var waybillNo: String? = null,
    @ShowAnnotation
    var packageType: String? = null,
    @ShowAnnotation
    var closeContainerTime: String? = null,
    @ShowAnnotation
    var oqcByName: String? = null
): BaseItemForPopup()
