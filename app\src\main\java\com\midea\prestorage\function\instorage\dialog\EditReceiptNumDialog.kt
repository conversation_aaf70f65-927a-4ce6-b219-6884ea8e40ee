package com.midea.prestorage.function.outstorage.dialog

import android.app.AlertDialog
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.net.InReceiptSerialScanDto
import com.midea.prestorage.beans.net.OutStorageDeleteQueryBean
import com.midea.prestorage.beans.net.SerialScanDto
import com.midea.prestoragesaas.databinding.DialogInStorageDeleteSerialBinding
import com.midea.prestoragesaas.databinding.DialogInStorageEditReceiptNumBinding
import com.midea.prestoragesaas.databinding.DialogOutStorageDeleteTipBinding
import com.midea.prestorage.function.instorage.InStorageDeleteActivity
import com.midea.prestorage.function.instorage.InStorageScanActivity

/**
 * Description:
 * Author:         wangqin
 * CreateDate:     2021/6/26$
 */
class EditReceiptNumDialog(var activity: InStorageScanActivity) : AlertDialog(activity) {

    var binding: DialogInStorageEditReceiptNumBinding


    init {
        val contentView = LayoutInflater.from(activity).inflate(R.layout.dialog_in_storage_edit_receipt_num, null)
        setView(contentView)
        binding = DataBindingUtil.bind(contentView)!!
        binding!!.vm = EditReceiptNumDialogVM(this)

        setCanceledOnTouchOutside(true)
    }


    fun showDlg(strDefaultReceiptNum: String, inReceiptSerialScanDto: InReceiptSerialScanDto) {
        super.show()
        binding!!.vm!!.show(strDefaultReceiptNum, inReceiptSerialScanDto)
    }
}
