package com.midea.prestorage.function.inpool;

import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.midea.prestorage.beans.net.InPoolStorageDetail;

import java.io.Serializable;
import java.util.List;


/**
 * Created by LUCY6 on 2017-5-23.
 */

public class InPoolStorageDetailHelp implements MultiItemEntity {

    public InPoolStorageDetail.OrderInfoList title;
    public InPoolStorageDetail.OrderInfoList.OrderInfoDetailsList child;

    public InPoolStorageDetailHelp(InPoolStorageDetail.OrderInfoList title) {
        this.title = title;
    }

    public InPoolStorageDetailHelp(InPoolStorageDetail.OrderInfoList.OrderInfoDetailsList child) {
        this.child = child;
    }

    @Override
    public int getItemType() {
        if (title != null) {
            return 0;
        } else {
            return 1;
        }
    }

    public void setTitle(InPoolStorageDetail.OrderInfoList title) {
        this.title = title;
    }

    public InPoolStorageDetail.OrderInfoList getTitle() {
        return title;
    }

    public void setChild(InPoolStorageDetail.OrderInfoList.OrderInfoDetailsList child) {
        this.child = child;
    }

    public InPoolStorageDetail.OrderInfoList.OrderInfoDetailsList getChild() {
        return child;
    }
}
