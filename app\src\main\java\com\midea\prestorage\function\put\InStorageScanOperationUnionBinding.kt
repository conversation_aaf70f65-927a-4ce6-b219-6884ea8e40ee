package com.midea.prestorage.function.put

import android.widget.*
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestorage.widgets.TrailingZerosEditText
import com.midea.prestoragesaas.databinding.ActivityInStorageScanOperationBinding
import com.midea.prestoragesaas.databinding.ActivityInStorageScanOperationCareBinding
import com.midea.prestoragesaas.databinding.ItemPackageRelationQtyInputCareBinding

sealed class InStorageScanOperationUnionBinding {
    abstract var vm: InStorageScanPutOperationVM?
    abstract val edQty: TrailingZerosEditText
    abstract val itemQtyInput1: ItemQtyInputUnionBinding
    abstract val itemQtyInput2: ItemQtyInputUnionBinding
    abstract val itemQtyInput3: ItemQtyInputUnionBinding
    abstract val itemQtyInput4: ItemQtyInputUnionBinding
    abstract val rgUnit: RadioGroup
    abstract val rv: RecyclerView
    abstract val etScan: EditText
    abstract val edGoods: EditText
    abstract val rbCs: RadioButton
    abstract val rbEa: RadioButton
    abstract val constraintLayout: LinearLayout
    abstract val tvNumTitle: TextView
    abstract val tvNotification: TextView
    abstract var lifecycleOwner: LifecycleOwner?

    class V2(val binding: ActivityInStorageScanOperationCareBinding) : InStorageScanOperationUnionBinding() {
        override var vm: InStorageScanPutOperationVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val edQty = binding.edQty
        override val itemQtyInput1 = ItemQtyInputUnionBinding.V2(binding.itemQtyInput1)
        override val itemQtyInput2 = ItemQtyInputUnionBinding.V2(binding.itemQtyInput2)
        override val itemQtyInput3 = ItemQtyInputUnionBinding.V2(binding.itemQtyInput3)
        override val itemQtyInput4 = ItemQtyInputUnionBinding.V2(binding.itemQtyInput4)
        override val rgUnit = binding.rgUnit
        override val rv = binding.rv
        override val etScan = binding.etScan
        override val edGoods = binding.edGoods
        override val rbCs = binding.rbCs
        override val rbEa = binding.rbEa
        override val constraintLayout = binding.constraintLayout
        override val tvNumTitle = binding.tvNumTitle
        override val tvNotification = binding.tvNotification
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }

    class V1(val binding: ActivityInStorageScanOperationBinding) : InStorageScanOperationUnionBinding() {
        override var vm: InStorageScanPutOperationVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val edQty = binding.edQty
        override val itemQtyInput1 = ItemQtyInputUnionBinding.V1(binding.itemQtyInput1)
        override val itemQtyInput2 = ItemQtyInputUnionBinding.V1(binding.itemQtyInput2)
        override val itemQtyInput3 = ItemQtyInputUnionBinding.V1(binding.itemQtyInput3)
        override val itemQtyInput4 = ItemQtyInputUnionBinding.V1(binding.itemQtyInput4)
        override val rgUnit = binding.rgUnit
        override val rv = binding.rv
        override val etScan = binding.etScan
        override val edGoods = binding.edGoods
        override val rbCs = binding.rbCs
        override val rbEa = binding.rbEa
        override val constraintLayout = binding.constraintLayout
        override val tvNumTitle = binding.tvNumTitle
        override val tvNotification = binding.tvNotification
        override var lifecycleOwner: LifecycleOwner?
            get() = binding.lifecycleOwner
            set(value) {
                binding.lifecycleOwner = value
            }
    }
}
