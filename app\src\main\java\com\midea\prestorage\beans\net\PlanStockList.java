package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;

public class PlanStockList implements Serializable {

    @ShowAnnotation
    private String stocktakeCode;
    @ShowAnnotation
    private String areaAndLocCode;
    @ShowAnnotation
    private String taskCode;
    @ShowAnnotation
    private String taskPlanEndTime;
    @ShowAnnotation
    private String strategyType;
    @ShowAnnotation
    private String stocktakeRange;
    @ShowAnnotation
    private String areaCode;
    @ShowAnnotation
    private String status;
    @ShowAnnotation
    private String confirmedBy;
    @ShowAnnotation
    private String taskStartTime;

    private String locCode;
    private String zoneCode;

    public String getTaskStartTime() {
        return taskStartTime;
    }

    public void setTaskStartTime(String taskStartTime) {
        this.taskStartTime = taskStartTime;
    }

    public String getConfirmedBy() {
        return confirmedBy;
    }

    public void setConfirmedBy(String confirmedBy) {
        this.confirmedBy = confirmedBy;
    }

    public String getStocktakeCode() {
        return stocktakeCode;
    }

    public void setStocktakeCode(String stocktakeCode) {
        this.stocktakeCode = stocktakeCode;
    }

    public String getLocCode() {
        return locCode;
    }

    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTaskPlanEndTime() {
        return taskPlanEndTime;
    }

    public void setTaskPlanEndTime(String taskPlanEndTime) {
        this.taskPlanEndTime = taskPlanEndTime;
    }

    public String getStocktakeRange() {
        return stocktakeRange;
    }

    public void setStocktakeRange(String stocktakeRange) {
        this.stocktakeRange = stocktakeRange;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getStrategyType() {
        return strategyType;
    }

    public void setStrategyType(String strategyType) {
        this.strategyType = strategyType;
    }

    public String getAreaAndLocCode() {
        return zoneCode + "/" + locCode;
    }

    public void setAreaAndLocCode(String areaAndLocCode) {
        this.areaAndLocCode = areaAndLocCode;
    }

    public String getZoneCode() {
        return zoneCode;
    }

    public void setZoneCode(String zoneCode) {
        this.zoneCode = zoneCode;
    }
}
