package com.midea.prestorage.function.instorage.response;

import com.midea.prestorage.beans.base.BaseItemForPopup;

import java.util.List;

//收货条码记录
public class InReceiptSerial  {

    private String id;

    /**
     * 小箱条码
     */
    private String serialNo;

    /**
     * 大箱条码
     */
    private String serialCs;

    /**
     * 容器号
     */
    private String containerCode;

    /**
     * 货主
     */
    private String ownerCode;

    /**
     * 客户订单号
     */
    private String custOrderNo;

    /**
     * 入库单号
     */
    private String receiptCode;

    /**
     * 入库明细行id
     */
    private String receiptDetailId;

    /**
     * 客户货品编码
     */
    private String custItemCode;

    /**
     * 安得货品编码
     */
    private String itemCode;

    /**
     * 货品描述
     */
    private String itemName;

    /**
     * 条码类型0 实物条码 1 虚拟条码，默认实物条码0
     */
    private String serialType;

    /**
     * 数量
     */
    private Double qty;

    /**
     * 单位
     */
    private String unit;

    /**
     * 库位
     */
    private String toLoc;

    /**
     * 批次属性号
     */
    private String lotNum;

    /**
     * 仓库
     */
    private String whCode;

    /**
     * 关联单号
     */
    private String referenceNo;

    /**
     * 收货渠道
     */
    private String receiptChannel;

    /**
     * 收货状态 开始：100；结束：900
     */
    private String status;

    /**
     * 扫码值
     */
    private String barcode;

    /**
     * 批次属性01（生产日期）
     */
    private String lotAtt01;

    /**
     * 批次属性02（失效日期）
     */
    private String lotAtt02;

    /**
     * 批次属性03
     */
    private String lotAtt03;

    /**
     * 批次属性4(品质状态)
     */
    private String lotAtt04;

    /**
     * 批次属性05
     */
    private String lotAtt05;

    /**
     * 批次属性06
     */
    private String lotAtt06;

    /**
     * 批次属性07
     */
    private String lotAtt07;

    /**
     * 批次属性08
     */
    private String lotAtt08;

    /**
     * 批次属性09
     */
    private String lotAtt09;

    /**
     * 批次属性10
     */
    private String lotAtt10;

    /**
     * 批次属性11
     */
    private String lotAtt11;

    /**
     * 批次属性12
     */
    private String lotAtt12;

    /**
     * 收货箱ID
     */
    private String receiptContainerId;


    private List<String> receiptCodeList;

    /**
     * 套件编码
     */
    private String itemSuiteCode;

    /**
     * 套散件比例
     */
    private Double itemSuiteQty;

    /**
     * 商品数量
     */
    private Integer itemQty;

    /**
     * 客户订单明细行
     */
    private String custOrderLineNum;

    /**
     * 批次属性拼接
     */
    private String lotAtt;

    /**
     * 库位
     */
    private String locCode;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getSerialCs() {
        return serialCs;
    }

    public void setSerialCs(String serialCs) {
        this.serialCs = serialCs;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getCustOrderNo() {
        return custOrderNo;
    }

    public void setCustOrderNo(String custOrderNo) {
        this.custOrderNo = custOrderNo;
    }

    public String getReceiptCode() {
        return receiptCode;
    }

    public void setReceiptCode(String receiptCode) {
        this.receiptCode = receiptCode;
    }

    public String getReceiptDetailId() {
        return receiptDetailId;
    }

    public void setReceiptDetailId(String receiptDetailId) {
        this.receiptDetailId = receiptDetailId;
    }

    public String getCustItemCode() {
        return custItemCode;
    }

    public void setCustItemCode(String custItemCode) {
        this.custItemCode = custItemCode;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getSerialType() {
        return serialType;
    }

    public void setSerialType(String serialType) {
        this.serialType = serialType;
    }

    public Double getQty() {
        return qty;
    }

    public void setQty(Double qty) {
        this.qty = qty;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getToLoc() {
        return toLoc;
    }

    public void setToLoc(String toLoc) {
        this.toLoc = toLoc;
    }

    public String getLotNum() {
        return lotNum;
    }

    public void setLotNum(String lotNum) {
        this.lotNum = lotNum;
    }

    public String getWhCode() {
        return whCode;
    }

    public void setWhCode(String whCode) {
        this.whCode = whCode;
    }

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public String getReceiptChannel() {
        return receiptChannel;
    }

    public void setReceiptChannel(String receiptChannel) {
        this.receiptChannel = receiptChannel;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getLotAtt01() {
        return lotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        this.lotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return lotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        this.lotAtt02 = lotAtt02;
    }

    public String getLotAtt03() {
        return lotAtt03;
    }

    public void setLotAtt03(String lotAtt03) {
        this.lotAtt03 = lotAtt03;
    }

    public String getLotAtt04() {
        return lotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        this.lotAtt04 = lotAtt04;
    }

    public String getLotAtt05() {
        return lotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        this.lotAtt05 = lotAtt05;
    }

    public String getLotAtt06() {
        return lotAtt06;
    }

    public void setLotAtt06(String lotAtt06) {
        this.lotAtt06 = lotAtt06;
    }

    public String getLotAtt07() {
        return lotAtt07;
    }

    public void setLotAtt07(String lotAtt07) {
        this.lotAtt07 = lotAtt07;
    }

    public String getLotAtt08() {
        return lotAtt08;
    }

    public void setLotAtt08(String lotAtt08) {
        this.lotAtt08 = lotAtt08;
    }

    public String getLotAtt09() {
        return lotAtt09;
    }

    public void setLotAtt09(String lotAtt09) {
        this.lotAtt09 = lotAtt09;
    }

    public String getLotAtt10() {
        return lotAtt10;
    }

    public void setLotAtt10(String lotAtt10) {
        this.lotAtt10 = lotAtt10;
    }

    public String getLotAtt11() {
        return lotAtt11;
    }

    public void setLotAtt11(String lotAtt11) {
        this.lotAtt11 = lotAtt11;
    }

    public String getLotAtt12() {
        return lotAtt12;
    }

    public void setLotAtt12(String lotAtt12) {
        this.lotAtt12 = lotAtt12;
    }

    public String getReceiptContainerId() {
        return receiptContainerId;
    }

    public void setReceiptContainerId(String receiptContainerId) {
        this.receiptContainerId = receiptContainerId;
    }

    public List<String> getReceiptCodeList() {
        return receiptCodeList;
    }

    public void setReceiptCodeList(List<String> receiptCodeList) {
        this.receiptCodeList = receiptCodeList;
    }

    public String getItemSuiteCode() {
        return itemSuiteCode;
    }

    public void setItemSuiteCode(String itemSuiteCode) {
        this.itemSuiteCode = itemSuiteCode;
    }

    public Double getItemSuiteQty() {
        return itemSuiteQty;
    }

    public void setItemSuiteQty(Double itemSuiteQty) {
        this.itemSuiteQty = itemSuiteQty;
    }

    public Integer getItemQty() {
        return itemQty;
    }

    public void setItemQty(Integer itemQty) {
        this.itemQty = itemQty;
    }

    public String getCustOrderLineNum() {
        return custOrderLineNum;
    }

    public void setCustOrderLineNum(String custOrderLineNum) {
        this.custOrderLineNum = custOrderLineNum;
    }

    public String getLotAtt() {
        return lotAtt;
    }

    public void setLotAtt(String lotAtt) {
        this.lotAtt = lotAtt;
    }

    public String getLocCode() {
        return locCode;
    }

    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }
}
