package com.midea.prestorage.dialog

import android.text.TextUtils
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.midea.prestorage.widgets.ViewBindingAdapter

class InStorageDialogVM(val dialog: InStorageDialog) {
    val title = ObservableField<String>("提示")
    val etInfo = ObservableField("")
    val isShowEdit = ObservableField(false)
    val isShowCheckBox = ObservableField(false)
    val isShowConfirm = ObservableField(false)

    fun onEnterAnyCode() {
        if (CheckUtil.isFastDoubleClick()) {
            if (etInfo.get().isNullOrBlank()) {
                return
            }
            etInfo.get()?.let { dialog.backDeleteBarcode(it) }
        }
    }

    fun close() {
        dialog.dismiss()
    }

    fun editUnable() {
        isShowEdit.set(true)
        isShowConfirm.set(false)
    }

    fun checkBoxUnable() {
        isShowCheckBox.set(true)
        isShowConfirm.set(true)
    }

    fun confirm() {
        dialog.backConfirm()
    }

}