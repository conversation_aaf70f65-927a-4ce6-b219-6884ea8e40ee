package com.midea.prestorage.dialog

import androidx.databinding.ObservableField
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.widgets.ViewBindingAdapter

class MultiChooseDialogVM(val dialog: MultiChooseDialog) {

    var allData: MutableList<BaseItemShowInfo>? = null
    val title = ObservableField<String>("提示")

    fun confirm() {
        val checkData = dialog.getCheckData()
        checkData.forEach {
            it.isSelected = it.isTempSelected
        }
        dialog.backConfirm(checkData)
    }

    fun cancel() {
        dialog.adapter.data.forEach {
            it.isTempSelected = it.isSelected
        }
        close()
    }

    fun close() {
        dialog.dismiss()
    }
}