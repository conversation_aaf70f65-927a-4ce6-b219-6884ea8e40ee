package com.midea.prestorage.function.inv.dialog

import CheckUtil
import android.text.TextUtils
import androidx.databinding.ObservableField
import com.midea.prestorage.function.inv.CountInTimeSettingVM
import com.midea.prestorage.function.inv.InvReconciliationDetailVM
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.widgets.ViewBindingAdapter
import java.math.BigDecimal

class InvReconciliationInputDialogVM(val dialog: InvReconciliationInputDialog) {

    val title = ObservableField<String>("采集数量")
    val goodsNo = ObservableField<String>("")
    val explain = ObservableField<String>("")

    fun show() {

    }

    val goodsEnterKeyPress = object : ViewBindingAdapter.OnKeyListener {

        override fun onEnterKey() {
            if (CheckUtil.isFastDoubleClick()) {
                if (!TextUtils.isEmpty(goodsNo.get())) {
                    confirm()
                }
            }
        }
    }

    fun close() {
        dialog.inputBack?.inputFail()
        dialog.dismiss()
    }

    fun confirm() {
        if (InvReconciliationDetailVM.unitInputType == InvReconciliationDetailVM.MULTIPLE_UNIT_INPUT) {
            var num = BigDecimal.ZERO
            dialog.adapterNumber?.getData().forEach {
                try {
                    if (!it.num.isNullOrEmpty() && it.qty != null && it.qty?.compareTo(
                            BigDecimal.ZERO) == 1) {
                        num += AppUtils.getBigDecimalValue(it.num).multiply(it.qty)
                    }
                }catch (e: NumberFormatException) {
                    AppUtils.showToast(dialog.mContext, "数量格式错误")
                }
            }
            if (!AppUtils.isZero(num)) {
                goodsNo.set(AppUtils.getBigDecimalValueStr(num))
            }else {
                goodsNo.set("0")
            }
        }
        if (goodsNo.get().isNullOrEmpty()) {
            AppUtils.showToast(dialog.mContext, "请输入数量!")
            return
        }
        dialog.inputBack?.inputOk(dialog.deleteInfo!!, AppUtils.getBigDecimalValue(goodsNo.get()!!))
        dialog.dismiss()
    }
}