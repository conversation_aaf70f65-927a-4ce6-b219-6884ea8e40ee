package com.midea.prestorage.function.inpool

import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestoragesaas.R
import com.midea.prestorage.beans.net.InPoolStorageDetail
import com.midea.prestorage.utils.DCUtils


class InPoolDetailAdapter(data: MutableList<InPoolStorageDetailHelp>?, vm: InPoolStorageDetailVM) :
    BaseMultiItemQuickAdapter<InPoolStorageDetailHelp, BaseViewHolder>(data) {

    var inPoolStorageDetailVM: InPoolStorageDetailVM

    init {
        addItemType(0, R.layout.item_order_in_pool_detail_title)
        addItemType(1, R.layout.item_order_in_pool_detail_child)
        inPoolStorageDetailVM = vm
    }

    override fun convert(holder: BaseViewHolder, item: InPoolStorageDetailHelp) {
        if (item.itemType == 0) {
            val title = item.title
            holder.setText(R.id.tv_tk_order, title.receiptCode)
            holder.setText(R.id.tv_customer, title.custOrderNo)
            holder.setText(R.id.tv_shipment, title.dispatchNo)
            holder.setText(R.id.tv_car_no, title.carNo)
            holder.setText(R.id.tv_pickup_time, title.pickupTime)

            if (!title.totalQty.isNullOrBlank()) {
                holder.setText(R.id.tv_num, title.totalQty.toDouble().toInt().toString())
            }


            // 取消申请不扫码的按钮 ：已申请不扫码 并且还没被审核的单 才显示 取消申请不扫码按钮
            if (!title.unscanMark.isNullOrBlank() && title.unscanMark.equals("00")) {
                // 显示 取消申请不扫码 按钮 并监听事件
                holder.getView<LinearLayout>(R.id.isShowBtnCancelUnscan).visibility = View.VISIBLE
                holder.getView<Button>(R.id.btnCancelUnScan).setOnClickListener {
                    inPoolStorageDetailVM.onSubmitCancelUnScan(item)
                }
            } else {
                holder.getView<LinearLayout>(R.id.isShowBtnCancelUnscan).visibility = View.GONE
            }

            // unscanMark 不为空 的 都显示印章  (申请中，审核通过)
            if (!title.unscanMark.isNullOrBlank()) {
                // 显示 “已申请不扫码” 红色印章
                holder.getView<LinearLayout>(R.id.redMarkUnScan).visibility = View.VISIBLE

                if (title.unscanMark.equals("00")) {
                    holder.getView<TextView>(R.id.tvUnscanText).setText("不扫码申请中")
                } else if (title.unscanMark.equals("01")) {
                    holder.getView<TextView>(R.id.tvUnscanText).setText("不扫码已审核")
                }
            } else {
                holder.getView<LinearLayout>(R.id.redMarkUnScan).visibility = View.INVISIBLE
            }

            holder.itemView.findViewById<View>(R.id.ll_checked).setOnClickListener {
                val result = data.filter { it.title != null && it.title.isChecked }
                if (result.isNotEmpty()) {
                    result[0].title.isChecked = false
                }

                item.title.isChecked = true
                notifyDataSetChanged()
            }

            val check = holder.getView<ImageView>(R.id.img_select)
            if (item.title.isChecked) {
                check.setImageResource(R.drawable.ic_check_selected)
            } else {
                check.setImageResource(R.drawable.ic_check_unselect)
            }
        } else if (item.itemType == 1) {
            val child = item.child
            if (!child.totalQty.isNullOrBlank() && !child.receiptQty.isNullOrBlank()) {
                holder.setText(
                    R.id.tv_num,
                    child.receiptQty.toDouble().toInt().toString() + "/" + child.totalQty.toDouble()
                        .toInt().toString()
                )
            }
            holder.setText(R.id.tv_unit, child.unit)
            holder.setText(R.id.tv_goods_code, child.custItemCode)
            holder.setText(R.id.tv_goods, child.itemName)

            if (!child.lotAtt04.isNullOrBlank()) {
                if (DCUtils.lot4TypeC2N.get(child.lotAtt04) != null) {
                    holder.setText(
                        R.id.tvLot4Name,
                        DCUtils.lot4TypeC2N.get(child.lotAtt04).toString()
                    )
                } else {
                    holder.setText(R.id.tvLot4Name, child.lotAtt04)
                }
            }
        }
    }

    fun getCheckedItem(): InPoolStorageDetail.OrderInfoList? {
        return data.find { it.title != null && it.title.isChecked }?.getTitle()
    }
}