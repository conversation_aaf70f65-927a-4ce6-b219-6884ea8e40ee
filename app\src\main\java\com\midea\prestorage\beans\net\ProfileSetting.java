package com.midea.prestorage.beans.net;

import java.io.Serializable;

public class ProfileSetting implements Serializable {

    // 开始日  0表示当天  1表示1天前   2表示2天前 以此类推
    int dashboardStartDay;
    // 结束日  0表示当天  1表示1天前   2表示2天前 以此类推
    int dashboardEndDay;

    String dashboardStartHour;
    String dashboardEndHour;


    public int getDashboardStartDay() {
        return dashboardStartDay;
    }

    public void setDashboardStartDay(int dashboardStartDay) {
        this.dashboardStartDay = dashboardStartDay;
    }

    public int getDashboardEndDay() {
        return dashboardEndDay;
    }

    public void setDashboardEndDay(int dashboardEndDay) {
        this.dashboardEndDay = dashboardEndDay;
    }

    public String getDashboardStartHour() {
        return dashboardStartHour;
    }

    public void setDashboardStartHour(String dashboardStartHour) {
        this.dashboardStartHour = dashboardStartHour;
    }

    public String getDashboardEndHour() {
        return dashboardEndHour;
    }

    public void setDashboardEndHour(String dashboardEndHour) {
        this.dashboardEndHour = dashboardEndHour;
    }
}
