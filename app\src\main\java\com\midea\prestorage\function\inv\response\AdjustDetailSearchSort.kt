package com.midea.prestorage.function.inv.response

import androidx.databinding.ObservableField
import com.midea.prestorage.utils.AppUtils

class AdjustDetailSearchSort(var item: AdjustDetailSearchResp): Comparable<AdjustDetailSearchSort> {

    val vs = ViewStyle()

    inner class ViewStyle {
        val sortFlag = ObservableField<Int>(0)
    }

    init {
        vs.sortFlag.set(if (AppUtils.getBigDecimalValueStr(item.scanNum) == "0" && AppUtils.getBigDecimalValueStr(item.allocatedQty) == "0") 1 else if (AppUtils.getBigDecimalValueStr(item.scanNum) == "0") 1 else if (AppUtils.getBigDecimalValueStr(item.allocatedQty).toInt() > AppUtils.getBigDecimalValueStr(item.scanNum).toInt()) 0 else 2)
    }

    override fun compareTo(other: AdjustDetailSearchSort): Int {
        return vs.sortFlag.get()!!.compareTo(other.vs.sortFlag.get()!!)
    }
}