package com.midea.prestorage.function.barcode.dialog

import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.midea.prestorage.base.BaseViewModelDialog
import com.midea.prestorage.function.planstock.dialog.AddStockLocDialogVM
import com.midea.prestorage.utils.AppUtils
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.DialogDeleteCollectionBarcodeBinding
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.xuexiang.xqrcode.XQRCode

class DeleteCollectionBarcodeDialog : BaseViewModelDialog<DeleteCollectionBarcodeDialogVM>() {
    private var binding: DialogDeleteCollectionBarcodeBinding? = null
    private var deleteback: DeleteBarcodeBack? = null
    private var onDismissListener: DialogInterface.OnDismissListener? = null
    private var mTitle: String? = null

    override fun beforeOnCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        vm = ViewModelProvider.AndroidViewModelFactory(requireActivity().application)
            .create(DeleteCollectionBarcodeDialogVM::class.java)
        binding = DataBindingUtil.inflate(
            inflater,
            R.layout.dialog_delete_collection_barcode,
            container,
            false
        )
        binding!!.vm = vm
        binding!!.lifecycleOwner = this

        initView()
        return binding!!.root
    }

    private fun initView() {
        //关闭对话框
        binding?.vm?.isDissmiss?.observe(this, Observer<Boolean> {
            if (it) {
                this.dismiss()
            }
        })

        vm.content.observe(this, Observer<String> { data ->
            if (data.isNotEmpty()) {
                backDeleteBarcode(data)
            }
        })

        //扫码
        binding?.vm?.startScan?.observe(this, Observer<Boolean> {
            if (it) {
                XQRCode.startScan(this, AddStockLocDialogVM.QRCODE_BACK)
            }
        })


    }

    fun setTitle(title: String) {
        if (!TextUtils.isEmpty(title)) {
            mTitle = title
        }
    }

    override fun showNow(manager: FragmentManager, tag: String?) {
        super.showNow(manager, tag)

        binding?.vm?.apply {
            title.set(mTitle)
        }
        AppUtils.requestFocus(binding?.edDelete)
    }

    fun setDeleteBack(backImpl: DeleteBarcodeBack) {
        deleteback = backImpl
    }

    fun backDeleteBarcode(barcode : String) {
        if (deleteback != null) {
            deleteback!!.deleteBarcodeBack(barcode)
            binding!!.vm!!.etInfo.value = ""
        }
    }

    fun setOnDismissListener(listener: DialogInterface.OnDismissListener) {
        onDismissListener = listener
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onDismissListener?.onDismiss(dialog)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //处理二维码扫描结果
        if (requestCode == AddStockLocDialogVM.QRCODE_BACK && resultCode == RxAppCompatActivity.RESULT_OK) {
            //处理扫描结果（在界面上显示）
            binding?.vm?.etInfo?.value = data?.extras?.getString(XQRCode.RESULT_DATA)
            binding?.vm?.onEnterAnyCode()
        }
    }

    interface DeleteBarcodeBack {
        fun deleteBarcodeBack(barcode : String)
    }
}