package com.midea.mideadspda.module.electro.fragment

import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestorage.base.adapter.ListCheckBoxAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemForPopup
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.AreaList
import com.midea.prestorage.beans.net.ReplenishmentBean
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.addgoods.AddGoodsActivity
import com.midea.prestorage.function.addgoods.fragment.AddFinishVM
import com.midea.prestorage.function.addgoods.fragment.FragmentFinishGoodsUnionBinding
import com.midea.prestorage.function.addgoods.fragment.FragmentMyGoodsUnionBinding
import com.midea.prestorage.function.addgoods.fragment.PopViewTaskPoolGoodsUnionBinding
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.WaitingDialogHelp
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.*
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.components.support.RxFragment

class AddFinishFragment : RxFragment(), AddGoodsActivity.OnRightClick,
    AddGoodsActivity.OnUnitInitOk {

    companion object {
        val TAG = "AddFinishFragment"

        fun newInstance(): AddFinishFragment {
            val bundle = Bundle()

            val fragment = AddFinishFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    private lateinit var binding: FragmentFinishGoodsUnionBinding
    private var vm: AddFinishVM? = null
    private lateinit var popupWindow: PopupWindow
    private lateinit var popViewBinding: PopViewTaskPoolGoodsUnionBinding
    val adapter = AddFinishAdapter()
    lateinit var waitingDialogHelp: WaitingDialogHelp

    private lateinit var dayDialog: FilterDialog
    private lateinit var areaDialog: FilterDialog

    var goodsInfo: String = ""

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            FragmentFinishGoodsUnionBinding.V2(inflater.let {
                FragmentFinishGoodsCareBinding.inflate(
                    it,
                    container,
                    false
                )
            })
        } else {
            FragmentFinishGoodsUnionBinding.V1(inflater.let {
                FragmentFinishGoodsBinding.inflate(
                    it,
                    container,
                    false
                )
            })
        }
        initView()
        initRecycle()
        return binding.root
    }

    private fun initRecycle() {
        binding.srl.setOnRefreshListener(vm!!.onRefreshCommand)
        binding.rv.layoutManager = LinearLayoutManager(activity)
        binding.rv.adapter = adapter
    }

    fun initArea(data: MutableList<AreaList>) {
        val deepCopy = AppUtils.cloneObj(data) as MutableList<AreaList>
        deepCopy.add(0, AreaList("全部"))
        vm!!.areaArgs = deepCopy[0]

        areaDialog = FilterDialog(activity as RxAppCompatActivity)
        areaDialog.setTitle("请选择库区")
        areaDialog.dismissEdit()
        areaDialog.addAllData(deepCopy.toMutableList())
        areaDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            popViewBinding.tvOpenLaunchArea.text = (it as AreaList).zoneName
            vm!!.areaArgs = it
            areaDialog.dismiss()
        })
        updateArgs()
    }

    fun showNoDataInfo() {
        binding.rv.visibility = View.GONE
        binding.ivNoOrder?.visibility = View.VISIBLE
    }

    fun showDataInfo() {
        binding.rv.visibility = View.VISIBLE
        binding.ivNoOrder.visibility = View.GONE
    }

    private fun initView() {
        if (vm == null) {
            vm = AddFinishVM(this)
            binding.vm = vm
        }
        initPopWindow()
        binding.lifecycleOwner = this
        binding.llContainer.setOnClickListener(mClick)

        initLoadMore()
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.initData(false)
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    private fun initPopWindow() {
        popViewBinding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            PopViewTaskPoolGoodsUnionBinding.V2(
                DataBindingUtil.inflate(layoutInflater, R.layout.pop_view_task_pool_goods_care, null, false)
            )
        } else {
            PopViewTaskPoolGoodsUnionBinding.V1(
                DataBindingUtil.inflate(layoutInflater, R.layout.pop_view_task_pool_goods, null, false)
            )
        }
        popupWindow = PopupWindow(
            popViewBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        popupWindow.setBackgroundDrawable(
            ColorDrawable(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.bg_color
                )
            )
        )
        popupWindow.isOutsideTouchable = true
        popViewBinding.linOpenLaunchClose.setOnClickListener {
            popupWindow.dismiss()
        }

        popupWindow.setOnDismissListener {
            goodsInfo = popViewBinding.edGoodsInfo.text.toString()
            updateArgs()
            vm?.refreshData()
        }

        val dayTest = vm!!.days
        popViewBinding.tvDayNum.text = dayTest[1].key
        dayDialog = FilterDialog(activity as RxAppCompatActivity)
        dayDialog.setTitle("请选择天数")
        dayDialog.dismissEdit()
        dayDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            popViewBinding.tvDayNum.text = it.showInfo
            vm!!.dayType = it as DCBean
            dayDialog.dismiss()
        })

        popViewBinding.edGoodsInfo.setOnEditorActionListener { v, actionId, event ->
            if (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER) {
                popupWindow.dismiss()
                vm!!.isEnterGoods = true
            }
            true
        }

        popViewBinding.relaOpenLaunchDate.setOnClickListener(mClick)
        popViewBinding.relaOpenLaunchArea.setOnClickListener(mClick)
        popViewBinding.imgClean.setOnClickListener(mClick)
        popViewBinding.linOpenLaunchSelectCom.visibility = View.INVISIBLE
        updateArgs()
    }

    override fun onRightClick() {
    }

    override fun onPageEnter() {
        vm!!.refreshData()
    }

    private var mClick: View.OnClickListener = View.OnClickListener {
        when (it.id) {
            R.id.ll_container -> {
                popupWindow.showAsDropDown(
                    binding.llContainer,
                    0,
                    -binding.llContainer.measuredHeight
                )
            }
            R.id.rela_open_launch_date -> {
                dayDialog.addAllData(vm!!.days as MutableList<BaseItemShowInfo>)
                dayDialog.show()
            }
            R.id.rela_open_launch_area -> {
                if (this@AddFinishFragment::areaDialog.isInitialized) {
                    areaDialog.show()
                }
            }
            R.id.img_clean -> {
                popViewBinding.edGoodsInfo.setText("")
            }
        }
    }

    private fun updateArgs() {
        binding.warpLinear.removeAllViews()
        if (vm!!.dayType != null) {
            binding.warpLinear.addView(getShowView(vm!!.dayType.key ?: ""))
        }
        if (vm!!.areaArgs != null) {
            binding.warpLinear.addView(getShowView(vm!!.areaArgs?.zoneName ?: ""))
        } else {
            binding.warpLinear.addView(getShowView("全部"))
        }
        if (goodsInfo.isNotEmpty()) {
            binding.warpLinear.addView(getShowView(goodsInfo))
        }
    }

    private fun getShowView(text: String): View {
        var view: View = LayoutInflater.from(this.context).inflate(
            if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_filter_care else R.layout.item_filter,
            null
        )
        view.findViewById<TextView>(R.id.tv_item).text = text
        return view
    }

    fun cleanOrderNo() {
        popViewBinding.tvCheckName.text = ""
        goodsInfo = ""
        updateArgs()
    }

    override fun unitOk() {
        vm!!.unitOk()
    }

    class AddFinishAdapter :
        ListCheckBoxAdapter<ReplenishmentBean.ReplenishmentListBean>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_add_finish_care else R.layout.item_add_finish),
        LoadMoreModule {
        override fun convert(helper: BaseViewHolder?, item: BaseItemForPopup?) {
            super.convert(helper, item as BaseItemForPopup)

            val bean = item as ReplenishmentBean.ReplenishmentListBean
            helper?.setText(R.id.tv_unit_info, bean.unitQty + bean.unit)
            helper?.setText(R.id.tv_batch_info, bean.orderByAttribute + ":")

            when (bean.taskLevel) {
                1 -> helper?.setBackgroundResource(
                    R.id.img_status,
                    if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.drawable.ic_urgent else R.mipmap.icon_second
                )
            }

            helper?.setVisible(R.id.img_status, bean.taskLevel != 0)
        }
    }
}