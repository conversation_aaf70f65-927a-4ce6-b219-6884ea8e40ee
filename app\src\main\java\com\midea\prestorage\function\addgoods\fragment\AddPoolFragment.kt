package com.midea.mideadspda.module.electro.fragment

import android.annotation.SuppressLint
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.midea.prestorage.base.adapter.CommonAdapter
import com.midea.prestorage.base.adapter.ListChoiceClickAdapter
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.AreaList
import com.midea.prestorage.beans.net.ReplenishmentBean
import com.midea.prestorage.dialog.FilterDialog
import com.midea.prestorage.function.addgoods.AddGoodsActivity
import com.midea.prestorage.function.addgoods.fragment.AddPoolVM
import com.midea.prestorage.function.addgoods.fragment.FragmentAddPoolUnionBinding
import com.midea.prestorage.function.addgoods.fragment.PopViewTaskPoolGoodsUnionBinding
import com.midea.prestorage.function.login.fragment.FragmentPasswordLoginUnionBinding
import com.midea.prestorage.function.main.ProfileVM
import com.midea.prestorage.function.receivecpkx.PopViewContainerListUnionBinding
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.SPUtils
import com.midea.prestorage.utils.WaitingDialogHelp
import com.midea.prestoragesaas.R
import com.midea.prestoragesaas.databinding.*
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.components.support.RxFragment

class AddPoolFragment : RxFragment(), AddGoodsActivity.OnRightClick, AddGoodsActivity.OnUnitInitOk {

    companion object {
        val TAG = "AddPoolFragment"

        fun newInstance(): AddPoolFragment {
            val bundle = Bundle()

            val fragment = AddPoolFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    private lateinit var binding: FragmentAddPoolUnionBinding
    private var vm: AddPoolVM? = null
    private lateinit var popupWindow: PopupWindow
    private lateinit var popViewBinding: PopViewTaskPoolGoodsUnionBinding
    val adapter = AddPoolAdapter()
    lateinit var waitingDialogHelp: WaitingDialogHelp

    private lateinit var dayDialog: FilterDialog
    private lateinit var areaDialog: FilterDialog

    var goodsInfo: String = ""

    var isCheckAll: Boolean = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            FragmentAddPoolUnionBinding.V2(inflater.let {
                FragmentAddPoolCareBinding.inflate(
                    it,
                    container,
                    false
                )
            })
        } else {
            FragmentAddPoolUnionBinding.V1(inflater.let {
                FragmentAddPoolBinding.inflate(
                    it,
                    container,
                    false
                )
            })
        }
        initView()
        initRecycle()
        return binding.root
    }

    private fun initRecycle() {
        binding.srl.setOnRefreshListener(vm!!.onRefreshCommand)
        binding.rv.layoutManager = LinearLayoutManager(activity)
        binding.rv.adapter = adapter

        adapter.setOnItemChildClickListener() { adapter, _, position ->
            val bean = adapter.data[position] as ReplenishmentBean.ReplenishmentListBean
            vm!!.receiveData(mutableListOf(bean.id))
        }
    }

    fun initArea(data: MutableList<AreaList>) {
        val deepCopy = AppUtils.cloneObj(data) as MutableList<AreaList>
        deepCopy.add(0, AreaList("全部"))
        vm!!.areaArgs = deepCopy[0]

        areaDialog = FilterDialog(activity as RxAppCompatActivity)
        areaDialog.setTitle("请选择库区")
        areaDialog.dismissEdit()
        areaDialog.addAllData(deepCopy.toMutableList())
        areaDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            popViewBinding.tvOpenLaunchArea.text = (it as AreaList).zoneName
            vm!!.areaArgs = it
            areaDialog.dismiss()
        })
        updateArgs()
    }

    private fun initView() {
        if (vm == null) {
            vm = AddPoolVM(this)
            binding.vm = vm
        }
        initPopWindow()
        binding.srl.setOnRefreshListener(vm!!.onRefreshCommand)

        binding.lifecycleOwner = this
        binding.llContainer.setOnClickListener(mClick)
        binding.linOpenLaunchSelectCom.setOnClickListener(mClick)
        initLoadMore()

        waitingDialogHelp = WaitingDialogHelp(activity)
    }

    fun showNoDataInfo() {
        binding.rv.visibility = View.GONE
        binding.ivNoOrder.visibility = View.VISIBLE
    }

    fun showDataInfo() {
        binding.rv.visibility = View.VISIBLE
        binding.ivNoOrder.visibility = View.GONE
    }

    @SuppressLint("SetTextI18n")
    fun checkNum(totalCount: Int) {
        binding.tvTotalNumber.text = "任务数：$totalCount"
    }

    override fun onRightClick() {
    }

    override fun onPageEnter() {
        vm!!.refreshData()
    }

    private fun initPopWindow() {
        popViewBinding = if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
            PopViewTaskPoolGoodsUnionBinding.V2(
                DataBindingUtil.inflate(layoutInflater, R.layout.pop_view_task_pool_goods_care, null, false)
            )
        } else {
            PopViewTaskPoolGoodsUnionBinding.V1(
                DataBindingUtil.inflate(layoutInflater, R.layout.pop_view_task_pool_goods, null, false)
            )
        }

        popupWindow = PopupWindow(
            popViewBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        popupWindow.setBackgroundDrawable(
            ColorDrawable(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.bg_color
                )
            )
        )
        popupWindow.isOutsideTouchable = true
        popViewBinding.linOpenLaunchClose.setOnClickListener {
            popupWindow.dismiss()
        }
        popupWindow.setOnDismissListener {
            goodsInfo = popViewBinding.edGoodsInfo.text.toString()
            updateArgs()
            vm?.refreshData()
        }

        val dayTest = vm!!.days
        popViewBinding.tvDayNum.text = dayTest[1].key
        dayDialog = FilterDialog(activity as RxAppCompatActivity)
        dayDialog.setTitle("请选择天数")
        dayDialog.dismissEdit()
        dayDialog.setOnCheckListener(ListChoiceClickAdapter.OnCheckListener {
            popViewBinding.tvDayNum.text = it.showInfo
            vm!!.dayType = it as DCBean
            dayDialog.dismiss()
        })

        popViewBinding.edGoodsInfo.setOnEditorActionListener { v, actionId, event ->
            if (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER) {
                popupWindow.dismiss()

                vm!!.isEnterGoods = true
            }
            true
        }

        popViewBinding.relaOpenLaunchDate.setOnClickListener(mClick)
        popViewBinding.relaOpenLaunchArea.setOnClickListener(mClick)
        popViewBinding.imgClean.setOnClickListener(mClick)
        popViewBinding.linOpenLaunchSelectCom.setOnClickListener(mClick)
        updateArgs()
    }

    @SuppressLint("SetTextI18n")
    private var mClick: View.OnClickListener = View.OnClickListener {
        when (it.id) {
            R.id.lin_open_launch_select_com -> {
                if (!isCheckAll) {
                    if (adapter.data.isEmpty()) {
                        return@OnClickListener
                    }
                    //选中
                    isCheckAll = true
                    if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                        binding.ivOpenLaunchSelectCom.setImageResource(R.drawable.select_selected_care)
                        popViewBinding.ivOpenLaunchSelectCom.setImageResource(R.drawable.select_selected_care)
                    } else {
                        binding.ivOpenLaunchSelectCom.setImageResource(R.mipmap.select_selected)
                        popViewBinding.ivOpenLaunchSelectCom.setImageResource(R.mipmap.select_selected)
                    }
                    binding.llConfirmGet.visibility = View.VISIBLE
                    binding.tvTotalNumber.text = "任务数：${vm?.totalCount.toString()}"
                } else {
                    //未选中
                    isCheckAll = false
                    if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) {
                        binding.ivOpenLaunchSelectCom.setImageResource(R.drawable.select_normal_care)
                        popViewBinding.ivOpenLaunchSelectCom.setImageResource(R.drawable.select_normal_care)
                    } else {
                        binding.ivOpenLaunchSelectCom.setImageResource(R.mipmap.select_normal)
                        popViewBinding.ivOpenLaunchSelectCom.setImageResource(R.mipmap.select_normal)
                    }
                    binding.llConfirmGet.visibility = View.GONE
                }
            }
            R.id.ll_container -> {
                popupWindow.showAsDropDown(
                    binding.llContainer,
                    0,
                    -binding.llContainer.measuredHeight
                )
            }
            R.id.rela_open_launch_date -> {
                dayDialog.addAllData(vm!!.days as MutableList<BaseItemShowInfo>)
                dayDialog.show()
            }
            R.id.rela_open_launch_area -> {
                if (this@AddPoolFragment::areaDialog.isInitialized) {
                    areaDialog.show()
                }
            }
            R.id.img_clean -> {
                popViewBinding.edGoodsInfo.setText("")
            }
        }
    }

    fun setAllNotSelect() {
        if (isCheckAll) {
            binding.linOpenLaunchSelectCom.performClick()
        }
    }

    private fun updateArgs() {
        binding.warpLinear.removeAllViews()
        binding.warpLinear.addView(getShowView(vm!!.dayType.key ?: ""))
        if (vm!!.areaArgs != null) {
            binding.warpLinear.addView(getShowView(vm!!.areaArgs?.zoneName ?: ""))
        } else {
            binding.warpLinear.addView(getShowView("全部"))
        }
        if (goodsInfo.isNotEmpty()) {
            binding.warpLinear.addView(getShowView(goodsInfo))
        }
    }

    private fun getShowView(text: String): View {
        var view: View =
            LayoutInflater.from(<EMAIL>).inflate(
                if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_filter_care else R.layout.item_filter,
                null
            )
        view.findViewById<TextView>(R.id.tv_item).text = text
        return view
    }

    fun cleanOrderNo() {
        popViewBinding.tvCheckName.text = ""
        goodsInfo = ""
        updateArgs()
    }

    /**
     * 初始化  上滑加载更多
     */
    private fun initLoadMore() {
        adapter.loadMoreModule.setOnLoadMoreListener {
            binding.vm?.initData(false)
        }
        adapter.loadMoreModule.isAutoLoadMore = true
        //当自动加载开启，同时数据不满一屏时，是否继续执行自动加载更多(默认为true)
        adapter.loadMoreModule.enableLoadMoreEndClick = false
    }

    override fun unitOk() {
        vm!!.unitOk()
    }

    class AddPoolAdapter :
        CommonAdapter<ReplenishmentBean.ReplenishmentListBean>(if (SPUtils[ProfileVM.CARE_MODE, false] as Boolean) R.layout.item_add_goods_care else R.layout.item_add_goods),
        LoadMoreModule {

        init {
            addChildClickViewIds(R.id.tv_get)
        }

        override fun convert(
            helper: BaseViewHolder?,
            item: ReplenishmentBean.ReplenishmentListBean?
        ) {
            super.convert(helper, item)

            val bean = item as ReplenishmentBean.ReplenishmentListBean
            helper?.setText(R.id.tv_unit_info, bean.unitQty + bean.unit)
            helper?.setText(R.id.tv_batch_info, bean.orderByAttribute + ":")

            helper?.setVisible(R.id.img_status, bean.taskLevel != 0)
        }
    }
}