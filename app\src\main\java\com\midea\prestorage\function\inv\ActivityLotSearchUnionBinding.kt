package com.midea.prestorage.function.inv

import android.widget.EditText
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.midea.prestoragesaas.databinding.ActivityLotSearchBinding
import com.midea.prestoragesaas.databinding.ActivityLotSearchCareBinding

sealed class ActivityLotSearchUnionBinding{
    abstract var vm: LotSearchVM?
    abstract val llTitleBar: RelativeLayout
    abstract val etLocCode: EditText
    abstract val recyclerView: RecyclerView
    abstract val etCustItemCode: EditText
    abstract val tvNotification: TextView

    class V2(val binding: ActivityLotSearchCareBinding) : ActivityLotSearchUnionBinding() {
        override var vm: LotSearchVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etLocCode = binding.etLocCode
        override val recyclerView = binding.recyclerView
        override val etCustItemCode = binding.etCustItemCode
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityLotSearchBinding) : ActivityLotSearchUnionBinding() {
        override var vm: LotSearchVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val etLocCode = binding.etLocCode
        override val recyclerView = binding.recyclerView
        override val etCustItemCode = binding.etCustItemCode
        override val tvNotification = binding.tvNotification
    }
}
