package com.midea.prestorage.dialog

import android.view.View
import androidx.databinding.ObservableField

class TipNoCancelDialogVM(var dialog: TipNoCancelDialog) {
    val titleName = ObservableField<String>("")
    val msg = ObservableField<String>("")

    var listener: TipNoCancelDialog.OnTipNoCancelBack? = null

    val confirmClick = View.OnClickListener {
        dialog.dismiss()
        listener?.onConfirmClick()
    }
}