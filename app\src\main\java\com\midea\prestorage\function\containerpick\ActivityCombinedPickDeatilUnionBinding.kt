package com.midea.prestorage.function.containerpick

import android.widget.*
import com.midea.prestoragesaas.databinding.ActivityCombinedPickDeatilBinding
import com.midea.prestoragesaas.databinding.ActivityCombinedPickDeatilCareBinding

sealed class ActivityCombinedPickDeatilUnionBinding {
    abstract var vm: CombinedPickDeatilVM?
    abstract val llTitleBar: RelativeLayout
    abstract val gridNumber: GridView
    abstract val tvNotification: TextView

    class V2(val binding: ActivityCombinedPickDeatilCareBinding) :
        ActivityCombinedPickDeatilUnionBinding() {
        override var vm: CombinedPickDeatilVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val gridNumber = binding.gridNumber
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityCombinedPickDeatilBinding) :
        ActivityCombinedPickDeatilUnionBinding() {
        override var vm: CombinedPickDeatilVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val gridNumber = binding.gridNumber
        override val tvNotification = binding.tvNotification
    }
}
