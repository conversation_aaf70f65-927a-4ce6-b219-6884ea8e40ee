package com.midea.prestorage.function.inpool

import android.view.View
import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.beans.net.CloseOrderInfo
import com.midea.prestorage.beans.net.InPoolStorageDetail
import com.midea.prestorage.beans.net.InPoolStorageList
import com.midea.prestorage.beans.setting.InStorageInfo
import com.midea.prestorage.dialog.AlertDialogUtil
import com.midea.prestorage.dialog.TipDialog
import com.midea.prestorage.function.inv.response.InvStocktakeDetail
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DbUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import okhttp3.MediaType
import okhttp3.RequestBody


class InPoolStorageDetailVM(val activity: InPoolStorageDetailActivity) {

    private val bean = activity.intent.getSerializableExtra("bean") as InPoolStorageList
    private var originData: InPoolStorageDetail? = null
    private var inStorageInfo: InStorageInfo? = null

    val btnModeChange = ObservableField<String>("汇总")

    //记录订单详情是否汇总的字段  0为明细显示，1为汇总显示
    private var orderDetailShowMode = 0

    val waveNo = ObservableField("")
    val totalQty = ObservableField("")
    val totalVolume = ObservableField("")
    val totalWeight = ObservableField("")

    private var tipDialog: TipDialog


    val db = DbUtils.db

    init {
        initOrderDetailList()
        // 20211213  默认就是明细模式 orderDetailShowMode = 0 ， 不记忆上次是汇总还是明细模式
        //readProperty()


        tipDialog = TipDialog(activity)
        tipDialog.setTitle("提示")
        tipDialog.setOnTipBackListener(object : TipDialog.OnTipBack {
            override fun onConfirmClick() {
                closeOne(tipDialog.tag as InPoolStorageDetail.OrderInfoList)
            }

            override fun onDismissClick() {
            }
        })
    }

    private fun initOrderDetailList() {
        val param = mutableMapOf<String, String>()

        if (!bean.waveNo.isNullOrBlank()) {
            // 有波次号就按波次号查询数据
            param.put("waveNo", bean.waveNo)
        } else {
            // 需求 波次号为空时隐藏汇总按钮
            activity.binding.btnChangeMode.visibility = View.INVISIBLE
            // 没有波次号就按入库单号查询数据
            param.put("receiptCode", bean.receiptCode)
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getAppAPI()
            .inOrderDetail(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<InPoolStorageDetail>(activity) {
                override fun success(data: InPoolStorageDetail?) {
                    if (data != null) {
                        originData = data
                        initWaveNoInfo()
                        initRecycleViewData()
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    private fun initRecycleViewData() {
        val beans = mutableListOf<InPoolStorageDetailHelp>()

        if (originData != null) {
            val listData = originData!!.headerVOList

            listData.forEach { item ->
                // 模式切换
                if (orderDetailShowMode == 0) {
                    // 明细模式  [列表界面为 title+child  , title + child1 + child2 ,..... ] 的形式
                    beans.add(InPoolStorageDetailHelp(item))

                    item.receiptDetailVOList.forEach {
                        beans.add(InPoolStorageDetailHelp(it))
                    }
                } else {
                    // 汇总模式   列表界面 为  [汇总child1 , 汇总child2 , ...] 形式
                    val copyList =
                        mutableListOf<InPoolStorageDetail.OrderInfoList.OrderInfoDetailsList>()
                    item.receiptDetailVOList.forEach {
                        copyList.add(it.copy())
                    }

                    copyList.forEach {
                        //beans.add(InPoolStorageDetailHelp(it))
                        val result = beans.filter { fItem ->
                            fItem.child.custItemCode == it.custItemCode && fItem.child.lotAtt04 == it.lotAtt04
                        }

                        if (result.isNotEmpty()) {
                            result[0].getChild().totalQty =
                                result[0].child.totalQty.toDouble().toInt()
                                    .plus(it.totalQty.toDouble().toInt()).toString()
                        } else {
                            beans.add(InPoolStorageDetailHelp(it))
                        }
                    }
                }
            }
        }
        activity.resetAdapterData(beans)
    }

    private fun initWaveNoInfo() {
        if (originData != null) {
            waveNo.set(originData!!.waveNo)
            totalQty.set(originData!!.totalQty.toDouble().toInt().toString())

            // 总体积 保留两位小数
            originData!!.totalVolume?.let {
                totalVolume.set(originData!!.totalVolume.toDouble().toString())
            }
            // 总重量 保留两位小数
            originData!!.totalWeight?.let {
                totalWeight.set(originData!!.totalWeight.toDouble().toString())
            }

        }
    }

    fun back() {
        activity.finish()
    }

    fun modeChange() {
        orderDetailShowMode = when (orderDetailShowMode) {
            1 -> {
                btnModeChange.set("汇总")
                saveProperty()
                0
            }
            else -> {
                btnModeChange.set("明细")
                saveProperty()
                1
            }
        }
        initRecycleViewData()
    }

    private fun saveProperty() {
        Observable.create<String> {
            if (inStorageInfo == null) {
                inStorageInfo = InStorageInfo()
            }
            inStorageInfo!!.orderDetailShowMode = orderDetailShowMode
            db.saveOrUpdate(inStorageInfo)
            it.onComplete()
        }.compose(NetworkScheduler.compose())
            .bindUntilEvent(activity, ActivityEvent.DESTROY)
            .subscribe(object : Observer<String> {
                override fun onComplete() {
                }

                override fun onSubscribe(d: Disposable) {
                }

                override fun onNext(t: String) {
                }

                override fun onError(e: Throwable) {
                }
            })
    }


    fun onSubmitCancelUnScan(item: InPoolStorageDetailHelp) {

        AlertDialogUtil.showOkAndCancelDialog(activity, "确定取消申请不扫码？",
            { dialogInterface, i ->  //点了确定
                cancelUnscan(item)
            },
            { dialogInterface, i ->  //点了取消

            })

    }


    fun cancelUnscan(item: InPoolStorageDetailHelp) {
        val param = mutableMapOf<String, Any>()
        param.put("whCode", activity.getWhCode())
        param.put("orderType", "in")
        param.put("referenceNo", item.title.receiptCode) //入库单号

        // 三个必传参数:  ids 勾选的记录id列表 ,  status 要改成的状态， checkedBy 审核人
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        activity.waitingDialogHelp.showDialog()
        RetrofitHelper.getBarcodeAPI()
            .cancelApplyUnscan(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity) {
                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()

                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }

                override fun success(data: Any?) {
                    activity.waitingDialogHelp.hidenDialog()
                    AlertDialogUtil.showOnlyOkDialog(activity, "操作成功") {
                        //操作成功后重新刷新列表
                        activity.finish()
                    }
                }
            })
    }

    fun closeOrder() {
        val checkedItem = activity.adapter.getCheckedItem()
        if (checkedItem == null) {
            ToastUtils.getInstance().showErrorToastWithSound(activity, "请先勾选!")
            return
        }
        val param = mutableMapOf(
            "receiptCodeList" to mutableListOf(checkedItem.receiptCode)
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .orderMulti(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<CloseOrderInfo>(activity as RxAppCompatActivity) {
                override fun success(data: CloseOrderInfo?) {
                    if (data?.code == "1") {
                        tipDialog.setMsg(data.msg)
                        tipDialog.show()
                        tipDialog.tag = checkedItem
                    } else if (data?.code == "0") {
                        ToastUtils.getInstance().showSuccessToastWithSound(activity, "关单成功!")
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }

    fun closeOne(checkedItem: InPoolStorageDetail.OrderInfoList) {
        val param = mutableMapOf(
            "receiptCodeList" to mutableListOf(checkedItem.receiptCode)
        )
        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )
        RetrofitHelper.getOutStorageAPI()
            .orderOne(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(activity as RxAppCompatActivity) {
                override fun success(data: Any?) {
                    activity.finish()
                    AppUtils.showToast(activity, "关单成功!")
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    activity.waitingDialogHelp.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(activity, apiErrorModel.message)
                }
            })
    }
}