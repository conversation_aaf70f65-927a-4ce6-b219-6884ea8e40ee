package com.midea.prestorage.function.main.dialog

import androidx.databinding.ObservableField
import com.midea.prestorage.beans.net.TenantResp
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.widgets.ViewBindingAdapter

class TenantDialogVM(val dialog: TenantDialog) {

    val title = ObservableField<String>("提示")
    val filterInfo = ObservableField("")
    val isShowEdit = ObservableField(true)

    val allData = mutableListOf<TenantResp.TenantsDTO>()
    var isCancelAble = true

    val inputTextChange = object : ViewBindingAdapter.TextChangedListener {

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            if (!s.isNullOrBlank()) {
                val searchReactList = allData.filter {
                    it.tenantName.contains(s.trim()) || it.tenantCode.contains(s.trim())
                }
                dialog.notifyDataChange(searchReactList as MutableList<TenantResp.TenantsDTO>)
            } else {
                dialog.notifyDataChange(allData)
            }
        }
    }

    fun close() {
        dialog.dismiss()
    }

    fun cleanFilter() {
        filterInfo.set("")
    }

    fun refreshFromServer() {
        (dialog.mContext as WhChooseDialog.ServerRefresh).refreshByServer()
    }
}