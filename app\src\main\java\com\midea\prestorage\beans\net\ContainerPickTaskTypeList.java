package com.midea.prestorage.beans.net;

import com.midea.prestorage.base.annotation.ShowAnnotation;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

public class ContainerPickTaskTypeList implements Serializable {

    @ShowAnnotation
    private String eaUnit;
    private BigDecimal fromQty;
    private int gridNumber;

    public ContainerPickTaskTypeList(String eaUnit, BigDecimal fromQty, int gridNumber) {
        this.eaUnit = eaUnit;
        this.fromQty = fromQty;
        this.gridNumber = gridNumber;
    }

    public String getEaUnit() {
        return eaUnit;
    }

    public void setEaUnit(String eaUnit) {
        this.eaUnit = eaUnit;
    }

    public BigDecimal getFromQty() {
        return fromQty;
    }

    public void setFromQty(BigDecimal fromQty) {
        this.fromQty = fromQty;
    }

    public int getGridNumber() {
        return gridNumber;
    }

    public void setGridNumber(int gridNumber) {
        this.gridNumber = gridNumber;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ContainerPickTaskTypeList that = (ContainerPickTaskTypeList) o;
        return Objects.equals(gridNumber, that.gridNumber);
    }
}
