package com.midea.prestorage.base.adapter;

import android.widget.ImageView;

import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.midea.prestoragesaas.R;
import com.midea.prestorage.beans.base.BaseItemForPopup;

public class BaseSelectAdapter<T extends BaseItemForPopup> extends ListCheckBoxAdapter<T> {

    public BaseSelectAdapter(int layoutResId) {
        super(layoutResId);
    }

    @Override
    protected void convert(BaseViewHolder helper, BaseItemForPopup item) {
        super.convert(helper, (T) item);
        ImageView check = helper.getView(R.id.img_select);
        check.setScaleX(0.8f);
        check.setScaleY(0.8f);
        if (item.isSelected()) {
            check.setImageResource(R.mipmap.select_selected);
        } else {
            check.setImageResource(R.mipmap.select_normal);
        }
    }
}