package com.midea.prestorage.dialog

import androidx.databinding.ObservableField
import com.midea.prestorage.beans.base.BaseItemShowInfo
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.widgets.ViewBindingAdapter

class ConfirmFilterDialogVM(val dialog: ConfirmFilterDialog) {

    var allData: MutableList<BaseItemShowInfo>? = null
    val title = ObservableField<String>("提示")
    val filterInfo = ObservableField("")
    val isShowEdit = ObservableField(true)

    val inputTextChange = object : ViewBindingAdapter.TextChangedListener {

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            if (allData != null) {
                val results = allData!!.filter { it.showInfo.contains(s.toString()) }
                dialog.changeDataNotify(results)
            }
        }
    }

    fun close() {
        dialog.dismiss()
    }

    fun confirm() {
        val checkData = dialog.adapter.returnBean
        if (checkData == null) {
            AppUtils.showToast(dialog.mContext, "请先勾选!")
            return
        }
        dialog.backConfirm(checkData)
    }

    fun cleanFilter() {
        filterInfo.set("")
    }

    fun editUnable() {
        isShowEdit.set(false)
    }
}