package com.midea.prestorage.function.containerpick

import android.widget.EditText
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.midea.prestoragesaas.databinding.ActivityOutContainerPickBinding
import com.midea.prestoragesaas.databinding.ActivityOutContainerPickCareBinding
import com.midea.prestoragesaas.databinding.ActivityOutContainerPickSecondBinding
import com.midea.prestoragesaas.databinding.ActivityOutContainerPickSecondCareBinding

sealed class ActivityOutContainerPickSecondUnionBinding {
    abstract var vm: ContainerPickSecondVM?
    abstract val llTitleBar: RelativeLayout
    abstract val edContainer: EditText
    abstract val edLocation: EditText
    abstract val edGoodsCode: EditText
    abstract val recycle: RecyclerView
    abstract val tvNotification: TextView

    class V2(val binding: ActivityOutContainerPickSecondCareBinding) :
        ActivityOutContainerPickSecondUnionBinding() {
        override var vm: ContainerPickSecondVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val edContainer = binding.edContainer
        override val edLocation = binding.edLocation
        override val edGoodsCode = binding.edGoodsCode
        override val recycle = binding.recycle
        override val tvNotification = binding.tvNotification
    }

    class V1(val binding: ActivityOutContainerPickSecondBinding) :
        ActivityOutContainerPickSecondUnionBinding() {
        override var vm: ContainerPickSecondVM?
            get() = binding.vm
            set(value) {
                binding.vm = value
            }
        override val llTitleBar = binding.llTitleBar
        override val edContainer = binding.edContainer
        override val edLocation = binding.edLocation
        override val edGoodsCode = binding.edGoodsCode
        override val recycle = binding.recycle
        override val tvNotification = binding.tvNotification
    }
}
