package com.midea.prestorage.function.outstorage

import android.app.Application
import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.OutStorageQuery
import com.midea.prestorage.beans.net.RuleDataList
import com.midea.prestorage.beans.setting.HandingInfoDb
import com.midea.prestorage.function.outstorage.response.RespContainerQuery
import com.midea.prestorage.function.outstorage.response.RespShipmentDetail
import com.midea.prestorage.function.outstorage.response.ShipmentDetailSort
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.AppUtils
import com.midea.prestorage.utils.DbUtils
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.RequestBody
import org.xutils.db.sqlite.WhereBuilder

class ContainerTypeInVM(application: Application) : BaseViewModel(application) {
    var finishActivity = MutableLiveData(false)
    var curOrderNo = MutableLiveData<String>("") // 单号 (入库单号或波次单号)
    var containerNo = MutableLiveData<String>("")
    var containerLockNo = MutableLiveData<String>("")
    val processInfo = MutableLiveData<String>("0")
    var goodsRequest = MutableLiveData(false)
    var lockNoRequest = MutableLiveData(false)
    var isPalletEnter = MutableLiveData(false)
    var containerChangeTip = MutableLiveData(false)
    var showDatas = MutableLiveData<MutableList<RespContainerQuery>>()
    var bean: OutStorageQuery? = null
    val db = DbUtils.db
    var isShowTipDialog = MutableLiveData(false)

    override fun init() {

    }

    fun onEnterOrderNo() {
        if (CheckUtil.isFastDoubleClick()) {
            if (TextUtils.isEmpty(curOrderNo.value)) {
                showNotification("单号不能为空", false)
                return
            }
            queryShipmentDetail()
        }
    }

    fun containerNoEnterKeyPress() {
        if (CheckUtil.isFastDoubleClick()) {
            if (TextUtils.isEmpty(containerNo.value)) {
                showNotification("柜号不能为空", false)
                return
            }
            lockNoRequest.value = true
        }
    }

    fun containerLockNoEnterKeyPress() {
        if (CheckUtil.isFastDoubleClick()) {
            if (TextUtils.isEmpty(containerLockNo.value)) {
                showNotification("锁号不能为空", false)
                return
            }
        }
    }

    fun queryShipmentDetail() {
        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf(
                "whCode" to Constants.whInfo?.whCode,
                "orderNo" to curOrderNo.value.toString().trim()
            )
            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp =
                    async { RetrofitHelper.getOutStorageAPI().containerQuery(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {

                if (result.data != null && result.data!!.size > 0) {
                    isPalletEnter.value = true
                    goodsRequest.value = true
                    showDatas.value = result.data
                    processInfo.value = result.data!!.size.toString()
                }else {
                    curOrderNo.value = ""
                    showNotification("查询不到单据", false)
                }

            } else {
                curOrderNo.value = ""
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    fun submit() {
        if (CheckUtil.isFastDoubleClick()) {
            if (TextUtils.isEmpty(containerNo.value)) {
                showNotification("柜号不能为空", false)
                return
            }

            if (TextUtils.isEmpty(containerLockNo.value)) {
                showNotification("锁号不能为空", false)
                return
            }

            isShowTipDialog.value = true
        }
    }

    /**
     * 提交
     */
    fun sendConfirm(data: MutableList<String>?) {

        launch(showDialog = true,
            error = {
            }, finish = {
            }) {

            val param = mutableMapOf(
                "whCode" to Constants.whInfo?.whCode,
                "exportContainerNo" to containerNo.value.toString().trim(),
                "exportContainerLockNo" to containerLockNo.value.toString().trim(),
                "shipmentCodes" to data
            )

            val requestBody = RequestBody.create(
                MediaType.parse("Content-Type, application/json"),
                Gson().toJson(param)
            )

            val result = withContext(Dispatchers.IO) {
                val resp = async { RetrofitHelper.getOutStorageAPI().containerCommit(requestBody) }
                resp.await()
            }

            if (result.code == 0L) {
                result.msg?.let { showNotification(it, true) }
                containerChangeTip.value = true  //提交成功后，消息提醒更新成功5秒，并重置页面，光标定位到单号录入框
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }

    fun showErrorNotification(msg: String, isSuccess: Boolean) {
        showNotification(msg, isSuccess)
    }

    fun containerChangeTip() {
        if (CheckUtil.isFastDoubleClick()) {
            containerChangeTip.value = true
        }
    }

}