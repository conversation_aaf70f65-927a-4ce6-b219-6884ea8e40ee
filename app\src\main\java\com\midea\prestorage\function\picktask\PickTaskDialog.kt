package com.midea.prestorage.function.picktask

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.midea.prestoragesaas.R
import com.midea.prestorage.base.BaseViewModelDialog
import com.midea.prestoragesaas.databinding.ActivityPickTaskDialogBinding
import com.midea.prestorage.function.inv.response.InvStockTakeTaskDetail
import com.midea.prestorage.utils.AppUtils


class PickTaskDialog : BaseViewModelDialog<PickTaskDialogVM>() {

    private lateinit var binding: ActivityPickTaskDialogBinding
    private var data: InvStockTakeTaskDetail? = null
    private var back: PickTaskDialogBack? = null

    override fun beforeOnCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        vm = ViewModelProvider.AndroidViewModelFactory(requireActivity().application)
            .create(PickTaskDialogVM::class.java)
        binding = DataBindingUtil.inflate(
            inflater,
            R.layout.activity_pick_task_dialog,
            container,
            false
        )
        binding.vm = vm
        binding.lifecycleOwner = this

        initView()
        initOnClickEvent()
        return binding.root
    }

    fun setPickTaskDialogBack(pickTaskDialogBack: PickTaskDialogBack) {
        back = pickTaskDialogBack
    }

    private fun initView() {
        vm.isResultOk.observe(this, Observer<Boolean> {
            if (it) {
                back?.pickTaskDialogBack()
            }
        })
    }

    fun setData(allData: InvStockTakeTaskDetail) {
        data = allData
    }

    override fun showNow(manager: FragmentManager, tag: String?) {
        super.showNow(manager, tag)

        binding.vm?.apply {
            custItemCode.value = data?.custItemCode
            fromQty.value = data?.fromQty
            advance.value = "${data?.fromLoc}/${AppUtils.getBigDecimalValueStr(data?.fromQty)}"
            ids.value = data?.ids
        }
    }

    override fun onResume() {
        super.onResume()
        binding.inputContent.setSelection(binding.inputContent.text.toString().length)//光标定位到末尾
    }

    //初始化单击事件
    private fun initOnClickEvent() {
        //关闭对话框
        binding.vm?.isDissmiss?.observe(this, Observer<Boolean> {
            if (it) {
                this.dismiss()
            }
        })
    }

    interface PickTaskDialogBack {
        fun pickTaskDialogBack()
    }
}