package com.midea.prestorage.function.addgoods.fragment

import android.text.TextUtils
import androidx.databinding.ObservableBoolean
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.mideadspda.module.electro.fragment.AddPoolFragment
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.AreaList
import com.midea.prestorage.beans.net.ReplenishmentBean
import com.midea.prestorage.function.addgoods.AddGoodsActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.MySoundUtils
import com.midea.prestorage.utils.TTSUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody

class AddPoolVM(val fragment: AddPoolFragment) {

    val isRefreshing = ObservableBoolean(false)

    var dayType: DCBean = DCBean("3天", "3")
    var areaArgs: AreaList? = null

    var totalCount: Int = 0
    var pageNo = 1 //当前第一页

    var isEnterGoods = false //用于控制是否输入搜索的参数

    init {
        refreshData()
    }

    val onRefreshCommand = SwipeRefreshLayout.OnRefreshListener {
        refreshData()
    }

    fun refreshData() {
        isRefreshing.set(true)
        pageNo = 1
        initData(true)
    }

    fun receiveData(item: MutableList<String>) {
        fragment.waitingDialogHelp.showDialog()
        val list = mutableListOf<MutableMap<String, String?>>()
        item.forEach {
            val map = mutableMapOf<String, String?>()
            map["taskDetailId"] = it
            list.add(map)
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(mutableMapOf("whCode" to Constants.whInfo?.whCode, "details" to list))
        )

        val response = if (Constants.whInfo?.bearingSystem == "3") {
            RetrofitHelper.getAddGoodsService()
                .receiveBwms(requestBody)
        } else {
            RetrofitHelper.getAddGoodsService()
                .receive(requestBody)
        }

        response
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(fragment.requireActivity()) {
                override fun success(data: Any?) {
                    refreshData()
                    ToastUtils.getInstance().showSuccessToast(fragment.activity, "领取成功!")
                    TTSUtils.startAuto("领取成功")
                    fragment.setAllNotSelect()
                    fragment.waitingDialogHelp.hidenDialog()

                    refreshNum()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    showErrorInfo(apiErrorModel.message)
                    fragment.waitingDialogHelp.hidenDialog()
                }
            })
    }

    fun refreshNum() {
        val map = mutableMapOf(
            "whCode" to Constants.whInfo?.whCode,
            "day" to "30",
            "queryType" to 2,
            "pageNo" to 1,
            "pageSize" to 10,
            "queryCode" to "",
            "orderBy" to "task_level desc,id",
            "queryTaskCount " to 1,
            "orderByType" to "asc"
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        val response = if (Constants.whInfo?.bearingSystem == "3") {
            RetrofitHelper.getAddGoodsService()
                .rfQueryNew(requestBody)
        } else {
            RetrofitHelper.getAddGoodsService()
                .rfQuery(requestBody)
        }

        response
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ReplenishmentBean>(fragment.requireActivity()) {
                override fun success(data: ReplenishmentBean?) {
                    if (data != null) {
                        (fragment.activity as AddGoodsActivity).setMyDataNum(data.totalCount)
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isRefreshing.set(false)
                    showErrorInfo(apiErrorModel.message)
                    if (statusCode == 605025L) {
                        fragment.cleanOrderNo()
                    }
                }
            })
    }


    fun initData(isRefresh: Boolean) {
        val map = mutableMapOf(
            "whCode" to Constants.whInfo?.whCode,
            "day" to dayType.value,
            "queryType" to 1,
            "pageNo" to pageNo,
            "pageSize" to 10,
            "queryCode" to fragment.goodsInfo,
            "orderBy" to "task_level desc,id",
            "orderByType" to "asc"
        )
        if (!TextUtils.isEmpty(areaArgs?.zoneCode)) {
            map["zoneCodes"] = mutableListOf(areaArgs?.zoneCode)
        }

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(map)
        )

        val response = if (Constants.whInfo?.bearingSystem == "3") {
            RetrofitHelper.getAddGoodsService()
                .rfQueryNew(requestBody)
        } else {
            RetrofitHelper.getAddGoodsService()
                .rfQuery(requestBody)
        }

        response
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment.activity as RxAppCompatActivity, ActivityEvent.DESTROY)
            .subscribe(object : RequestCallback<ReplenishmentBean>(fragment.requireActivity()) {
                override fun success(data: ReplenishmentBean?) {
                    if (isRefresh) {
                        isRefreshing.set(false)
                    } else {
                        fragment.adapter.loadMoreModule.loadMoreEnd()
                    }
                    if (data != null && data.list != null && data.list.isNotEmpty()) {
                        data.list.forEach {
                            if ((fragment.activity as AddGoodsActivity).packageResp != null) {
                                val results = (fragment.activity as AddGoodsActivity)
                                    .packageResp?.list?.filter { item -> item.code == it.unit }
                                val resultBatch = (fragment.activity as AddGoodsActivity)
                                    .batchResp?.list?.filter { item -> item.code == it.orderByAttribute }
                                if (results != null && results.isNotEmpty()) {
                                    it.unit = results[0].name
                                }
                                if (resultBatch != null && resultBatch.isNotEmpty()) {
                                    it.orderByAttribute = resultBatch[0].name
                                }
                            }
                        }
                        if (isRefresh) {
                            fragment.adapter.setNewInstance(data.list)
                        } else {
                            fragment.adapter.addData(data.list)
                        }
                        totalCount = data.totalCount
                        fragment.showDataInfo()
                    } else {
                        if (isRefresh) {
                            totalCount = 0
                            fragment.showNoDataInfo()
                        }
                    }

                    if (pageNo >= data?.totalPage!!) {
                        //加载到了最后一页
                        fragment.adapter.loadMoreModule.isEnableLoadMore = false
                    } else {
                        pageNo += 1
                    }
                    (fragment.activity as AddGoodsActivity).setPoolDataNum(totalCount)
                    fragment.checkNum(totalCount)

                    if (isEnterGoods) {
                        MySoundUtils.getInstance().dingSound()
                        isEnterGoods = false
                    }
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    isRefreshing.set(false)
                    showErrorInfo(apiErrorModel.message)
                    if (statusCode == 605025L) {
                        fragment.cleanOrderNo()
                    }
                }
            })
    }

    fun showErrorInfo(info: String) {
        ToastUtils.getInstance().showErrorToastWithSound(fragment.activity, info)
    }

    fun showSuccessInfo(info: String) {
        ToastUtils.getInstance().showSuccessToastWithSound(fragment.activity, info)
    }

    fun receiveAllData() {
        if (!fragment.adapter.data.isNullOrEmpty()) {
            val list = mutableListOf<String>()
            fragment.adapter.data.forEach {
                list.add(it.id)
            }
            receiveData(list)
        }
    }

    fun unitOk() {
        (fragment.activity as AddGoodsActivity).vm.areaArgsIn?.let { fragment.initArea(it) }
    }

    //天数
    var days = mutableListOf(
        DCBean("1天", 1, DCBean.SHOW_KEY),
        DCBean("3天", 3, DCBean.SHOW_KEY),
        DCBean("7天", 7, DCBean.SHOW_KEY)
    )
}