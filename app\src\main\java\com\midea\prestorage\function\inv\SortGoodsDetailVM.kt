package com.midea.prestorage.function.inv

import android.app.Application
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.midea.prestorage.base.BaseViewModel
import com.midea.prestorage.beans.net.ReqQuerySort
import com.midea.prestorage.beans.net.SortInfoItem
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class SortGoodsDetailVM(application: Application) : BaseViewModel(application) {

    var containerCode = ObservableField<String>()
    var showDatas = MutableLiveData<MutableList<SortInfoItem>>()

    override fun init() {
    }

    fun initList() {

        launch(showDialog = true,
            isCancelEnable = false,
            error = {
            }, finish = {
            }) {

            val result = withContext(Dispatchers.IO) {
                RetrofitHelper.getOutStorageAPI()
                    .querySortWait(ReqQuerySort(containerCode = containerCode.get()))
            }

            if (result.code == 0L) {
                result.data?.let {
                    it.forEachIndexed { index, it ->
                        it.index = index + 1
                    }
                    showDatas.value = it
                }
            } else {
                result.msg?.let { showNotification(it, false) }
            }
        }
    }
}