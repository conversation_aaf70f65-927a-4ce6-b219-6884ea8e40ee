package com.midea.prestorage.beans.net

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

data class ResqContainerCode(
    var containerCode: String? = null,
    var waveNo: String? = null,
    var taskCode: String? = null,
    var taskHeaderId: String? = null,
    var comPickNo: String? = null,
    var pickingContainerList: List<PickingContainer>? = null
)

@Parcelize
data class PickingContainer(
    var containerCode: String? = null,
    var taskCode: String? = null,
    var waveNo: String? = null
): Parcelable
