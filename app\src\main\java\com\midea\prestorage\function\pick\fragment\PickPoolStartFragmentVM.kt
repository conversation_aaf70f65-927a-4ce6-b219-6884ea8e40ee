package com.midea.prestorage.function.pick.fragment

import androidx.databinding.ObservableField
import com.google.gson.Gson
import com.midea.mideadspda.http.auxiliary.NetworkScheduler
import com.midea.prestorage.base.BaseActivity
import com.midea.prestorage.beans.help.DCBean
import com.midea.prestorage.beans.net.OutPickPoolStorageDetail
import com.midea.prestorage.function.pick.PickPoolStartActivity
import com.midea.prestorage.http.RetrofitHelper
import com.midea.prestorage.http.auxiliary.ApiErrorModel
import com.midea.prestorage.http.auxiliary.RequestCallback
import com.midea.prestorage.http.constants.Constants
import com.midea.prestorage.utils.DCUtils
import com.midea.prestorage.utils.MySoundUtils
import com.midea.prestorage.utils.ToastUtils
import com.trello.rxlifecycle2.android.ActivityEvent
import com.trello.rxlifecycle2.android.FragmentEvent
import com.trello.rxlifecycle2.components.support.RxAppCompatActivity
import com.trello.rxlifecycle2.kotlin.bindUntilEvent
import okhttp3.MediaType
import okhttp3.RequestBody

class PickPoolStartFragmentVM(val fragment: PickPoolStartFragment) {

    var orderNo = ObservableField<String>("")

    var beans: ArrayList<OutPickPoolStorageDetail.DetailResponses>? = null
    var orderStatus: MutableList<DCBean>? = null

    fun init() {
        beans =
            fragment.arguments?.getSerializable("beans") as ArrayList<OutPickPoolStorageDetail.DetailResponses>?
        fragment.setIsShowHead(fragment.arguments?.getInt("mode", 0) == 0)
        val currentItem = fragment.arguments?.getInt("currentItem", 0)
        val allItem = fragment.arguments?.getInt("allItem", 0)
        if (beans != null && beans!!.isNotEmpty()) {
            fragment.setAdapter(beans!!)
        }

        if (beans != null && beans!!.isNotEmpty()) {
            fragment.setInfo(beans!![0])
            fragment.setPageInfo((currentItem!! + 1).toString() + "/" + allItem!!.toString())
        }

        DCUtils.getGoodsStatusDC(fragment.activity as RxAppCompatActivity, object : DCUtils.DCBack {
            override fun dcBack(statusDC: MutableList<DCBean>) {
                orderStatus = statusDC
                combineDCInfo()
            }
        })
    }

    /**
     * 融合数据字典
     */
    fun combineDCInfo() {
        fragment.adapter.data.forEach {
            if (orderStatus != null) {
                val results = orderStatus?.filter { item ->
                    item.value.toString() == it.lotAtt04
                }
                if (results != null && results.isNotEmpty()) {
                    it.lotAtt04Str = results[0].key
                }
            }
        }
        fragment.adapter.notifyDataSetChanged()
    }

    fun btnClick(bean: OutPickPoolStorageDetail.DetailResponses) {
        if (CheckUtil.isFastDoubleClick()) {
            if (fragment.isStarted || bean.status == 300) {
                complete(bean)
            } else {
                startPick(bean)
            }
        }
    }

    private fun startPick(bean: OutPickPoolStorageDetail.DetailResponses) {
        fragment.waitingDialogHelp!!.showDialogUnCancel()
        val ids = mutableListOf<String>()
        if (bean.ids.isEmpty()) {
            ids.add(bean.id)
        } else {
            ids.addAll(bean.ids)
        }
        val param = mutableMapOf(
            "whCode" to (fragment.activity as BaseActivity).getWhCode(),
            "createUserCode" to Constants.userInfo?.name,
            "detailIds" to ids
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .beginPick(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment, FragmentEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: Any?) {
                    fragment.waitingDialogHelp!!.hidenDialog()
                    ToastUtils.getInstance()
                        .showSuccessToastWithSound(fragment.activity, "开始拣货!")
                    bean.status = 300
                    fragment.adapter.notifyDataSetChanged()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    fragment.waitingDialogHelp!!.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(fragment.activity, apiErrorModel.message)
                    MySoundUtils.getInstance(fragment.activity).errorSound()
                }
            })
    }

    private fun complete(bean: OutPickPoolStorageDetail.DetailResponses) {
        fragment.waitingDialogHelp!!.showDialogUnCancel()
        val ids = mutableListOf<String>()
        if (bean.ids.isEmpty()) {
            ids.add(bean.id)
        } else {
            ids.addAll(bean.ids)
        }
        val param = mutableMapOf(
            "whCode" to (fragment.activity as BaseActivity).getWhCode(),
            "createUserCode" to Constants.userInfo?.name,
            "orderNo" to bean.waveNo,
            "detailIds" to ids
        )

        val requestBody = RequestBody.create(
            MediaType.parse("Content-Type, application/json"),
            Gson().toJson(param)
        )

        RetrofitHelper.getOutStorageAPI()
            .complete(requestBody)
            .compose(NetworkScheduler.compose())
            .bindUntilEvent(fragment, FragmentEvent.DESTROY)
            .subscribe(object : RequestCallback<Any>(fragment.activity as RxAppCompatActivity) {
                override fun success(data: Any?) {
                    fragment.waitingDialogHelp!!.hidenDialog()
                    ToastUtils.getInstance()
                        .showSuccessToastWithSound(fragment.activity, "结束拣货!")
                    bean.status = 900
                    fragment.adapter.notifyDataSetChanged()

                    (fragment.activity as PickPoolStartActivity).addPickNum(bean.requstQty)
                    checkIsNext()
                }

                override fun failure(statusCode: Long, apiErrorModel: ApiErrorModel) {
                    fragment.waitingDialogHelp!!.hidenDialog()
                    ToastUtils.getInstance()
                        .showErrorToastWithSound(fragment.activity, apiErrorModel.message)
                    MySoundUtils.getInstance(fragment.activity).errorSound()
                }
            })
    }

    private fun checkIsNext() {
        val results = beans?.filter { it.status != 900 }
        if (results == null || results.isEmpty()) {
            (fragment.activity as PickPoolStartActivity).next()
        }
        (fragment.activity as PickPoolStartActivity).checkStatue()
    }
}